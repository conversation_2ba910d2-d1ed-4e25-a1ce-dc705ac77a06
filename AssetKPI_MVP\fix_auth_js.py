# Create a temporary file with the fixed code
with open('static/js/auth.js', 'r') as file:
    content = file.read()

# Find the Firebase configuration section
firebase_config_section = '''// Firebase configuration - **MUST be loaded securely from backend/environment variables**
// DO NOT HARDCODE KEYS HERE
const firebaseConfig = {
    apiKey: "YOUR_API_KEY", // Replace with value loaded securely
    authDomain: "YOUR_AUTH_DOMAIN", // Replace with value loaded securely
    projectId: "YOUR_PROJECT_ID", // Replace with value loaded securely
    storageBucket: "YOUR_STORAGE_BUCKET", // Replace with value loaded securely
    messagingSenderId: "YOUR_MESSAGING_SENDER_ID", // Replace with value loaded securely
    appId: "YOUR_APP_ID" // Replace with value loaded securely
};

// Initialize Firebase - Ensure firebaseConfig is populated before calling this
// Consider initializing Firebase only after the config is loaded.
if (firebaseConfig.apiKey !== "YOUR_API_KEY") { // Basic check if config seems loaded
    firebase.initializeApp(firebaseConfig);
} else {
    console.error("Firebase configuration is missing. Cannot initialize Firebase.");
    // Optionally, disable auth features or show an error message to the user
}'''

# Replace it with the updated section
updated_section = '''// Firebase configuration is now loaded from the server in each page that needs it
// The login.html page initializes Firebase with the configuration from the server
// DO NOT HARDCODE KEYS HERE

// We assume Firebase is already initialized by the page that includes this script
// If Firebase is not initialized, auth functions will fail gracefully with error messages'''

content = content.replace(firebase_config_section, updated_section)

# Write the fixed content back to the file
with open('static/js/auth.js', 'w') as file:
    file.write(content)

print("Fixed the auth.js file")
