# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Replace the problematic import
old_import = """# Local application imports
# Import directly from models.py
from models import Base, UserRole, UserPermission, User, KpiReport, Sparepart, WorkOrder
from models import WorkOrderParts, KPI, CalculatedKpiHistory, InventoryRecommendation
from models import EOQCalculation, SafetyStockCalculation, InventoryAnalysis, InventoryConfig
from models import AssetLocation, AssetSystem, AssetCategory, Asset, AssetSpecification
from models import AssetWarranty, WorkOrderPlan, WorkOrderTask, LaborResource, WorkOrderLabor
from models import PMSchedule, PMJobPlan, PMJobTask, AssetMeter, MeterReading, Storeroom
from models import StorageLocation, Vendor, PurchaseOrder, POItem, InventoryTransaction
from models import ResourceSkill, ToolsEquipment, WorkOrderTool, TechnicalDocument
from models import AssetDocument, SafetyProcedure, WorkOrderProcedure
# Import from models package
from models.usage_analytics import UserActivityLog"""

new_import = """# Local application imports
# Import directly from db_models.py
from db_models import Base, UserRole, UserPermission, User, KpiReport, Sparepart, WorkOrder
from db_models import WorkOrderParts, KPI, CalculatedKpiHistory, InventoryRecommendation
from db_models import EOQCalculation, SafetyStockCalculation, InventoryAnalysis, InventoryConfig
from db_models import AssetLocation, AssetSystem, AssetCategory, Asset, AssetSpecification
from db_models import AssetWarranty, WorkOrderPlan, WorkOrderTask, LaborResource, WorkOrderLabor
from db_models import PMSchedule, PMJobPlan, PMJobTask, AssetMeter, MeterReading, Storeroom
from db_models import StorageLocation, Vendor, PurchaseOrder, POItem, InventoryTransaction
from db_models import ResourceSkill, ToolsEquipment, WorkOrderTool, TechnicalDocument
from db_models import AssetDocument, SafetyProcedure, WorkOrderProcedure
# Import from models package
from models.usage_analytics import UserActivityLog"""

content = content.replace(old_import, new_import)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed the imports in main.py")
