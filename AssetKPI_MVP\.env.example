# Database Configuration
DATABASE_URL=postgresql://postgres:Arcanum@localhost:5432/AssetKPI

# API Security
SECRET_API_KEY=c5e52be8-9b1c-4fcd-8457-741c91ef5c85

# Firebase Configuration
FIREBASE_SERVICE_ACCOUNT_KEY=firebase-service-account.json
FIREBASE_PROJECT_ID=ikios-59679
FIREBASE_WEB_API_KEY=AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4

# Application Configuration
APP_NAME=AssetKPI
APP_VERSION=1.0.0
DEBUG=False
HOST=0.0.0.0
PORT=8000

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
