<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth.js Tests</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .failure {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-log {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .test-item {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .test-pass {
            background-color: #d4edda;
        }
        .test-fail {
            background-color: #f8d7da;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0069d9;
        }
    </style>
</head>
<body>
    <h1>Auth.js Unit Tests</h1>
    
    <button id="runTestsBtn">Run Tests</button>
    
    <div id="testResults" class="test-results" style="display: none;"></div>
    
    <div id="testLog" class="test-log"></div>
    
    <!-- Include Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Include the auth.js file -->
    <script src="/static/js/auth.js"></script>
    
    <!-- Include the test file -->
    <script src="/static/js/auth.test.js"></script>
    
    <script>
        // Override console.log to capture output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const logElement = document.getElementById('testLog');
        
        console.log = function() {
            // Call original console.log
            originalConsoleLog.apply(console, arguments);
            
            // Add to log element
            const args = Array.from(arguments);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const logItem = document.createElement('div');
            logItem.className = 'test-item';
            logItem.textContent = message;
            
            if (message.includes('✅ PASSED:')) {
                logItem.classList.add('test-pass');
            } else if (message.includes('❌ FAILED:')) {
                logItem.classList.add('test-fail');
            }
            
            logElement.appendChild(logItem);
            logElement.scrollTop = logElement.scrollHeight;
        };
        
        console.error = function() {
            // Call original console.error
            originalConsoleError.apply(console, arguments);
            
            // Add to log element
            const args = Array.from(arguments);
            const message = args.map(arg => 
                typeof arg === 'object' && arg instanceof Error ? arg.stack : 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const logItem = document.createElement('div');
            logItem.className = 'test-item test-fail';
            logItem.textContent = message;
            
            logElement.appendChild(logItem);
            logElement.scrollTop = logElement.scrollHeight;
        };
        
        // Initialize Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
            authDomain: "ikios-59679.firebaseapp.com",
            projectId: "ikios-59679",
            storageBucket: "ikios-59679.appspot.com",
            messagingSenderId: "1045286122604",
            appId: "1:1045286122604:web:c9e9c9b9b9b9b9b9b9b9b9"
        };
        
        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        
        // Set up run tests button
        document.getElementById('runTestsBtn').addEventListener('click', function() {
            // Clear log
            logElement.innerHTML = '';
            
            // Run tests
            const results = runAuthTests();
            
            // Display results
            const resultsElement = document.getElementById('testResults');
            resultsElement.style.display = 'block';
            
            if (results.failed === 0) {
                resultsElement.className = 'test-results success';
                resultsElement.innerHTML = `
                    <h2>All Tests Passed! 🎉</h2>
                    <p>${results.passed} of ${results.total} tests passed.</p>
                `;
            } else {
                resultsElement.className = 'test-results failure';
                resultsElement.innerHTML = `
                    <h2>Tests Failed 😞</h2>
                    <p>${results.passed} of ${results.total} tests passed. ${results.failed} tests failed.</p>
                    <p>Check the log below for details.</p>
                `;
            }
        });
    </script>
</body>
</html>
