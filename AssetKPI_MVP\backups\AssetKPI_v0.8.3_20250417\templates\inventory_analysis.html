{% extends "layout.html" %}

{% block title %}Inventory Analysis | AssetKPI{% endblock %}

{% block styles %}
<style>
    .card-dashboard {
        transition: transform 0.3s;
    }
    .card-dashboard:hover {
        transform: translateY(-5px);
    }
    .metric-value {
        font-size: 1.8rem;
        font-weight: bold;
    }
    .metric-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }
    .savings-high {
        color: #28a745;
        font-weight: bold;
    }
    .savings-medium {
        color: #fd7e14;
        font-weight: bold;
    }
    .risk-high {
        color: #dc3545;
        font-weight: bold;
    }
    .risk-medium {
        color: #fd7e14;
        font-weight: bold;
    }
    .risk-low {
        color: #28a745;
    }
    .chart-container {
        height: 300px;
        margin-bottom: 20px;
    }
</style>
<!-- Add Chart.js library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mt-4">Inventory Analysis</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/inventory">Inventory</a></li>
                <li class="breadcrumb-item active">Analysis</li>
            </ol>
        </div>
        <div class="auth-required" data-role="MANAGER,ADMIN">
            <button id="runOptimizationBtn" class="btn btn-primary">
                <i class="fas fa-sync-alt"></i> Run Optimization
            </button>
        </div>
    </div>

    <!-- Summary Metrics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4 card-dashboard">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="metric-value" id="totalParts">-</div>
                            <div class="metric-label">Total Parts</div>
                        </div>
                        <div class="text-white">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4 card-dashboard">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="metric-value" id="totalSavings">-</div>
                            <div class="metric-label">Potential Savings</div>
                        </div>
                        <div class="text-white">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4 card-dashboard">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="metric-value" id="overstock">-</div>
                            <div class="metric-label">Overstocked Parts</div>
                        </div>
                        <div class="text-white">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4 card-dashboard">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="metric-value" id="stockoutRisk">-</div>
                            <div class="metric-label">Parts at Risk</div>
                        </div>
                        <div class="text-white">
                            <i class="fas fa-exclamation-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    Top 10 Potential Savings
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="savingsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Inventory Value by ABC Classification
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="abcChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Analysis Table -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Inventory Analysis
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="inventoryAnalysisTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Part ID</th>
                            <th>Part Name</th>
                            <th>Part Number</th>
                            <th>Current Stock</th>
                            <th>Optimal Stock</th>
                            <th>Difference</th>
                            <th>Current Cost</th>
                            <th>Optimal Cost</th>
                            <th>Potential Savings</th>
                            <th>Days of Supply</th>
                            <th>Stockout Risk</th>
                            <th>ABC Class</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="analysisTableBody">
                        <tr>
                            <td colspan="13" class="text-center">Loading inventory analysis data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Part Detail Modal -->
    <div class="modal fade" id="partDetailModal" tabindex="-1" aria-labelledby="partDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="partDetailModalLabel">Part Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Part Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th>Part ID:</th>
                                    <td id="modal-part-id"></td>
                                </tr>
                                <tr>
                                    <th>Part Name:</th>
                                    <td id="modal-part-name"></td>
                                </tr>
                                <tr>
                                    <th>Part Number:</th>
                                    <td id="modal-part-number"></td>
                                </tr>
                                <tr>
                                    <th>Manufacturer:</th>
                                    <td id="modal-manufacturer"></td>
                                </tr>
                                <tr>
                                    <th>Current Stock:</th>
                                    <td id="modal-stock-quantity"></td>
                                </tr>
                                <tr>
                                    <th>Unit Price:</th>
                                    <td id="modal-unit-price"></td>
                                </tr>
                                <tr>
                                    <th>ABC Classification:</th>
                                    <td id="modal-abc-classification"></td>
                                </tr>
                                <tr>
                                    <th>Last Restocked:</th>
                                    <td id="modal-last-restocked"></td>
                                </tr>
                                <tr>
                                    <th>Monthly Consumption:</th>
                                    <td id="modal-monthly-consumption"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Analysis Results</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th>Current Stock:</th>
                                    <td id="modal-current-stock"></td>
                                </tr>
                                <tr>
                                    <th>Optimal Stock:</th>
                                    <td id="modal-optimal-stock"></td>
                                </tr>
                                <tr>
                                    <th>Stock Difference:</th>
                                    <td id="modal-stock-difference"></td>
                                </tr>
                                <tr>
                                    <th>Current Cost:</th>
                                    <td id="modal-current-cost"></td>
                                </tr>
                                <tr>
                                    <th>Optimal Cost:</th>
                                    <td id="modal-optimal-cost"></td>
                                </tr>
                                <tr>
                                    <th>Potential Savings:</th>
                                    <td id="modal-potential-savings"></td>
                                </tr>
                                <tr>
                                    <th>Days of Supply:</th>
                                    <td id="modal-days-supply"></td>
                                </tr>
                                <tr>
                                    <th>Stockout Risk:</th>
                                    <td id="modal-stockout-risk"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>EOQ Calculation</h6>
                            <table class="table table-sm" id="eoq-table">
                                <tr>
                                    <th>Annual Demand:</th>
                                    <td id="modal-annual-demand"></td>
                                </tr>
                                <tr>
                                    <th>Ordering Cost:</th>
                                    <td id="modal-ordering-cost"></td>
                                </tr>
                                <tr>
                                    <th>Holding Cost:</th>
                                    <td id="modal-holding-cost"></td>
                                </tr>
                                <tr>
                                    <th>EOQ Value:</th>
                                    <td id="modal-eoq-value"></td>
                                </tr>
                                <tr>
                                    <th>Annual Ordering Cost:</th>
                                    <td id="modal-annual-ordering-cost"></td>
                                </tr>
                                <tr>
                                    <th>Annual Holding Cost:</th>
                                    <td id="modal-annual-holding-cost"></td>
                                </tr>
                                <tr>
                                    <th>Total Annual Cost:</th>
                                    <td id="modal-total-annual-cost"></td>
                                </tr>
                                <tr>
                                    <th>Optimal Order Frequency:</th>
                                    <td id="modal-optimal-order-frequency"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Safety Stock Calculation</h6>
                            <table class="table table-sm" id="ss-table">
                                <tr>
                                    <th>Avg Daily Demand:</th>
                                    <td id="modal-avg-daily-demand"></td>
                                </tr>
                                <tr>
                                    <th>Lead Time Days:</th>
                                    <td id="modal-lead-time-days"></td>
                                </tr>
                                <tr>
                                    <th>Demand Variability:</th>
                                    <td id="modal-demand-variability"></td>
                                </tr>
                                <tr>
                                    <th>Lead Time Variability:</th>
                                    <td id="modal-lead-time-variability"></td>
                                </tr>
                                <tr>
                                    <th>Service Level:</th>
                                    <td id="modal-service-level"></td>
                                </tr>
                                <tr>
                                    <th>Safety Stock Value:</th>
                                    <td id="modal-safety-stock-value"></td>
                                </tr>
                                <tr>
                                    <th>Reorder Point:</th>
                                    <td id="modal-reorder-point"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="#" id="modal-view-part-link" class="btn btn-primary">View in Inventory</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize authentication UI
        AssetKPIAuth.initAuth(updateUI);

        // Load inventory analysis data
        loadInventoryAnalysis();

        // Add event listener for run optimization button
        document.getElementById('runOptimizationBtn').addEventListener('click', runOptimization);

        // Initialize charts
        let savingsChart = null;
        let abcChart = null;

        function loadInventoryAnalysis() {
            AssetKPIAuth.authenticatedFetch('/api/inventory/analysis')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch inventory analysis data');
                    }
                    return response.json();
                })
                .then(data => {
                    // Update summary metrics
                    updateSummaryMetrics(data);

                    // Update charts
                    updateCharts(data);

                    // Update table
                    updateAnalysisTable(data);
                })
                .catch(error => {
                    console.error('Error loading inventory analysis:', error);
                    document.getElementById('analysisTableBody').innerHTML =
                        `<tr><td colspan="13" class="text-center text-danger">Error loading data: ${error.message}</td></tr>`;
                });
        }

        function updateSummaryMetrics(data) {
            // Calculate summary metrics
            const totalParts = data.length;
            const totalSavings = data.reduce((sum, item) => sum + item.potential_savings, 0);
            const overstock = data.filter(item => item.stock_difference > 0).length;
            const stockoutRisk = data.filter(item => item.stockout_risk > 20).length;

            // Update the UI
            document.getElementById('totalParts').textContent = totalParts;
            document.getElementById('totalSavings').textContent = '$' + totalSavings.toFixed(2);
            document.getElementById('overstock').textContent = overstock;
            document.getElementById('stockoutRisk').textContent = stockoutRisk;
        }

        function updateCharts(data) {
            // Top 10 Potential Savings Chart
            const topSavings = [...data]
                .sort((a, b) => b.potential_savings - a.potential_savings)
                .slice(0, 10);

            const savingsCtx = document.getElementById('savingsChart').getContext('2d');
            if (savingsChart) {
                savingsChart.destroy();
            }

            savingsChart = new Chart(savingsCtx, {
                type: 'bar',
                data: {
                    labels: topSavings.map(item => item.part_name),
                    datasets: [{
                        label: 'Potential Savings ($)',
                        data: topSavings.map(item => item.potential_savings),
                        backgroundColor: 'rgba(40, 167, 69, 0.7)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Potential Savings ($)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Part Name'
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    }
                }
            });

            // ABC Classification Chart
            const abcData = {
                'A': 0,
                'B': 0,
                'C': 0,
                'Unclassified': 0
            };

            data.forEach(item => {
                const classification = item.abc_classification || 'Unclassified';
                abcData[classification] += item.current_cost;
            });

            const abcCtx = document.getElementById('abcChart').getContext('2d');
            if (abcChart) {
                abcChart.destroy();
            }

            abcChart = new Chart(abcCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys(abcData),
                    datasets: [{
                        data: Object.values(abcData),
                        backgroundColor: [
                            'rgba(220, 53, 69, 0.7)',  // A - Red
                            'rgba(255, 193, 7, 0.7)',  // B - Yellow
                            'rgba(40, 167, 69, 0.7)',  // C - Green
                            'rgba(108, 117, 125, 0.7)' // Unclassified - Gray
                        ],
                        borderColor: [
                            'rgba(220, 53, 69, 1)',
                            'rgba(255, 193, 7, 1)',
                            'rgba(40, 167, 69, 1)',
                            'rgba(108, 117, 125, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const percentage = (value / Object.values(abcData).reduce((a, b) => a + b, 0) * 100).toFixed(1);
                                    return `${label}: $${value.toFixed(2)} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateAnalysisTable(data) {
            const tableBody = document.getElementById('analysisTableBody');

            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="13" class="text-center">No inventory analysis data available</td></tr>';
                return;
            }

            let tableHtml = '';
            data.forEach(item => {
                const savingsClass = item.potential_savings > 100 ? 'savings-high' :
                                    item.potential_savings > 50 ? 'savings-medium' : '';

                const riskClass = item.stockout_risk > 50 ? 'risk-high' :
                                 item.stockout_risk > 20 ? 'risk-medium' : 'risk-low';

                tableHtml += `
                    <tr>
                        <td>${item.part_id}</td>
                        <td>${item.part_name}</td>
                        <td>${item.part_number}</td>
                        <td>${item.current_stock}</td>
                        <td>${item.optimal_stock}</td>
                        <td>${item.stock_difference}</td>
                        <td>$${item.current_cost.toFixed(2)}</td>
                        <td>$${item.optimal_cost.toFixed(2)}</td>
                        <td class="${savingsClass}">$${item.potential_savings.toFixed(2)}</td>
                        <td>${item.days_of_supply}</td>
                        <td class="${riskClass}">${item.stockout_risk.toFixed(1)}%</td>
                        <td>${item.abc_classification || 'N/A'}</td>
                        <td>
                            <button class="btn btn-sm btn-primary view-details" data-part-id="${item.part_id}">
                                <i class="fas fa-search"></i> Details
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableBody.innerHTML = tableHtml;

            // Add event listeners to view details buttons
            document.querySelectorAll('.view-details').forEach(button => {
                button.addEventListener('click', function() {
                    const partId = this.getAttribute('data-part-id');
                    showPartDetails(partId);
                });
            });
        }

        function showPartDetails(partId) {
            AssetKPIAuth.authenticatedFetch(`/api/inventory/analysis/${partId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch part details');
                    }
                    return response.json();
                })
                .then(data => {
                    // Update modal with part details
                    updatePartDetailModal(data);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('partDetailModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading part details:', error);
                    alert(`Error loading part details: ${error.message}`);
                });
        }

        function updatePartDetailModal(data) {
            // Part Information
            document.getElementById('modal-part-id').textContent = data.part.part_id;
            document.getElementById('modal-part-name').textContent = data.part.part_name;
            document.getElementById('modal-part-number').textContent = data.part.part_number;
            document.getElementById('modal-manufacturer').textContent = data.part.manufacturer || 'N/A';
            document.getElementById('modal-stock-quantity').textContent = data.part.stock_quantity;
            document.getElementById('modal-unit-price').textContent = data.part.unit_price ? `$${data.part.unit_price.toFixed(2)}` : 'N/A';
            document.getElementById('modal-abc-classification').textContent = data.part.abc_classification || 'N/A';
            document.getElementById('modal-last-restocked').textContent = data.part.last_restocked ? new Date(data.part.last_restocked).toLocaleDateString() : 'N/A';
            document.getElementById('modal-monthly-consumption').textContent = data.part.avg_monthly_consumption ? data.part.avg_monthly_consumption.toFixed(2) : 'N/A';

            // Analysis Results
            if (data.analysis) {
                document.getElementById('modal-current-stock').textContent = data.analysis.current_stock;
                document.getElementById('modal-optimal-stock').textContent = data.analysis.optimal_stock;
                document.getElementById('modal-stock-difference').textContent = data.analysis.stock_difference;
                document.getElementById('modal-current-cost').textContent = `$${data.analysis.current_cost.toFixed(2)}`;
                document.getElementById('modal-optimal-cost').textContent = `$${data.analysis.optimal_cost.toFixed(2)}`;
                document.getElementById('modal-potential-savings').textContent = `$${data.analysis.potential_savings.toFixed(2)}`;
                document.getElementById('modal-days-supply').textContent = data.analysis.days_of_supply;
                document.getElementById('modal-stockout-risk').textContent = `${data.analysis.stockout_risk.toFixed(1)}%`;
            }

            // EOQ Calculation
            if (data.eoq) {
                document.getElementById('eoq-table').style.display = 'table';
                document.getElementById('modal-annual-demand').textContent = data.eoq.annual_demand ? data.eoq.annual_demand.toFixed(2) : 'N/A';
                document.getElementById('modal-ordering-cost').textContent = `$${data.eoq.ordering_cost.toFixed(2)}`;
                document.getElementById('modal-holding-cost').textContent = `$${data.eoq.holding_cost.toFixed(2)}`;
                document.getElementById('modal-eoq-value').textContent = data.eoq.eoq_value.toFixed(2);
                document.getElementById('modal-annual-ordering-cost').textContent = `$${data.eoq.annual_ordering_cost.toFixed(2)}`;
                document.getElementById('modal-annual-holding-cost').textContent = `$${data.eoq.annual_holding_cost.toFixed(2)}`;
                document.getElementById('modal-total-annual-cost').textContent = `$${data.eoq.total_annual_cost.toFixed(2)}`;
                document.getElementById('modal-optimal-order-frequency').textContent = `${data.eoq.optimal_order_frequency} orders/year`;
            } else {
                document.getElementById('eoq-table').style.display = 'none';
            }

            // Safety Stock Calculation
            if (data.safety_stock) {
                document.getElementById('ss-table').style.display = 'table';
                document.getElementById('modal-avg-daily-demand').textContent = data.safety_stock.avg_daily_demand ? data.safety_stock.avg_daily_demand.toFixed(2) : 'N/A';
                document.getElementById('modal-lead-time-days').textContent = data.safety_stock.lead_time_days;
                document.getElementById('modal-demand-variability').textContent = `${(data.safety_stock.demand_variability * 100).toFixed(1)}%`;
                document.getElementById('modal-lead-time-variability').textContent = `${(data.safety_stock.lead_time_variability * 100).toFixed(1)}%`;
                document.getElementById('modal-service-level').textContent = `${(data.safety_stock.service_level * 100).toFixed(1)}%`;
                document.getElementById('modal-safety-stock-value').textContent = data.safety_stock.safety_stock_value.toFixed(2);
                document.getElementById('modal-reorder-point').textContent = data.safety_stock.reorder_point.toFixed(2);
            } else {
                document.getElementById('ss-table').style.display = 'none';
            }

            // Update link to view part in inventory
            document.getElementById('modal-view-part-link').href = `/inventory?part=${data.part.part_id}`;

            // Update modal title
            document.getElementById('partDetailModalLabel').textContent = `Part Details: ${data.part.part_name} (${data.part.part_number})`;
        }

        function updateUI(user) {
            // This function is called after authentication state changes
            if (user) {
                // User is signed in, load data
                loadInventoryAnalysis();
            } else {
                // User is signed out, show message
                document.getElementById('analysisTableBody').innerHTML =
                    '<tr><td colspan="13" class="text-center">Please sign in to view inventory analysis</td></tr>';
            }
        }

        function runOptimization() {
            // Disable the button and show loading state
            const button = document.getElementById('runOptimizationBtn');
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running...';

            // Call the API to run the optimization job
            AssetKPIAuth.authenticatedFetch('/api/inventory/run-optimization')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to run optimization job');
                    }
                    return response.json();
                })
                .then(data => {
                    // Show success message
                    alert('Optimization job completed successfully!');

                    // Reload the data
                    loadInventoryAnalysis();
                })
                .catch(error => {
                    console.error('Error running optimization job:', error);
                    alert(`Error running optimization job: ${error.message}`);
                })
                .finally(() => {
                    // Re-enable the button
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
        }
    });
</script>
{% endblock %}
