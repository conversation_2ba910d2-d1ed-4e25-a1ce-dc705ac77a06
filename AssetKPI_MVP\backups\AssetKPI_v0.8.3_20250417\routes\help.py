"""
Help routes for the AssetKPI application.

This module defines the API routes for the help system.
"""

import logging
import json
import os
from typing import Dict, List, Any, Optional, Callable

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from models.user import User as UserModel

# Create a logger for this module
logger = logging.getLogger(__name__)

# Create a router for help endpoints
help_router = APIRouter(prefix="/api/help", tags=["Help"])

# Store dependencies
get_db_dependency = None
get_current_user_dependency = None


def init_router(
    get_db: Callable,
    get_current_user: Callable
):
    """
    Initialize the router with dependencies.
    
    Args:
        get_db: Dependency to get database session
        get_current_user: Dependency to get current user
    """
    global get_db_dependency, get_current_user_dependency
    
    get_db_dependency = get_db
    get_current_user_dependency = get_current_user


@help_router.get(
    "/content",
    summary="Get help content"
)
async def get_help_content(
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Get help content.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Help content
    """
    try:
        # Get help content file path
        help_content_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "static",
            "js",
            "help",
            "help-content.json"
        )
        
        # Check if help content file exists
        if not os.path.exists(help_content_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Help content not found"
            )
        
        # Read help content file
        with open(help_content_path, "r") as f:
            help_content = json.load(f)
        
        # Filter content based on user role
        filtered_content = filter_help_content_by_role(help_content, current_user.role)
        
        return filtered_content
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting help content: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting help content: {str(e)}"
        )


def filter_help_content_by_role(help_content: Dict[str, Any], role: str) -> Dict[str, Any]:
    """
    Filter help content based on user role.
    
    Args:
        help_content: Help content
        role: User role
        
    Returns:
        Filtered help content
    """
    # In a real implementation, you would filter content based on user role
    # For now, we'll just return all content
    return help_content
