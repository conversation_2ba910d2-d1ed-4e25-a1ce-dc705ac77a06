# AssetKPI Python SDK

A Python SDK for interacting with the AssetKPI API.

## Installation

```bash
pip install assetkpi-sdk
```

## Quick Start

```python
from assetkpi import AssetKPISDK

# Initialize the SDK with API key
sdk = AssetKPISDK(
    base_url="http://localhost:8000/api",
    api_key="your-api-key"
)

# Get inventory summary
inventory_summary = sdk.inventory.get_inventory_summary()
print(f"Total Parts: {inventory_summary['total_parts']}")
print(f"Total Value: ${inventory_summary['total_value']:.2f}")
print(f"Parts Below Reorder Level: {inventory_summary['below_reorder']}")

# Get latest KPIs
kpis = sdk.kpi.get_latest_kpis()
print(f"MTTR: {kpis['mttr']} hours")
print(f"MTBF: {kpis['mtbf']} hours")
print(f"Failure Rate: {kpis['failure_rate']} failures/year")
```

## Authentication

The SDK supports two authentication methods:

### API Key Authentication

```python
sdk = AssetKPISDK(
    base_url="http://localhost:8000/api",
    api_key="your-api-key"
)
```

### Firebase Authentication

```python
sdk = AssetKPISDK(
    base_url="http://localhost:8000/api",
    firebase_id_token="your-firebase-id-token"
)
```

## API Clients

The SDK provides the following API clients:

- `sdk.assets`: Asset-related endpoints
- `sdk.inventory`: Inventory-related endpoints
- `sdk.kpi`: KPI-related endpoints
- `sdk.workorders`: Work order-related endpoints
- `sdk.users`: User-related endpoints
- `sdk.analytics`: Analytics-related endpoints
- `sdk.recommendations`: Recommendation-related endpoints

## Examples

### Working with Assets

```python
# Get all assets
assets = sdk.assets.get_assets(limit=10)
for asset in assets["items"]:
    print(f"Asset ID: {asset['assetid']}, Name: {asset['assetname']}")

# Get a specific asset
asset = sdk.assets.get_asset(1)
print(f"Asset Details: {asset['assetname']} ({asset['assettype']})")

# Create a new asset
new_asset = {
    "assetname": "New Pump",
    "assettype": "Pump",
    "location": "Building A",
    "status": "Active"
}
result = sdk.assets.create_asset(new_asset)
print(f"Created asset with ID: {result['assetid']}")

# Update an asset
updates = {
    "status": "Inactive"
}
result = sdk.assets.update_asset(1, updates)
print(f"Update result: {result['message']}")

# Delete an asset
result = sdk.assets.delete_asset(1)
print(f"Delete result: {result['message']}")
```

### Working with Inventory

```python
# Get all spare parts
parts = sdk.inventory.get_parts(limit=10)
for part in parts["items"]:
    print(f"Part ID: {part['partid']}, Name: {part['partname']}, Stock: {part['stockquantity']}")

# Get a specific part
part = sdk.inventory.get_part(1)
print(f"Part Details: {part['partname']} ({part['partnumber']})")
print(f"Stock: {part['stockquantity']}, Reorder Level: {part['reorderlevel']}")

# Update a part
updates = {
    "stockquantity": 20,
    "reorderlevel": 8
}
result = sdk.inventory.update_part(1, updates)
print(f"Update result: {result['message']}")

# Get inventory analysis
analysis = sdk.inventory.get_inventory_analysis()
for item in analysis:
    print(f"Part: {item['part_name']}")
    print(f"  Current Stock: {item['current_stock']}, Optimal Stock: {item['optimal_stock']}")
    print(f"  Potential Savings: ${item['potential_savings']:.2f}")
    print(f"  Stockout Risk: {item['stockout_risk']}%")

# Run inventory optimization
result = sdk.inventory.run_optimization()
print(f"Optimization result: {result['message']}")
```

### Working with KPIs

```python
# Get latest KPIs
kpis = sdk.kpi.get_latest_kpis()
print(f"MTTR: {kpis['mttr']} hours")
print(f"MTBF: {kpis['mtbf']} hours")
print(f"Failure Rate: {kpis['failure_rate']} failures/year")

# Get KPI history
history = sdk.kpi.get_kpi_history("MTTR_Calculated", start_date="2023-01-01", end_date="2023-12-31")
for point in history:
    print(f"Date: {point['timestamp']}, Value: {point['value']}")

# Get KPI analytics
analytics = sdk.kpi.get_kpi_analytics(kpi_type="mttr", time_aggregation="monthly")
print(f"Analytics data: {analytics}")
```

### Working with Work Orders

```python
# Get all work orders
work_orders = sdk.workorders.get_workorders(limit=10)
for wo in work_orders["items"]:
    print(f"WO ID: {wo['workorderid']}, Type: {wo['workordertype']}, Status: {wo['status']}")

# Get a specific work order
wo = sdk.workorders.get_workorder(1)
print(f"WO Details: {wo['description']}")
print(f"Asset ID: {wo['assetid']}, Status: {wo['status']}")

# Create a new work order
new_wo = {
    "assetId": 1,
    "workOrderType": "Corrective",
    "description": "Fix pump leak",
    "status": "OPEN",
    "assignedTo": "John Smith"
}
result = sdk.workorders.create_workorder(new_wo)
print(f"Created work order with ID: {result['workorderid']}")

# Update a work order
updates = {
    "status": "CLOSED",
    "endDate": "2023-04-15T14:30:00Z",
    "repairTimeMinutes": 45
}
result = sdk.workorders.update_workorder(1, updates)
print(f"Update result: {result['message']}")

# Get work order count
count = sdk.workorders.get_workorders_count(status="OPEN")
print(f"Open work orders: {count['count']}")
```

### Working with Users

```python
# Get current user
user = sdk.users.get_current_user()
print(f"Current User: {user['full_name']} ({user['email']})")
print(f"Role: {user['role']}")

# Get all users (admin only)
users = sdk.users.get_users()
for user in users["items"]:
    print(f"User: {user['full_name']} ({user['email']}), Role: {user['role']}")

# Get user permissions
permissions = sdk.users.get_user_permissions("user-id")
print(f"Permissions: {permissions['permissions']}")

# Update user permissions
new_permissions = {
    "role": "MANAGER",
    "permissions": ["assets:read", "assets:write", "inventory:read", "inventory:write"]
}
result = sdk.users.update_user_permissions("user-id", new_permissions)
print(f"Update result: {result['message']}")

# Generate API key
api_key = sdk.users.generate_api_key("user-id")
print(f"API Key: {api_key['api_key']}")

# Revoke API key
result = sdk.users.revoke_api_key("user-id")
print(f"Revoke result: {result['message']}")
```

### Working with Analytics

```python
# Get user activity
activity = sdk.analytics.get_user_activity(start_date="2023-01-01", end_date="2023-12-31")
for event in activity["items"]:
    print(f"User: {event['user_id']}, Event: {event['event_type']}, Time: {event['timestamp']}")

# Get feature usage
usage = sdk.analytics.get_feature_usage(feature="dashboard", aggregation="daily")
for day in usage["data"]:
    print(f"Date: {day['date']}, Count: {day['count']}")

# Get user sessions
sessions = sdk.analytics.get_user_sessions(user_id="user-id")
for session in sessions["items"]:
    print(f"Session ID: {session['session_id']}, Duration: {session['duration_seconds']} seconds")
```

### Working with Recommendations

```python
# Get recommendations
recommendations = sdk.recommendations.get_recommendations(status="NEW")
for rec in recommendations["items"]:
    print(f"Recommendation: {rec['recommendation_type']} for Part ID: {rec['part_id']}")
    print(f"  Message: {rec['message']}")
    print(f"  Priority: {rec['priority']}")

# Update recommendation status
result = sdk.recommendations.update_recommendation_status(1, "ACKNOWLEDGED")
print(f"Update result: {result['message']}")
```

## Error Handling

The SDK provides detailed error handling:

```python
from assetkpi import AssetKPISDK, AuthenticationError, RateLimitError, APIError

sdk = AssetKPISDK(
    base_url="http://localhost:8000/api",
    api_key="your-api-key"
)

try:
    # Make API request
    result = sdk.inventory.get_parts()
    print(f"Found {len(result['items'])} parts")
except AuthenticationError as e:
    print(f"Authentication error: {str(e)}")
except RateLimitError as e:
    print(f"Rate limit exceeded. Retry after {e.retry_after} seconds")
except APIError as e:
    print(f"API error ({e.status_code}): {str(e)}")
except Exception as e:
    print(f"Unexpected error: {str(e)}")
```

## Advanced Configuration

```python
# Configure with custom retry settings
sdk = AssetKPISDK(
    base_url="http://localhost:8000/api",
    api_key="your-api-key",
    timeout=60,  # 60 second timeout
    max_retries=5,  # Retry up to 5 times
    retry_backoff_factor=1.0,  # Longer backoff between retries
    retry_status_codes=[429, 500, 502, 503, 504]  # Status codes to retry
)
```

## License

This SDK is licensed under the MIT License.
