{"version": "1.0.0", "topics": [{"id": "dashboard", "title": "Dashboard", "description": "Overview of the AssetKPI dashboard", "route": "/dashboard", "content": "<p>The dashboard provides an overview of your assets, inventory, and key performance indicators.</p><p>Use the dashboard to monitor the health of your assets, track inventory levels, and view recent work orders.</p>", "elements": [{"id": "asset-summary", "selector": ".asset-summary-card", "title": "<PERSON><PERSON> Summary", "content": "The Asset Summary card shows the total number of assets by status. Click on a status to view assets with that status.", "type": "tooltip"}, {"id": "kpi-overview", "selector": ".kpi-overview-card", "title": "KPI Overview", "content": "The KPI Overview card shows key performance indicators for your assets, including MTTR, MTBF, and OEE.", "type": "tooltip"}, {"id": "recent-work-orders", "selector": ".recent-work-orders-card", "title": "Recent Work Orders", "content": "The Recent Work Orders card shows the most recent work orders created or updated in the system.", "type": "tooltip"}, {"id": "inventory-status", "selector": ".inventory-status-card", "title": "Inventory Status", "content": "The Inventory Status card shows the status of your inventory, including items that are below the reorder point.", "type": "tooltip"}]}, {"id": "assets", "title": "Asset Management", "description": "Manage your assets in AssetKPI", "route": "/assets", "content": "<p>The Asset Management page allows you to view, create, edit, and delete assets in your organization.</p><p>Assets are the core of your maintenance operations. They represent the equipment, machinery, vehicles, and other items that you maintain and track performance for.</p>", "elements": [{"id": "add-asset", "selector": ".add-asset-btn", "title": "Add <PERSON>set", "content": "Click this button to add a new asset to your inventory.", "type": "tooltip"}, {"id": "asset-filter", "selector": ".asset-filter", "title": "<PERSON><PERSON>", "content": "Use these filters to find specific assets based on status, type, or location.", "type": "tooltip"}, {"id": "asset-search", "selector": ".asset-search", "title": "Search Assets", "content": "Enter keywords to search for assets by name, ID, or description.", "type": "tooltip"}, {"id": "asset-table", "selector": ".asset-table", "title": "Asset Table", "content": "This table displays all your assets. Click on an asset ID or name to view details.", "type": "tooltip"}, {"id": "asset-actions", "selector": ".asset-actions", "title": "Asset Actions", "content": "Use these buttons to view, edit, or delete an asset.", "type": "tooltip"}]}, {"id": "asset-details", "title": "Asset Details", "description": "View detailed information about an asset", "route": "/assets/\\d+", "content": "<p>The Asset Details page shows comprehensive information about a specific asset, including its specifications, maintenance history, and performance metrics.</p>", "elements": [{"id": "asset-info", "selector": ".asset-info-card", "title": "Asset Information", "content": "This section shows basic information about the asset, including its name, ID, type, and status.", "type": "tooltip"}, {"id": "asset-specs", "selector": ".asset-specs-card", "title": "Asset Specifications", "content": "This section shows technical specifications for the asset, such as manufacturer, model, and serial number.", "type": "tooltip"}, {"id": "asset-location", "selector": ".asset-location-card", "title": "Asset Location", "content": "This section shows the current location of the asset, including facility, area, and position.", "type": "tooltip"}, {"id": "asset-maintenance", "selector": ".asset-maintenance-card", "title": "Maintenance History", "content": "This section shows the maintenance history for the asset, including completed and scheduled work orders.", "type": "tooltip"}, {"id": "asset-performance", "selector": ".asset-performance-card", "title": "Performance Metrics", "content": "This section shows performance metrics for the asset, including uptime, downtime, and efficiency.", "type": "tooltip"}, {"id": "asset-documents", "selector": ".asset-documents-card", "title": "Documents", "content": "This section shows documents related to the asset, such as manuals, warranties, and certificates.", "type": "tooltip"}, {"id": "edit-asset", "selector": ".edit-asset-btn", "title": "Edit Asset", "content": "Click this button to edit the asset information.", "type": "tooltip"}, {"id": "delete-asset", "selector": ".delete-asset-btn", "title": "Delete Asset", "content": "Click this button to delete the asset. This action cannot be undone.", "type": "tooltip"}]}, {"id": "inventory", "title": "Inventory Management", "description": "Manage your inventory in AssetKPI", "route": "/inventory", "content": "<p>The Inventory Management page allows you to view, create, edit, and delete inventory items in your organization.</p><p>Inventory management is crucial for maintaining optimal stock levels, reducing costs, and ensuring parts availability for maintenance operations.</p>", "elements": [{"id": "add-part", "selector": ".add-part-btn", "title": "Add Part", "content": "Click this button to add a new part to your inventory.", "type": "tooltip"}, {"id": "part-filter", "selector": ".part-filter", "title": "Filter Parts", "content": "Use these filters to find specific parts based on category, location, or status.", "type": "tooltip"}, {"id": "part-search", "selector": ".part-search", "title": "Search Parts", "content": "Enter keywords to search for parts by name, ID, or description.", "type": "tooltip"}, {"id": "part-table", "selector": ".part-table", "title": "Part Table", "content": "This table displays all your inventory items. Click on a part ID or name to view details.", "type": "tooltip"}, {"id": "part-actions", "selector": ".part-actions", "title": "Part Actions", "content": "Use these buttons to view, edit, or delete a part.", "type": "tooltip"}, {"id": "stock-quantity", "selector": ".stock-quantity", "title": "Stock Quantity", "content": "This column shows the current quantity of each part in inventory. Parts with stock levels below the reorder point are highlighted in red.", "type": "tooltip"}, {"id": "reorder-level", "selector": ".reorder-level", "title": "Reorder Level", "content": "This column shows the minimum quantity at which you should reorder the part. This is calculated based on usage patterns, lead time, and safety stock requirements.", "type": "tooltip"}]}, {"id": "work-orders", "title": "Work Orders", "description": "Manage work orders in AssetKPI", "route": "/work-orders", "content": "<p>The Work Orders page allows you to view, create, edit, and delete work orders in your organization.</p><p>Work orders are used to track maintenance activities, repairs, and other tasks related to your assets.</p>", "elements": [{"id": "add-work-order", "selector": ".add-work-order-btn", "title": "Add Work Order", "content": "Click this button to create a new work order.", "type": "tooltip"}, {"id": "work-order-filter", "selector": ".work-order-filter", "title": "Filter Work Orders", "content": "Use these filters to find specific work orders based on status, priority, or assigned technician.", "type": "tooltip"}, {"id": "work-order-search", "selector": ".work-order-search", "title": "Search Work Orders", "content": "Enter keywords to search for work orders by ID, description, or asset.", "type": "tooltip"}, {"id": "work-order-table", "selector": ".work-order-table", "title": "Work Order Table", "content": "This table displays all your work orders. Click on a work order ID to view details.", "type": "tooltip"}, {"id": "work-order-actions", "selector": ".work-order-actions", "title": "Work Order Actions", "content": "Use these buttons to view, edit, or delete a work order.", "type": "tooltip"}]}, {"id": "kpi", "title": "KPI Tracking", "description": "Track key performance indicators in AssetKPI", "route": "/kpi", "content": "<p>The KPI Tracking page allows you to monitor key performance indicators for your assets and maintenance operations.</p><p>KPIs help you measure the effectiveness of your maintenance program and identify areas for improvement.</p>", "elements": [{"id": "kpi-filter", "selector": ".kpi-filter", "title": "Filter <PERSON>s", "content": "Use these filters to view KPIs for specific assets, time periods, or metrics.", "type": "tooltip"}, {"id": "kpi-chart", "selector": ".kpi-chart", "title": "KPI Chart", "content": "This chart displays KPI trends over time. Hover over data points to see detailed values.", "type": "tooltip"}, {"id": "kpi-table", "selector": ".kpi-table", "title": "KPI Table", "content": "This table displays KPI values for each asset or metric. Click on a value to see detailed information.", "type": "tooltip"}, {"id": "export-kpi", "selector": ".export-kpi-btn", "title": "Export KPIs", "content": "Click this button to export KPI data to CSV or PDF format.", "type": "tooltip"}]}]}