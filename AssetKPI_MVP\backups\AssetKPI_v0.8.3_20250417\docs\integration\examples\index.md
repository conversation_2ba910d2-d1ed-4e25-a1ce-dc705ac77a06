# Examples

Welcome to the AssetKPI Integration Examples. These examples demonstrate how to integrate with AssetKPI using webhooks, ERP connectors, and the Python SDK.

## Table of Contents

- [Webhook Examples](#webhook-examples)
- [ERP Integration Examples](#erp-integration-examples)
- [SDK Examples](#sdk-examples)

## Webhook Examples

### Basic Webhook Receiver

A simple webhook receiver that logs incoming webhook events.

- [Python](./webhooks/python-receiver.md)
- [Node.js](./webhooks/nodejs-receiver.md)
- [C#](./webhooks/csharp-receiver.md)

### Inventory Webhook Examples

Examples of using webhooks to monitor inventory changes.

- [Inventory Threshold Alert](./webhooks/inventory-threshold-alert.md)
- [Inventory Update Notification](./webhooks/inventory-update-notification.md)
- [Inventory Sync with External System](./webhooks/inventory-sync.md)

### Work Order Webhook Examples

Examples of using webhooks to monitor work order changes.

- [Work Order Creation Notification](./webhooks/workorder-creation-notification.md)
- [Work Order Completion Alert](./webhooks/workorder-completion-alert.md)
- [Work Order Assignment Notification](./webhooks/workorder-assignment-notification.md)

## ERP Integration Examples

### SAP Integration

Examples of integrating with SAP ERP systems.

- [SAP Connection Setup](./erp/sap-connection-setup.md)
- [SAP Material Master Sync](./erp/sap-material-master-sync.md)
- [SAP Work Order Sync](./erp/sap-workorder-sync.md)

### Oracle Integration

Examples of integrating with Oracle ERP systems.

- [Oracle Connection Setup](./erp/oracle-connection-setup.md)
- [Oracle Inventory Sync](./erp/oracle-inventory-sync.md)
- [Oracle Work Order Sync](./erp/oracle-workorder-sync.md)

### Microsoft Dynamics Integration

Examples of integrating with Microsoft Dynamics ERP systems.

- [Dynamics Connection Setup](./erp/dynamics-connection-setup.md)
- [Dynamics Inventory Sync](./erp/dynamics-inventory-sync.md)
- [Dynamics Work Order Sync](./erp/dynamics-workorder-sync.md)

## SDK Examples

### Basic SDK Usage

Examples of basic SDK usage.

- [Authentication](./sdk/authentication.md)
- [Error Handling](./sdk/error-handling.md)
- [Pagination](./sdk/pagination.md)
- [Filtering](./sdk/filtering.md)

### Asset Management

Examples of using the SDK for asset management.

- [Creating Assets](./sdk/creating-assets.md)
- [Updating Assets](./sdk/updating-assets.md)
- [Querying Assets](./sdk/querying-assets.md)
- [Bulk Asset Operations](./sdk/bulk-asset-operations.md)

### Inventory Management

Examples of using the SDK for inventory management.

- [Managing Spare Parts](./sdk/managing-spare-parts.md)
- [Inventory Analysis](./sdk/inventory-analysis.md)
- [Inventory Optimization](./sdk/inventory-optimization.md)
- [Bulk Inventory Operations](./sdk/bulk-inventory-operations.md)

### Work Order Management

Examples of using the SDK for work order management.

- [Creating Work Orders](./sdk/creating-workorders.md)
- [Updating Work Orders](./sdk/updating-workorders.md)
- [Querying Work Orders](./sdk/querying-workorders.md)
- [Bulk Work Order Operations](./sdk/bulk-workorder-operations.md)
