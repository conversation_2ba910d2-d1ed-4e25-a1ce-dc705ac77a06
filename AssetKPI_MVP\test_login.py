import requests
import json
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

# Base URL
base_url = "http://127.0.0.1:8000"

def test_login_page():
    """
    Test the login page with Selenium to see if it's working correctly.
    """
    print("Testing login page with Selenium...")
    
    # Set up Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Run in headless mode
    
    # Initialize the driver
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Navigate to the login page
        driver.get(f"{base_url}/login")
        print(f"Opened {base_url}/login")
        
        # Wait for the page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "login-button"))
        )
        
        # Get the email and password fields
        email_field = driver.find_element(By.ID, "email")
        password_field = driver.find_element(By.ID, "password")
        login_button = driver.find_element(By.ID, "login-button")
        
        # Enter credentials
        email_field.clear()
        email_field.send_keys("<EMAIL>")
        password_field.clear()
        password_field.send_keys("TestTest")
        
        # Click the login button
        login_button.click()
        
        # Wait for the authentication to complete
        time.sleep(5)
        
        # Check if login was successful
        try:
            auth_token_section = WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.ID, "auth-token-section"))
            )
            print("Login successful! Auth token section is visible.")
            
            # Get the token
            id_token = driver.find_element(By.ID, "id-token").text
            print(f"Token: {id_token[:20]}...")
            
            # Test the API endpoint
            test_api_button = driver.find_element(By.ID, "test-api-button")
            test_api_button.click()
            
            # Wait for the API response
            time.sleep(2)
            
            # Check the API response
            api_response = driver.find_element(By.ID, "api-response").text
            print(f"API response: {api_response}")
            
            return True
        except Exception as e:
            print(f"Login failed: {e}")
            
            # Check for error message
            try:
                error_message = driver.find_element(By.ID, "error-message").text
                print(f"Error message: {error_message}")
            except:
                print("No error message found.")
            
            return False
    finally:
        # Close the browser
        driver.quit()

if __name__ == "__main__":
    test_login_page()
