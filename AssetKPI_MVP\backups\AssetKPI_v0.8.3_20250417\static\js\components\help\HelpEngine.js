/**
 * Help Engine
 * 
 * This class manages contextual help in the AssetKPI application.
 * It handles help content loading, context awareness, and help display.
 */

class HelpEngine {
    /**
     * Initialize the help engine.
     * 
     * @param {Object} options - Configuration options
     * @param {string} options.containerId - ID of the container element for help UI
     * @param {boolean} options.enabled - Whether help is enabled
     * @param {string} options.helpLevel - Help detail level (basic, detailed, advanced)
     */
    constructor(options = {}) {
        this.containerId = options.containerId || 'help-container';
        this.enabled = options.enabled !== undefined ? options.enabled : true;
        this.helpLevel = options.helpLevel || 'basic';
        
        // Initialize state
        this.helpContent = {};
        this.currentContext = {
            route: window.location.pathname,
            element: null,
            section: null
        };
        
        // UI elements
        this.container = null;
        this.panel = null;
        this.tooltip = null;
        this.modal = null;
        
        // Load user preferences
        this.loadUserPreferences();
    }
    
    /**
     * Initialize the help engine.
     */
    async init() {
        // Create container if it doesn't exist
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = this.containerId;
            document.body.appendChild(this.container);
        }
        
        // Create UI elements
        this.createUIElements();
        
        // Add event listeners
        this.addEventListeners();
        
        // Load help content
        await this.loadHelpContent();
        
        // Add help icons to the page
        this.addHelpIcons();
        
        // Update context based on current page
        this.updateContext();
    }
    
    /**
     * Create UI elements for help.
     */
    createUIElements() {
        // Create help panel
        this.panel = document.createElement('div');
        this.panel.className = 'help-panel';
        this.panel.innerHTML = `
            <div class="help-panel-header">
                <h3 class="help-panel-title">Help</h3>
                <button class="help-panel-close-btn">&times;</button>
            </div>
            <div class="help-panel-search">
                <input type="text" class="help-search-input" placeholder="Search help...">
                <button class="help-search-btn"><i class="bi bi-search"></i></button>
            </div>
            <div class="help-panel-content"></div>
        `;
        this.container.appendChild(this.panel);
        
        // Create help tooltip
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'help-tooltip';
        this.tooltip.innerHTML = `
            <div class="help-tooltip-content"></div>
            <div class="help-tooltip-footer">
                <a href="#" class="help-more-link">Learn more</a>
            </div>
        `;
        this.container.appendChild(this.tooltip);
        
        // Create help modal
        this.modal = document.createElement('div');
        this.modal.className = 'help-modal';
        this.modal.innerHTML = `
            <div class="help-modal-dialog">
                <div class="help-modal-header">
                    <h3 class="help-modal-title"></h3>
                    <button class="help-modal-close-btn">&times;</button>
                </div>
                <div class="help-modal-body"></div>
                <div class="help-modal-footer">
                    <button class="help-modal-close-btn-bottom">Close</button>
                </div>
            </div>
        `;
        this.container.appendChild(this.modal);
        
        // Hide all elements initially
        this.hideUIElements();
    }
    
    /**
     * Add event listeners to UI elements.
     */
    addEventListeners() {
        // Panel close button
        const panelCloseBtn = this.panel.querySelector('.help-panel-close-btn');
        panelCloseBtn.addEventListener('click', () => {
            this.hidePanel();
        });
        
        // Modal close buttons
        const modalCloseBtns = this.modal.querySelectorAll('.help-modal-close-btn, .help-modal-close-btn-bottom');
        modalCloseBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.hideModal();
            });
        });
        
        // Search input
        const searchInput = this.panel.querySelector('.help-search-input');
        searchInput.addEventListener('input', () => {
            this.searchHelp(searchInput.value);
        });
        
        // Search button
        const searchBtn = this.panel.querySelector('.help-search-btn');
        searchBtn.addEventListener('click', () => {
            const searchInput = this.panel.querySelector('.help-search-input');
            this.searchHelp(searchInput.value);
        });
        
        // Learn more link in tooltip
        const moreLink = this.tooltip.querySelector('.help-more-link');
        moreLink.addEventListener('click', (event) => {
            event.preventDefault();
            this.showDetailedHelp();
        });
        
        // Route change detection
        window.addEventListener('popstate', () => {
            this.updateContext();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            // F1 key for help
            if (event.key === 'F1') {
                event.preventDefault();
                this.togglePanel();
            }
            
            // Shift+? for help search
            if (event.key === '?' && event.shiftKey) {
                event.preventDefault();
                this.showPanel();
                const searchInput = this.panel.querySelector('.help-search-input');
                searchInput.focus();
            }
        });
    }
    
    /**
     * Hide all UI elements.
     */
    hideUIElements() {
        this.panel.style.display = 'none';
        this.tooltip.style.display = 'none';
        this.modal.style.display = 'none';
    }
    
    /**
     * Load help content from the server.
     */
    async loadHelpContent() {
        try {
            // Fetch help content
            const response = await fetch('/api/help/content');
            if (!response.ok) {
                throw new Error(`Failed to load help content: ${response.statusText}`);
            }
            
            const content = await response.json();
            this.helpContent = content;
            
            return true;
        } catch (error) {
            console.error('Error loading help content:', error);
            
            // Load fallback content
            await this.loadFallbackContent();
            
            return false;
        }
    }
    
    /**
     * Load fallback help content.
     */
    async loadFallbackContent() {
        try {
            // Fetch fallback content from static file
            const response = await fetch('/static/js/help/help-content.json');
            if (!response.ok) {
                throw new Error(`Failed to load fallback help content: ${response.statusText}`);
            }
            
            const content = await response.json();
            this.helpContent = content;
            
            return true;
        } catch (error) {
            console.error('Error loading fallback help content:', error);
            
            // Use minimal content
            this.helpContent = {
                topics: [
                    {
                        id: 'general',
                        title: 'General Help',
                        content: 'Help content could not be loaded. Please try again later.'
                    }
                ]
            };
            
            return false;
        }
    }
    
    /**
     * Add help icons to the page.
     */
    addHelpIcons() {
        if (!this.enabled || !this.helpContent.topics) {
            return;
        }
        
        // Find all topics for the current route
        const currentRoute = window.location.pathname;
        const relevantTopics = this.helpContent.topics.filter(topic => 
            topic.route === currentRoute || 
            (topic.route instanceof RegExp && topic.route.test(currentRoute))
        );
        
        // Add help icons for each element in the relevant topics
        relevantTopics.forEach(topic => {
            if (!topic.elements) {
                return;
            }
            
            topic.elements.forEach(element => {
                const targetElements = document.querySelectorAll(element.selector);
                
                targetElements.forEach(targetEl => {
                    // Check if help icon already exists
                    if (targetEl.querySelector('.help-icon')) {
                        return;
                    }
                    
                    // Create help icon
                    const helpIcon = document.createElement('span');
                    helpIcon.className = 'help-icon';
                    helpIcon.innerHTML = '<i class="bi bi-question-circle"></i>';
                    helpIcon.setAttribute('data-help-id', element.id || '');
                    helpIcon.setAttribute('data-help-topic', topic.id || '');
                    helpIcon.setAttribute('data-help-type', element.type || 'tooltip');
                    helpIcon.setAttribute('title', element.title || '');
                    
                    // Add event listeners
                    helpIcon.addEventListener('mouseenter', () => {
                        this.showTooltip(helpIcon, element);
                    });
                    
                    helpIcon.addEventListener('mouseleave', () => {
                        this.hideTooltip();
                    });
                    
                    helpIcon.addEventListener('click', (event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        
                        if (element.type === 'modal') {
                            this.showModal(element);
                        } else {
                            this.showPanel(topic.id, element.id);
                        }
                    });
                    
                    // Add icon to target element
                    targetEl.appendChild(helpIcon);
                });
            });
        });
    }
    
    /**
     * Update context based on current page.
     */
    updateContext() {
        this.currentContext = {
            route: window.location.pathname,
            element: null,
            section: document.querySelector('h1, h2, h3')?.textContent || null
        };
        
        // Add help icons to the page
        this.addHelpIcons();
    }
    
    /**
     * Show the help tooltip.
     * 
     * @param {HTMLElement} targetEl - Target element
     * @param {Object} helpData - Help data for the element
     */
    showTooltip(targetEl, helpData) {
        if (!this.enabled) {
            return;
        }
        
        // Set tooltip content
        const contentEl = this.tooltip.querySelector('.help-tooltip-content');
        contentEl.innerHTML = helpData.content || '';
        
        // Position tooltip
        const rect = targetEl.getBoundingClientRect();
        const tooltipWidth = 300; // Fixed width
        
        // Position above or below based on space
        const spaceAbove = rect.top;
        const spaceBelow = window.innerHeight - rect.bottom;
        
        if (spaceBelow >= 150 || spaceBelow > spaceAbove) {
            // Position below
            this.tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
            this.tooltip.classList.remove('tooltip-top');
            this.tooltip.classList.add('tooltip-bottom');
        } else {
            // Position above
            this.tooltip.style.top = `${rect.top + window.scrollY - this.tooltip.offsetHeight - 5}px`;
            this.tooltip.classList.remove('tooltip-bottom');
            this.tooltip.classList.add('tooltip-top');
        }
        
        // Center horizontally
        const left = rect.left + (rect.width / 2) - (tooltipWidth / 2);
        this.tooltip.style.left = `${Math.max(10, Math.min(left, window.innerWidth - tooltipWidth - 10))}px`;
        this.tooltip.style.width = `${tooltipWidth}px`;
        
        // Store current help data for "Learn more" link
        this.tooltip.dataset.topicId = helpData.topicId || '';
        this.tooltip.dataset.elementId = helpData.id || '';
        
        // Show tooltip
        this.tooltip.style.display = 'block';
    }
    
    /**
     * Hide the help tooltip.
     */
    hideTooltip() {
        this.tooltip.style.display = 'none';
    }
    
    /**
     * Show the help panel.
     * 
     * @param {string} topicId - Optional topic ID to show
     * @param {string} elementId - Optional element ID to highlight
     */
    showPanel(topicId = null, elementId = null) {
        if (!this.enabled) {
            return;
        }
        
        // Get content element
        const contentEl = this.panel.querySelector('.help-panel-content');
        
        // If topic ID is provided, show that topic
        if (topicId) {
            const topic = this.findTopic(topicId);
            if (topic) {
                contentEl.innerHTML = this.renderTopic(topic, elementId);
            } else {
                contentEl.innerHTML = '<p>Help topic not found.</p>';
            }
        } else {
            // Otherwise, show topics for current route
            const currentRoute = this.currentContext.route;
            const relevantTopics = this.helpContent.topics.filter(topic => 
                topic.route === currentRoute || 
                (topic.route instanceof RegExp && topic.route.test(currentRoute))
            );
            
            if (relevantTopics.length > 0) {
                contentEl.innerHTML = this.renderTopicsList(relevantTopics);
            } else {
                contentEl.innerHTML = '<p>No help available for this page.</p>';
            }
        }
        
        // Show panel
        this.panel.style.display = 'block';
        
        // Add click handlers to links
        this.addPanelLinkHandlers();
    }
    
    /**
     * Hide the help panel.
     */
    hidePanel() {
        this.panel.style.display = 'none';
    }
    
    /**
     * Toggle the help panel.
     */
    togglePanel() {
        if (this.panel.style.display === 'none') {
            this.showPanel();
        } else {
            this.hidePanel();
        }
    }
    
    /**
     * Show the help modal.
     * 
     * @param {Object} helpData - Help data to show
     */
    showModal(helpData) {
        if (!this.enabled) {
            return;
        }
        
        // Set modal content
        const titleEl = this.modal.querySelector('.help-modal-title');
        const bodyEl = this.modal.querySelector('.help-modal-body');
        
        titleEl.textContent = helpData.title || 'Help';
        bodyEl.innerHTML = helpData.content || '';
        
        // Show modal
        this.modal.style.display = 'block';
    }
    
    /**
     * Hide the help modal.
     */
    hideModal() {
        this.modal.style.display = 'none';
    }
    
    /**
     * Show detailed help for the current tooltip.
     */
    showDetailedHelp() {
        const topicId = this.tooltip.dataset.topicId;
        const elementId = this.tooltip.dataset.elementId;
        
        this.hideTooltip();
        this.showPanel(topicId, elementId);
    }
    
    /**
     * Search help content.
     * 
     * @param {string} query - Search query
     */
    searchHelp(query) {
        if (!query) {
            this.showPanel();
            return;
        }
        
        // Normalize query
        const normalizedQuery = query.toLowerCase().trim();
        
        // Search topics
        const results = [];
        
        this.helpContent.topics.forEach(topic => {
            // Check topic title and content
            const matchesTopic = 
                topic.title.toLowerCase().includes(normalizedQuery) || 
                (topic.content && topic.content.toLowerCase().includes(normalizedQuery));
            
            // Check elements
            const matchingElements = topic.elements ? topic.elements.filter(element => 
                element.title.toLowerCase().includes(normalizedQuery) || 
                (element.content && element.content.toLowerCase().includes(normalizedQuery))
            ) : [];
            
            if (matchesTopic || matchingElements.length > 0) {
                results.push({
                    topic,
                    matchingElements
                });
            }
        });
        
        // Display results
        const contentEl = this.panel.querySelector('.help-panel-content');
        
        if (results.length > 0) {
            contentEl.innerHTML = this.renderSearchResults(results, normalizedQuery);
        } else {
            contentEl.innerHTML = `
                <div class="help-search-results">
                    <p>No results found for "${query}".</p>
                    <p>Try using different keywords or check the spelling.</p>
                </div>
            `;
        }
        
        // Add click handlers to links
        this.addPanelLinkHandlers();
    }
    
    /**
     * Find a topic by ID.
     * 
     * @param {string} topicId - Topic ID
     * @returns {Object|null} - Topic object or null if not found
     */
    findTopic(topicId) {
        return this.helpContent.topics.find(topic => topic.id === topicId) || null;
    }
    
    /**
     * Find an element by ID within a topic.
     * 
     * @param {Object} topic - Topic object
     * @param {string} elementId - Element ID
     * @returns {Object|null} - Element object or null if not found
     */
    findElement(topic, elementId) {
        if (!topic.elements) {
            return null;
        }
        
        return topic.elements.find(element => element.id === elementId) || null;
    }
    
    /**
     * Render a list of topics.
     * 
     * @param {Array} topics - Array of topic objects
     * @returns {string} - HTML for the topics list
     */
    renderTopicsList(topics) {
        let html = '<div class="help-topics-list">';
        
        topics.forEach(topic => {
            html += `
                <div class="help-topic-item">
                    <h4 class="help-topic-title">
                        <a href="#" data-topic-id="${topic.id}" class="help-topic-link">${topic.title}</a>
                    </h4>
                    <p class="help-topic-description">${topic.description || ''}</p>
                </div>
            `;
        });
        
        html += '</div>';
        
        return html;
    }
    
    /**
     * Render a topic.
     * 
     * @param {Object} topic - Topic object
     * @param {string} highlightElementId - Optional element ID to highlight
     * @returns {string} - HTML for the topic
     */
    renderTopic(topic, highlightElementId = null) {
        let html = `
            <div class="help-topic">
                <h3 class="help-topic-title">${topic.title}</h3>
                <div class="help-topic-content">${topic.content || ''}</div>
        `;
        
        if (topic.elements && topic.elements.length > 0) {
            html += '<div class="help-elements-list">';
            
            topic.elements.forEach(element => {
                const isHighlighted = element.id === highlightElementId;
                
                html += `
                    <div class="help-element-item ${isHighlighted ? 'highlighted' : ''}">
                        <h4 class="help-element-title">${element.title}</h4>
                        <div class="help-element-content">${element.content || ''}</div>
                    </div>
                `;
            });
            
            html += '</div>';
        }
        
        html += `
                <div class="help-topic-footer">
                    <a href="#" class="help-back-link">Back to topics</a>
                </div>
            </div>
        `;
        
        return html;
    }
    
    /**
     * Render search results.
     * 
     * @param {Array} results - Array of search result objects
     * @param {string} query - Search query
     * @returns {string} - HTML for the search results
     */
    renderSearchResults(results, query) {
        let html = `
            <div class="help-search-results">
                <h3 class="help-search-title">Search results for "${query}"</h3>
        `;
        
        results.forEach(result => {
            const { topic, matchingElements } = result;
            
            html += `
                <div class="help-search-result-item">
                    <h4 class="help-topic-title">
                        <a href="#" data-topic-id="${topic.id}" class="help-topic-link">${topic.title}</a>
                    </h4>
            `;
            
            if (topic.content && topic.content.toLowerCase().includes(query)) {
                const excerpt = this.getExcerpt(topic.content, query);
                html += `<p class="help-search-excerpt">${excerpt}</p>`;
            }
            
            if (matchingElements.length > 0) {
                html += '<ul class="help-search-elements">';
                
                matchingElements.forEach(element => {
                    html += `
                        <li>
                            <a href="#" data-topic-id="${topic.id}" data-element-id="${element.id}" class="help-element-link">
                                ${element.title}
                            </a>
                        </li>
                    `;
                });
                
                html += '</ul>';
            }
            
            html += '</div>';
        });
        
        html += '</div>';
        
        return html;
    }
    
    /**
     * Get an excerpt of text containing the search query.
     * 
     * @param {string} text - Text to excerpt
     * @param {string} query - Search query
     * @returns {string} - Excerpt with highlighted query
     */
    getExcerpt(text, query) {
        const normalizedText = text.toLowerCase();
        const index = normalizedText.indexOf(query);
        
        if (index === -1) {
            return text.substring(0, 100) + '...';
        }
        
        const start = Math.max(0, index - 40);
        const end = Math.min(text.length, index + query.length + 40);
        let excerpt = text.substring(start, end);
        
        if (start > 0) {
            excerpt = '...' + excerpt;
        }
        
        if (end < text.length) {
            excerpt = excerpt + '...';
        }
        
        // Highlight query
        const regex = new RegExp(`(${query})`, 'gi');
        excerpt = excerpt.replace(regex, '<mark>$1</mark>');
        
        return excerpt;
    }
    
    /**
     * Add click handlers to links in the help panel.
     */
    addPanelLinkHandlers() {
        // Topic links
        const topicLinks = this.panel.querySelectorAll('.help-topic-link');
        topicLinks.forEach(link => {
            link.addEventListener('click', (event) => {
                event.preventDefault();
                
                const topicId = link.dataset.topicId;
                this.showPanel(topicId);
            });
        });
        
        // Element links
        const elementLinks = this.panel.querySelectorAll('.help-element-link');
        elementLinks.forEach(link => {
            link.addEventListener('click', (event) => {
                event.preventDefault();
                
                const topicId = link.dataset.topicId;
                const elementId = link.dataset.elementId;
                this.showPanel(topicId, elementId);
            });
        });
        
        // Back links
        const backLinks = this.panel.querySelectorAll('.help-back-link');
        backLinks.forEach(link => {
            link.addEventListener('click', (event) => {
                event.preventDefault();
                this.showPanel();
            });
        });
    }
    
    /**
     * Load user preferences.
     */
    loadUserPreferences() {
        const preferencesJson = localStorage.getItem('helpPreferences');
        if (preferencesJson) {
            try {
                const preferences = JSON.parse(preferencesJson);
                this.enabled = preferences.enabled !== undefined ? preferences.enabled : this.enabled;
                this.helpLevel = preferences.helpLevel || this.helpLevel;
            } catch (error) {
                console.error('Error loading help preferences:', error);
            }
        }
    }
    
    /**
     * Save user preferences.
     */
    saveUserPreferences() {
        const preferences = {
            enabled: this.enabled,
            helpLevel: this.helpLevel
        };
        
        localStorage.setItem('helpPreferences', JSON.stringify(preferences));
    }
    
    /**
     * Enable help.
     */
    enableHelp() {
        this.enabled = true;
        this.saveUserPreferences();
        this.addHelpIcons();
    }
    
    /**
     * Disable help.
     */
    disableHelp() {
        this.enabled = false;
        this.saveUserPreferences();
        this.hideUIElements();
        
        // Remove help icons
        document.querySelectorAll('.help-icon').forEach(icon => {
            icon.remove();
        });
    }
    
    /**
     * Set help level.
     * 
     * @param {string} level - Help level (basic, detailed, advanced)
     */
    setHelpLevel(level) {
        this.helpLevel = level;
        this.saveUserPreferences();
        
        // Refresh help icons
        document.querySelectorAll('.help-icon').forEach(icon => {
            icon.remove();
        });
        
        this.addHelpIcons();
    }
}

// Export the HelpEngine class
export default HelpEngine;
