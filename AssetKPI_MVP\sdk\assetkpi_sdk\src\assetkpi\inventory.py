"""
Inventory API client module.

This module provides methods for interacting with the inventory-related endpoints
of the AssetKPI API.
"""

from typing import Dict, List, Optional, Union, Any

from .client import AssetKPIClient


class InventoryClient:
    """Client for inventory-related API endpoints."""
    
    def __init__(self, client: AssetKPIClient):
        """
        Initialize the inventory client.
        
        Args:
            client: The AssetKPI API client
        """
        self.client = client
    
    def get_parts(
        self,
        limit: int = 100,
        offset: int = 0,
        sort: str = "partid",
        order: str = "asc",
        filters: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Get a list of spare parts.
        
        Args:
            limit: Maximum number of items to return
            offset: Number of items to skip
            sort: Field to sort by
            order: Sort order (asc or desc)
            filters: List of filter conditions
            
        Returns:
            Paginated response with spare parts
        """
        params = {
            "limit": limit,
            "offset": offset,
            "sort": sort,
            "order": order,
        }
        
        if filters:
            params["filters"] = filters
        
        return self.client.get("/inventory/parts", params=params)
    
    def get_part(self, part_id: int) -> Dict[str, Any]:
        """
        Get a specific spare part.
        
        Args:
            part_id: ID of the spare part
            
        Returns:
            Spare part details
        """
        return self.client.get(f"/inventory/parts/{part_id}")
    
    def create_part(self, part_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new spare part.
        
        Args:
            part_data: Spare part data
            
        Returns:
            Created spare part
        """
        return self.client.post("/inventory/parts", data=part_data)
    
    def update_part(self, part_id: int, part_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a spare part.
        
        Args:
            part_id: ID of the spare part
            part_data: Updated spare part data
            
        Returns:
            Updated spare part
        """
        return self.client.put(f"/inventory/parts/{part_id}", data=part_data)
    
    def delete_part(self, part_id: int) -> Dict[str, Any]:
        """
        Delete a spare part.
        
        Args:
            part_id: ID of the spare part
            
        Returns:
            Deletion confirmation
        """
        return self.client.delete(f"/inventory/parts/{part_id}")
    
    def bulk_create_parts(self, parts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create multiple spare parts in a single request.
        
        Args:
            parts: List of spare part data
            
        Returns:
            Creation confirmation
        """
        return self.client.post("/inventory/parts/bulk", data={"items": parts})
    
    def bulk_update_parts(self, parts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Update multiple spare parts in a single request.
        
        Args:
            parts: List of spare part data with IDs
            
        Returns:
            Update confirmation
        """
        return self.client.put("/inventory/parts/bulk", data={"items": parts})
    
    def bulk_delete_parts(self, part_ids: List[int]) -> Dict[str, Any]:
        """
        Delete multiple spare parts in a single request.
        
        Args:
            part_ids: List of spare part IDs
            
        Returns:
            Deletion confirmation
        """
        return self.client.delete("/inventory/parts/bulk", data={"ids": part_ids})
    
    def get_inventory_summary(self) -> Dict[str, Any]:
        """
        Get inventory summary data.
        
        Returns:
            Inventory summary
        """
        return self.client.get("/inventory/summary")
    
    def get_inventory_analysis(self) -> List[Dict[str, Any]]:
        """
        Get inventory analysis data for all parts.
        
        Returns:
            List of inventory analysis data
        """
        return self.client.get("/inventory/analysis")
    
    def get_part_inventory_analysis(self, part_id: int) -> Dict[str, Any]:
        """
        Get inventory analysis data for a specific part.
        
        Args:
            part_id: ID of the spare part
            
        Returns:
            Inventory analysis data
        """
        return self.client.get(f"/inventory/analysis/{part_id}")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """
        Get inventory optimization report data.
        
        Returns:
            Optimization report
        """
        return self.client.get("/inventory/optimization-report")
    
    def get_eoq_summary(self) -> Dict[str, Any]:
        """
        Get summary of EOQ calculations.
        
        Returns:
            EOQ summary
        """
        return self.client.get("/inventory/eoq-summary")
    
    def run_optimization(self) -> Dict[str, Any]:
        """
        Manually trigger the inventory optimization job.
        
        Returns:
            Optimization job status
        """
        return self.client.get("/inventory/run-optimization")
    
    def get_inventory_config(self) -> List[Dict[str, Any]]:
        """
        Get all inventory configuration parameters.
        
        Returns:
            List of inventory configuration parameters
        """
        return self.client.get("/inventory/config")
    
    def create_inventory_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new inventory configuration parameter.
        
        Args:
            config_data: Configuration parameter data
            
        Returns:
            Created configuration parameter
        """
        return self.client.post("/inventory/config", data=config_data)
    
    def update_inventory_config(
        self, param_id: int, config_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update an existing inventory configuration parameter.
        
        Args:
            param_id: ID of the configuration parameter
            config_data: Updated configuration parameter data
            
        Returns:
            Updated configuration parameter
        """
        return self.client.put(f"/inventory/config/{param_id}", data=config_data)
    
    def delete_inventory_config(self, param_id: int) -> Dict[str, Any]:
        """
        Delete an inventory configuration parameter.
        
        Args:
            param_id: ID of the configuration parameter
            
        Returns:
            Deletion confirmation
        """
        return self.client.delete(f"/inventory/config/{param_id}")
    
    def get_part_eoq_params(self, part_id: int) -> Dict[str, Any]:
        """
        Get EOQ parameters for a specific part.
        
        Args:
            part_id: ID of the spare part
            
        Returns:
            EOQ parameters
        """
        return self.client.get(f"/inventory/parts/{part_id}/eoq-params")
    
    def update_part_eoq_params(
        self, part_id: int, params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update EOQ parameters for a specific part.
        
        Args:
            part_id: ID of the spare part
            params: EOQ parameters
            
        Returns:
            Updated EOQ parameters
        """
        return self.client.put(f"/inventory/parts/{part_id}/eoq-params", data=params)
    
    def delete_part_eoq_params(self, part_id: int) -> Dict[str, Any]:
        """
        Delete EOQ parameters for a specific part.
        
        Args:
            part_id: ID of the spare part
            
        Returns:
            Deletion confirmation
        """
        return self.client.delete(f"/inventory/parts/{part_id}/eoq-params")
