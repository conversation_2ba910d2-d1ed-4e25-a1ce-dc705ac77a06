/**
 * Firebase Authentication Utility for AssetKPI
 *
 * This module provides functions for handling Firebase authentication
 * and token management across the application.
 */

// Firebase configuration is now loaded from the server in each page that needs it
// The login.html page initializes Firebase with the configuration from the server
// DO NOT HARDCODE KEYS HERE

// Check if Firebase is initialized
let auth;
try {
    if (typeof firebase !== 'undefined' && firebase.auth) {
        auth = firebase.auth();
        console.log('Firebase auth initialized successfully in auth.js');
    } else {
        console.error('Firebase is not initialized or firebase.auth is not available');
        // Create a dummy auth object to prevent errors
        auth = {
            signInWithEmailAndPassword: () => Promise.reject(new Error('Firebase not initialized')),
            signOut: () => Promise.reject(new Error('Firebase not initialized')),
            onAuthStateChanged: () => {}
        };
    }
} catch (error) {
    console.error('Error accessing firebase.auth:', error);
    // Create a dummy auth object to prevent errors
    auth = {
        signInWithEmailAndPassword: () => Promise.reject(new Error('Firebase not initialized')),
        signOut: () => Promise.reject(new Error('Firebase not initialized')),
        onAuthStateChanged: () => {}
    };
}

// Auth state variable
let currentUser = null;
let currentIdToken = null;

// Token refresh interval in milliseconds (15 minutes)
const TOKEN_REFRESH_INTERVAL = 15 * 60 * 1000;

// Variable to store the refresh interval ID
let refreshIntervalId = null;

/**
 * Sign in with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} - User credential object
 */
async function signIn(email, password) {
    try {
        const userCredential = await auth.signInWithEmailAndPassword(email, password);
        currentUser = userCredential.user;

        // Get and store the ID token
        currentIdToken = await currentUser.getIdToken();
        localStorage.setItem('firebaseIdToken', currentIdToken);

        // Also set a cookie for server-side authentication
        document.cookie = `firebaseIdToken=${currentIdToken}; path=/; max-age=3600; SameSite=Strict`;
        console.log('Token stored in cookie for server-side auth');

        // Start token refresh mechanism
        startTokenRefresh();

        return {
            success: true,
            user: currentUser,
            token: currentIdToken
        };
    } catch (error) {
        console.error('Sign in error:', error);
        return {
            success: false,
            error: error
        };
    }
}

/**
 * Sign out the current user
 * @returns {Promise<Object>} - Result object
 */
async function signOut() {
    try {
        await auth.signOut();
        currentUser = null;
        currentIdToken = null;
        localStorage.removeItem('firebaseIdToken');

        // Clear the cookie as well
        document.cookie = 'firebaseIdToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';

        // Stop token refresh mechanism
        stopTokenRefresh();

        return {
            success: true
        };
    } catch (error) {
        console.error('Sign out error:', error);
        return {
            success: false,
            error: error
        };
    }
}

/**
 * Get the current user's ID token
 * @param {boolean} forceRefresh - Whether to force a token refresh
 * @returns {Promise<string|null>} - The ID token or null if not authenticated
 */
async function getIdToken(forceRefresh = false) {
    if (!currentUser) {
        // Try to get token from localStorage
        const storedToken = localStorage.getItem('firebaseIdToken');
        if (storedToken) {
            console.log('Retrieved token from localStorage');
            return storedToken;
        }
        console.log('No user and no token in localStorage');
        return null;
    }

    try {
        console.log('Getting fresh token from Firebase');
        currentIdToken = await currentUser.getIdToken(forceRefresh);
        localStorage.setItem('firebaseIdToken', currentIdToken);
        return currentIdToken;
    } catch (error) {
        console.error('Error getting ID token:', error);
        // Try to fall back to stored token if available
        const storedToken = localStorage.getItem('firebaseIdToken');
        if (storedToken) {
            console.log('Falling back to stored token after error');
            return storedToken;
        }
        return null;
    }
}

/**
 * Check if the user is authenticated
 * @returns {boolean} - Whether the user is authenticated
 */
function isAuthenticated() {
    return !!currentUser || !!localStorage.getItem('firebaseIdToken');
}

/**
 * Get the current user
 * @returns {Object|null} - The current user or null if not authenticated
 */
function getCurrentUser() {
    return currentUser;
}

/**
 * Start the token refresh mechanism
 * This will periodically refresh the Firebase ID token
 */
function startTokenRefresh() {
    // Clear any existing interval
    if (refreshIntervalId) {
        clearInterval(refreshIntervalId);
    }

    // Set up the refresh interval
    refreshIntervalId = setInterval(async () => {
        try {
            // Check if user is logged in
            if (currentUser) {
                console.log('Refreshing Firebase ID token...');
                // Force refresh token
                currentIdToken = await currentUser.getIdToken(true);

                // Store the refreshed token
                localStorage.setItem('firebaseIdToken', currentIdToken);

                // Update the cookie for server-side authentication
                document.cookie = `firebaseIdToken=${currentIdToken}; path=/; max-age=3600; SameSite=Strict`;

                console.log('Firebase ID token refreshed successfully');
            }
        } catch (error) {
            console.error('Error refreshing Firebase ID token:', error);
        }
    }, TOKEN_REFRESH_INTERVAL);

    console.log('Token refresh mechanism started');
}

/**
 * Stop the token refresh mechanism
 */
function stopTokenRefresh() {
    if (refreshIntervalId) {
        clearInterval(refreshIntervalId);
        refreshIntervalId = null;
        console.log('Token refresh mechanism stopped');
    }
}

/**
 * Initialize the authentication state
 * @param {Function} callback - Callback function to run when auth state changes
 * @returns {Function} - Unsubscribe function
 */
function initAuth(callback) {
    console.log('Initializing Firebase Auth state');

    // Check if we have a token in localStorage
    const storedToken = localStorage.getItem('firebaseIdToken');
    if (storedToken) {
        console.log('Found token in localStorage during init');
        // Also set a cookie for server-side authentication if we have a token in localStorage
        document.cookie = `firebaseIdToken=${storedToken}; path=/; max-age=3600; SameSite=Strict`;
        console.log('Token from localStorage also stored in cookie for server-side auth');
    }

    return auth.onAuthStateChanged(async (user) => {
        console.log('Auth state changed:', user ? `User: ${user.email}` : 'No user');
        currentUser = user;

        if (user) {
            try {
                console.log('Getting fresh token during auth state change');
                currentIdToken = await user.getIdToken(true); // Force refresh
                localStorage.setItem('firebaseIdToken', currentIdToken);
                console.log('Token stored in localStorage');

                // Also set a cookie for server-side authentication
                document.cookie = `firebaseIdToken=${currentIdToken}; path=/; max-age=3600; SameSite=Strict`;
                console.log('Token stored in cookie for server-side auth');

                // Start token refresh mechanism
                startTokenRefresh();
            } catch (error) {
                console.error('Error getting ID token during init:', error);
            }
        } else {
            console.log('No user, clearing token');
            currentIdToken = null;
            localStorage.removeItem('firebaseIdToken');
            // Clear the cookie as well
            document.cookie = 'firebaseIdToken=; path=/; max-age=0; SameSite=Strict';
            console.log('Token cookie cleared');

            // Stop token refresh mechanism
            stopTokenRefresh();
        }

        if (callback && typeof callback === 'function') {
            callback(user);
        }
    });
}

/**
 * Make an authenticated API request
 * @param {string} url - The API endpoint URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} - Fetch response
 */
async function authenticatedFetch(url, options = {}) {
    // Try to get token from current user first, then from localStorage as fallback
    let token = null;

    if (currentUser) {
        try {
            // Force refresh token to ensure it's valid
            token = await currentUser.getIdToken(true);
            // Update stored token
            localStorage.setItem('firebaseIdToken', token);
            console.log('Using fresh token from current user');
        } catch (error) {
            console.error('Error refreshing token:', error);
        }
    }

    // If we couldn't get a token from the current user, try localStorage
    if (!token) {
        token = localStorage.getItem('firebaseIdToken');
        console.log('Using token from localStorage');
    }

    if (!token) {
        console.error('No authentication token available');
        throw new Error('Not authenticated');
    }

    // Log token info for debugging (first and last 10 chars only for security)
    const tokenPreview = token.length > 20 ?
        `${token.substring(0, 10)}...${token.substring(token.length - 10)}` :
        '[token too short]';
    console.log(`Using token: ${tokenPreview}`);

    // Check if token is valid JWT format (should have 3 parts separated by dots)
    const parts = token.split('.');
    if (parts.length !== 3) {
        console.error('Token is not in valid JWT format (should have 3 parts):', parts.length);
        // Try to clean up the token - sometimes line breaks or other characters get added
        token = token.trim().replace(/\s+/g, '');
        console.log('Cleaned token parts:', token.split('.').length);
    }

    const headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`
    };

    console.log(`Making authenticated request to: ${url}`);

    // For debugging - log the full Authorization header
    console.log(`Authorization header: Bearer ${token.substring(0, 10)}...`);

    return fetch(url, {
        ...options,
        headers
    });
}

// Export the auth functions
const AssetKPIAuth = {
    signIn,
    signOut,
    getIdToken,
    isAuthenticated,
    getCurrentUser,
    initAuth,
    authenticatedFetch,
    startTokenRefresh,
    stopTokenRefresh
};

// Make it available globally immediately
window.AssetKPIAuth = AssetKPIAuth;
console.log('AssetKPIAuth object created and attached to window object');

// For debugging - log the methods available
console.log('Available methods:', Object.keys(AssetKPIAuth));

// Dispatch an event when the auth module is loaded
document.dispatchEvent(new Event('assetKPIAuthLoaded'));
