<!DOCTYPE html>
<html lang="en">
<head>
    <title>AssetKPI - Login</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 40px;
        }
        .login-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header img {
            max-width: 200px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .btn-login {
            width: 100%;
            padding: 10px;
            background-color: #0d6efd;
            border: none;
        }
        .login-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #6c757d;
        }
        .alert {
            display: none;
            margin-top: 20px;
        }
        #loading-spinner {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        .auth-token-section {
            margin-top: 30px;
            display: none;
        }
        .auth-token-section pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .token-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <h2>AssetKPI</h2>
                <p class="text-muted">Sign in to access your dashboard</p>
            </div>

            <ul class="nav nav-tabs" id="authTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="firebase-tab" data-bs-toggle="tab" data-bs-target="#firebase-auth" type="button" role="tab" aria-controls="firebase-auth" aria-selected="true">Firebase Auth</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="test-tab" data-bs-toggle="tab" data-bs-target="#test-auth" type="button" role="tab" aria-controls="test-auth" aria-selected="false">Test Auth</button>
                </li>
            </ul>

            <div class="tab-content" id="authTabsContent">
                <!-- Firebase Authentication Tab -->
                <div class="tab-pane fade show active" id="firebase-auth" role="tabpanel" aria-labelledby="firebase-tab">
                    <div id="login-form" class="mt-3">
                        <div class="form-group">
                            <label for="email">Email address</label>
                            <input type="email" class="form-control" id="email" placeholder="Enter email" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" class="form-control" id="password" placeholder="Password" value="password">
                        </div>
                        <div class="alert alert-info">
                            <strong>Test Users:</strong>
                            <ul>
                                <li>Admin: <EMAIL> / password</li>
                                <li>Manager: <EMAIL> / password</li>
                                <li>Engineer: <EMAIL> / password</li>
                                <li>Viewer: <EMAIL> / password</li>
                            </ul>
                        </div>
                        <button type="button" class="btn btn-primary btn-login" id="login-button">Sign In with Firebase</button>
                    </div>
                </div>

                <!-- Test Authentication Tab -->
                <div class="tab-pane fade" id="test-auth" role="tabpanel" aria-labelledby="test-tab">
                    <div id="test-login-form" class="mt-3">
                        <div class="alert alert-info">
                            <strong>Test Tokens:</strong>
                            <p>Use these tokens for testing without Firebase authentication:</p>
                            <ul>
                                <li><code>test-admin-token</code> - Admin role</li>
                                <li><code>test-manager-token</code> - Manager role</li>
                                <li><code>test-engineer-token</code> - Engineer role</li>
                                <li><code>test-viewer-token</code> - Viewer role</li>
                                <li><code>johan-token</code> - Johan Borgulf (Admin)</li>
                            </ul>
                        </div>
                        <div class="form-group">
                            <label for="test-token">Test Token</label>
                            <input type="text" class="form-control" id="test-token" placeholder="Enter test token" value="test-admin-token">
                        </div>
                        <button type="button" class="btn btn-success btn-login" id="test-login-button">Sign In with Test Token</button>
                    </div>
                </div>
            </div>

                <div class="alert alert-danger" id="error-message" role="alert"></div>
                <div class="alert alert-success" id="success-message" role="alert"></div>

                <div id="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Authenticating...</p>
                </div>
            </div>

            <div class="auth-token-section" id="auth-token-section">
                <h5>Authentication Successful</h5>
                <p>Your ID token:</p>
                <pre id="id-token"></pre>
                <div class="token-actions">
                    <button type="button" class="btn btn-outline-secondary" id="copy-token-button">Copy Token</button>
                    <button type="button" class="btn btn-success" id="test-api-button">Test API Endpoint</button>
                    <button type="button" class="btn btn-info" id="test-register-button">Test Registration Endpoint</button>
                    <button type="button" class="btn btn-primary" id="go-to-dashboard">Go to Dashboard</button>
                </div>
                <div class="alert alert-info mt-3" id="api-response" role="alert"></div>
                <button type="button" class="btn btn-outline-danger mt-3" id="sign-out-button">Sign Out</button>
            </div>

            <div class="login-footer">
                <p>AssetKPI &copy; 2025. All rights reserved.</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <!-- Firebase Auth -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>

    <script>
        // Initialize Firebase first
        try {
            // Define Firebase config
            const firebaseConfig = {
                apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
                authDomain: "ikios-59679.firebaseapp.com",
                projectId: "ikios-59679",
                storageBucket: "ikios-59679.appspot.com",
                messagingSenderId: "1045444071940",
                appId: "1:1045444071940:web:c5e52be89b1c4fcd8457741"
            };

            // Initialize Firebase - this is safe to call multiple times in Firebase 9
            // If already initialized, it will return the existing instance
            firebase.initializeApp(firebaseConfig);
            console.log('Firebase initialized with configuration from server');

            // Define a global variable to track script loading
            window.AssetKPIScriptsLoaded = {
                tokenRefresh: false,
                auth: false,
                testAuth: false,
                combinedAuth: false
            };

            // Function to load scripts in sequence
            function loadScript(url, callback) {
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = url;
                script.onload = callback;
                document.head.appendChild(script);
            }

            // Load scripts in sequence
            loadScript('/static/js/auth_simple.js', function() {
                console.log('Simplified auth script loaded');
                window.AssetKPIScriptsLoaded.auth = true;

                // Load test auth script
                loadScript('/static/js/test_auth.js', function() {
                    console.log('Test auth script loaded');
                    window.AssetKPIScriptsLoaded.testAuth = true;

                    // Load combined auth script
                    loadScript('/static/js/combined_auth.js', function() {
                        console.log('Combined auth script loaded');
                        window.AssetKPIScriptsLoaded.combinedAuth = true;

                        // Dispatch an event when all scripts are loaded
                        document.dispatchEvent(new Event('assetKPIScriptsLoaded'));
                    });
                });
            });

        } catch (error) {
            // If error is about app already initialized, that's fine
            if (error.code === 'app/duplicate-app') {
                console.log('Firebase already initialized, using existing instance');
            } else {
                console.error('Error initializing Firebase:', error);
            }
        }
    </script>

    <script>

        // Ensure AssetKPIAuth is defined
        if (typeof AssetKPIAuth === 'undefined') {
            console.error('AssetKPIAuth is not defined. Creating a placeholder to prevent errors.');
            window.AssetKPIAuth = {
                signIn: () => Promise.reject(new Error('AssetKPIAuth not initialized')),
                signOut: () => Promise.reject(new Error('AssetKPIAuth not initialized')),
                getIdToken: () => Promise.reject(new Error('AssetKPIAuth not initialized')),
                isAuthenticated: () => false,
                getCurrentUser: () => null,
                initAuth: () => {},
                authenticatedFetch: () => Promise.reject(new Error('AssetKPIAuth not initialized')),
                startTokenRefresh: () => {},
                stopTokenRefresh: () => {}
            };
        } else {
            console.log('AssetKPIAuth is defined and available');
        }

        // DOM elements
        const loginButton = document.getElementById('login-button');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const errorMessage = document.getElementById('error-message');
        const successMessage = document.getElementById('success-message');
        const loadingSpinner = document.getElementById('loading-spinner');
        const authTokenSection = document.getElementById('auth-token-section');
        const idTokenElement = document.getElementById('id-token');
        const copyTokenButton = document.getElementById('copy-token-button');
        const testApiButton = document.getElementById('test-api-button');
        const apiResponse = document.getElementById('api-response');
        const goToDashboardButton = document.getElementById('go-to-dashboard');
        const signOutButton = document.getElementById('sign-out-button');
        const loginForm = document.getElementById('login-form');

        // Function to show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
            loadingSpinner.style.display = 'none';
        }

        // Function to show success message
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        // Function to handle login
        async function handleLogin() {
            const email = emailInput.value.trim();
            const password = passwordInput.value;

            // Basic validation
            if (!email || !password) {
                showError('Please enter both email and password.');
                return;
            }

            // Show loading spinner
            loadingSpinner.style.display = 'block';
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            try {
                console.log('Attempting to sign in with:', email);

                // Check if Firebase is initialized properly
                if (!firebase || !firebase.auth) {
                    console.error('Firebase not initialized properly');
                    showError('Firebase initialization error. Please check the console for details.');
                    return;
                }

                // Try direct Firebase auth first for debugging
                try {
                    console.log('Trying direct Firebase authentication...');
                    const directUserCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
                    console.log('Direct Firebase auth successful:', directUserCredential.user.email);
                } catch (directError) {
                    console.error('Direct Firebase auth failed:', directError);
                }

                // Check if AssetKPIAuth is available
                if (typeof AssetKPIAuth === 'undefined' || !AssetKPIAuth.signIn) {
                    console.error('AssetKPIAuth is not available or signIn method is missing');
                    throw new Error('Authentication utility not available');
                }

                // Sign in with Firebase using our auth utility
                console.log('Trying auth utility...');
                const result = await AssetKPIAuth.signIn(email, password);
                console.log('Auth utility result:', result);

                if (result.success) {
                    // Show success and token
                    showSuccess('Authentication successful!');
                    idTokenElement.textContent = result.token;

                    // Show token section and hide login form
                    authTokenSection.style.display = 'block';
                    loginForm.style.display = 'none';

                    // Start token refresh mechanism
                    if (window.tokenRefresh) {
                        window.tokenRefresh.start();
                        console.log('Token refresh mechanism started');
                    }

                    console.log('User authenticated:', result.user.email);
                } else {
                    console.error('Auth utility returned error:', result.error);
                    throw result.error;
                }
            } catch (error) {
                console.error('Authentication error:', error);
                console.error('Error code:', error.code);
                console.error('Error message:', error.message);

                // Handle specific Firebase auth errors
                let errorMsg = 'Authentication failed. Please check your credentials.';

                if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
                    errorMsg = 'Invalid email or password.';
                } else if (error.code === 'auth/invalid-email') {
                    errorMsg = 'Invalid email format.';
                } else if (error.code === 'auth/too-many-requests') {
                    errorMsg = 'Too many failed login attempts. Please try again later.';
                } else if (error.code) {
                    errorMsg = `Authentication error: ${error.code}`;
                }

                showError(errorMsg);
            } finally {
                // Hide loading spinner
                loadingSpinner.style.display = 'none';
            }
        }

        // Function to handle sign out
        async function handleSignOut() {
            try {
                let result;

                // Try to use CombinedAuth if available
                if (typeof CombinedAuth !== 'undefined' && CombinedAuth.signOut) {
                    console.log('Using CombinedAuth for sign out');
                    result = await CombinedAuth.signOut();
                }
                // Fallback to AssetKPIAuth
                else if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.signOut) {
                    console.log('Using AssetKPIAuth for sign out');
                    result = await AssetKPIAuth.signOut();
                } else {
                    throw new Error('No authentication utility available for sign out');
                }

                if (result.success) {
                    // Show login form and hide token section
                    authTokenSection.style.display = 'none';
                    loginForm.style.display = 'block';
                    document.getElementById('test-login-form').style.display = 'block';

                    // Reset the tabs to show the first tab
                    document.getElementById('firebase-tab').click();

                    // Clear token display
                    idTokenElement.textContent = '';

                    // Hide any messages
                    errorMessage.style.display = 'none';
                    successMessage.style.display = 'none';
                    apiResponse.style.display = 'none';

                    // Stop token refresh mechanism
                    if (window.tokenRefresh) {
                        window.tokenRefresh.stop();
                        console.log('Token refresh mechanism stopped');
                    }

                    console.log('User signed out');
                } else {
                    throw result.error;
                }
            } catch (error) {
                console.error('Sign out error:', error);
                showError('Error signing out: ' + (error.message || 'Unknown error'));
            }
        }

        // Function to test API endpoint with token
        async function testApiEndpoint() {
            apiResponse.textContent = 'Testing API endpoint...';
            apiResponse.style.display = 'block';

            try {
                let response;

                // Try to use CombinedAuth if available
                if (typeof CombinedAuth !== 'undefined' && CombinedAuth.authenticatedFetch) {
                    console.log('Using CombinedAuth for API test');
                    response = await CombinedAuth.authenticatedFetch('/api/kpi/history/MTTR_Calculated');
                }
                // Fallback to AssetKPIAuth
                else if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.authenticatedFetch) {
                    console.log('Using AssetKPIAuth for API test');
                    response = await AssetKPIAuth.authenticatedFetch('/api/kpi/history/MTTR_Calculated');
                } else {
                    throw new Error('No authentication utility available for API test');
                }

                if (response.ok) {
                    const data = await response.json();
                    apiResponse.textContent = 'API call successful! Received data for MTTR_Calculated.';
                    apiResponse.className = 'alert alert-success mt-3';
                } else {
                    apiResponse.textContent = `API call failed: ${response.status} ${response.statusText}`;
                    apiResponse.className = 'alert alert-danger mt-3';
                }
            } catch (error) {
                console.error('API test error:', error);
                apiResponse.textContent = `Error testing API: ${error.message || 'Unknown error'}`;
                apiResponse.className = 'alert alert-danger mt-3';
            }
        }

        // Function to copy token to clipboard
        async function copyTokenToClipboard() {
            let token;

            // Try to use CombinedAuth if available
            if (typeof CombinedAuth !== 'undefined' && CombinedAuth.getToken) {
                console.log('Using CombinedAuth for getting token');
                token = await CombinedAuth.getToken();
            }
            // Fallback to AssetKPIAuth
            else if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.getIdToken) {
                console.log('Using AssetKPIAuth for getting token');
                token = await AssetKPIAuth.getIdToken();
            } else {
                console.error('No authentication utility available for getting token');
                showError('Failed to get token: No authentication utility available');
                return;
            }

            if (token) {
                navigator.clipboard.writeText(token)
                    .then(() => {
                        // Show temporary success message
                        const originalText = copyTokenButton.textContent;
                        copyTokenButton.textContent = 'Copied!';
                        setTimeout(() => {
                            copyTokenButton.textContent = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        showError('Failed to copy token to clipboard.');
                    });
            } else {
                showError('No token available to copy');
            }
        }

        // Function to test registration endpoint
        async function testRegistrationEndpoint() {
            apiResponse.textContent = 'Testing registration endpoint...';
            apiResponse.style.display = 'block';
            apiResponse.className = 'alert alert-info mt-3';

            try {
                let token;

                // Try to use CombinedAuth if available
                if (typeof CombinedAuth !== 'undefined' && CombinedAuth.getToken) {
                    console.log('Using CombinedAuth for registration test');
                    token = await CombinedAuth.getToken();
                }
                // Fallback to AssetKPIAuth
                else if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.getIdToken) {
                    console.log('Using AssetKPIAuth for registration test');
                    token = await AssetKPIAuth.getIdToken();
                } else {
                    throw new Error('No authentication utility available for registration test');
                }

                if (!token) {
                    apiResponse.textContent = 'No authentication token available';
                    apiResponse.className = 'alert alert-danger mt-3';
                    return;
                }

                // Call the registration endpoint
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    apiResponse.textContent = `Registration successful! ${data.message}`;
                    apiResponse.className = 'alert alert-success mt-3';
                } else {
                    apiResponse.textContent = `Registration failed: ${data.error || response.statusText}`;
                    apiResponse.className = 'alert alert-danger mt-3';
                }
            } catch (error) {
                console.error('Registration test error:', error);
                apiResponse.textContent = `Error testing registration: ${error.message || 'Unknown error'}`;
                apiResponse.className = 'alert alert-danger mt-3';
            }
        }

        // Function to handle test user login
        async function handleTestLogin() {
            const testToken = document.getElementById('test-token').value.trim();

            // Basic validation
            if (!testToken) {
                showError('Please enter a test token.');
                return;
            }

            // Show loading spinner
            loadingSpinner.style.display = 'block';
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            try {
                console.log('Attempting to sign in with test token:', testToken);

                // Check if CombinedAuth is available
                if (typeof CombinedAuth === 'undefined' || !CombinedAuth.signInWithTestToken) {
                    console.error('CombinedAuth is not available or signInWithTestToken method is missing');
                    throw new Error('Test authentication utility not available');
                }

                // Sign in with test token
                const result = await CombinedAuth.signInWithTestToken(testToken);
                console.log('Test auth result:', result);

                if (result.success) {
                    // Show success and token
                    showSuccess('Test authentication successful!');
                    idTokenElement.textContent = result.token;

                    // Show token section and hide login form
                    authTokenSection.style.display = 'block';
                    loginForm.style.display = 'none';
                    document.getElementById('test-login-form').style.display = 'none';

                    console.log('User authenticated with test token:', result.user.email);
                } else {
                    console.error('Test auth returned error:', result.error);
                    throw result.error;
                }
            } catch (error) {
                console.error('Test authentication error:', error);
                showError('Test authentication failed: ' + (error.message || 'Unknown error'));
            } finally {
                // Hide loading spinner
                loadingSpinner.style.display = 'none';
            }
        }

        // Event listeners
        loginButton.addEventListener('click', handleLogin);
        document.getElementById('test-login-button').addEventListener('click', handleTestLogin);
        signOutButton.addEventListener('click', handleSignOut);
        testApiButton.addEventListener('click', testApiEndpoint);
        testRegisterButton = document.getElementById('test-register-button');
        testRegisterButton.addEventListener('click', testRegistrationEndpoint);
        copyTokenButton.addEventListener('click', copyTokenToClipboard);
        goToDashboardButton.addEventListener('click', () => window.location.href = '/');

        // Allow Enter key to submit the form
        passwordInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                handleLogin();
            }
        });

        // Allow Enter key to submit the test token form
        document.getElementById('test-token').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                handleTestLogin();
            }
        });

        // Function to initialize auth state
        function initializeAuthState() {
            console.log('Initializing auth state');

            // Check if CombinedAuth is available
            if (typeof CombinedAuth !== 'undefined') {
                console.log('Using CombinedAuth for initialization');

                // Initialize CombinedAuth
                CombinedAuth.init().then(() => {
                    if (CombinedAuth.isAuthenticated()) {
                        console.log('User is already authenticated with CombinedAuth');

                        // Get and display token
                        CombinedAuth.getToken().then(token => {
                            if (token) {
                                idTokenElement.textContent = token;
                                authTokenSection.style.display = 'block';
                                loginForm.style.display = 'none';
                                document.getElementById('test-login-form').style.display = 'none';
                                console.log('Token displayed for existing session');
                            }
                        });
                    }
                });
            }
            // Fallback to AssetKPIAuth if CombinedAuth is not available
            else if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.initAuth) {
                console.log('Falling back to AssetKPIAuth for initialization');
                AssetKPIAuth.initAuth(function(user) {
                    if (user) {
                        // User is signed in
                        console.log('User is already signed in:', user.email);

                        // Get and display token
                        AssetKPIAuth.getIdToken().then(token => {
                            idTokenElement.textContent = token;
                            authTokenSection.style.display = 'block';
                            loginForm.style.display = 'none';

                            // Start token refresh mechanism
                            if (window.tokenRefresh) {
                                window.tokenRefresh.start();
                                console.log('Token refresh mechanism started for existing session');
                            }
                        });
                    }
                });
            } else {
                console.error('Neither CombinedAuth nor AssetKPIAuth is available');
            }
        }

        // Listen for the scripts loaded event
        document.addEventListener('assetKPIScriptsLoaded', function() {
            console.log('All scripts loaded event received');
            initializeAuthState();
        });

        // Also try to initialize after a delay as a fallback
        setTimeout(() => {
            console.log('Fallback: Initializing auth state after delay');
            if (typeof AssetKPIAuth !== 'undefined') {
                initializeAuthState();
            } else {
                console.error('AssetKPIAuth is still not available after delay');
            }
        }, 1000); // 1 second delay
    </script>
</body>
</html>