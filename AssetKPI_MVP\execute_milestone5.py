import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def execute_migration():
    """Execute the SQL migration script for Milestone 5."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Read the SQL migration script
        with open('db_migration_milestone5.sql', 'r') as f:
            sql_script = f.read()
        
        # Split the script into individual statements
        statements = sql_script.split(';')
        
        # Execute each statement
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement + ';')
                    print(f"Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"Error executing statement: {statement[:50]}...")
                    print(f"Error: {e}")
        
        # Commit the changes
        conn.commit()
        print("Milestone 5 migration completed successfully!")
        
        # Return the connection and cursor for data population
        return conn, cursor
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return None, None

def populate_sample_data(conn, cursor):
    """Populate the database with sample data for Milestone 5."""
    try:
        print("\nPopulating sample data for Milestone 5...")
        
        # Sample storerooms
        storerooms = [
            ('Main Warehouse', 'Building A', 'Primary storage facility for all spare parts'),
            ('Production Floor Storage', 'Building B - Production Area', 'Quick access storage for production parts'),
            ('Maintenance Shop', 'Building C - Maintenance Area', 'Storage for maintenance tools and parts'),
            ('Electrical Shop', 'Building C - Electrical Area', 'Storage for electrical components'),
            ('HVAC Storage', 'Building D - Mechanical Room', 'Storage for HVAC components'),
            ('External Warehouse', 'Off-site Location', 'Long-term storage for low-turnover items')
        ]
        
        print("Inserting storerooms...")
        storeroom_ids = {}
        for storeroom in storerooms:
            cursor.execute(
                """
                INSERT INTO storerooms 
                (storeroom_name, location, description)
                VALUES (%s, %s, %s)
                RETURNING storeroom_id
                """,
                storeroom
            )
            storeroom_id = cursor.fetchone()[0]
            storeroom_ids[storeroom[0]] = storeroom_id
            print(f"  - Added storeroom: {storeroom[0]}")
        
        # Sample storage locations
        storage_locations = []
        
        # Main Warehouse locations
        main_warehouse_id = storeroom_ids['Main Warehouse']
        for aisle in ['A', 'B', 'C', 'D', 'E']:
            for shelf in range(1, 5):
                for bin_num in range(1, 6):
                    location_name = f"MW-{aisle}{shelf}-{bin_num}"
                    storage_locations.append((main_warehouse_id, location_name, f"{bin_num}", aisle, f"{shelf}"))
        
        # Production Floor Storage locations
        prod_storage_id = storeroom_ids['Production Floor Storage']
        for aisle in ['P1', 'P2', 'P3']:
            for shelf in range(1, 4):
                for bin_num in range(1, 4):
                    location_name = f"PF-{aisle}{shelf}-{bin_num}"
                    storage_locations.append((prod_storage_id, location_name, f"{bin_num}", aisle, f"{shelf}"))
        
        # Maintenance Shop locations
        maint_shop_id = storeroom_ids['Maintenance Shop']
        for aisle in ['M1', 'M2']:
            for shelf in range(1, 4):
                for bin_num in range(1, 4):
                    location_name = f"MS-{aisle}{shelf}-{bin_num}"
                    storage_locations.append((maint_shop_id, location_name, f"{bin_num}", aisle, f"{shelf}"))
        
        # Electrical Shop locations
        elec_shop_id = storeroom_ids['Electrical Shop']
        for aisle in ['E1', 'E2']:
            for shelf in range(1, 4):
                for bin_num in range(1, 4):
                    location_name = f"ES-{aisle}{shelf}-{bin_num}"
                    storage_locations.append((elec_shop_id, location_name, f"{bin_num}", aisle, f"{shelf}"))
        
        # HVAC Storage locations
        hvac_storage_id = storeroom_ids['HVAC Storage']
        for aisle in ['H1', 'H2']:
            for shelf in range(1, 3):
                for bin_num in range(1, 3):
                    location_name = f"HS-{aisle}{shelf}-{bin_num}"
                    storage_locations.append((hvac_storage_id, location_name, f"{bin_num}", aisle, f"{shelf}"))
        
        # External Warehouse locations
        ext_warehouse_id = storeroom_ids['External Warehouse']
        for aisle in ['X1', 'X2', 'X3', 'X4']:
            for shelf in range(1, 5):
                for bin_num in range(1, 4):
                    location_name = f"EW-{aisle}{shelf}-{bin_num}"
                    storage_locations.append((ext_warehouse_id, location_name, f"{bin_num}", aisle, f"{shelf}"))
        
        print(f"Inserting {len(storage_locations)} storage locations...")
        location_ids = {}
        for i, location in enumerate(storage_locations):
            cursor.execute(
                """
                INSERT INTO storage_locations 
                (storeroom_id, location_name, bin, aisle, shelf)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING location_id
                """,
                location
            )
            location_id = cursor.fetchone()[0]
            location_ids[location[1]] = location_id
            
            if (i + 1) % 20 == 0:
                print(f"  - Added {i + 1} storage locations so far...")
        
        print(f"  - Added {len(storage_locations)} storage locations")
        
        # Sample vendors
        vendors = [
            ('SKF Bearings', 'John Anderson', '555-123-4567', '<EMAIL>', '123 Bearing Way, Bearingville, BV 12345'),
            ('Parker Hydraulics', 'Sarah Johnson', '555-234-5678', '<EMAIL>', '456 Hydraulic Drive, Fluidtown, FT 23456'),
            ('Siemens Electric', 'Michael Brown', '555-345-6789', '<EMAIL>', '789 Electric Avenue, Powerburg, PB 34567'),
            ('Grainger Industrial', 'Emily Davis', '555-456-7890', '<EMAIL>', '101 Supply Street, Stockton, ST 45678'),
            ('McMaster-Carr', 'David Wilson', '555-567-8901', '<EMAIL>', '202 Parts Parkway, Componentville, CV 56789'),
            ('MSC Industrial', 'Jennifer Taylor', '555-678-9012', '<EMAIL>', '303 Tool Terrace, Toolsville, TV 67890'),
            ('Fastenal', 'Robert Martinez', '555-789-0123', '<EMAIL>', '404 Fastener Freeway, Boltburg, BB 78901'),
            ('Motion Industries', 'Lisa Thompson', '555-890-1234', '<EMAIL>', '505 Motion Drive, Geartown, GT 89012'),
            ('Applied Industrial', 'James Garcia', '555-901-2345', '<EMAIL>', '606 Industrial Avenue, Machineville, MV 90123'),
            ('Kaman Industrial', 'Patricia Rodriguez', '************', '<EMAIL>', '707 Distribution Drive, Supplyville, SV 01234')
        ]
        
        print("Inserting vendors...")
        vendor_ids = {}
        for i, vendor in enumerate(vendors):
            # Set some vendors as preferred
            preferred = True if i < 3 else False
            
            cursor.execute(
                """
                INSERT INTO vendors 
                (vendor_name, contact_person, phone, email, address, preferred)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING vendor_id
                """,
                (*vendor, preferred)
            )
            vendor_id = cursor.fetchone()[0]
            vendor_ids[vendor[0]] = vendor_id
            print(f"  - Added vendor: {vendor[0]}")
        
        # Update spare parts with storage locations and vendors
        print("Updating spare parts with storage locations and vendors...")
        
        # Get all spare parts
        cursor.execute("SELECT partid, partname, manufacturer FROM spareparts")
        spare_parts = cursor.fetchall()
        
        if spare_parts:
            # Create a mapping of part types to appropriate storage areas
            part_type_locations = {
                'Bearing': ['MW-A1-1', 'MW-A1-2', 'MW-A1-3', 'PF-P1-1'],
                'Belt': ['MW-A2-1', 'MW-A2-2', 'MW-A2-3', 'PF-P1-2'],
                'Coupling': ['MW-A3-1', 'MW-A3-2', 'MW-A3-3'],
                'Gear': ['MW-B1-1', 'MW-B1-2', 'MW-B1-3'],
                'Chain': ['MW-B2-1', 'MW-B2-2', 'MW-B2-3'],
                'Sprocket': ['MW-B3-1', 'MW-B3-2', 'MW-B3-3'],
                'Pulley': ['MW-B4-1', 'MW-B4-2', 'MW-B4-3'],
                'Shaft': ['MW-C1-1', 'MW-C1-2', 'MW-C1-3'],
                'Key': ['MW-C2-1', 'MW-C2-2', 'MW-C2-3'],
                'Bushing': ['MW-C3-1', 'MW-C3-2', 'MW-C3-3'],
                'Motor': ['MW-D1-1', 'MW-D1-2', 'MW-D1-3', 'ES-E1-1'],
                'Contactor': ['ES-E1-2', 'ES-E1-3', 'ES-E2-1'],
                'Relay': ['ES-E2-2', 'ES-E2-3', 'ES-E1-1'],
                'Circuit': ['ES-E1-2', 'ES-E1-3', 'ES-E2-1'],
                'Fuse': ['ES-E2-2', 'ES-E2-3', 'ES-E1-1'],
                'Switch': ['ES-E1-2', 'ES-E1-3', 'ES-E2-1'],
                'Sensor': ['ES-E2-2', 'ES-E2-3', 'ES-E1-1'],
                'Hydraulic': ['MS-M1-1', 'MS-M1-2', 'MS-M1-3'],
                'Pneumatic': ['MS-M2-1', 'MS-M2-2', 'MS-M2-3'],
                'HVAC': ['HS-H1-1', 'HS-H1-2', 'HS-H2-1', 'HS-H2-2'],
                'Pipe': ['MS-M1-1', 'MS-M1-2', 'MS-M1-3'],
                'Valve': ['MS-M2-1', 'MS-M2-2', 'MS-M2-3'],
                'Gasket': ['MS-M1-1', 'MS-M1-2', 'MS-M1-3'],
                'Bolt': ['PF-P2-1', 'PF-P2-2', 'PF-P2-3'],
                'Nut': ['PF-P2-1', 'PF-P2-2', 'PF-P2-3'],
                'Washer': ['PF-P2-1', 'PF-P2-2', 'PF-P2-3'],
                'Screw': ['PF-P2-1', 'PF-P2-2', 'PF-P2-3']
            }
            
            # Create a mapping of manufacturers to vendors
            manufacturer_vendors = {
                'SKF': 'SKF Bearings',
                'Timken': 'Motion Industries',
                'NSK': 'Motion Industries',
                'Gates': 'Motion Industries',
                'Continental': 'Grainger Industrial',
                'Goodyear': 'Grainger Industrial',
                'Lovejoy': 'Motion Industries',
                'Rexnord': 'Motion Industries',
                'Boston Gear': 'Motion Industries',
                'Martin': 'Motion Industries',
                'Browning': 'Motion Industries',
                'Diamond': 'Grainger Industrial',
                'Tsubaki': 'Motion Industries',
                'TB Woods': 'Motion Industries',
                'Thomson': 'McMaster-Carr',
                'Ruland': 'McMaster-Carr',
                'Generic': 'Fastenal',
                'Dodge': 'Motion Industries',
                'Baldor': 'Siemens Electric',
                'WEG': 'Siemens Electric',
                'Siemens': 'Siemens Electric',
                'Allen-Bradley': 'Applied Industrial',
                'Schneider': 'Applied Industrial',
                'Eaton': 'Applied Industrial',
                'Omron': 'Applied Industrial',
                'Finder': 'Applied Industrial',
                'ABB': 'Siemens Electric',
                'Square D': 'Applied Industrial',
                'Bussmann': 'Applied Industrial',
                'Littelfuse': 'Applied Industrial',
                'Mersen': 'Applied Industrial',
                'Danfoss': 'Applied Industrial',
                'Honeywell': 'Applied Industrial',
                'IFM': 'Applied Industrial',
                'Omega': 'Applied Industrial',
                'Parker': 'Parker Hydraulics',
                'Bosch Rexroth': 'Parker Hydraulics',
                'Ingersoll Rand': 'Grainger Industrial',
                'SMC': 'Grainger Industrial',
                'Festo': 'Grainger Industrial',
                'Atlas Copco': 'Grainger Industrial',
                'Dupont': 'MSC Industrial',
                'Nu-Calgon': 'MSC Industrial',
                'Watts': 'Grainger Industrial',
                'Garlock': 'Grainger Industrial'
            }
            
            parts_updated = 0
            for part_id, part_name, manufacturer in spare_parts:
                # Find appropriate storage location
                location_id = None
                for part_type, locations in part_type_locations.items():
                    if part_type in part_name:
                        # Pick a random location from the appropriate list
                        location_name = random.choice(locations)
                        location_id = location_ids.get(location_name)
                        break
                
                # If no specific location found, use a random one
                if location_id is None:
                    location_name = random.choice(list(location_ids.keys()))
                    location_id = location_ids[location_name]
                
                # Find appropriate vendor
                vendor_id = None
                if manufacturer in manufacturer_vendors:
                    vendor_name = manufacturer_vendors[manufacturer]
                    vendor_id = vendor_ids.get(vendor_name)
                
                # If no specific vendor found, use a random one
                if vendor_id is None:
                    vendor_name = random.choice(list(vendor_ids.keys()))
                    vendor_id = vendor_ids[vendor_name]
                
                # Update the part
                cursor.execute(
                    """
                    UPDATE spareparts 
                    SET storage_location_id = %s, preferred_vendor_id = %s
                    WHERE partid = %s
                    """,
                    (location_id, vendor_id, part_id)
                )
                
                parts_updated += 1
            
            print(f"  - Updated {parts_updated} spare parts with storage locations and vendors")
        else:
            print("  - No spare parts found to update")
        
        # Commit the changes
        conn.commit()
        print("Sample data population completed successfully!")
        
    except Exception as e:
        print(f"Error populating sample data: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()

def main():
    """Main function to execute migration and populate data."""
    conn, cursor = execute_migration()
    
    if conn and cursor:
        populate_sample_data(conn, cursor)
        cursor.close()
        conn.close()
    
    print("Milestone 5 completed!")

if __name__ == "__main__":
    main()
