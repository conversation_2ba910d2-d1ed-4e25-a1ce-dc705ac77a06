{"id": "inventory-management", "title": "Inventory Management Tutorial", "description": "Learn how to manage inventory in AssetKPI", "version": "1.0.0", "steps": [{"id": "step1", "title": "Welcome to Inventory Management", "content": "<p>Welcome to the Inventory Management tutorial! This tutorial will guide you through the basics of managing inventory in AssetKPI.</p><p>Inventory management is crucial for maintaining optimal stock levels, reducing costs, and ensuring parts availability for maintenance operations.</p>", "target": ".navbar-brand", "position": "bottom", "highlight": true}, {"id": "step2", "title": "Navigate to Inventory", "content": "<p>To start managing your inventory, click on the <strong>Inventory</strong> menu in the navigation bar.</p>", "target": "#inventoryDropdown", "position": "bottom", "highlight": true, "action": {"type": "click", "element": "#inventoryDropdown"}, "waitForElement": ".dropdown-menu"}, {"id": "step3", "title": "View Parts List", "content": "<p>Click on <strong>Parts</strong> to see all your inventory items.</p>", "target": "a[href='/inventory']", "position": "right", "highlight": true, "action": {"type": "click", "element": "a[href='/inventory']"}, "waitForPage": "/inventory"}, {"id": "step4", "title": "Inventory List Overview", "content": "<p>This is the Inventory List page. Here you can see all your spare parts, their stock levels, and key information.</p><p>You can sort, filter, and search for parts using the controls at the top of the table.</p>", "target": ".card-header:contains('Inventory')", "position": "bottom", "highlight": true}, {"id": "step5", "title": "Part Details", "content": "<p>To view details for a specific part, click on the part ID or name in the table.</p><p>This will show you comprehensive information about the part, including its stock level, reorder point, and usage history.</p>", "target": "table tbody tr:first-child td:first-child", "position": "right", "highlight": true}, {"id": "step6", "title": "Stock Levels", "content": "<p>The <strong>Stock Quantity</strong> column shows the current quantity of each part in inventory.</p><p>Parts with stock levels below the reorder point are highlighted in red, indicating that you should consider restocking.</p>", "target": "table thead th:contains('Stock Quantity')", "position": "bottom", "highlight": true}, {"id": "step7", "title": "Reorder Levels", "content": "<p>The <strong>Reorder Level</strong> column shows the minimum quantity at which you should reorder the part.</p><p>This is calculated based on usage patterns, lead time, and safety stock requirements.</p>", "target": "table thead th:contains('Reorder Level')", "position": "bottom", "highlight": true}, {"id": "step8", "title": "Add New Part", "content": "<p>To add a new part to your inventory, click the <strong>Add Part</strong> button at the top of the page.</p><p>This will open a form where you can enter the details for the new part.</p>", "target": ".btn:contains('Add Part')", "position": "bottom", "highlight": true}, {"id": "step9", "title": "Inventory Analysis", "content": "<p>For advanced inventory analysis, navigate back to the Inventory menu and select <strong>Analysis</strong>.</p>", "target": "#inventoryDropdown", "position": "bottom", "highlight": true, "action": {"type": "click", "element": "#inventoryDropdown"}, "waitForElement": ".dropdown-menu"}, {"id": "step10", "title": "Analysis Page", "content": "<p>Click on <strong>Analysis</strong> to see detailed inventory analytics.</p>", "target": "a[href='/inventory/analysis']", "position": "right", "highlight": true, "action": {"type": "click", "element": "a[href='/inventory/analysis']"}, "waitForPage": "/inventory/analysis"}, {"id": "step11", "title": "Inventory Analysis Overview", "content": "<p>The Inventory Analysis page provides insights into your inventory performance.</p><p>Here you can see metrics such as inventory turnover, days of supply, and potential cost savings.</p>", "target": ".card-header:contains('Inventory Analysis')", "position": "bottom", "highlight": true}, {"id": "step12", "title": "Inventory Optimization", "content": "<p>For inventory optimization recommendations, navigate back to the Inventory menu and select <strong>Optimization Report</strong>.</p>", "target": "#inventoryDropdown", "position": "bottom", "highlight": true, "action": {"type": "click", "element": "#inventoryDropdown"}, "waitForElement": ".dropdown-menu"}, {"id": "step13", "title": "Optimization Report", "content": "<p>Click on <strong>Optimization Report</strong> to see recommendations for optimizing your inventory.</p>", "target": "a[href='/inventory/optimization-report']", "position": "right", "highlight": true}, {"id": "step14", "title": "Congratulations!", "content": "<p>Congratulations! You've completed the Inventory Management tutorial.</p><p>You now know how to:</p><ul><li>Navigate to the Inventory List</li><li>View part details</li><li>Monitor stock levels</li><li>Add new parts</li><li>Analyze inventory performance</li><li>Access optimization recommendations</li></ul><p>Feel free to explore more features of AssetKPI!</p>", "target": ".navbar-brand", "position": "bottom", "highlight": false}]}