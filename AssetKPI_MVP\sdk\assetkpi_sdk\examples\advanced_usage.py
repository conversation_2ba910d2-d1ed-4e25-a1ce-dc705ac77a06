"""
Advanced usage examples for the AssetKPI SDK.
"""

import os
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from assetkpi import AssetKPISDK, AuthenticationError, RateLimitError, APIError

# Load environment variables
load_dotenv()

# Get API key from environment variable
API_KEY = os.getenv("ASSETKPI_API_KEY")
BASE_URL = os.getenv("ASSETKPI_BASE_URL", "http://localhost:8000/api")

def inventory_optimization_example(sdk):
    """Example of inventory optimization workflow."""
    print("=== Inventory Optimization Example ===")
    
    # Get current inventory status
    print("Getting current inventory status...")
    inventory_summary = sdk.inventory.get_inventory_summary()
    print(f"Current inventory value: ${inventory_summary.get('total_value', 0):.2f}")
    print(f"Parts below reorder level: {inventory_summary.get('below_reorder', 0)}")
    
    # Get inventory analysis
    print("\nGetting inventory analysis...")
    analysis = sdk.inventory.get_inventory_analysis()
    
    # Find parts with high potential savings
    high_savings_parts = [
        item for item in analysis 
        if item.get('potential_savings', 0) > 100
    ]
    
    print(f"Found {len(high_savings_parts)} parts with high potential savings:")
    for item in high_savings_parts[:3]:  # Show top 3
        print(f"  Part: {item.get('part_name', 'Unknown')}")
        print(f"    Current Stock: {item.get('current_stock', 0)}, Optimal Stock: {item.get('optimal_stock', 0)}")
        print(f"    Potential Savings: ${item.get('potential_savings', 0):.2f}")
    
    # Run inventory optimization
    print("\nRunning inventory optimization...")
    result = sdk.inventory.run_optimization()
    print(f"Optimization result: {result.get('message', 'Unknown')}")
    
    # Get recommendations
    print("\nGetting inventory recommendations...")
    recommendations = sdk.recommendations.get_recommendations(status="NEW")
    
    print(f"Found {len(recommendations.get('items', []))} new recommendations:")
    for rec in recommendations.get('items', [])[:5]:  # Show top 5
        print(f"  Type: {rec.get('recommendation_type', 'Unknown')}, Part ID: {rec.get('part_id', 'Unknown')}")
        print(f"    Message: {rec.get('message', 'Unknown')}")
        print(f"    Priority: {rec.get('priority', 'Unknown')}")
    
    # Acknowledge a recommendation (if any exist)
    if recommendations.get('items', []):
        rec_id = recommendations['items'][0].get('id')
        print(f"\nAcknowledging recommendation {rec_id}...")
        result = sdk.recommendations.update_recommendation_status(rec_id, "ACKNOWLEDGED")
        print(f"Result: {result.get('message', 'Unknown')}")


def kpi_analysis_example(sdk):
    """Example of KPI analysis workflow."""
    print("\n=== KPI Analysis Example ===")
    
    # Get latest KPIs
    print("Getting latest KPIs...")
    kpis = sdk.kpi.get_latest_kpis()
    print(f"MTTR: {kpis.get('mttr', 'N/A')} hours")
    print(f"MTBF: {kpis.get('mtbf', 'N/A')} hours")
    print(f"Failure Rate: {kpis.get('failure_rate', 'N/A')} failures/year")
    
    # Get KPI history for the last 30 days
    print("\nGetting KPI history for the last 30 days...")
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    mttr_history = sdk.kpi.get_kpi_history(
        "MTTR_Calculated", 
        start_date=start_date,
        end_date=end_date
    )
    
    print(f"Found {len(mttr_history)} MTTR data points")
    
    # Calculate average MTTR
    if mttr_history:
        mttr_values = [point.get('value', 0) for point in mttr_history if point.get('value') is not None]
        if mttr_values:
            avg_mttr = sum(mttr_values) / len(mttr_values)
            print(f"Average MTTR over the last 30 days: {avg_mttr:.2f} hours")
    
    # Get KPI analytics
    print("\nGetting KPI analytics...")
    analytics = sdk.kpi.get_kpi_analytics(
        kpi_type="mttr",
        date_from=start_date,
        date_to=end_date,
        time_aggregation="weekly"
    )
    
    print("KPI analytics data retrieved successfully")
    
    # Get asset performance
    print("\nGetting asset performance data...")
    performance = sdk.kpi.get_asset_performance(
        metric="availability",
        time_period="month",
        aggregation="daily",
        limit="5"
    )
    
    print("Asset performance data retrieved successfully")


def user_management_example(sdk):
    """Example of user management workflow."""
    print("\n=== User Management Example ===")
    
    # Get current user
    print("Getting current user...")
    user = sdk.users.get_current_user()
    print(f"Current User: {user.get('full_name', 'Unknown')} ({user.get('email', 'Unknown')})")
    print(f"Role: {user.get('role', 'Unknown')}")
    
    # Get all users (admin only)
    try:
        print("\nGetting all users...")
        users = sdk.users.get_users(limit=5)
        print(f"Found {len(users.get('items', []))} users:")
        for user in users.get('items', []):
            print(f"  User: {user.get('full_name', 'Unknown')} ({user.get('email', 'Unknown')})")
            print(f"    Role: {user.get('role', 'Unknown')}, Last Login: {user.get('last_login', 'Unknown')}")
    except APIError as e:
        print(f"Error getting users: {str(e)}")
    
    # Get user permissions (admin only)
    if user.get('role') == 'ADMIN' and users.get('items', []):
        try:
            user_id = users['items'][0].get('user_id')
            print(f"\nGetting permissions for user {user_id}...")
            permissions = sdk.users.get_user_permissions(user_id)
            print(f"Permissions: {permissions.get('permissions', [])}")
        except APIError as e:
            print(f"Error getting user permissions: {str(e)}")


def analytics_example(sdk):
    """Example of analytics workflow."""
    print("\n=== Analytics Example ===")
    
    # Get user activity
    print("Getting user activity...")
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    
    try:
        activity = sdk.analytics.get_user_activity(
            start_date=start_date,
            end_date=end_date,
            limit=5
        )
        
        print(f"Found {len(activity.get('items', []))} activity records:")
        for event in activity.get('items', [])[:3]:  # Show top 3
            print(f"  User: {event.get('user_id', 'Unknown')}, Event: {event.get('event_type', 'Unknown')}")
            print(f"    Time: {event.get('timestamp', 'Unknown')}")
    except APIError as e:
        print(f"Error getting user activity: {str(e)}")
    
    # Get feature usage
    print("\nGetting feature usage...")
    try:
        usage = sdk.analytics.get_feature_usage(
            start_date=start_date,
            end_date=end_date,
            aggregation="daily"
        )
        
        print("Feature usage data retrieved successfully")
    except APIError as e:
        print(f"Error getting feature usage: {str(e)}")
    
    # Get user sessions
    print("\nGetting user sessions...")
    try:
        sessions = sdk.analytics.get_user_sessions(
            start_date=start_date,
            end_date=end_date,
            limit=5
        )
        
        print(f"Found {len(sessions.get('items', []))} user sessions:")
        for session in sessions.get('items', [])[:3]:  # Show top 3
            print(f"  Session ID: {session.get('session_id', 'Unknown')}")
            print(f"    User: {session.get('user_id', 'Unknown')}")
            print(f"    Duration: {session.get('duration_seconds', 0)} seconds")
    except APIError as e:
        print(f"Error getting user sessions: {str(e)}")


def main():
    """Run advanced SDK examples."""
    # Initialize the SDK with custom retry settings
    sdk = AssetKPISDK(
        base_url=BASE_URL,
        api_key=API_KEY,
        timeout=60,  # 60 second timeout
        max_retries=3,  # Retry up to 3 times
        retry_backoff_factor=0.5,  # Backoff between retries
    )
    
    try:
        # Run examples
        inventory_optimization_example(sdk)
        kpi_analysis_example(sdk)
        user_management_example(sdk)
        analytics_example(sdk)
        
    except AuthenticationError as e:
        print(f"Authentication error: {str(e)}")
    except RateLimitError as e:
        print(f"Rate limit exceeded. Retry after {e.retry_after} seconds")
    except APIError as e:
        print(f"API error ({e.status_code}): {str(e)}")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")


if __name__ == "__main__":
    main()
