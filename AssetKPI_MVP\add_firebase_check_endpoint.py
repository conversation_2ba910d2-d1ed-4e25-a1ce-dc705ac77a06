# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Add a Firebase check endpoint after the debug_token function
firebase_check_endpoint = '''

@app.get("/api/firebase-check", tags=["Authentication"])
async def firebase_check():
    """
    Check if Firebase Admin SDK is properly initialized.
    """
    try:
        # Check if Firebase Admin SDK is initialized
        if not firebase_admin._apps:
            return JSONResponse(
                status_code=500,
                content={"error": "Firebase Admin SDK is not initialized"}
            )
        
        # Try to list users (limited to 1) to verify the service account has proper permissions
        try:
            users = auth.list_users(max_results=1)
            user_list = [{"uid": user.uid, "email": user.email} for user in users.users]
            
            return {
                "status": "success",
                "message": "Firebase Admin SDK is properly initialized",
                "sample_users": user_list
            }
        except Exception as e:
            return JSONResponse(
                status_code=500,
                content={"error": f"Firebase Admin SDK is initialized but failed to list users: {str(e)}"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error checking Firebase Admin SDK: {str(e)}"}
        )
'''

# Find a good place to add the Firebase check endpoint (after the debug_token function)
debug_token_end = '''    except Exception as e:
        print(f"Error debugging token: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

content = content.replace(debug_token_end, debug_token_end + firebase_check_endpoint)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Added Firebase check endpoint to main.py")
