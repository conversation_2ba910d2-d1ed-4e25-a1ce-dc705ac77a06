import requests
import json
import sys

def get_id_token_with_password(email, password, api_key):
    """
    Get an ID token by signing in with email and password using the Firebase Auth REST API
    """
    url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={api_key}"
    payload = {
        "email": email,
        "password": password,
        "returnSecureToken": True
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        data = response.json()
        id_token = data.get("idToken")
        
        if id_token:
            print("Successfully obtained ID token")
            return id_token
        else:
            print("ID token not found in response")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Error signing in with email and password: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def main():
    # User credentials
    email = "<EMAIL>"
    password = "TestTest"
    
    # Firebase Web API Key (from Firebase console -> Project settings -> Web API Key)
    firebase_api_key = input("Enter your Firebase Web API Key: ")
    
    # Get ID token
    id_token = get_id_token_with_password(email, password, firebase_api_key)
    if not id_token:
        print("Failed to get ID token")
        sys.exit(1)
    
    # Print the ID token
    print("\n=== Firebase ID Token ===")
    print(id_token)
    print("\n=== Use this token in your Authorization header ===")
    print(f"Authorization: Bearer {id_token}")
    
    # Save token to file for convenience
    with open("firebase_id_token.txt", "w") as f:
        f.write(id_token)
    print("\nToken saved to firebase_id_token.txt")
    
    # Create a sample curl command
    curl_command = f'curl -X GET "http://localhost:8000/api/kpi/history/MTTR_Calculated" -H "Authorization: Bearer {id_token}"'
    with open("test_api_curl.sh", "w") as f:
        f.write(curl_command)
    print("\nSample curl command saved to test_api_curl.sh")

if __name__ == "__main__":
    main()
