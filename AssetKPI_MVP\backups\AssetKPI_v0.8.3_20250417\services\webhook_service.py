"""
Webhook service for the AssetKPI application.

This module provides functions for delivering webhook events to subscribers.
"""

import json
import logging
import time
import random
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from sqlalchemy.orm import Session
from sqlalchemy import desc

from models.webhooks import WebhookSubscription, WebhookEvent

# Create a logger for this module
logger = logging.getLogger(__name__)


async def deliver_webhook_event(event_id: int, db: Session) -> bool:
    """
    Deliver a webhook event to the subscriber.
    
    Args:
        event_id: ID of the webhook event
        db: Database session
        
    Returns:
        True if delivery was successful, False otherwise
    """
    try:
        # Get event
        event = db.query(WebhookEvent).filter(WebhookEvent.id == event_id).first()
        if not event:
            logger.error(f"Webhook event with ID {event_id} not found")
            return False
        
        # Get subscription
        subscription = db.query(WebhookSubscription).filter(
            WebhookSubscription.id == event.subscription_id
        ).first()
        if not subscription:
            logger.error(f"Webhook subscription with ID {event.subscription_id} not found")
            
            # Update event status
            event.status = "failure"
            event.error_message = f"Webhook subscription with ID {event.subscription_id} not found"
            event.updated_at = datetime.now()
            db.commit()
            
            return False
        
        # Check if subscription is active
        if not subscription.is_active:
            logger.warning(f"Webhook subscription with ID {subscription.id} is inactive")
            
            # Update event status
            event.status = "failure"
            event.error_message = "Webhook subscription is inactive"
            event.updated_at = datetime.now()
            db.commit()
            
            return False
        
        # Prepare headers
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "AssetKPI-Webhook-Service/1.0",
            "X-AssetKPI-Event": event.event_type,
            "X-AssetKPI-Delivery": str(event.id),
            "X-AssetKPI-Timestamp": datetime.now().isoformat()
        }
        
        # Add authentication headers
        if subscription.auth_type == "basic" and subscription.auth_credentials:
            import base64
            auth_header = f"Basic {subscription.auth_credentials}"
            headers["Authorization"] = auth_header
        elif subscription.auth_type == "bearer" and subscription.auth_credentials:
            auth_header = f"Bearer {subscription.auth_credentials}"
            headers["Authorization"] = auth_header
        elif subscription.auth_type == "api_key" and subscription.auth_credentials:
            # Parse API key format (header_name:key_value)
            try:
                header_name, key_value = subscription.auth_credentials.split(":", 1)
                headers[header_name] = key_value
            except ValueError:
                logger.warning(f"Invalid API key format for subscription {subscription.id}")
        
        # Prepare payload
        payload = {
            "id": str(event.id),
            "event": event.event_type,
            "timestamp": datetime.now().isoformat(),
            "data": event.payload
        }
        
        # Increment attempt count
        event.attempts += 1
        event.updated_at = datetime.now()
        db.commit()
        
        # Send webhook
        async with aiohttp.ClientSession() as session:
            try:
                timeout = aiohttp.ClientTimeout(total=subscription.timeout)
                async with session.post(
                    subscription.url,
                    json=payload,
                    headers=headers,
                    timeout=timeout
                ) as response:
                    # Get response
                    response_status = response.status
                    response_text = await response.text()
                    
                    # Update event with response
                    event.response_status_code = response_status
                    event.response_body = response_text
                    
                    # Check if successful
                    if 200 <= response_status < 300:
                        # Success
                        event.status = "success"
                        event.delivered_at = datetime.now()
                        event.next_attempt_time = None
                        event.error_message = None
                        
                        # Update subscription stats
                        subscription.delivery_success_count += 1
                        subscription.last_delivery_status = "success"
                        subscription.last_delivery_time = datetime.now()
                        
                        logger.info(f"Webhook event {event.id} delivered successfully to {subscription.url}")
                        
                        db.commit()
                        return True
                    else:
                        # Failure
                        event.status = "failure"
                        event.error_message = f"HTTP {response_status}: {response_text[:200]}"
                        
                        # Schedule retry if attempts < retry_count
                        if event.attempts < subscription.retry_count:
                            # Calculate next attempt time with exponential backoff and jitter
                            backoff = min(
                                3600,  # Max 1 hour
                                subscription.retry_interval * (2 ** (event.attempts - 1)) + random.uniform(0, 1)
                            )
                            event.next_attempt_time = datetime.now() + timedelta(seconds=backoff)
                            event.status = "pending"
                            
                            logger.warning(
                                f"Webhook event {event.id} delivery failed with HTTP {response_status}. "
                                f"Retrying in {backoff:.2f} seconds."
                            )
                        else:
                            # Max retries reached
                            logger.error(
                                f"Webhook event {event.id} delivery failed with HTTP {response_status}. "
                                f"Max retries reached."
                            )
                            
                            # Update subscription stats
                            subscription.delivery_failure_count += 1
                            subscription.last_delivery_status = "failure"
                            subscription.last_delivery_time = datetime.now()
                        
                        db.commit()
                        return False
            
            except asyncio.TimeoutError:
                # Timeout
                event.status = "failure"
                event.error_message = f"Request timed out after {subscription.timeout} seconds"
                
                # Schedule retry if attempts < retry_count
                if event.attempts < subscription.retry_count:
                    # Calculate next attempt time with exponential backoff and jitter
                    backoff = min(
                        3600,  # Max 1 hour
                        subscription.retry_interval * (2 ** (event.attempts - 1)) + random.uniform(0, 1)
                    )
                    event.next_attempt_time = datetime.now() + timedelta(seconds=backoff)
                    event.status = "pending"
                    
                    logger.warning(
                        f"Webhook event {event.id} delivery timed out. "
                        f"Retrying in {backoff:.2f} seconds."
                    )
                else:
                    # Max retries reached
                    logger.error(
                        f"Webhook event {event.id} delivery timed out. "
                        f"Max retries reached."
                    )
                    
                    # Update subscription stats
                    subscription.delivery_failure_count += 1
                    subscription.last_delivery_status = "failure"
                    subscription.last_delivery_time = datetime.now()
                
                db.commit()
                return False
            
            except Exception as e:
                # Other error
                event.status = "failure"
                event.error_message = f"Error: {str(e)}"
                
                # Schedule retry if attempts < retry_count
                if event.attempts < subscription.retry_count:
                    # Calculate next attempt time with exponential backoff and jitter
                    backoff = min(
                        3600,  # Max 1 hour
                        subscription.retry_interval * (2 ** (event.attempts - 1)) + random.uniform(0, 1)
                    )
                    event.next_attempt_time = datetime.now() + timedelta(seconds=backoff)
                    event.status = "pending"
                    
                    logger.warning(
                        f"Webhook event {event.id} delivery failed with error: {str(e)}. "
                        f"Retrying in {backoff:.2f} seconds."
                    )
                else:
                    # Max retries reached
                    logger.error(
                        f"Webhook event {event.id} delivery failed with error: {str(e)}. "
                        f"Max retries reached."
                    )
                    
                    # Update subscription stats
                    subscription.delivery_failure_count += 1
                    subscription.last_delivery_status = "failure"
                    subscription.last_delivery_time = datetime.now()
                
                db.commit()
                return False
    
    except Exception as e:
        logger.error(f"Error delivering webhook event {event_id}: {str(e)}")
        return False


async def process_pending_webhook_events(db: Session) -> int:
    """
    Process all pending webhook events.
    
    Args:
        db: Database session
        
    Returns:
        Number of events processed
    """
    try:
        # Get pending events that are due for delivery
        pending_events = db.query(WebhookEvent).filter(
            WebhookEvent.status == "pending",
            WebhookEvent.next_attempt_time <= datetime.now()
        ).order_by(WebhookEvent.next_attempt_time.asc()).limit(50).all()
        
        if not pending_events:
            return 0
        
        logger.info(f"Processing {len(pending_events)} pending webhook events")
        
        # Process events
        tasks = []
        for event in pending_events:
            tasks.append(deliver_webhook_event(event.id, db))
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful deliveries
        success_count = sum(1 for result in results if result is True)
        
        logger.info(f"Processed {len(pending_events)} webhook events. {success_count} succeeded.")
        
        return len(pending_events)
    
    except Exception as e:
        logger.error(f"Error processing pending webhook events: {str(e)}")
        return 0


def create_webhook_event(
    db: Session,
    event_type: str,
    payload: Dict[str, Any],
    subscription_ids: Optional[List[int]] = None
) -> List[int]:
    """
    Create webhook events for all matching subscriptions.
    
    Args:
        db: Database session
        event_type: Type of the event
        payload: Event payload
        subscription_ids: Optional list of subscription IDs to filter by
        
    Returns:
        List of created event IDs
    """
    try:
        # Find matching subscriptions
        query = db.query(WebhookSubscription).filter(
            WebhookSubscription.is_active == True,
            WebhookSubscription.event_types.contains([event_type])
        )
        
        # Filter by subscription IDs if provided
        if subscription_ids:
            query = query.filter(WebhookSubscription.id.in_(subscription_ids))
        
        subscriptions = query.all()
        
        if not subscriptions:
            logger.debug(f"No active subscriptions found for event type {event_type}")
            return []
        
        # Create events
        event_ids = []
        for subscription in subscriptions:
            event = WebhookEvent(
                subscription_id=subscription.id,
                event_type=event_type,
                payload=payload,
                status="pending",
                attempts=0,
                next_attempt_time=datetime.now(),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.add(event)
            db.flush()  # Flush to get the ID
            
            event_ids.append(event.id)
        
        db.commit()
        
        logger.info(f"Created {len(event_ids)} webhook events for event type {event_type}")
        
        return event_ids
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating webhook events: {str(e)}")
        return []


async def webhook_delivery_worker(db_factory):
    """
    Background worker for delivering webhook events.
    
    Args:
        db_factory: Function that returns a database session
    """
    while True:
        try:
            # Get a new database session
            db = db_factory()
            
            # Process pending events
            processed_count = await process_pending_webhook_events(db)
            
            # Close the database session
            db.close()
            
            # Sleep for a short time if no events were processed
            if processed_count == 0:
                await asyncio.sleep(5)
            else:
                # Sleep for a very short time to avoid CPU hogging
                await asyncio.sleep(0.1)
        
        except Exception as e:
            logger.error(f"Error in webhook delivery worker: {str(e)}")
            await asyncio.sleep(5)  # Sleep longer on error
