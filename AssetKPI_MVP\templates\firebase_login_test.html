<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Login Test</title>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="/static/js/token_refresh.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Firebase Authentication Test</h1>

    <div id="login-section">
        <h2>Login</h2>
        <button id="login-google">Login with Google</button>
        <button id="login-email">Login with Email/Password</button>
        <div id="email-login-form" class="hidden">
            <input type="email" id="email" placeholder="Email">
            <input type="password" id="password" placeholder="Password">
            <button id="submit-login">Login</button>
        </div>
    </div>

    <div id="user-section" class="hidden">
        <h2>User Info</h2>
        <p>Logged in as: <span id="user-email"></span></p>
        <p>UID: <span id="user-uid"></span></p>
        <button id="logout">Logout</button>
    </div>

    <div id="token-section" class="hidden">
        <h2>ID Token</h2>
        <button id="get-token">Get ID Token</button>
        <pre id="token-display"></pre>

        <h2>Test API Endpoints</h2>
        <button id="test-debug-token">Test Debug Token Endpoint</button>
        <button id="test-user-check">Test User Check Endpoint</button>
        <button id="test-register">Test Registration Endpoint</button>
        <pre id="api-response"></pre>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
            authDomain: "ikios-596779.firebaseapp.com",
            projectId: "ikios-596779",
            storageBucket: "ikios-596779.appspot.com",
            messagingSenderId: "1045444071940",
            appId: "1:1045444071940:web:c5e52be89b1c4fcd8457741"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();

        // DOM elements
        const loginSection = document.getElementById('login-section');
        const userSection = document.getElementById('user-section');
        const tokenSection = document.getElementById('token-section');
        const userEmail = document.getElementById('user-email');
        const userUid = document.getElementById('user-uid');
        const tokenDisplay = document.getElementById('token-display');
        const apiResponse = document.getElementById('api-response');
        const emailLoginForm = document.getElementById('email-login-form');

        // Login with Google
        document.getElementById('login-google').addEventListener('click', () => {
            const provider = new firebase.auth.GoogleAuthProvider();
            auth.signInWithPopup(provider)
                .catch(error => {
                    console.error('Error signing in with Google:', error);
                    alert(`Error signing in: ${error.message}`);
                });
        });

        // Show email login form
        document.getElementById('login-email').addEventListener('click', () => {
            emailLoginForm.classList.toggle('hidden');
        });

        // Login with email/password
        document.getElementById('submit-login').addEventListener('click', () => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            auth.signInWithEmailAndPassword(email, password)
                .catch(error => {
                    console.error('Error signing in with email/password:', error);
                    alert(`Error signing in: ${error.message}`);
                });
        });

        // Logout
        document.getElementById('logout').addEventListener('click', () => {
            auth.signOut()
                .catch(error => {
                    console.error('Error signing out:', error);
                    alert(`Error signing out: ${error.message}`);
                });
        });

        // Get ID token
        document.getElementById('get-token').addEventListener('click', async () => {
            try {
                const user = auth.currentUser;
                if (user) {
                    const token = await user.getIdToken(true);
                    tokenDisplay.textContent = token;
                }
            } catch (error) {
                console.error('Error getting token:', error);
                alert(`Error getting token: ${error.message}`);
            }
        });

        // Test debug token endpoint
        document.getElementById('test-debug-token').addEventListener('click', async () => {
            try {
                const user = auth.currentUser;
                if (user) {
                    const token = await user.getIdToken(true);
                    const response = await fetch('/api/debug-token', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    const data = await response.json();
                    apiResponse.textContent = JSON.stringify(data, null, 2);
                }
            } catch (error) {
                console.error('Error testing debug token endpoint:', error);
                apiResponse.textContent = `Error: ${error.message}`;
            }
        });

        // Test user check endpoint
        document.getElementById('test-user-check').addEventListener('click', async () => {
            try {
                const user = auth.currentUser;
                if (user) {
                    const response = await fetch(`/api/user-check/${user.uid}`);
                    const data = await response.json();
                    apiResponse.textContent = JSON.stringify(data, null, 2);
                }
            } catch (error) {
                console.error('Error testing user check endpoint:', error);
                apiResponse.textContent = `Error: ${error.message}`;
            }
        });

        // Test registration endpoint
        document.getElementById('test-register').addEventListener('click', async () => {
            try {
                const user = auth.currentUser;
                if (user) {
                    const token = await user.getIdToken(true);
                    const response = await fetch('/api/register', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    const data = await response.json();
                    apiResponse.textContent = JSON.stringify(data, null, 2);
                }
            } catch (error) {
                console.error('Error testing registration endpoint:', error);
                apiResponse.textContent = `Error: ${error.message}`;
            }
        });

        // Auth state change listener
        auth.onAuthStateChanged(user => {
            if (user) {
                // User is signed in
                loginSection.classList.add('hidden');
                userSection.classList.remove('hidden');
                tokenSection.classList.remove('hidden');
                userEmail.textContent = user.email;
                userUid.textContent = user.uid;

                // Start token refresh mechanism
                if (window.tokenRefresh) {
                    window.tokenRefresh.start();
                }

                // Get initial token and store it
                user.getIdToken(true).then(token => {
                    localStorage.setItem('firebaseIdToken', token);
                    document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;
                });
            } else {
                // User is signed out
                loginSection.classList.remove('hidden');
                userSection.classList.add('hidden');
                tokenSection.classList.add('hidden');
                tokenDisplay.textContent = '';
                apiResponse.textContent = '';

                // Stop token refresh mechanism
                if (window.tokenRefresh) {
                    window.tokenRefresh.stop();
                }

                // Clear stored token
                localStorage.removeItem('firebaseIdToken');
                document.cookie = 'firebaseIdToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';
            }
        });
    </script>
</body>
</html>
