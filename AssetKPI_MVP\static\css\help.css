/**
 * Help System Styles
 * 
 * This file contains styles for the help system, documentation,
 * and support features across the AssetKPI application.
 */

/* ===== Help Container ===== */

.help-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.help-header {
  text-align: center;
  margin-bottom: 3rem;
}

.help-title {
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 1rem;
}

.help-subtitle {
  color: #7f8c8d;
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== Search Bar ===== */

.help-search {
  max-width: 500px;
  margin: 0 auto 3rem;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #bdc3c7;
  border-radius: 2rem;
  font-size: 1rem;
  background-color: #fff;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  font-size: 1.2rem;
}

/* ===== Help Categories ===== */

.help-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.help-category {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #dee2e6;
  text-decoration: none;
  color: inherit;
}

.help-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: inherit;
}

.category-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
}

.category-title {
  color: #2c3e50;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.category-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.category-count {
  color: #95a5a6;
  font-size: 0.9rem;
}

/* ===== FAQ Section ===== */

.faq-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 2rem;
  margin-bottom: 2rem;
}

.faq-title {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.faq-item {
  border-bottom: 1px solid #ecf0f1;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
}

.faq-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.faq-question {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 0.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.faq-question:hover {
  color: #3498db;
}

.faq-toggle {
  color: #7f8c8d;
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.faq-question.active .faq-toggle {
  transform: rotate(180deg);
}

.faq-answer {
  color: #7f8c8d;
  line-height: 1.6;
  padding-left: 1rem;
  display: none;
}

.faq-answer.show {
  display: block;
}

/* ===== Help Article ===== */

.help-article {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 2rem;
  margin-bottom: 2rem;
}

.article-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #ecf0f1;
}

.article-title {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.article-meta {
  color: #7f8c8d;
  font-size: 0.9rem;
  display: flex;
  gap: 1rem;
}

.article-content {
  line-height: 1.8;
  color: #2c3e50;
}

.article-content h2 {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 2rem 0 1rem;
}

.article-content h3 {
  color: #2c3e50;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem;
}

.article-content p {
  margin-bottom: 1rem;
}

.article-content ul,
.article-content ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.article-content li {
  margin-bottom: 0.5rem;
}

.article-content code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #e74c3c;
}

.article-content pre {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  padding: 1rem;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.article-content pre code {
  background: none;
  padding: 0;
  color: #2c3e50;
}

/* ===== Contact Support ===== */

.contact-support {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  margin-bottom: 2rem;
}

.contact-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.contact-description {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.contact-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.btn-contact {
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary-contact {
  background: white;
  color: #3498db;
}

.btn-primary-contact:hover {
  background: #ecf0f1;
  color: #2980b9;
}

.btn-secondary-contact {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary-contact:hover {
  background: white;
  color: #3498db;
}

/* ===== Breadcrumbs ===== */

.help-breadcrumbs {
  margin-bottom: 2rem;
}

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 0;
  list-style: none;
  background-color: transparent;
}

.breadcrumb-item {
  display: flex;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "/";
  color: #7f8c8d;
  margin: 0 0.5rem;
}

.breadcrumb-item a {
  color: #3498db;
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: #2980b9;
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: #7f8c8d;
}

/* ===== Responsive Design ===== */

@media (max-width: 767.98px) {
  .help-container {
    padding: 1rem;
  }
  
  .help-title {
    font-size: 2rem;
  }
  
  .help-categories {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .help-category {
    padding: 1.5rem;
  }
  
  .category-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .help-article,
  .faq-section {
    padding: 1rem;
  }
  
  .article-title {
    font-size: 1.5rem;
  }
  
  .contact-buttons {
    flex-direction: column;
  }
  
  .btn-contact {
    width: 100%;
    text-align: center;
  }
  
  .article-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
}
