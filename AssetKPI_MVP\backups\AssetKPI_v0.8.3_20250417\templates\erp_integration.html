{% extends "layout.html" %}

{% block title %}ERP Integration - AssetKPI{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4">ERP Integration</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
        <li class="breadcrumb-item active">ERP Integration</li>
    </ol>

    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-plug me-1"></i>
                        ERP Connections
                    </div>
                    <button class="btn btn-primary btn-sm" id="createConnectionBtn">
                        <i class="fas fa-plus"></i> Create Connection
                    </button>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        This page allows you to configure connections to external ERP systems and set up data mappings for integration.
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="erpConnectionsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>System Type</th>
                                    <th>Status</th>
                                    <th>Last Sync</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- ERP connections will be loaded here -->
                                <tr>
                                    <td colspan="6" class="text-center">No ERP connections configured yet.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-map me-1"></i>
                    Data Mappings
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Data mappings define how data is synchronized between AssetKPI and external ERP systems.
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataMappingsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Source</th>
                                    <th>Destination</th>
                                    <th>Sync Direction</th>
                                    <th>Last Sync</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data mappings will be loaded here -->
                                <tr>
                                    <td colspan="7" class="text-center">No data mappings configured yet.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Connection Modal -->
    <div class="modal fade" id="createConnectionModal" tabindex="-1" aria-labelledby="createConnectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createConnectionModalLabel">Create ERP Connection</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger" id="connectionFormError" style="display: none;"></div>
                    <form id="createConnectionForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="connectionName" class="form-label">Connection Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="connectionName" placeholder="e.g., Production SAP System" required>
                            </div>
                            <div class="col-md-6">
                                <label for="systemType" class="form-label">System Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="systemType" required>
                                    <option value="">Select System Type</option>
                                    <option value="SAP">SAP</option>
                                    <option value="Oracle">Oracle EBS</option>
                                    <option value="Microsoft">Microsoft Dynamics</option>
                                    <option value="Infor">Infor</option>
                                    <option value="Custom">Custom API</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="serverUrl" class="form-label">Server URL <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="serverUrl" placeholder="https://erp.example.com/api" required>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" placeholder="API Username">
                            </div>
                            <div class="col-md-6">
                                <label for="password" class="form-label">Password/API Key</label>
                                <input type="password" class="form-control" id="password" placeholder="API Password or Key">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="connectionNotes" class="form-label">Notes</label>
                            <textarea class="form-control" id="connectionNotes" rows="3" placeholder="Additional information about this connection"></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="testConnection">
                            <label class="form-check-label" for="testConnection">Test connection before saving</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveConnectionBtn">Save Connection</button>
                </div>
            </div>
        </div>
    </div>
</div><!-- /.container-fluid -->
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Create Connection button click event
        $('#createConnectionBtn').click(function() {
            $('#createConnectionModal').modal('show');
        });
        
        // Handle form submission
        $('#saveConnectionBtn').click(function() {
            // Get form values
            const connectionName = $('#connectionName').val();
            const systemType = $('#systemType').val();
            const serverUrl = $('#serverUrl').val();
            const username = $('#username').val();
            const password = $('#password').val();
            
            // Validate form
            if (!connectionName || !systemType || !serverUrl) {
                $('#connectionFormError').text('Please fill in all required fields.').show();
                return;
            }
            
            // Hide error message
            $('#connectionFormError').hide();
            
            // Show loading state
            $(this).prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...');
            
            // Simulate API call
            setTimeout(function() {
                // Add new row to table
                const newRow = `
                    <tr>
                        <td>${Math.floor(Math.random() * 1000)}</td>
                        <td>${connectionName}</td>
                        <td>${systemType}</td>
                        <td><span class="badge bg-success">Active</span></td>
                        <td>Never</td>
                        <td>
                            <button class="btn btn-sm btn-primary"><i class="fas fa-sync"></i></button>
                            <button class="btn btn-sm btn-info"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>
                `;
                
                // Remove "No connections" row if it exists
                if ($('#erpConnectionsTable tbody tr td').length === 1 && 
                    $('#erpConnectionsTable tbody tr td').text().includes('No ERP connections')) {
                    $('#erpConnectionsTable tbody').empty();
                }
                
                // Add new row
                $('#erpConnectionsTable tbody').append(newRow);
                
                // Reset form
                $('#createConnectionForm')[0].reset();
                
                // Hide modal
                $('#createConnectionModal').modal('hide');
                
                // Show success message
                const alert = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        Connection "${connectionName}" created successfully!
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                $('.container-fluid').prepend(alert);
                
                // Reset button
                $('#saveConnectionBtn').prop('disabled', false).html('Save Connection');
            }, 1500);
        });
    });
</script>
{% endblock %}
