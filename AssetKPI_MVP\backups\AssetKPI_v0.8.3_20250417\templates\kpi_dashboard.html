{% extends "layout.html" %}

{% block title %}KPI Dashboard{% endblock %}

{% block styles %}
<style>
    .kpi-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
        height: 100%;
    }
    .kpi-card:hover {
        transform: translateY(-5px);
    }
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 1rem;
    }
    .kpi-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .kpi-trend {
        font-size: 0.9rem;
    }
    .kpi-target {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .kpi-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }
    .card-header-tabs {
        margin-right: -1rem;
        margin-left: -1rem;
        margin-bottom: -1rem;
    }
    .chart-actions {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }
    .no-data-msg {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2">KPI Dashboard</h1>
            <p class="text-muted">Comprehensive view of key performance indicators with historical trends</p>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="startDateInput">Start Date:</label>
                                <input type="date" class="form-control" id="startDateInput">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="endDateInput">End Date:</label>
                                <input type="date" class="form-control" id="endDateInput">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="assetFilter">Asset:</label>
                                <select class="form-control" id="assetFilter">
                                    <option value="all">All Assets</option>
                                    <!-- Asset options will be populated dynamically -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" class="btn btn-primary w-100" id="applyFilters">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Summary Cards -->
    <div class="row mb-4">
        <!-- MTTR Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="kpi-card card border-left-danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                MTTR
                            </div>
                            <div class="kpi-value" id="mttrValue">-</div>
                            <div class="kpi-trend" id="mttrTrend"></div>
                            <div class="kpi-target" id="mttrTarget"></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wrench kpi-icon text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- MTBF Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="kpi-card card border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                MTBF
                            </div>
                            <div class="kpi-value" id="mtbfValue">-</div>
                            <div class="kpi-trend" id="mtbfTrend"></div>
                            <div class="kpi-target" id="mtbfTarget"></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock kpi-icon text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Failure Rate Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="kpi-card card border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Failure Rate
                            </div>
                            <div class="kpi-value" id="failureRateValue">-</div>
                            <div class="kpi-trend" id="failureRateTrend"></div>
                            <div class="kpi-target" id="failureRateTarget"></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle kpi-icon text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Availability Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="kpi-card card border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Availability
                            </div>
                            <div class="kpi-value" id="availabilityValue">-</div>
                            <div class="kpi-trend" id="availabilityTrend"></div>
                            <div class="kpi-target" id="availabilityTarget"></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle kpi-icon text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Trend Charts -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="kpiTrendTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link active" id="mttr-tab" data-bs-toggle="tab" href="#mttr-content" role="tab" aria-controls="mttr-content" aria-selected="true">MTTR</a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="mtbf-tab" data-bs-toggle="tab" href="#mtbf-content" role="tab" aria-controls="mtbf-content" aria-selected="false">MTBF</a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="failure-rate-tab" data-bs-toggle="tab" href="#failure-rate-content" role="tab" aria-controls="failure-rate-content" aria-selected="false">Failure Rate</a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="availability-tab" data-bs-toggle="tab" href="#availability-content" role="tab" aria-controls="availability-content" aria-selected="false">Availability</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="kpiTrendTabContent">
                        <!-- MTTR Trend Content -->
                        <div class="tab-pane fade show active" id="mttr-content" role="tabpanel" aria-labelledby="mttr-tab">
                            <div class="chart-container">
                                <div class="chart-actions">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('mttrTrendChart', 'mttr-trend')">
                                            <i class="bi bi-file-earmark-image"></i> PNG
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('mttrTrendChart', 'MTTR Trend', 'mttr-trend')">
                                            <i class="bi bi-file-earmark-pdf"></i> PDF
                                        </button>
                                    </div>
                                </div>
                                <canvas id="mttrTrendChart"></canvas>
                                <p class="no-data-msg">No historical MTTR data available for selected period.</p>
                            </div>
                        </div>

                        <!-- MTBF Trend Content -->
                        <div class="tab-pane fade" id="mtbf-content" role="tabpanel" aria-labelledby="mtbf-tab">
                            <div class="chart-container">
                                <div class="chart-actions">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('mtbfTrendChart', 'mtbf-trend')">
                                            <i class="bi bi-file-earmark-image"></i> PNG
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('mtbfTrendChart', 'MTBF Trend', 'mtbf-trend')">
                                            <i class="bi bi-file-earmark-pdf"></i> PDF
                                        </button>
                                    </div>
                                </div>
                                <canvas id="mtbfTrendChart"></canvas>
                                <p class="no-data-msg">No historical MTBF data available for selected period.</p>
                            </div>
                        </div>

                        <!-- Failure Rate Trend Content -->
                        <div class="tab-pane fade" id="failure-rate-content" role="tabpanel" aria-labelledby="failure-rate-tab">
                            <div class="chart-container">
                                <div class="chart-actions">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('failureRateTrendChart', 'failure-rate-trend')">
                                            <i class="bi bi-file-earmark-image"></i> PNG
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('failureRateTrendChart', 'Failure Rate Trend', 'failure-rate-trend')">
                                            <i class="bi bi-file-earmark-pdf"></i> PDF
                                        </button>
                                    </div>
                                </div>
                                <canvas id="failureRateTrendChart"></canvas>
                                <p class="no-data-msg">No historical Failure Rate data available for selected period.</p>
                            </div>
                        </div>

                        <!-- Availability Trend Content -->
                        <div class="tab-pane fade" id="availability-content" role="tabpanel" aria-labelledby="availability-tab">
                            <div class="chart-container">
                                <div class="chart-actions">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('availabilityTrendChart', 'availability-trend')">
                                            <i class="bi bi-file-earmark-image"></i> PNG
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('availabilityTrendChart', 'Availability Trend', 'availability-trend')">
                                            <i class="bi bi-file-earmark-pdf"></i> PDF
                                        </button>
                                    </div>
                                </div>
                                <canvas id="availabilityTrendChart"></canvas>
                                <p class="no-data-msg">No historical Availability data available for selected period.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Comparison Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">KPI Comparison</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="chart-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('kpiComparisonChart', 'kpi-comparison')">
                                    <i class="bi bi-file-earmark-image"></i> PNG
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('kpiComparisonChart', 'KPI Comparison', 'kpi-comparison')">
                                    <i class="bi bi-file-earmark-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                        <canvas id="kpiComparisonChart"></canvas>
                        <p class="no-data-msg">No KPI comparison data available for selected period.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Target vs. Actual</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="chart-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('targetVsActualChart', 'target-vs-actual')">
                                    <i class="bi bi-file-earmark-image"></i> PNG
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('targetVsActualChart', 'Target vs. Actual', 'target-vs-actual')">
                                    <i class="bi bi-file-earmark-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                        <canvas id="targetVsActualChart"></canvas>
                        <p class="no-data-msg">No target vs. actual data available for selected period.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('KPI Dashboard loaded');

        // Set default dates (last 30 days)
        const today = new Date();
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);

        document.getElementById('startDateInput').value = thirtyDaysAgo.toISOString().split('T')[0];
        document.getElementById('endDateInput').value = today.toISOString().split('T')[0];

        // Initialize the KPI Dashboard
        KPIDashboard.init();
    });
</script>
{% endblock %}
