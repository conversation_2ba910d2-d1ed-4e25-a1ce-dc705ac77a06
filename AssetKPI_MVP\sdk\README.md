# AssetKPI SDK

This directory contains the AssetKPI SDK, which provides a convenient way to interact with the AssetKPI API.

## Python SDK

The Python SDK is located in the `assetkpi_sdk` directory. It provides a comprehensive set of client classes for all API endpoints, with advanced error handling and retry logic.

### Features

- **Comprehensive API Coverage**: Client classes for all API endpoints
- **Advanced Error Handling**: Detailed error classes and retry logic
- **Authentication Support**: Both Firebase token and API key authentication
- **Bulk Operations**: Support for efficient bulk operations
- **Filtering and Pagination**: Advanced filtering and pagination capabilities
- **Command-Line Interface**: CLI for common operations
- **Examples**: Basic and advanced usage examples

### Installation

To install the SDK from the local directory:

```bash
cd sdk/assetkpi_sdk
pip install -e .
```

### Basic Usage

```python
from assetkpi import AssetKPISDK

# Initialize the SDK with API key
sdk = AssetKPISDK(
    base_url="http://localhost:8000/api",
    api_key="your-api-key"
)

# Get inventory summary
inventory_summary = sdk.inventory.get_inventory_summary()
print(f"Total Parts: {inventory_summary['total_parts']}")
print(f"Total Value: ${inventory_summary['total_value']:.2f}")
print(f"Parts Below Reorder Level: {inventory_summary['below_reorder']}")

# Get latest KPIs
kpis = sdk.kpi.get_latest_kpis()
print(f"MTTR: {kpis['mttr']} hours")
print(f"MTBF: {kpis['mtbf']} hours")
print(f"Failure Rate: {kpis['failure_rate']} failures/year")
```

### Examples

The SDK includes several examples in the `assetkpi_sdk/examples` directory:

- `basic_usage.py`: Basic usage examples
- `advanced_usage.py`: Advanced usage examples
- `bulk_operations.py`: Bulk operations examples

### Command-Line Interface

The SDK includes a command-line interface for common operations:

```bash
# Get inventory summary
python -m assetkpi inventory summary

# Get spare parts
python -m assetkpi inventory parts --limit 5

# Get latest KPIs
python -m assetkpi kpi latest

# Get KPI history
python -m assetkpi kpi history MTTR_Calculated --start-date 2023-01-01 --end-date 2023-12-31
```

### Documentation

For detailed documentation, see the `assetkpi_sdk/README.md` file.

## Future SDKs

The following SDKs are planned for future releases:

- **JavaScript SDK**: For browser and Node.js applications
- **C# SDK**: For .NET applications
- **Java SDK**: For Java applications

## Contributing

Contributions to the SDK are welcome! Please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This SDK is licensed under the MIT License.
