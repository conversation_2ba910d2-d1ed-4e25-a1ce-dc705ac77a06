/**
 * Combined Authentication Utility for AssetKPI
 * 
 * This module provides a unified interface for both Firebase and test user authentication.
 * It automatically detects which authentication method to use based on the token format.
 */

// Create the CombinedAuth object
const CombinedAuth = {
    // Authentication mode
    mode: null, // 'firebase' or 'test'

    /**
     * Initialize the authentication system
     * @returns {Promise<void>}
     */
    init: async function() {
        console.log('Initializing CombinedAuth...');

        // Check if we have a stored token
        const firebaseToken = localStorage.getItem('firebaseIdToken');
        const testToken = localStorage.getItem('testUserToken');

        if (testToken) {
            console.log('Found test token in localStorage');
            this.mode = 'test';
            // Set the cookie for server-side authentication
            document.cookie = `testUserToken=${testToken}; path=/; max-age=3600; SameSite=Strict`;
        } else if (firebaseToken) {
            console.log('Found Firebase token in localStorage');
            this.mode = 'firebase';
            // Set the cookie for server-side authentication
            document.cookie = `firebaseIdToken=${firebaseToken}; path=/; max-age=3600; SameSite=Strict`;
        } else {
            console.log('No authentication tokens found');
            this.mode = null;
        }

        console.log(`Authentication mode: ${this.mode || 'none'}`);
    },

    /**
     * Sign in with email and password (Firebase only)
     * @param {string} email - User email
     * @param {string} password - User password
     * @returns {Promise<Object>} - Authentication result
     */
    signInWithEmailAndPassword: async function(email, password) {
        try {
            // Check if Firebase is available
            if (!window.AssetKPIAuth) {
                throw new Error('Firebase authentication not available');
            }

            // Sign in with Firebase
            const result = await window.AssetKPIAuth.signIn(email, password);
            if (result.success) {
                this.mode = 'firebase';
            }
            return result;
        } catch (error) {
            console.error('Sign in error:', error);
            return {
                success: false,
                error: error
            };
        }
    },

    /**
     * Sign in with a test token
     * @param {string} token - The test token
     * @returns {Promise<Object>} - Authentication result
     */
    signInWithTestToken: async function(token) {
        try {
            // Check if TestAuth is available
            if (!window.TestAuth) {
                throw new Error('Test authentication not available');
            }

            // Sign in with test token
            const result = await window.TestAuth.authenticateWithTestToken(token);
            if (result.success) {
                this.mode = 'test';
            }
            return result;
        } catch (error) {
            console.error('Test sign in error:', error);
            return {
                success: false,
                error: error
            };
        }
    },

    /**
     * Sign out the current user
     * @returns {Promise<Object>} - Sign out result
     */
    signOut: async function() {
        try {
            if (this.mode === 'firebase' && window.AssetKPIAuth) {
                // Sign out from Firebase
                await window.AssetKPIAuth.signOut();
            } else if (this.mode === 'test' && window.TestAuth) {
                // Sign out test user
                await window.TestAuth.signOut();
            }

            // Clear mode
            this.mode = null;

            return {
                success: true
            };
        } catch (error) {
            console.error('Sign out error:', error);
            return {
                success: false,
                error: error
            };
        }
    },

    /**
     * Get the current authentication token
     * @param {boolean} forceRefresh - Whether to force a token refresh (Firebase only)
     * @returns {Promise<string|null>} - The current token, or null if not authenticated
     */
    getToken: async function(forceRefresh = false) {
        try {
            console.log('CombinedAuth.getToken called, mode:', this.mode, 'forceRefresh:', forceRefresh);

            if (this.mode === 'firebase' && window.AssetKPIAuth) {
                console.log('Getting Firebase token...');
                const token = await window.AssetKPIAuth.getIdToken(forceRefresh);
                console.log('Firebase token obtained:', token ? 'Yes' : 'No');
                if (token) {
                    console.log('Token preview:', token.substring(0, 20) + '...');
                }
                return token;
            } else if (this.mode === 'test' && window.TestAuth) {
                console.log('Getting test token...');
                const token = window.TestAuth.getCurrentToken();
                console.log('Test token obtained:', token ? 'Yes' : 'No');
                return token;
            }

            console.log('No valid authentication mode or auth object available');
            console.log('Available objects:', {
                AssetKPIAuth: !!window.AssetKPIAuth,
                TestAuth: !!window.TestAuth,
                mode: this.mode
            });
            return null;
        } catch (error) {
            console.error('Error getting token:', error);
            return null;
        }
    },

    /**
     * Check if the user is authenticated
     * @returns {boolean} - Whether the user is authenticated
     */
    isAuthenticated: function() {
        if (this.mode === 'firebase' && window.AssetKPIAuth) {
            return window.AssetKPIAuth.isAuthenticated();
        } else if (this.mode === 'test' && window.TestAuth) {
            return window.TestAuth.isAuthenticated();
        }
        return false;
    },

    /**
     * Get the current user
     * @returns {Object|null} - The current user, or null if not authenticated
     */
    getCurrentUser: function() {
        if (this.mode === 'firebase' && window.AssetKPIAuth) {
            return window.AssetKPIAuth.getCurrentUser();
        }
        // For test users, we don't have a user object
        return null;
    },

    /**
     * Make an authenticated API request
     * @param {string} url - The API endpoint URL
     * @param {Object} options - Fetch options
     * @returns {Promise<Response>} - Fetch response
     */
    authenticatedFetch: async function(url, options = {}) {
        try {
            console.log('CombinedAuth.authenticatedFetch called for URL:', url);

            // Get the current token
            const token = await this.getToken();
            if (!token) {
                console.error('No token available for authenticated fetch');
                throw new Error('Not authenticated');
            }

            console.log('Making authenticated request with token:', token.substring(0, 20) + '...');

            // Add the Authorization header
            const headers = {
                ...options.headers,
                'Authorization': `Bearer ${token}`
            };

            // Make the request
            const response = await fetch(url, {
                ...options,
                headers
            });

            console.log('Authenticated fetch response status:', response.status);
            return response;
        } catch (error) {
            console.error('Authenticated fetch error:', error);
            throw error;
        }
    }
};

// Initialize the CombinedAuth object
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await CombinedAuth.init();
        console.log('CombinedAuth initialized');
    } catch (error) {
        console.error('Error initializing CombinedAuth:', error);
    }
});

// Export the CombinedAuth object
window.CombinedAuth = CombinedAuth;
console.log('CombinedAuth object created and attached to window object');
