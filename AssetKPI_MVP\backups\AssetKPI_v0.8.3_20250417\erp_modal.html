<!-- Create Connection Modal -->
<div class="modal fade" id="createConnectionModal" tabindex="-1" aria-labelledby="createConnectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createConnectionModalLabel">Create ERP Connection</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger" id="connectionFormError" style="display: none;"></div>
                <form id="createConnectionForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="connectionName" class="form-label">Connection Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="connectionName" placeholder="e.g., Production SAP System" required>
                        </div>
                        <div class="col-md-6">
                            <label for="systemType" class="form-label">System Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="systemType" required>
                                <option value="">Select System Type</option>
                                <option value="SAP">SAP</option>
                                <option value="Oracle">Oracle EBS</option>
                                <option value="Microsoft">Microsoft Dynamics</option>
                                <option value="Infor">Infor</option>
                                <option value="Custom">Custom API</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="serverUrl" class="form-label">Server URL <span class="text-danger">*</span></label>
                        <input type="url" class="form-control" id="serverUrl" placeholder="https://erp.example.com/api" required>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" placeholder="API Username">
                        </div>
                        <div class="col-md-6">
                            <label for="password" class="form-label">Password/API Key</label>
                            <input type="password" class="form-control" id="password" placeholder="API Password or Key">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="connectionNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="connectionNotes" rows="3" placeholder="Additional information about this connection"></textarea>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="testConnection">
                        <label class="form-check-label" for="testConnection">Test connection before saving</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveConnectionBtn">Save Connection</button>
            </div>
        </div>
    </div>
</div>
