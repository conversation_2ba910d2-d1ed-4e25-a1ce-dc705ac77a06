import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Import the User model and UserRole enum
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from main import User, UserRole

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def create_user_in_db(user_id, email, role, full_name=None):
    """Create a user in the PostgreSQL database"""
    try:
        # Check if user already exists in database
        existing_user = db.query(User).filter(User.user_id == user_id).first()
        if existing_user:
            print(f"User with ID {user_id} already exists in database.")
            return existing_user
        
        # Create new user in database
        user = User(
            user_id=user_id,
            email=email,
            role=role,
            full_name=full_name,
            created_at=datetime.now(),
            last_login=None
        )
        
        # Add to database
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print(f"Created user in database: {user.email} with role {user.role}")
        return user
    
    except Exception as e:
        db.rollback()
        print(f"Error creating user in database: {e}")
        return None

def main():
    # Create test users with different roles
    
    # Admin user
    create_user_in_db(
        user_id="firebase-test-admin-uid",
        email="<EMAIL>",
        role=UserRole.ADMIN,
        full_name="Johan Borgulf (Admin)"
    )
    
    # Manager user
    create_user_in_db(
        user_id="firebase-test-manager-uid",
        email="<EMAIL>",
        role=UserRole.MANAGER,
        full_name="Test Manager"
    )
    
    # Engineer user
    create_user_in_db(
        user_id="firebase-test-engineer-uid",
        email="<EMAIL>",
        role=UserRole.ENGINEER,
        full_name="Test Engineer"
    )
    
    # Viewer user
    create_user_in_db(
        user_id="firebase-test-viewer-uid",
        email="<EMAIL>",
        role=UserRole.VIEWER,
        full_name="Test Viewer"
    )
    
    # List all users in the database
    print("\nUsers in database:")
    users = db.query(User).all()
    for user in users:
        print(f"- {user.user_id}: {user.email} ({user.role})")

if __name__ == "__main__":
    main()
