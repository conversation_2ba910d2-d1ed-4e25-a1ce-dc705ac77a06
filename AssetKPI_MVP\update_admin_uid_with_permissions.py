import psycopg2

# Connect to the database
conn = psycopg2.connect("postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
cur = conn.cursor()

try:
    # Start a transaction
    conn.autocommit = False
    
    # First update the user_permissions table
    cur.execute("UPDATE user_permissions SET user_id = 'firebase-test-admin-uid' WHERE user_id = 'test-admin-uid'")
    permissions_updated = cur.rowcount
    print(f"Updated {permissions_updated} rows in user_permissions table")
    
    # Then update the users table
    cur.execute("UPDATE users SET user_id = 'firebase-test-admin-uid' WHERE user_id = 'test-admin-uid'")
    users_updated = cur.rowcount
    print(f"Updated {users_updated} rows in users table")
    
    # Commit the transaction
    conn.commit()
    print("Transaction committed successfully")
    
except Exception as e:
    # Roll back the transaction in case of error
    conn.rollback()
    print(f"Error: {e}")
    print("Transaction rolled back")
finally:
    # Close the connection
    cur.close()
    conn.close()
    print("Connection closed")
