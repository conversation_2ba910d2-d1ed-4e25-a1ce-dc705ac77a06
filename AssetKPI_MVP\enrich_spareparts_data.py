import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def enrich_spareparts_data():
    """Add more comprehensive spare parts data to the database."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # First, get existing data to avoid duplicates
        cursor.execute("SELECT partid, partname, partnumber FROM spareparts")
        existing_parts = {row[2]: row[0] for row in cursor.fetchall()}
        
        # Sample spare parts data
        new_parts = [
            # Mechanical Parts
            ('Bearing - Ball', 'BRG-001', 'SKF', 50, 10, 20, 15, 30, 45.99),
            ('Bearing - Roller', 'BRG-002', 'Timken', 30, 5, 15, 10, 45, 65.75),
            ('Bearing - Thrust', 'BRG-003', 'NSK', 20, 2, 10, 5, 60, 89.99),
            ('Belt - V-Belt', 'BLT-001', 'Gates', 25, 5, 15, 10, 14, 22.50),
            ('Belt - Timing', 'BLT-002', 'Continental', 15, 3, 10, 5, 21, 45.99),
            ('Belt - Flat', 'BLT-003', 'Goodyear', 10, 2, 5, 3, 30, 35.25),
            ('Coupling - Flexible', 'CPL-001', 'Lovejoy', 10, 1, 5, 3, 45, 125.50),
            ('Coupling - Rigid', 'CPL-002', 'Rexnord', 5, 1, 3, 2, 60, 95.75),
            ('Gear - Spur', 'GER-001', 'Boston Gear', 8, 1, 4, 2, 90, 175.99),
            ('Gear - Bevel', 'GER-002', 'Martin', 6, 1, 3, 2, 120, 225.50),
            ('Gear - Worm', 'GER-003', 'Browning', 4, 1, 2, 1, 150, 275.25),
            ('Chain - Roller', 'CHN-001', 'Diamond', 15, 2, 8, 5, 30, 45.99),
            ('Chain - Silent', 'CHN-002', 'Tsubaki', 10, 1, 5, 3, 45, 65.75),
            ('Sprocket', 'SPR-001', 'Martin', 12, 2, 6, 4, 60, 55.50),
            ('Pulley - V-Belt', 'PLY-001', 'TB Woods', 15, 2, 8, 5, 45, 35.99),
            ('Pulley - Timing', 'PLY-002', 'Gates', 10, 1, 5, 3, 60, 45.75),
            ('Shaft - Steel', 'SFT-001', 'Thomson', 8, 1, 4, 2, 90, 125.50),
            ('Shaft Collar', 'SFT-002', 'Ruland', 20, 5, 10, 8, 30, 15.99),
            ('Key - Square', 'KEY-001', 'Generic', 50, 10, 25, 20, 14, 5.50),
            ('Bushing', 'BSH-001', 'Dodge', 25, 5, 15, 10, 30, 35.75),
            
            # Electrical Parts
            ('Motor - 1HP', 'MTR-001', 'Baldor', 5, 1, 3, 2, 60, 450.99),
            ('Motor - 5HP', 'MTR-002', 'WEG', 3, 1, 2, 1, 90, 750.50),
            ('Motor - 10HP', 'MTR-003', 'Siemens', 2, 1, 1, 1, 120, 1250.75),
            ('Contactor - 25A', 'CNT-001', 'Allen-Bradley', 10, 2, 5, 3, 30, 85.99),
            ('Contactor - 50A', 'CNT-002', 'Schneider', 8, 1, 4, 2, 45, 125.50),
            ('Contactor - 100A', 'CNT-003', 'Eaton', 5, 1, 3, 2, 60, 195.75),
            ('Relay - Control', 'RLY-001', 'Omron', 20, 5, 10, 8, 14, 35.99),
            ('Relay - Power', 'RLY-002', 'Finder', 15, 3, 8, 5, 21, 55.50),
            ('Relay - Time Delay', 'RLY-003', 'ABB', 10, 2, 5, 3, 30, 75.75),
            ('Circuit Breaker - 10A', 'CBR-001', 'Square D', 15, 3, 8, 5, 30, 45.99),
            ('Circuit Breaker - 20A', 'CBR-002', 'Siemens', 12, 2, 6, 4, 45, 55.50),
            ('Circuit Breaker - 50A', 'CBR-003', 'Eaton', 8, 1, 4, 2, 60, 85.75),
            ('Fuse - 10A', 'FSE-001', 'Bussmann', 50, 10, 25, 20, 7, 5.99),
            ('Fuse - 20A', 'FSE-002', 'Littelfuse', 40, 8, 20, 15, 14, 7.50),
            ('Fuse - 50A', 'FSE-003', 'Mersen', 30, 5, 15, 10, 21, 12.75),
            ('Switch - Limit', 'SWT-001', 'Allen-Bradley', 15, 3, 8, 5, 30, 65.99),
            ('Switch - Pressure', 'SWT-002', 'Danfoss', 10, 2, 5, 3, 45, 85.50),
            ('Switch - Flow', 'SWT-003', 'Honeywell', 8, 1, 4, 2, 60, 95.75),
            ('Sensor - Proximity', 'SNS-001', 'IFM', 12, 2, 6, 4, 30, 75.99),
            ('Sensor - Temperature', 'SNS-002', 'Omega', 10, 2, 5, 3, 45, 95.50),
            
            # Hydraulic Parts
            ('Hydraulic Pump', 'HYD-001', 'Parker', 3, 1, 2, 1, 90, 850.99),
            ('Hydraulic Motor', 'HYD-002', 'Eaton', 2, 1, 1, 1, 120, 750.50),
            ('Hydraulic Cylinder', 'HYD-003', 'Bosch Rexroth', 5, 1, 3, 2, 60, 450.75),
            ('Hydraulic Valve - Directional', 'HYD-004', 'Parker', 8, 1, 4, 2, 45, 250.99),
            ('Hydraulic Valve - Pressure', 'HYD-005', 'Eaton', 6, 1, 3, 2, 60, 175.50),
            ('Hydraulic Valve - Flow', 'HYD-006', 'Bosch Rexroth', 7, 1, 4, 2, 45, 195.75),
            ('Hydraulic Filter', 'HYD-007', 'Parker', 10, 2, 5, 3, 30, 65.99),
            ('Hydraulic Hose - 1/4"', 'HYD-008', 'Gates', 20, 5, 10, 8, 14, 35.50),
            ('Hydraulic Hose - 1/2"', 'HYD-009', 'Gates', 15, 3, 8, 5, 21, 45.75),
            ('Hydraulic Fitting - 1/4"', 'HYD-010', 'Parker', 50, 10, 25, 20, 7, 12.99),
            
            # Pneumatic Parts
            ('Air Compressor Filter', 'PNE-001', 'Ingersoll Rand', 10, 2, 5, 3, 30, 45.99),
            ('Air Regulator', 'PNE-002', 'SMC', 8, 1, 4, 2, 45, 65.50),
            ('Air Lubricator', 'PNE-003', 'Festo', 8, 1, 4, 2, 45, 55.75),
            ('Pneumatic Cylinder', 'PNE-004', 'SMC', 12, 2, 6, 4, 30, 125.99),
            ('Pneumatic Valve - Directional', 'PNE-005', 'Festo', 10, 2, 5, 3, 45, 95.50),
            ('Pneumatic Valve - Pressure', 'PNE-006', 'SMC', 8, 1, 4, 2, 60, 75.75),
            ('Air Hose - 1/4"', 'PNE-007', 'Parker', 25, 5, 15, 10, 14, 25.99),
            ('Air Fitting - 1/4"', 'PNE-008', 'SMC', 50, 10, 25, 20, 7, 8.50),
            ('Air Filter Element', 'PNE-009', 'Ingersoll Rand', 15, 3, 8, 5, 30, 35.75),
            ('Air Dryer Element', 'PNE-010', 'Atlas Copco', 5, 1, 3, 2, 180, 125.99),
            
            # HVAC Parts
            ('HVAC Filter - 20x20', 'HVC-001', 'Honeywell', 20, 5, 10, 8, 30, 15.99),
            ('HVAC Filter - 24x24', 'HVC-002', 'Honeywell', 15, 3, 8, 5, 30, 18.50),
            ('HVAC Belt', 'HVC-003', 'Gates', 10, 2, 5, 3, 45, 25.75),
            ('HVAC Motor', 'HVC-004', 'Baldor', 5, 1, 3, 2, 60, 350.99),
            ('HVAC Contactor', 'HVC-005', 'Schneider', 8, 1, 4, 2, 45, 85.50),
            ('HVAC Capacitor', 'HVC-006', 'Packard', 12, 2, 6, 4, 30, 45.75),
            ('Refrigerant R-410A', 'HVC-007', 'Dupont', 5, 1, 3, 2, 60, 125.99),
            ('Refrigerant R-134a', 'HVC-008', 'Dupont', 5, 1, 3, 2, 60, 95.50),
            ('Refrigerant Oil', 'HVC-009', 'Nu-Calgon', 8, 1, 4, 2, 45, 35.75),
            ('Thermostat', 'HVC-010', 'Honeywell', 10, 2, 5, 3, 30, 75.99),
            
            # Plumbing Parts
            ('Pipe - 1" Steel', 'PLM-001', 'Generic', 15, 3, 8, 5, 30, 25.99),
            ('Pipe - 2" Steel', 'PLM-002', 'Generic', 10, 2, 5, 3, 45, 45.50),
            ('Pipe Fitting - 1" Elbow', 'PLM-003', 'Generic', 30, 5, 15, 10, 14, 8.75),
            ('Pipe Fitting - 2" Elbow', 'PLM-004', 'Generic', 25, 5, 15, 10, 14, 12.99),
            ('Valve - Ball 1"', 'PLM-005', 'Watts', 15, 3, 8, 5, 30, 35.50),
            ('Valve - Ball 2"', 'PLM-006', 'Watts', 10, 2, 5, 3, 45, 55.75),
            ('Valve - Check 1"', 'PLM-007', 'Watts', 12, 2, 6, 4, 30, 45.99),
            ('Valve - Check 2"', 'PLM-008', 'Watts', 8, 1, 4, 2, 45, 65.50),
            ('Gasket - 1"', 'PLM-009', 'Garlock', 50, 10, 25, 20, 14, 5.75),
            ('Gasket - 2"', 'PLM-010', 'Garlock', 40, 8, 20, 15, 14, 8.99),
            
            # Fasteners
            ('Bolt - 1/4" x 1"', 'FST-001', 'Generic', 100, 20, 50, 40, 7, 0.50),
            ('Bolt - 3/8" x 1"', 'FST-002', 'Generic', 100, 20, 50, 40, 7, 0.75),
            ('Bolt - 1/2" x 1"', 'FST-003', 'Generic', 80, 15, 40, 30, 7, 1.25),
            ('Nut - 1/4"', 'FST-004', 'Generic', 100, 20, 50, 40, 7, 0.25),
            ('Nut - 3/8"', 'FST-005', 'Generic', 100, 20, 50, 40, 7, 0.35),
            ('Nut - 1/2"', 'FST-006', 'Generic', 80, 15, 40, 30, 7, 0.50),
            ('Washer - 1/4"', 'FST-007', 'Generic', 100, 20, 50, 40, 7, 0.15),
            ('Washer - 3/8"', 'FST-008', 'Generic', 100, 20, 50, 40, 7, 0.20),
            ('Washer - 1/2"', 'FST-009', 'Generic', 80, 15, 40, 30, 7, 0.30),
            ('Screw - #8 x 1"', 'FST-010', 'Generic', 100, 20, 50, 40, 7, 0.25)
        ]
        
        print(f"Adding {len(new_parts)} new spare parts...")
        
        # Insert new parts
        parts_added = 0
        for part in new_parts:
            part_name, part_number, manufacturer, stock_qty, monthly_demand, reorder_level, safety_stock, lead_time, unit_price = part
            
            # Skip if part already exists
            if part_number in existing_parts:
                print(f"  - Skipping existing part: {part_number} - {part_name}")
                continue
            
            # Calculate last restocked date (random date in the past year)
            days_ago = random.randint(0, 365)
            last_restocked = datetime.now().date() - timedelta(days=days_ago)
            
            # Calculate ABC classification based on unit price and monthly demand
            value = unit_price * monthly_demand
            if value > 100:
                abc_class = 'A'
            elif value > 50:
                abc_class = 'B'
            else:
                abc_class = 'C'
            
            # Calculate EOQ
            ordering_cost = 25.0  # Assume $25 ordering cost
            holding_cost_percent = 0.25  # Assume 25% annual holding cost
            holding_cost = unit_price * holding_cost_percent
            
            if monthly_demand > 0 and holding_cost > 0:
                eoq = round(((2 * ordering_cost * monthly_demand * 12) / holding_cost) ** 0.5, 2)
            else:
                eoq = 0
            
            # Calculate safety stock
            service_level_z = 1.65  # 95% service level
            demand_variability = monthly_demand * 0.2  # Assume 20% variability
            lead_time_variability = lead_time * 0.1  # Assume 10% variability
            
            calculated_safety_stock = round(service_level_z * ((monthly_demand ** 2 * lead_time_variability) + 
                                                             (lead_time * demand_variability ** 2)) ** 0.5, 2)
            
            # Calculate stock out risk
            if stock_qty > 0 and reorder_level > 0:
                stock_out_risk = round(max(0, min(100, 100 * (1 - (stock_qty / reorder_level)))), 2)
            else:
                stock_out_risk = 0
            
            # Insert the new part
            cursor.execute(
                """
                INSERT INTO spareparts 
                (partname, partnumber, manufacturer, stockquantity, monthlydemand, reorderlevel, 
                 safetystock, leadtimedays, unitprice, stockoutrisk, lastrestocked, 
                 avg_monthly_consumption, abc_classification, inventory_metrics_last_updated, calculated_safety_stock, eoq)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING partid
                """,
                (part_name, part_number, manufacturer, stock_qty, monthly_demand, reorder_level, 
                 safety_stock, lead_time, unit_price, stock_out_risk, last_restocked, 
                 monthly_demand, abc_class, datetime.now(), calculated_safety_stock, eoq)
            )
            
            part_id = cursor.fetchone()[0]
            parts_added += 1
            
            print(f"  - Added part: {part_number} - {part_name} (ID: {part_id})")
        
        # Commit the changes
        conn.commit()
        print(f"Successfully added {parts_added} new spare parts!")
        
        # Update existing parts with calculated values
        print("\nUpdating existing parts with calculated values...")
        
        cursor.execute("SELECT partid, partname, unitprice, monthlydemand, leadtimedays, stockquantity, reorderlevel FROM spareparts WHERE eoq IS NULL OR calculated_safety_stock IS NULL")
        existing_parts = cursor.fetchall()
        
        parts_updated = 0
        for part_id, part_name, unit_price, monthly_demand, lead_time, stock_qty, reorder_level in existing_parts:
            # Calculate ABC classification based on unit price and monthly demand
            if monthly_demand is None:
                monthly_demand = random.randint(1, 10)
            
            if unit_price is None:
                unit_price = random.uniform(10, 100)
            
            if lead_time is None:
                lead_time = random.randint(7, 60)
            
            value = unit_price * monthly_demand
            if value > 100:
                abc_class = 'A'
            elif value > 50:
                abc_class = 'B'
            else:
                abc_class = 'C'
            
            # Calculate EOQ
            ordering_cost = 25.0  # Assume $25 ordering cost
            holding_cost_percent = 0.25  # Assume 25% annual holding cost
            holding_cost = unit_price * holding_cost_percent
            
            if monthly_demand > 0 and holding_cost > 0:
                eoq = round(((2 * ordering_cost * monthly_demand * 12) / holding_cost) ** 0.5, 2)
            else:
                eoq = 0
            
            # Calculate safety stock
            service_level_z = 1.65  # 95% service level
            demand_variability = monthly_demand * 0.2  # Assume 20% variability
            lead_time_variability = lead_time * 0.1  # Assume 10% variability
            
            calculated_safety_stock = round(service_level_z * ((monthly_demand ** 2 * lead_time_variability) + 
                                                             (lead_time * demand_variability ** 2)) ** 0.5, 2)
            
            # Calculate stock out risk
            if stock_qty is not None and reorder_level is not None and stock_qty > 0 and reorder_level > 0:
                stock_out_risk = round(max(0, min(100, 100 * (1 - (stock_qty / reorder_level)))), 2)
            else:
                stock_out_risk = 0
            
            # Update the part
            cursor.execute(
                """
                UPDATE spareparts 
                SET monthlydemand = %s, 
                    avg_monthly_consumption = %s,
                    abc_classification = %s, 
                    eoq = %s, 
                    calculated_safety_stock = %s, 
                    stockoutrisk = %s,
                    inventory_metrics_last_updated = %s
                WHERE partid = %s
                """,
                (monthly_demand, monthly_demand, abc_class, eoq, calculated_safety_stock, stock_out_risk, datetime.now(), part_id)
            )
            
            parts_updated += 1
            
            if parts_updated % 10 == 0:
                print(f"  - Updated {parts_updated} parts so far...")
        
        # Commit the changes
        conn.commit()
        print(f"Successfully updated {parts_updated} existing parts!")
        
    except Exception as e:
        print(f"Error enriching spare parts data: {e}")
        import traceback
        traceback.print_exc()
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    enrich_spareparts_data()
