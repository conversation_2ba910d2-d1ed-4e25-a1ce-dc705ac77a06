"""
ERP integration service.

This module provides services for integrating with ERP systems.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from sqlalchemy.orm import Session

from models.erp_integration import ERPConnection, ERPDataMapping, ERPSyncLog
from services.erp.connector_factory import ERPConnectorFactory
from services.erp.data_mapper import DataMapper

# Create a logger for this module
logger = logging.getLogger(__name__)


class ERPIntegrationService:
    """
    Service for integrating with ERP systems.
    
    This class provides methods for managing ERP connections and synchronizing data.
    """
    
    @staticmethod
    def get_connections(db: Session) -> List[ERPConnection]:
        """
        Get all ERP connections.
        
        Args:
            db: Database session
            
        Returns:
            List of ERP connections
        """
        return db.query(ERPConnection).all()
    
    @staticmethod
    def get_connection(db: Session, connection_id: int) -> Optional[ERPConnection]:
        """
        Get an ERP connection by ID.
        
        Args:
            db: Database session
            connection_id: ID of the connection
            
        Returns:
            ERP connection if found, None otherwise
        """
        return db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
    
    @staticmethod
    def create_connection(db: Session, connection_data: Dict[str, Any], user_id: str) -> ERPConnection:
        """
        Create a new ERP connection.
        
        Args:
            db: Database session
            connection_data: Connection data
            user_id: ID of the user creating the connection
            
        Returns:
            Created ERP connection
        """
        connection = ERPConnection(
            name=connection_data["name"],
            description=connection_data.get("description"),
            system_type=connection_data["system_type"],
            version=connection_data.get("version"),
            connection_url=connection_data["connection_url"],
            connection_params=connection_data.get("connection_params"),
            auth_type=connection_data["auth_type"],
            auth_credentials=connection_data.get("auth_credentials"),
            status="inactive",
            created_by=user_id
        )
        
        db.add(connection)
        db.commit()
        db.refresh(connection)
        
        return connection
    
    @staticmethod
    def update_connection(db: Session, connection_id: int, connection_data: Dict[str, Any]) -> Optional[ERPConnection]:
        """
        Update an ERP connection.
        
        Args:
            db: Database session
            connection_id: ID of the connection
            connection_data: Updated connection data
            
        Returns:
            Updated ERP connection if found, None otherwise
        """
        connection = db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
        
        if not connection:
            return None
        
        # Update fields if provided
        if "name" in connection_data:
            connection.name = connection_data["name"]
        
        if "description" in connection_data:
            connection.description = connection_data["description"]
        
        if "system_type" in connection_data:
            connection.system_type = connection_data["system_type"]
        
        if "version" in connection_data:
            connection.version = connection_data["version"]
        
        if "connection_url" in connection_data:
            connection.connection_url = connection_data["connection_url"]
        
        if "connection_params" in connection_data:
            connection.connection_params = connection_data["connection_params"]
        
        if "auth_type" in connection_data:
            connection.auth_type = connection_data["auth_type"]
        
        if "auth_credentials" in connection_data:
            connection.auth_credentials = connection_data["auth_credentials"]
        
        if "status" in connection_data:
            connection.status = connection_data["status"]
        
        connection.updated_at = datetime.now()
        
        db.commit()
        db.refresh(connection)
        
        return connection
    
    @staticmethod
    def delete_connection(db: Session, connection_id: int) -> bool:
        """
        Delete an ERP connection.
        
        Args:
            db: Database session
            connection_id: ID of the connection
            
        Returns:
            True if deletion is successful, False otherwise
        """
        connection = db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
        
        if not connection:
            return False
        
        # Delete associated data mappings
        db.query(ERPDataMapping).filter(ERPDataMapping.connection_id == connection_id).delete()
        
        # Delete the connection
        db.delete(connection)
        db.commit()
        
        return True
    
    @staticmethod
    def test_connection(db: Session, connection_id: Optional[int] = None, connection_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Test an ERP connection.
        
        Args:
            db: Database session
            connection_id: Optional ID of an existing connection
            connection_data: Optional data for a new connection
            
        Returns:
            Test results
        """
        try:
            # Get connection configuration
            if connection_id:
                connection = db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
                
                if not connection:
                    return {
                        "success": False,
                        "message": f"Connection with ID {connection_id} not found"
                    }
                
                system_type = connection.system_type
                connection_config = {
                    "connection_url": connection.connection_url,
                    "auth_type": connection.auth_type,
                    "auth_credentials": connection.auth_credentials,
                }
                
                if connection.connection_params:
                    connection_config.update(connection.connection_params)
            
            elif connection_data:
                system_type = connection_data["system_type"]
                connection_config = {
                    "connection_url": connection_data["connection_url"],
                    "auth_type": connection_data["auth_type"],
                    "auth_credentials": connection_data.get("auth_credentials"),
                }
                
                if connection_data.get("connection_params"):
                    connection_config.update(connection_data["connection_params"])
            
            else:
                return {
                    "success": False,
                    "message": "Either connection_id or connection_data must be provided"
                }
            
            # Create connector
            connector = ERPConnectorFactory.create_connector(system_type, connection_config)
            
            if not connector:
                return {
                    "success": False,
                    "message": f"Unsupported ERP system type: {system_type}"
                }
            
            # Test connection
            test_result = connector.test_connection()
            
            # Update connection status if using an existing connection
            if connection_id and test_result["success"]:
                connection.status = "active"
                db.commit()
            
            return test_result
        
        except Exception as e:
            logger.error(f"Error testing ERP connection: {str(e)}")
            
            return {
                "success": False,
                "message": f"Error testing ERP connection: {str(e)}"
            }
    
    @staticmethod
    def get_data_mappings(db: Session, connection_id: Optional[int] = None) -> List[ERPDataMapping]:
        """
        Get data mappings.
        
        Args:
            db: Database session
            connection_id: Optional ID of the connection to filter by
            
        Returns:
            List of data mappings
        """
        query = db.query(ERPDataMapping)
        
        if connection_id:
            query = query.filter(ERPDataMapping.connection_id == connection_id)
        
        return query.all()
    
    @staticmethod
    def get_data_mapping(db: Session, mapping_id: int) -> Optional[ERPDataMapping]:
        """
        Get a data mapping by ID.
        
        Args:
            db: Database session
            mapping_id: ID of the data mapping
            
        Returns:
            Data mapping if found, None otherwise
        """
        return db.query(ERPDataMapping).filter(ERPDataMapping.id == mapping_id).first()
    
    @staticmethod
    def create_data_mapping(db: Session, mapping_data: Dict[str, Any]) -> ERPDataMapping:
        """
        Create a new data mapping.
        
        Args:
            db: Database session
            mapping_data: Data mapping data
            
        Returns:
            Created data mapping
        """
        # Convert field mappings to JSON
        field_mappings = mapping_data.get("field_mappings", [])
        
        # Convert transformation rules to JSON
        transformation_rules = mapping_data.get("transformation_rules", [])
        
        mapping = ERPDataMapping(
            connection_id=mapping_data["connection_id"],
            name=mapping_data["name"],
            description=mapping_data.get("description"),
            assetkpi_entity=mapping_data["assetkpi_entity"],
            erp_entity=mapping_data["erp_entity"],
            field_mappings=field_mappings,
            transformation_rules=transformation_rules,
            sync_direction=mapping_data["sync_direction"],
            sync_schedule=mapping_data.get("sync_schedule"),
            is_active=mapping_data.get("is_active", True)
        )
        
        db.add(mapping)
        db.commit()
        db.refresh(mapping)
        
        return mapping
    
    @staticmethod
    def update_data_mapping(db: Session, mapping_id: int, mapping_data: Dict[str, Any]) -> Optional[ERPDataMapping]:
        """
        Update a data mapping.
        
        Args:
            db: Database session
            mapping_id: ID of the data mapping
            mapping_data: Updated data mapping data
            
        Returns:
            Updated data mapping if found, None otherwise
        """
        mapping = db.query(ERPDataMapping).filter(ERPDataMapping.id == mapping_id).first()
        
        if not mapping:
            return None
        
        # Update fields if provided
        if "name" in mapping_data:
            mapping.name = mapping_data["name"]
        
        if "description" in mapping_data:
            mapping.description = mapping_data["description"]
        
        if "assetkpi_entity" in mapping_data:
            mapping.assetkpi_entity = mapping_data["assetkpi_entity"]
        
        if "erp_entity" in mapping_data:
            mapping.erp_entity = mapping_data["erp_entity"]
        
        if "field_mappings" in mapping_data:
            mapping.field_mappings = mapping_data["field_mappings"]
        
        if "transformation_rules" in mapping_data:
            mapping.transformation_rules = mapping_data["transformation_rules"]
        
        if "sync_direction" in mapping_data:
            mapping.sync_direction = mapping_data["sync_direction"]
        
        if "sync_schedule" in mapping_data:
            mapping.sync_schedule = mapping_data["sync_schedule"]
        
        if "is_active" in mapping_data:
            mapping.is_active = mapping_data["is_active"]
        
        mapping.updated_at = datetime.now()
        
        db.commit()
        db.refresh(mapping)
        
        return mapping
    
    @staticmethod
    def delete_data_mapping(db: Session, mapping_id: int) -> bool:
        """
        Delete a data mapping.
        
        Args:
            db: Database session
            mapping_id: ID of the data mapping
            
        Returns:
            True if deletion is successful, False otherwise
        """
        mapping = db.query(ERPDataMapping).filter(ERPDataMapping.id == mapping_id).first()
        
        if not mapping:
            return False
        
        db.delete(mapping)
        db.commit()
        
        return True
    
    @staticmethod
    def get_entity_schema(db: Session, connection_id: int, entity_type: str) -> Dict[str, Any]:
        """
        Get the schema for an entity type in the ERP system.
        
        Args:
            db: Database session
            connection_id: ID of the connection
            entity_type: Type of entity
            
        Returns:
            Entity schema
        """
        try:
            # Get connection
            connection = db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
            
            if not connection:
                return {
                    "success": False,
                    "message": f"Connection with ID {connection_id} not found"
                }
            
            # Create connector
            connection_config = {
                "connection_url": connection.connection_url,
                "auth_type": connection.auth_type,
                "auth_credentials": connection.auth_credentials,
            }
            
            if connection.connection_params:
                connection_config.update(connection.connection_params)
            
            connector = ERPConnectorFactory.create_connector(connection.system_type, connection_config)
            
            if not connector:
                return {
                    "success": False,
                    "message": f"Unsupported ERP system type: {connection.system_type}"
                }
            
            # Connect to ERP system
            if not connector.connect():
                return {
                    "success": False,
                    "message": "Failed to connect to ERP system"
                }
            
            # Get entity schema
            try:
                schema = connector.get_entity_schema(entity_type)
                
                return {
                    "success": True,
                    "schema": schema
                }
            
            finally:
                # Disconnect from ERP system
                connector.disconnect()
        
        except Exception as e:
            logger.error(f"Error getting entity schema: {str(e)}")
            
            return {
                "success": False,
                "message": f"Error getting entity schema: {str(e)}"
            }
    
    @staticmethod
    def sync_data(db: Session, mapping_id: int, force: bool = False) -> Dict[str, Any]:
        """
        Synchronize data between AssetKPI and ERP system.
        
        Args:
            db: Database session
            mapping_id: ID of the data mapping
            force: Whether to force synchronization regardless of schedule
            
        Returns:
            Synchronization results
        """
        try:
            # Get data mapping
            mapping = db.query(ERPDataMapping).filter(ERPDataMapping.id == mapping_id).first()
            
            if not mapping:
                return {
                    "success": False,
                    "message": f"Data mapping with ID {mapping_id} not found"
                }
            
            # Check if mapping is active
            if not mapping.is_active and not force:
                return {
                    "success": False,
                    "message": "Data mapping is inactive"
                }
            
            # Get connection
            connection = db.query(ERPConnection).filter(ERPConnection.id == mapping.connection_id).first()
            
            if not connection:
                return {
                    "success": False,
                    "message": f"Connection with ID {mapping.connection_id} not found"
                }
            
            # Check if connection is active
            if connection.status != "active" and not force:
                return {
                    "success": False,
                    "message": "Connection is inactive"
                }
            
            # Create sync log
            sync_log = ERPSyncLog(
                connection_id=connection.id,
                mapping_id=mapping.id,
                operation_type=mapping.sync_direction,
                entity_type=mapping.assetkpi_entity,
                status="running",
                start_time=datetime.now()
            )
            
            db.add(sync_log)
            db.commit()
            db.refresh(sync_log)
            
            # Create connector
            connection_config = {
                "connection_url": connection.connection_url,
                "auth_type": connection.auth_type,
                "auth_credentials": connection.auth_credentials,
            }
            
            if connection.connection_params:
                connection_config.update(connection.connection_params)
            
            connector = ERPConnectorFactory.create_connector(connection.system_type, connection_config)
            
            if not connector:
                sync_log.status = "failure"
                sync_log.end_time = datetime.now()
                sync_log.error_message = f"Unsupported ERP system type: {connection.system_type}"
                db.commit()
                
                return {
                    "success": False,
                    "message": f"Unsupported ERP system type: {connection.system_type}",
                    "log_id": sync_log.id
                }
            
            # Connect to ERP system
            if not connector.connect():
                sync_log.status = "failure"
                sync_log.end_time = datetime.now()
                sync_log.error_message = "Failed to connect to ERP system"
                db.commit()
                
                return {
                    "success": False,
                    "message": "Failed to connect to ERP system",
                    "log_id": sync_log.id
                }
            
            try:
                # Create data mapper
                data_mapper = DataMapper(
                    field_mappings=mapping.field_mappings,
                    transformation_rules=mapping.transformation_rules
                )
                
                # Perform synchronization based on direction
                if mapping.sync_direction == "import":
                    result = ERPIntegrationService._import_data(
                        db=db,
                        connector=connector,
                        data_mapper=data_mapper,
                        mapping=mapping,
                        sync_log=sync_log
                    )
                
                elif mapping.sync_direction == "export":
                    result = ERPIntegrationService._export_data(
                        db=db,
                        connector=connector,
                        data_mapper=data_mapper,
                        mapping=mapping,
                        sync_log=sync_log
                    )
                
                elif mapping.sync_direction == "bidirectional":
                    # First import, then export
                    import_result = ERPIntegrationService._import_data(
                        db=db,
                        connector=connector,
                        data_mapper=data_mapper,
                        mapping=mapping,
                        sync_log=sync_log
                    )
                    
                    export_result = ERPIntegrationService._export_data(
                        db=db,
                        connector=connector,
                        data_mapper=data_mapper,
                        mapping=mapping,
                        sync_log=sync_log
                    )
                    
                    # Combine results
                    result = {
                        "success": import_result["success"] and export_result["success"],
                        "message": f"Import: {import_result['message']}; Export: {export_result['message']}",
                        "records_processed": import_result["records_processed"] + export_result["records_processed"],
                        "records_succeeded": import_result["records_succeeded"] + export_result["records_succeeded"],
                        "records_failed": import_result["records_failed"] + export_result["records_failed"],
                    }
                
                else:
                    sync_log.status = "failure"
                    sync_log.end_time = datetime.now()
                    sync_log.error_message = f"Unsupported sync direction: {mapping.sync_direction}"
                    db.commit()
                    
                    return {
                        "success": False,
                        "message": f"Unsupported sync direction: {mapping.sync_direction}",
                        "log_id": sync_log.id
                    }
                
                # Update sync log
                sync_log.status = "success" if result["success"] else "failure"
                sync_log.end_time = datetime.now()
                sync_log.records_processed = result["records_processed"]
                sync_log.records_succeeded = result["records_succeeded"]
                sync_log.records_failed = result["records_failed"]
                
                if not result["success"]:
                    sync_log.error_message = result["message"]
                
                db.commit()
                
                # Update connection last sync time
                connection.last_sync_time = datetime.now()
                db.commit()
                
                return {
                    "success": result["success"],
                    "message": result["message"],
                    "log_id": sync_log.id,
                    "details": {
                        "records_processed": result["records_processed"],
                        "records_succeeded": result["records_succeeded"],
                        "records_failed": result["records_failed"],
                    }
                }
            
            finally:
                # Disconnect from ERP system
                connector.disconnect()
        
        except Exception as e:
            logger.error(f"Error synchronizing data: {str(e)}")
            
            # Update sync log if it exists
            if 'sync_log' in locals():
                sync_log.status = "failure"
                sync_log.end_time = datetime.now()
                sync_log.error_message = str(e)
                db.commit()
                
                return {
                    "success": False,
                    "message": f"Error synchronizing data: {str(e)}",
                    "log_id": sync_log.id
                }
            
            return {
                "success": False,
                "message": f"Error synchronizing data: {str(e)}"
            }
    
    @staticmethod
    def _import_data(
        db: Session,
        connector: Any,
        data_mapper: DataMapper,
        mapping: ERPDataMapping,
        sync_log: ERPSyncLog
    ) -> Dict[str, Any]:
        """
        Import data from ERP system to AssetKPI.
        
        Args:
            db: Database session
            connector: ERP connector
            data_mapper: Data mapper
            mapping: Data mapping
            sync_log: Synchronization log
            
        Returns:
            Import results
        """
        # This is a placeholder implementation
        # In a real implementation, you would:
        # 1. Get data from ERP system
        # 2. Map it to AssetKPI format
        # 3. Save it to AssetKPI database
        
        # For now, we'll just simulate the import
        import time
        time.sleep(2)  # Simulate import time
        
        return {
            "success": True,
            "message": "Data imported successfully",
            "records_processed": 10,
            "records_succeeded": 10,
            "records_failed": 0,
        }
    
    @staticmethod
    def _export_data(
        db: Session,
        connector: Any,
        data_mapper: DataMapper,
        mapping: ERPDataMapping,
        sync_log: ERPSyncLog
    ) -> Dict[str, Any]:
        """
        Export data from AssetKPI to ERP system.
        
        Args:
            db: Database session
            connector: ERP connector
            data_mapper: Data mapper
            mapping: Data mapping
            sync_log: Synchronization log
            
        Returns:
            Export results
        """
        # This is a placeholder implementation
        # In a real implementation, you would:
        # 1. Get data from AssetKPI database
        # 2. Map it to ERP format
        # 3. Save it to ERP system
        
        # For now, we'll just simulate the export
        import time
        time.sleep(2)  # Simulate export time
        
        return {
            "success": True,
            "message": "Data exported successfully",
            "records_processed": 5,
            "records_succeeded": 5,
            "records_failed": 0,
        }
    
    @staticmethod
    def get_sync_logs(db: Session, connection_id: Optional[int] = None, mapping_id: Optional[int] = None, limit: int = 100) -> List[ERPSyncLog]:
        """
        Get synchronization logs.
        
        Args:
            db: Database session
            connection_id: Optional ID of the connection to filter by
            mapping_id: Optional ID of the data mapping to filter by
            limit: Maximum number of logs to return
            
        Returns:
            List of synchronization logs
        """
        query = db.query(ERPSyncLog).order_by(ERPSyncLog.start_time.desc())
        
        if connection_id:
            query = query.filter(ERPSyncLog.connection_id == connection_id)
        
        if mapping_id:
            query = query.filter(ERPSyncLog.mapping_id == mapping_id)
        
        return query.limit(limit).all()
    
    @staticmethod
    def get_sync_log(db: Session, log_id: int) -> Optional[ERPSyncLog]:
        """
        Get a synchronization log by ID.
        
        Args:
            db: Database session
            log_id: ID of the synchronization log
            
        Returns:
            Synchronization log if found, None otherwise
        """
        return db.query(ERPSyncLog).filter(ERPSyncLog.id == log_id).first()
