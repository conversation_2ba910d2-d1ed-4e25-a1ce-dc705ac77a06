-- AssetKPI Database Migration: Milestone 5 - Inventory Management Enhancements

-- 5.1 Storerooms and Locations
CREATE TABLE IF NOT EXISTS storerooms (
    storeroom_id SERIAL PRIMARY KEY,
    storeroom_name VARCHAR(100) NOT NULL,
    location VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS storage_locations (
    location_id SERIAL PRIMARY KEY,
    storeroom_id INTEGER REFERENCES storerooms(storeroom_id),
    location_name VARCHAR(100) NOT NULL,
    bin VARCHAR(50),
    aisle VARCHAR(50),
    shelf VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add column to spareparts table
ALTER TABLE spareparts ADD COLUMN IF NOT EXISTS storage_location_id INTEGER REFERENCES storage_locations(location_id);

-- 5.2 Vendors
CREATE TABLE IF NOT EXISTS vendors (
    vendor_id SERIAL PRIMARY KEY,
    vendor_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(50),
    email VARCHAR(100),
    address TEXT,
    preferred BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add column to spareparts table
ALTER TABLE spareparts ADD COLUMN IF NOT EXISTS preferred_vendor_id INTEGER REFERENCES vendors(vendor_id);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_storage_locations_storeroom_id ON storage_locations(storeroom_id);
CREATE INDEX IF NOT EXISTS idx_spareparts_storage_location_id ON spareparts(storage_location_id);
CREATE INDEX IF NOT EXISTS idx_spareparts_preferred_vendor_id ON spareparts(preferred_vendor_id);

-- End of Milestone 5 migration script
