import requests
import time
import concurrent.futures
import matplotlib.pyplot as plt
import numpy as np

# Configuration
API_BASE_URL = "http://localhost:8000/api"
API_KEY = "c5e52be8-9b1c-4fcd-8457-741c91ef5c85"
ENDPOINT = "/inventory/parts"  # Simple endpoint for testing
NUM_REQUESTS = 150  # Number of requests to make
CONCURRENCY = 10  # Number of concurrent requests

def make_request(request_id):
    """Make a request to the API and return the response details."""
    start_time = time.time()

    try:
        response = requests.get(
            f"{API_BASE_URL}{ENDPOINT}",
            headers={"X-API-Key": API_KEY}
        )

        status_code = response.status_code

        # Extract rate limit headers
        limit = int(response.headers.get("X-RateLimit-Limit", 0))
        remaining = int(response.headers.get("X-RateLimit-Remaining", 0))
        reset = int(response.headers.get("X-RateLimit-Reset", 0))

        # Extract retry-after header if rate limited
        retry_after = int(response.headers.get("Retry-After", 0)) if status_code == 429 else 0

        return {
            "request_id": request_id,
            "status_code": status_code,
            "time": time.time() - start_time,
            "limit": limit,
            "remaining": remaining,
            "reset": reset,
            "retry_after": retry_after
        }
    except Exception as e:
        return {
            "request_id": request_id,
            "status_code": 0,
            "time": time.time() - start_time,
            "error": str(e),
            "limit": 0,
            "remaining": 0,
            "reset": 0,
            "retry_after": 0
        }

def run_test():
    """Run the rate limiting test."""
    print(f"Testing rate limiting with {NUM_REQUESTS} requests ({CONCURRENCY} concurrent)...")

    results = []

    # Use ThreadPoolExecutor to make concurrent requests
    with concurrent.futures.ThreadPoolExecutor(max_workers=CONCURRENCY) as executor:
        # Submit all requests
        futures = [executor.submit(make_request, i) for i in range(NUM_REQUESTS)]

        # Process results as they complete
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            results.append(result)

            # Print progress
            if len(results) % 10 == 0:
                print(f"Completed {len(results)}/{NUM_REQUESTS} requests")

    # Sort results by request ID
    results.sort(key=lambda x: x["request_id"])

    # Print summary
    success_count = sum(1 for r in results if r["status_code"] == 200)
    rate_limited_count = sum(1 for r in results if r["status_code"] == 429)
    error_count = sum(1 for r in results if r["status_code"] not in [200, 429])

    print("\nTest Results:")
    print(f"Total Requests: {len(results)}")
    print(f"Successful Requests: {success_count} ({success_count/len(results)*100:.2f}%)")
    print(f"Rate Limited Requests: {rate_limited_count} ({rate_limited_count/len(results)*100:.2f}%)")
    print(f"Error Requests: {error_count} ({error_count/len(results)*100:.2f}%)")

    # Calculate average response time for successful requests
    if success_count > 0:
        avg_time = sum(r["time"] for r in results if r["status_code"] == 200) / success_count
        print(f"Average Response Time (successful): {avg_time:.4f} seconds")

    # Print rate limit information from the first successful response
    for result in results:
        if result["status_code"] == 200:
            print(f"\nRate Limit Information:")
            print(f"Limit: {result['limit']} requests")
            print(f"Reset Time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(result['reset']))}")
            break

    # Visualize the results
    visualize_results(results)

    return results

def visualize_results(results):
    """Create visualizations of the test results."""
    # Extract data for plotting
    request_ids = [r["request_id"] for r in results]
    status_codes = [r["status_code"] for r in results]
    remaining = [r["remaining"] for r in results]

    # Create a figure with multiple subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # Plot 1: Status Codes
    colors = ['green' if s == 200 else 'red' if s == 429 else 'gray' for s in status_codes]
    ax1.scatter(request_ids, status_codes, c=colors, alpha=0.7)
    ax1.set_xlabel('Request ID')
    ax1.set_ylabel('Status Code')
    ax1.set_title('API Response Status Codes')
    ax1.set_yticks([200, 429])
    ax1.set_yticklabels(['200 OK', '429 Too Many Requests'])
    ax1.grid(True, linestyle='--', alpha=0.7)

    # Plot 2: Remaining Rate Limit
    ax2.plot(request_ids, remaining, 'b-', alpha=0.7)
    ax2.set_xlabel('Request ID')
    ax2.set_ylabel('Remaining Requests')
    ax2.set_title('Remaining Rate Limit')
    ax2.grid(True, linestyle='--', alpha=0.7)

    # Adjust layout and save
    plt.tight_layout()
    plt.savefig('rate_limit_test_results.png')
    print("\nVisualization saved to 'rate_limit_test_results.png'")

    # Try to display the plot
    try:
        plt.show()
    except:
        print("Could not display plot. The image has been saved to disk.")

if __name__ == "__main__":
    run_test()
