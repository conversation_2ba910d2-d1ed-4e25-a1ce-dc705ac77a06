{% extends "layout.html" %}

{% block title %}KPI Analytics | AssetKPI{% endblock %}

{% block styles %}
<style>
    .chart-container {
        height: 400px;
        margin-bottom: 20px;
    }
    .metric-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
        height: 100%;
    }
    .metric-card:hover {
        transform: translateY(-5px);
    }
    .metric-value {
        font-size: 24px;
        font-weight: bold;
    }
    .metric-unit {
        font-size: 14px;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2">KPI Analytics</h1>
            <p class="text-muted">Advanced analysis of key performance indicators</p>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Filters</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="kpiType" class="form-label">KPI Type:</label>
                                <select class="form-select" id="kpiType">
                                    <option value="oee">Overall Equipment Effectiveness (OEE)</option>
                                    <option value="mttr">Mean Time To Repair (MTTR)</option>
                                    <option value="mtbf">Mean Time Between Failures (MTBF)</option>
                                    <option value="availability">Availability</option>
                                    <option value="performance">Performance</option>
                                    <option value="quality">Quality</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="assetCategory" class="form-label">Asset Category:</label>
                                <select class="form-select" id="assetCategory">
                                    <option value="all">All Categories</option>
                                    <option value="production">Production</option>
                                    <option value="utility">Utility</option>
                                    <option value="facility">Facility</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="dateFrom" class="form-label">From Date:</label>
                                <input type="date" class="form-control" id="dateFrom">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="dateTo" class="form-label">To Date:</label>
                                <input type="date" class="form-control" id="dateTo">
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="timeAggregation" class="form-label">Time Aggregation:</label>
                                <select class="form-select" id="timeAggregation">
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly" selected>Monthly</option>
                                    <option value="quarterly">Quarterly</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="anomalyDetection" class="form-label">Anomaly Detection:</label>
                                <select class="form-select" id="anomalyDetection">
                                    <option value="off">Off</option>
                                    <option value="standard" selected>Standard (±2σ)</option>
                                    <option value="strict">Strict (±1.5σ)</option>
                                    <option value="loose">Loose (±3σ)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="benchmark" class="form-label">Benchmark:</label>
                                <select class="form-select" id="benchmark">
                                    <option value="none" selected>None</option>
                                    <option value="industry">Industry Average</option>
                                    <option value="target">Target Values</option>
                                    <option value="historical">Historical Best</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" class="btn btn-primary w-100" id="applyFilters">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <h5 class="card-title text-muted">Current Value</h5>
                    <h2 class="display-5" id="currentValue">85.2%</h2>
                    <p class="text-success" id="trendIndicator">
                        <i class="bi bi-arrow-up"></i> 2.3% vs previous period
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <h5 class="card-title text-muted">Average</h5>
                    <h2 class="display-5" id="averageValue">78.5%</h2>
                    <p class="text-muted">Last 12 months</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <h5 class="card-title text-muted">Target</h5>
                    <h2 class="display-5" id="targetValue">90.0%</h2>
                    <p class="text-danger" id="gapIndicator">4.8% gap to target</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <h5 class="card-title text-muted">Anomalies</h5>
                    <h2 class="display-5" id="anomalyCount">3</h2>
                    <p class="text-warning">Detected in period</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">KPI Trend Analysis</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportCsv">
                            <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportPdf">
                            <i class="bi bi-file-earmark-pdf"></i> Export PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:400px;">
                        <canvas id="mainChart"></canvas>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">Click on any data point to see detailed breakdown</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Anomaly Detection Section -->
    <div class="row mb-4" id="anomalySection">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Anomaly Detection</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="anomalyTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Asset</th>
                                    <th>KPI Value</th>
                                    <th>Expected Range</th>
                                    <th>Deviation</th>
                                    <th>Severity</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="7" class="text-center">Loading anomaly data...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Include Chart.js directly in this template -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

<script>
    // Global variables
    let mainChart = null;
    
    // Sample data
    const sampleData = {
        timeLabels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        kpiData: [82.5, 84.3, 79.8, 85.2, 87.1, 86.5, 88.2, 85.7, 83.9, 86.3, 88.5, 89.2],
        benchmarkData: [],
        summary: {
            currentValue: 89.2,
            averageValue: 85.6,
            targetValue: 90.0,
            trend: 0.7,
            gap: 0.8,
            anomalyCount: 3
        },
        anomalies: [
            {
                date: '2023-03-15',
                value: 79.8,
                expected_range: [84.0, 88.0],
                deviation: -4.2,
                severity: 'medium',
                index: 2
            },
            {
                date: '2023-08-22',
                value: 83.9,
                expected_range: [85.0, 89.0],
                deviation: -1.1,
                severity: 'low',
                index: 8
            },
            {
                date: '2023-12-10',
                value: 89.2,
                expected_range: [84.0, 88.0],
                deviation: 1.2,
                severity: 'low',
                index: 11
            }
        ]
    };
    
    // Initialize the page when the window loads
    window.onload = function() {
        console.log('Window loaded, initializing KPI Analytics page');
        
        // Set default dates (last 12 months)
        const today = new Date();
        const oneYearAgo = new Date(today);
        oneYearAgo.setFullYear(today.getFullYear() - 1);
        
        document.getElementById('dateFrom').value = oneYearAgo.toISOString().split('T')[0];
        document.getElementById('dateTo').value = today.toISOString().split('T')[0];
        
        // Add event listeners
        document.getElementById('applyFilters').addEventListener('click', function() {
            console.log('Apply filters clicked');
            // Show loading indicators
            document.querySelectorAll('.chart-container').forEach(container => {
                container.innerHTML = '<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
            });
            
            // Simulate API call with a delay
            setTimeout(function() {
                // Restore chart containers
                restoreChartContainers();
                
                // Update UI with sample data
                updateSummaryCards(sampleData.summary);
                updateAnomalyTable(sampleData.anomalies, sampleData.timeLabels);
                createMainChart(sampleData.timeLabels, sampleData.kpiData, sampleData.benchmarkData, 'oee');
            }, 1000);
        });
        
        document.getElementById('exportCsv').addEventListener('click', exportToCsv);
        document.getElementById('exportPdf').addEventListener('click', exportToPdf);
        
        // Load initial data
        updateSummaryCards(sampleData.summary);
        updateAnomalyTable(sampleData.anomalies, sampleData.timeLabels);
        
        // Create the main chart after a short delay to ensure DOM is ready
        setTimeout(function() {
            createMainChart(sampleData.timeLabels, sampleData.kpiData, sampleData.benchmarkData, 'oee');
        }, 500);
    };
    
    // Function to restore chart containers
    function restoreChartContainers() {
        document.querySelectorAll('.chart-container').forEach(container => {
            const canvasId = container.querySelector('canvas') ? container.querySelector('canvas').id : null;
            if (canvasId) {
                // Clear the container and create a new canvas
                container.innerHTML = '';
                const canvas = document.createElement('canvas');
                canvas.id = canvasId;
                container.appendChild(canvas);
                console.log(`Restored canvas with id: ${canvasId}`);
            }
        });
    }
    
    // Function to update summary cards
    function updateSummaryCards(summary) {
        document.getElementById('currentValue').textContent = formatValue(summary.currentValue);
        document.getElementById('averageValue').textContent = formatValue(summary.averageValue);
        document.getElementById('targetValue').textContent = formatValue(summary.targetValue);
        document.getElementById('anomalyCount').textContent = summary.anomalyCount;

        // Update trend indicator
        const trendElement = document.getElementById('trendIndicator');
        if (summary.trend > 0) {
            trendElement.className = 'text-success';
            trendElement.innerHTML = `<i class="bi bi-arrow-up"></i> ${Math.abs(summary.trend).toFixed(1)}% vs previous period`;
        } else if (summary.trend < 0) {
            trendElement.className = 'text-danger';
            trendElement.innerHTML = `<i class="bi bi-arrow-down"></i> ${Math.abs(summary.trend).toFixed(1)}% vs previous period`;
        } else {
            trendElement.className = 'text-muted';
            trendElement.innerHTML = `<i class="bi bi-dash"></i> No change vs previous period`;
        }

        // Update gap to target
        const gapElement = document.getElementById('gapIndicator');
        if (summary.gap > 0) {
            gapElement.className = 'text-danger';
            gapElement.textContent = `${summary.gap.toFixed(1)}% gap to target`;
        } else if (summary.gap < 0) {
            gapElement.className = 'text-success';
            gapElement.textContent = `${Math.abs(summary.gap).toFixed(1)}% above target`;
        } else {
            gapElement.className = 'text-muted';
            gapElement.textContent = `At target`;
        }
    }
    
    // Function to format KPI values based on type
    function formatValue(value) {
        const kpiType = document.getElementById('kpiType').value;
        if (kpiType === 'mttr' || kpiType === 'mtbf') {
            return value.toFixed(1);
        } else {
            return value.toFixed(1) + '%';
        }
    }
    
    // Function to update anomaly table
    function updateAnomalyTable(anomalies, timeLabels) {
        const tableBody = document.querySelector('#anomalyTable tbody');
        tableBody.innerHTML = '';
        
        if (!anomalies || anomalies.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center">No anomalies detected in the selected period.</td></tr>';
            return;
        }
        
        anomalies.forEach(anomaly => {
            const row = document.createElement('tr');
            
            // Add severity class
            if (anomaly.severity === 'high') {
                row.classList.add('table-danger');
            } else if (anomaly.severity === 'medium') {
                row.classList.add('table-warning');
            }
            
            row.innerHTML = `
                <td>${anomaly.date}</td>
                <td>Multiple Assets</td>
                <td>${formatValue(anomaly.value)}</td>
                <td>${formatValue(anomaly.expected_range[0])} - ${formatValue(anomaly.expected_range[1])}</td>
                <td>${anomaly.deviation > 0 ? '+' : ''}${formatValue(anomaly.deviation)}</td>
                <td><span class="badge ${anomaly.severity === 'high' ? 'bg-danger' : anomaly.severity === 'medium' ? 'bg-warning' : 'bg-info'}">${anomaly.severity.charAt(0).toUpperCase() + anomaly.severity.slice(1)}</span></td>
                <td><button class="btn btn-sm btn-outline-primary analyze-btn" data-index="${anomaly.index}">Analyze</button></td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners to analyze buttons
        document.querySelectorAll('.analyze-btn').forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                alert(`Analysis for period ${timeLabels[index]} will be implemented in a future update.`);
            });
        });
    }
    
    // Function to create main chart
    function createMainChart(timeLabels, kpiData, benchmarkData, kpiType) {
        console.log('Creating main chart with data:', { timeLabels, kpiData, benchmarkData, kpiType });
        
        const canvas = document.getElementById('mainChart');
        if (!canvas) {
            console.error('Main chart canvas not found');
            return;
        }
        
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Could not get 2D context for main chart');
            return;
        }
        
        // Destroy existing chart if it exists
        if (mainChart) {
            mainChart.destroy();
        }
        
        // Determine y-axis label based on KPI type
        let yAxisLabel = 'Value';
        let chartTitle = 'KPI Trend';
        let datasetLabel = 'Value';
        
        if (kpiType === 'oee') {
            yAxisLabel = 'OEE (%)';
            chartTitle = 'Overall Equipment Effectiveness (OEE) Trend';
            datasetLabel = 'OEE (%)';
        } else if (kpiType === 'mttr') {
            yAxisLabel = 'MTTR (hours)';
            chartTitle = 'Mean Time To Repair (MTTR) Trend';
            datasetLabel = 'MTTR (hours)';
        } else if (kpiType === 'mtbf') {
            yAxisLabel = 'MTBF (hours)';
            chartTitle = 'Mean Time Between Failures (MTBF) Trend';
            datasetLabel = 'MTBF (hours)';
        } else if (kpiType === 'availability') {
            yAxisLabel = 'Availability (%)';
            chartTitle = 'Availability Trend';
            datasetLabel = 'Availability (%)';
        } else if (kpiType === 'performance') {
            yAxisLabel = 'Performance (%)';
            chartTitle = 'Performance Trend';
            datasetLabel = 'Performance (%)';
        } else if (kpiType === 'quality') {
            yAxisLabel = 'Quality (%)';
            chartTitle = 'Quality Trend';
            datasetLabel = 'Quality (%)';
        }
        
        try {
            // Create datasets array
            const datasets = [
                {
                    label: datasetLabel,
                    data: kpiData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }
            ];
            
            // Add benchmark dataset if available
            if (benchmarkData && benchmarkData.length > 0) {
                datasets.push({
                    label: 'Benchmark',
                    data: benchmarkData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    tension: 0.1,
                    fill: false
                });
            }
            
            // Create chart
            mainChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: yAxisLabel
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Time Period'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: chartTitle
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    onClick: function(event, elements) {
                        if (elements && elements.length > 0) {
                            const index = elements[0].index;
                            alert(`Drill-down for ${timeLabels[index]} will be implemented in a future update.`);
                        }
                    }
                }
            });
            
            console.log('Main chart created successfully');
        } catch (error) {
            console.error('Error creating main chart:', error);
            alert('Error creating chart: ' + error.message);
        }
    }
    
    // Function to export data to CSV
    function exportToCsv() {
        console.log('Exporting data to CSV');
        
        // Get the current KPI type
        const kpiType = document.getElementById('kpiType').value;
        
        // Create CSV content
        let csvContent = 'data:text/csv;charset=utf-8,Period,Value\n';
        
        // Add data rows
        const labels = sampleData.timeLabels;
        const data = sampleData.kpiData;
        
        for (let i = 0; i < labels.length; i++) {
            csvContent += `${labels[i]},${data[i]}\n`;
        }
        
        // Create download link
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement('a');
        link.setAttribute('href', encodedUri);
        link.setAttribute('download', `${kpiType}_data.csv`);
        document.body.appendChild(link);
        
        // Trigger download
        link.click();
        
        // Clean up
        document.body.removeChild(link);
    }
    
    // Function to export data to PDF
    function exportToPdf() {
        console.log('Exporting data to PDF');
        alert('PDF export functionality will be implemented in a future update.');
    }
</script>
{% endblock %}
