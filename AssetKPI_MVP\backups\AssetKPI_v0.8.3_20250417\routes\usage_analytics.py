"""
API routes for usage analytics in the AssetKPI application.

These routes provide access to usage analytics data for administrators.
"""
import os
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import pandas as pd
import io
import csv
import json
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, Response, Path
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy import func, desc, and_, or_, not_
from sqlalchemy.orm import Session

# Import models and schemas
from models.usage_analytics import UserActivityLog, ScheduledReport, UserNotificationPreferences
from schemas.usage_analytics import (
    UserActivityLogRead, UserActivityLogFilter,
    ScheduledReportRead, ScheduledReportCreate,
    UserNotificationPreferencesRead, UserNotificationPreferencesCreate, UserNotificationPreferencesUpdate
)
from middleware.usage_tracking import track_feature_usage

# Import utility functions
from jobs.usage_reporting import generate_usage_report, distribute_usage_report

# Import database and authentication dependencies
# These will be passed in when the router is included in the main app
get_db = None
require_permission = None

# Create router
usage_analytics_router = APIRouter(
    prefix="/api/analytics",
    tags=["analytics"],
    responses={404: {"description": "Not found"}},
)

# For backward compatibility
router = usage_analytics_router

def init_router(get_db_func, require_permission_func):
    """Initialize the router with dependencies."""
    global get_db, require_permission, router
    get_db = get_db_func
    require_permission = require_permission_func
    # For backward compatibility
    router = usage_analytics_router

@usage_analytics_router.get("/activity", response_model=List[UserActivityLogRead])
async def get_user_activity(
    user_id: Optional[str] = None,
    event_type: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    session_id: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Get user activity logs with optional filtering.

    Args:
        user_id: Filter by user ID
        event_type: Filter by event type (PAGE_VIEW, FEATURE_USE, API_CALL, etc.)
        start_date: Filter by start date
        end_date: Filter by end date
        session_id: Filter by session ID
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        List of user activity logs
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="view_activity_logs",
        additional_details={"filters": {
            "user_id": user_id,
            "event_type": event_type,
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None,
            "session_id": session_id,
            "limit": limit,
            "offset": offset
        }}
    )

    # Build query
    query = db.query(UserActivityLog)

    # Apply filters
    if user_id:
        query = query.filter(UserActivityLog.user_id == user_id)
    if event_type:
        query = query.filter(UserActivityLog.event_type == event_type)
    if start_date:
        query = query.filter(UserActivityLog.timestamp >= start_date)
    if end_date:
        query = query.filter(UserActivityLog.timestamp <= end_date)
    if session_id:
        query = query.filter(UserActivityLog.session_id == session_id)

    # Order by timestamp descending
    query = query.order_by(desc(UserActivityLog.timestamp))

    # Apply pagination
    query = query.limit(limit).offset(offset)

    # Execute query
    logs = query.all()

    return logs

@usage_analytics_router.get("/summary", response_model=Dict[str, Any])
async def get_usage_summary(
    start_date: Optional[datetime] = Query(None, description="Start date for the summary period"),
    end_date: Optional[datetime] = Query(None, description="End date for the summary period"),
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Get a summary of usage analytics.

    Args:
        start_date: Start date for the summary period (defaults to 30 days ago)
        end_date: End date for the summary period (defaults to now)

    Returns:
        Summary statistics
    """
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.now()
    if not start_date:
        start_date = end_date - timedelta(days=30)

    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="view_summary",
        additional_details={"filters": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }}
    )

    # Base query filter for the date range
    date_filter = and_(
        UserActivityLog.timestamp >= start_date,
        UserActivityLog.timestamp <= end_date
    )

    # Get total activity count
    total_activities = db.query(func.count(UserActivityLog.id)).filter(date_filter).scalar()

    # Get unique users count
    unique_users = db.query(func.count(func.distinct(UserActivityLog.user_id))).filter(
        and_(date_filter, UserActivityLog.user_id != None)
    ).scalar()

    # Get anonymous users count (sessions without user_id)
    anonymous_sessions = db.query(func.count(func.distinct(UserActivityLog.session_id))).filter(
        and_(date_filter, UserActivityLog.user_id == None)
    ).scalar()

    # Get event type breakdown
    event_types = db.query(
        UserActivityLog.event_type,
        func.count(UserActivityLog.id).label("count")
    ).filter(date_filter).group_by(UserActivityLog.event_type).all()

    event_type_counts = {event_type: count for event_type, count in event_types}

    # Get top pages viewed
    top_pages = []
    page_views = db.query(UserActivityLog).filter(
        and_(date_filter, UserActivityLog.event_type == "PAGE_VIEW")
    ).all()

    page_counts = {}
    for log in page_views:
        path = log.details.get("path", "unknown")
        if path in page_counts:
            page_counts[path] += 1
        else:
            page_counts[path] = 1

    top_pages = [{"path": path, "count": count} for path, count in
                sorted(page_counts.items(), key=lambda x: x[1], reverse=True)[:10]]

    # Get top features used
    top_features = []
    feature_uses = db.query(UserActivityLog).filter(
        and_(date_filter, UserActivityLog.event_type == "FEATURE_USE")
    ).all()

    feature_counts = {}
    for log in feature_uses:
        feature = log.details.get("feature", "unknown")
        if feature in feature_counts:
            feature_counts[feature] += 1
        else:
            feature_counts[feature] = 1

    top_features = [{"feature": feature, "count": count} for feature, count in
                   sorted(feature_counts.items(), key=lambda x: x[1], reverse=True)[:10]]

    # Return summary
    return {
        "period": {
            "start_date": start_date,
            "end_date": end_date,
            "days": (end_date - start_date).days
        },
        "total_activities": total_activities,
        "unique_users": unique_users,
        "anonymous_sessions": anonymous_sessions,
        "event_type_counts": event_type_counts,
        "top_pages": top_pages,
        "top_features": top_features
    }

@usage_analytics_router.get("/user/{user_id}/activity", response_model=List[UserActivityLogRead])
async def get_user_activity_by_id(
    user_id: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    event_type: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Get activity logs for a specific user.

    Args:
        user_id: The user ID to get activity for
        start_date: Filter by start date
        end_date: Filter by end date
        event_type: Filter by event type
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        List of user activity logs
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="view_user_activity",
        additional_details={"target_user_id": user_id}
    )

    # Build query
    query = db.query(UserActivityLog).filter(UserActivityLog.user_id == user_id)

    # Apply filters
    if event_type:
        query = query.filter(UserActivityLog.event_type == event_type)
    if start_date:
        query = query.filter(UserActivityLog.timestamp >= start_date)
    if end_date:
        query = query.filter(UserActivityLog.timestamp <= end_date)

    # Order by timestamp descending
    query = query.order_by(desc(UserActivityLog.timestamp))

    # Apply pagination
    query = query.limit(limit).offset(offset)

    # Execute query
    logs = query.all()

    return logs

@usage_analytics_router.get("/sessions/{session_id}", response_model=List[UserActivityLogRead])
async def get_session_activity(
    session_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Get activity logs for a specific session.

    Args:
        session_id: The session ID to get activity for

    Returns:
        List of user activity logs for the session
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="view_session_activity",
        additional_details={"session_id": session_id}
    )

    # Query logs for the session
    logs = db.query(UserActivityLog).filter(
        UserActivityLog.session_id == session_id
    ).order_by(UserActivityLog.timestamp).all()

    return logs

@usage_analytics_router.get("/export", response_class=StreamingResponse)
async def export_usage_data(
    start_date: Optional[datetime] = Query(None, description="Start date for filtering data"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering data"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    format: str = Query("csv", description="Export format (csv, excel, json)"),
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Export usage analytics data in the specified format.

    Args:
        start_date: Start date for filtering data
        end_date: End date for filtering data
        event_type: Filter by event type
        user_id: Filter by user ID
        format: Export format (csv, excel, json)

    Returns:
        File download response with the exported data
    """
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.now()
    if not start_date:
        start_date = end_date - timedelta(days=30)

    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="export_data",
        additional_details={
            "format": format,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "event_type": event_type,
            "user_id": user_id
        },
        conversion_event=True
    )

    # Build query
    query = db.query(UserActivityLog)

    # Apply filters
    if start_date:
        query = query.filter(UserActivityLog.timestamp >= start_date)
    if end_date:
        query = query.filter(UserActivityLog.timestamp <= end_date)
    if event_type:
        query = query.filter(UserActivityLog.event_type == event_type)
    if user_id:
        query = query.filter(UserActivityLog.user_id == user_id)

    # Execute query
    logs = query.order_by(desc(UserActivityLog.timestamp)).all()

    # Convert to list of dictionaries for easier processing
    data = []
    for log in logs:
        log_dict = {
            "id": log.id,
            "user_id": log.user_id,
            "timestamp": log.timestamp.isoformat(),
            "event_type": log.event_type,
            "session_id": log.session_id,
            "duration_seconds": log.duration_seconds,
            "component_id": log.component_id,
            "previous_page": log.previous_page,
            "conversion_event": log.conversion_event,
            "user_role": log.user_role,
            "interaction_depth": log.interaction_depth
        }

        # Add details as separate columns
        if log.details:
            for key, value in log.details.items():
                # Skip complex nested objects
                if not isinstance(value, (dict, list)):
                    log_dict[f"details_{key}"] = value

        # Add browser info as separate columns
        if log.browser_info:
            for key, value in log.browser_info.items():
                if not isinstance(value, (dict, list)):
                    log_dict[f"browser_{key}"] = value

        data.append(log_dict)

    # Generate file name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"usage_analytics_export_{timestamp}"

    # Export in the requested format
    if format.lower() == "csv":
        # Create CSV in memory
        output = io.StringIO()
        if data:
            writer = csv.DictWriter(output, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)
        else:
            output.write("No data found for the specified filters.")

        # Return CSV response
        output.seek(0)
        return StreamingResponse(
            iter([output.getvalue()]),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}.csv"}
        )

    elif format.lower() == "excel":
        # Create Excel in memory
        output = io.BytesIO()
        df = pd.DataFrame(data)
        with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
            df.to_excel(writer, sheet_name="Usage Analytics", index=False)

        # Return Excel response
        output.seek(0)
        return StreamingResponse(
            iter([output.getvalue()]),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}.xlsx"}
        )

    elif format.lower() == "json":
        # Create JSON in memory
        return Response(
            content=json.dumps(data, indent=2),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={filename}.json"}
        )

    else:
        raise HTTPException(status_code=400, detail=f"Unsupported format: {format}")

@usage_analytics_router.post("/reports", response_model=ScheduledReportRead)
async def generate_report(
    report_data: ScheduledReportCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Generate a usage analytics report.

    Args:
        report_data: Report configuration
        background_tasks: Background tasks for async processing

    Returns:
        Created report record
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="generate_report",
        additional_details={
            "report_type": report_data.report_type,
            "period": report_data.period,
            "format": report_data.format
        },
        conversion_event=True
    )

    try:
        # Generate the report
        report_result = generate_usage_report(
            period=report_data.period,
            report_format=report_data.format,
            start_date=report_data.start_date,
            end_date=report_data.end_date
        )

        # Get the report record from the database
        report = db.query(ScheduledReport).filter(ScheduledReport.id == report_result["report_id"]).first()
        if not report:
            raise HTTPException(status_code=404, detail="Report not found")

        # Schedule distribution in the background
        background_tasks.add_task(distribute_usage_report, report.id)

        return report

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating report: {str(e)}")

@usage_analytics_router.get("/reports", response_model=List[ScheduledReportRead])
async def list_reports(
    report_type: Optional[str] = Query(None, description="Filter by report type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    List generated reports.

    Args:
        report_type: Filter by report type
        status: Filter by status
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        List of report records
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="list_reports"
    )

    # Build query
    query = db.query(ScheduledReport)

    # Apply filters
    if report_type:
        query = query.filter(ScheduledReport.report_type == report_type)
    if status:
        query = query.filter(ScheduledReport.status == status)

    # Apply pagination
    query = query.order_by(desc(ScheduledReport.created_at)).limit(limit).offset(offset)

    # Execute query
    reports = query.all()

    return reports

@usage_analytics_router.get("/reports/{report_id}", response_model=ScheduledReportRead)
async def get_report(
    report_id: int = Path(..., description="Report ID"),
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Get a specific report.

    Args:
        report_id: Report ID

    Returns:
        Report record
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="get_report",
        additional_details={"report_id": report_id}
    )

    # Get the report
    report = db.query(ScheduledReport).filter(ScheduledReport.id == report_id).first()
    if not report:
        raise HTTPException(status_code=404, detail="Report not found")

    return report

@usage_analytics_router.get("/reports/{report_id}/download")
async def download_report(
    report_id: int = Path(..., description="Report ID"),
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Download a generated report file.

    Args:
        report_id: Report ID

    Returns:
        File download response
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="download_report",
        additional_details={"report_id": report_id},
        conversion_event=True
    )

    # Get the report
    report = db.query(ScheduledReport).filter(ScheduledReport.id == report_id).first()
    if not report:
        raise HTTPException(status_code=404, detail="Report not found")

    # Check if the file exists
    if not report.file_path or not os.path.exists(report.file_path):
        raise HTTPException(status_code=404, detail="Report file not found")

    # Return the file
    return FileResponse(
        path=report.file_path,
        filename=os.path.basename(report.file_path),
        media_type="application/octet-stream"
    )

@usage_analytics_router.get("/notification-preferences", response_model=UserNotificationPreferencesRead)
async def get_notification_preferences(
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Get notification preferences for the current user.

    Returns:
        User notification preferences
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="get_notification_preferences"
    )

    # Get preferences
    preferences = db.query(UserNotificationPreferences).filter(
        UserNotificationPreferences.user_id == current_user.user_id
    ).first()

    # If no preferences exist, create default preferences
    if not preferences:
        preferences = UserNotificationPreferences(
            user_id=current_user.user_id,
            receive_usage_reports=False,
            receive_inventory_reports=False,
            receive_kpi_reports=False,
            report_frequency="weekly",
            email_format="html"
        )
        db.add(preferences)
        db.commit()
        db.refresh(preferences)

    return preferences

@usage_analytics_router.put("/notification-preferences", response_model=UserNotificationPreferencesRead)
async def update_notification_preferences(
    preferences: UserNotificationPreferencesUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(lambda: require_permission("users:read"))
):
    """
    Update notification preferences for the current user.

    Args:
        preferences: Updated preferences

    Returns:
        Updated user notification preferences
    """
    # Track this feature usage
    track_feature_usage(
        db=db,
        user_id=current_user.user_id,
        feature_name="usage_analytics",
        action="update_notification_preferences",
        additional_details=preferences.model_dump()
    )

    # Get existing preferences
    existing_preferences = db.query(UserNotificationPreferences).filter(
        UserNotificationPreferences.user_id == current_user.user_id
    ).first()

    # If no preferences exist, create new preferences
    if not existing_preferences:
        new_preferences = UserNotificationPreferences(
            user_id=current_user.user_id,
            **preferences.model_dump()
        )
        db.add(new_preferences)
        db.commit()
        db.refresh(new_preferences)
        return new_preferences

    # Update existing preferences
    for key, value in preferences.model_dump().items():
        setattr(existing_preferences, key, value)

    # Update timestamp
    existing_preferences.updated_at = datetime.now()

    db.commit()
    db.refresh(existing_preferences)

    return existing_preferences
