# Create a temporary file with the fixed code
with open('templates/simple_login.html', 'r') as file:
    content = file.read()

# Fix the missing closing braces
old_code = """                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent += '\\nToken verified by backend. User role: ' + data.role;

                    // Redirect to dashboard after successful login
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                } else {
                    resultDiv.textContent += '\\nToken verification failed: ' + response.statusText;
                }
            } catch (error) {
                console.error('Sign in error:', error);
                resultDiv.textContent = 'Error signing in: ' + error.message;
                resultDiv.className = 'error';
            }"""

new_code = """                if (response.ok) {
                    const data = await response.json();
                    console.log('Token verification successful:', data);
                    resultDiv.textContent += '\\nToken verification successful. Role: ' + data.role;
                    
                    // Redirect to dashboard after successful login
                    // window.location.href = '/dashboard';
                } else {
                    const error = await response.text();
                    console.error('Token verification failed:', error);
                    resultDiv.textContent += '\\nToken verification failed: ' + response.statusText;
                    
                    // Try to parse the error
                    try {
                        const errorObj = JSON.parse(error);
                        console.error('Token verification error details:', errorObj);
                        resultDiv.textContent += '\\nError details: ' + errorObj.error;
                    } catch (e) {
                        console.error('Could not parse error:', error);
                        resultDiv.textContent += '\\nRaw error: ' + error;
                    }
                }
                } catch (error) {
                    console.error('Error calling verify-token endpoint:', error);
                    resultDiv.textContent += '\\nError calling verify-token endpoint: ' + error.message;
                }
            } catch (error) {
                console.error('Sign in error:', error);
                resultDiv.textContent = 'Error signing in: ' + error.message;
                resultDiv.className = 'error';
            }"""

content = content.replace(old_code, new_code)

# Write the fixed content back to the file
with open('templates/simple_login.html', 'w') as file:
    file.write(content)

print("Fixed simple_login.html")
