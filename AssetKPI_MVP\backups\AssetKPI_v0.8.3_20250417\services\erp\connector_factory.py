"""
ERP connector factory.

This module provides a factory for creating ERP connectors.
"""

import logging
from typing import Dict, Any, Optional

from .base_connector import BaseERPConnector
from .sap_connector import SAPConnector

# Create a logger for this module
logger = logging.getLogger(__name__)


class ERPConnectorFactory:
    """
    Factory for creating ERP connectors.
    
    This class provides methods for creating connectors for different ERP systems.
    """
    
    @staticmethod
    def create_connector(system_type: str, connection_config: Dict[str, Any]) -> Optional[BaseERPConnector]:
        """
        Create an ERP connector.
        
        Args:
            system_type: Type of ERP system
            connection_config: Configuration for connecting to the ERP system
            
        Returns:
            ERP connector if successful, None otherwise
        """
        try:
            system_type = system_type.lower()
            
            if system_type == "sap":
                return SAPConnector(connection_config)
            
            # Add more connectors here as they are implemented
            # elif system_type == "oracle":
            #     return OracleConnector(connection_config)
            # elif system_type == "microsoft_dynamics":
            #     return MicrosoftDynamicsConnector(connection_config)
            
            else:
                logger.error(f"Unsupported ERP system type: {system_type}")
                return None
        
        except Exception as e:
            logger.error(f"Error creating ERP connector for system type {system_type}: {str(e)}")
            return None
