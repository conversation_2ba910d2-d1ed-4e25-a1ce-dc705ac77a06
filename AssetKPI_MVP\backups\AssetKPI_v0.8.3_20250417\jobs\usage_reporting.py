"""
Scheduled jobs for generating and distributing usage analytics reports.

This module contains APScheduler jobs for automatically generating
usage analytics reports and sending them to administrators.
"""
import os
import io
import csv
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy import func, desc, and_
from sqlalchemy.orm import Session
from fastapi import Depends
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

# Import models and schemas
from models.usage_analytics import UserActivityLog, ScheduledReport, UserNotificationPreferences
from main import User  # Import User model from main
from utils.email import send_email

# Import database session
from main import SessionLocal, scheduler

def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        return db
    finally:
        db.close()

def generate_usage_report(
    period: str = "weekly",
    report_format: str = "csv",
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> Dict[str, Any]:
    """
    Generate a usage analytics report for the specified period.

    Args:
        period: The period to generate the report for ("daily", "weekly", "monthly")
        report_format: The format of the report ("csv", "excel", "json")
        start_date: Optional start date for custom period
        end_date: Optional end date for custom period

    Returns:
        Dictionary with report metadata and file path
    """
    # Get database session
    db = get_db()

    try:
        # Determine date range if not provided
        if not end_date:
            end_date = datetime.now()

        if not start_date:
            if period == "daily":
                start_date = end_date - timedelta(days=1)
            elif period == "weekly":
                start_date = end_date - timedelta(days=7)
            elif period == "monthly":
                start_date = end_date - timedelta(days=30)
            else:
                # Default to weekly
                start_date = end_date - timedelta(days=7)

        # Create reports directory if it doesn't exist
        reports_dir = os.path.join(os.getcwd(), "reports")
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"usage_report_{period}_{timestamp}.{report_format}"
        file_path = os.path.join(reports_dir, filename)

        # Query data
        date_filter = and_(
            UserActivityLog.timestamp >= start_date,
            UserActivityLog.timestamp <= end_date
        )

        # Get activity logs
        logs = db.query(UserActivityLog).filter(date_filter).order_by(desc(UserActivityLog.timestamp)).all()

        # Convert to list of dictionaries for easier processing
        data = []
        for log in logs:
            log_dict = {
                "id": log.id,
                "user_id": log.user_id,
                "timestamp": log.timestamp.isoformat(),
                "event_type": log.event_type,
                "session_id": log.session_id
            }

            # Add details as separate columns
            if log.details:
                for key, value in log.details.items():
                    # Skip complex nested objects
                    if not isinstance(value, (dict, list)):
                        log_dict[f"details_{key}"] = value

            data.append(log_dict)

        # Create DataFrame
        df = pd.DataFrame(data)

        # Generate summary statistics
        total_activities = len(logs)
        unique_users = len(set(log.user_id for log in logs if log.user_id))
        anonymous_sessions = len(set(log.session_id for log in logs if not log.user_id))

        # Event type breakdown
        event_types = {}
        for log in logs:
            if log.event_type in event_types:
                event_types[log.event_type] += 1
            else:
                event_types[log.event_type] = 1

        # Page view breakdown
        page_views = {}
        for log in logs:
            if log.event_type == "PAGE_VIEW" and log.details and "path" in log.details:
                path = log.details["path"]
                if path in page_views:
                    page_views[path] += 1
                else:
                    page_views[path] = 1

        # Feature usage breakdown
        feature_usage = {}
        for log in logs:
            if log.event_type == "FEATURE_USE" and log.details and "feature" in log.details:
                feature = log.details["feature"]
                if feature in feature_usage:
                    feature_usage[feature] += 1
                else:
                    feature_usage[feature] = 1

        # Create summary DataFrame
        summary = {
            "Report Period": f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
            "Total Activities": total_activities,
            "Unique Users": unique_users,
            "Anonymous Sessions": anonymous_sessions,
            "Top Event Types": json.dumps(event_types),
            "Top Pages": json.dumps(dict(sorted(page_views.items(), key=lambda x: x[1], reverse=True)[:10])),
            "Top Features": json.dumps(dict(sorted(feature_usage.items(), key=lambda x: x[1], reverse=True)[:10]))
        }

        summary_df = pd.DataFrame([summary])

        # Save report
        if report_format == "csv":
            # Save summary
            summary_df.to_csv(file_path.replace(".csv", "_summary.csv"), index=False)
            # Save details
            df.to_csv(file_path, index=False)
        elif report_format == "excel":
            with pd.ExcelWriter(file_path) as writer:
                summary_df.to_excel(writer, sheet_name="Summary", index=False)
                df.to_excel(writer, sheet_name="Details", index=False)
        elif report_format == "json":
            with open(file_path, "w") as f:
                json.dump({
                    "summary": summary,
                    "details": data
                }, f, indent=2)

        # Create report record in database
        report = ScheduledReport(
            report_type="usage_analytics",
            period=period,
            start_date=start_date,
            end_date=end_date,
            format=report_format,
            file_path=file_path,
            status="generated"
        )
        db.add(report)
        db.commit()

        return {
            "report_id": report.id,
            "period": period,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "format": report_format,
            "file_path": file_path,
            "record_count": total_activities
        }

    except Exception as e:
        db.rollback()
        print(f"Error generating usage report: {e}")
        raise
    finally:
        db.close()

def distribute_usage_report(report_id: int) -> Dict[str, Any]:
    """
    Distribute a usage report to administrators.

    Args:
        report_id: ID of the report to distribute

    Returns:
        Dictionary with distribution status
    """
    db = get_db()

    try:
        # Get report
        report = db.query(ScheduledReport).filter(ScheduledReport.id == report_id).first()
        if not report:
            raise ValueError(f"Report with ID {report_id} not found")

        # Get admin users
        admin_users = db.query(User).filter(User.role == "ADMIN").all()
        if not admin_users:
            print("No admin users found to send report to")
            return {"status": "no_recipients"}

        # Get admin users with notification preferences enabled
        recipients = []
        for user in admin_users:
            # Check if user has notification preferences
            prefs = db.query(UserNotificationPreferences).filter(
                UserNotificationPreferences.user_id == user.user_id
            ).first()

            # If user has preferences and wants to receive usage reports
            if prefs and prefs.receive_usage_reports:
                recipients.append(user.email)

        if not recipients:
            print("No admin users with notification preferences enabled")
            return {"status": "no_recipients_with_preferences"}

        # Prepare email
        subject = f"AssetKPI Usage Analytics Report - {report.period.capitalize()}"

        # Create email body
        body = f"""
        <html>
        <body>
        <h2>AssetKPI Usage Analytics Report</h2>
        <p>Period: {report.start_date.strftime('%Y-%m-%d')} to {report.end_date.strftime('%Y-%m-%d')}</p>
        <p>Report Type: {report.report_type}</p>
        <p>Format: {report.format}</p>
        <p>Please find the attached report.</p>
        </body>
        </html>
        """

        # Send email with attachment
        send_email(
            recipients=recipients,
            subject=subject,
            html_content=body,
            attachment_path=report.file_path
        )

        # Update report status
        report.status = "sent"
        report.sent_to = recipients
        db.commit()

        return {
            "status": "sent",
            "recipients": recipients,
            "report_id": report.id
        }

    except Exception as e:
        db.rollback()
        print(f"Error distributing usage report: {e}")
        raise
    finally:
        db.close()

def scheduled_usage_report_job():
    """
    Scheduled job to generate and distribute usage reports.
    This job runs according to the schedule defined in the main application.
    """
    try:
        print(f"Running scheduled usage report job at {datetime.now()}")

        # Generate daily report
        daily_report = generate_usage_report(period="daily", report_format="csv")
        print(f"Generated daily report: {daily_report}")

        # Distribute daily report
        distribute_result = distribute_usage_report(daily_report["report_id"])
        print(f"Distributed daily report: {distribute_result}")

        # Generate weekly report on Mondays
        if datetime.now().weekday() == 0:  # Monday
            weekly_report = generate_usage_report(period="weekly", report_format="excel")
            print(f"Generated weekly report: {weekly_report}")

            # Distribute weekly report
            distribute_result = distribute_usage_report(weekly_report["report_id"])
            print(f"Distributed weekly report: {distribute_result}")

        # Generate monthly report on the 1st of the month
        if datetime.now().day == 1:
            monthly_report = generate_usage_report(period="monthly", report_format="excel")
            print(f"Generated monthly report: {monthly_report}")

            # Distribute monthly report
            distribute_result = distribute_usage_report(monthly_report["report_id"])
            print(f"Distributed monthly report: {distribute_result}")

        print(f"Scheduled usage report job completed at {datetime.now()}")

    except Exception as e:
        print(f"Error in scheduled usage report job: {e}")

# Register the scheduled job
def register_scheduled_jobs():
    """Register scheduled jobs with the APScheduler instance."""
    try:
        # Run daily at 1:00 AM
        scheduler.add_job(
            scheduled_usage_report_job,
            "cron",
            hour=1,
            minute=0,
            id="usage_report_job",
            replace_existing=True
        )
        print("Registered scheduled usage report job")
    except Exception as e:
        print(f"Error registering scheduled usage report job: {e}")
