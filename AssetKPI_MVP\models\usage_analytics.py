from sqlalchemy import Column, Integer, String, DateTime, Index, Text, Boolean, ForeignKey, ARRAY, Float
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class UserActivityLog(Base):
    """
    Model for tracking user activity and feature usage in the application.

    This model stores various types of user interactions including page views,
    feature usage, API calls, and report generation events.
    """
    __tablename__ = "user_activity_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    # Note: We're not using ForeignKey here to avoid issues if the users table doesn't exist
    # In a production environment, you would want to use ForeignKey("users.user_id")
    user_id = Column(String(255), nullable=True,
                    comment="Firebase UID of the user, nullable for anonymous users")
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False,
                      comment="When the activity occurred")
    event_type = Column(String(50), nullable=False,
                       comment="Type of activity: PAGE_VIEW, FEATURE_USE, API_CALL, REPORT_GENERATED, etc.")
    details = Column(JSONB, nullable=True,
                    comment="JSON details about the activity (page path, feature name, parameters, etc.)")
    session_id = Column(String(255), nullable=True,
                       comment="Browser session ID to track user journey")

    # Enhanced tracking fields
    duration_seconds = Column(Float, nullable=True,
                           comment="Duration of the activity in seconds (e.g., time spent on page)")
    component_id = Column(String(255), nullable=True,
                        comment="ID of the UI component interacted with")
    previous_page = Column(String(255), nullable=True,
                         comment="Previous page in the user journey")
    browser_info = Column(JSONB, nullable=True,
                        comment="Browser and device information")
    interaction_depth = Column(Integer, nullable=True,
                             comment="Depth of interaction (e.g., clicks, scrolls)")
    conversion_event = Column(Boolean, default=False,
                            comment="Whether this activity represents a conversion event")
    user_role = Column(String(50), nullable=True,
                     comment="Role of the user at the time of the activity")

    # Create indexes for efficient querying
    __table_args__ = (
        Index('idx_user_activity_user_id', 'user_id'),
        Index('idx_user_activity_timestamp', 'timestamp'),
        Index('idx_user_activity_event_type', 'event_type'),
        Index('idx_user_activity_user_timestamp', 'user_id', 'timestamp'),
        Index('idx_user_activity_session_id', 'session_id'),
        Index('idx_user_activity_component_id', 'component_id'),
        Index('idx_user_activity_conversion_event', 'conversion_event'),
        {'comment': 'Logs of user activity for analytics purposes'}
    )

    def __repr__(self):
        return f"<UserActivityLog(id={self.id}, user_id={self.user_id}, event_type={self.event_type})>"

class ScheduledReport(Base):
    """
    Model for tracking scheduled reports generated by the system.
    """
    __tablename__ = "scheduled_reports"

    id = Column(Integer, primary_key=True, autoincrement=True)
    report_type = Column(String(50), nullable=False,
                       comment="Type of report: usage_analytics, inventory, kpi, etc.")
    period = Column(String(20), nullable=False,
                  comment="Period covered by the report: daily, weekly, monthly, custom")
    start_date = Column(DateTime(timezone=True), nullable=False,
                      comment="Start date of the report period")
    end_date = Column(DateTime(timezone=True), nullable=False,
                    comment="End date of the report period")
    format = Column(String(20), nullable=False,
                  comment="Format of the report: csv, excel, pdf, etc.")
    file_path = Column(String(255), nullable=True,
                     comment="Path to the generated report file")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False,
                      comment="When the report was generated")
    sent_to = Column(ARRAY(String), nullable=True,
                   comment="List of email addresses the report was sent to")
    status = Column(String(20), default="pending", nullable=False,
                  comment="Status of the report: pending, generated, sent, failed")

    # Create indexes for efficient querying
    __table_args__ = (
        Index('idx_scheduled_reports_report_type', 'report_type'),
        Index('idx_scheduled_reports_period', 'period'),
        Index('idx_scheduled_reports_created_at', 'created_at'),
        Index('idx_scheduled_reports_status', 'status'),
        {'comment': 'Scheduled reports generated by the system'}
    )

    def __repr__(self):
        return f"<ScheduledReport(id={self.id}, report_type={self.report_type}, period={self.period})>"

class UserNotificationPreferences(Base):
    """
    Model for storing user notification preferences.
    """
    __tablename__ = "user_notification_preferences"

    user_id = Column(String(255), primary_key=True,
                    comment="Firebase UID of the user")
    receive_usage_reports = Column(Boolean, default=False,
                                 comment="Whether the user wants to receive usage reports")
    receive_inventory_reports = Column(Boolean, default=False,
                                     comment="Whether the user wants to receive inventory reports")
    receive_kpi_reports = Column(Boolean, default=False,
                               comment="Whether the user wants to receive KPI reports")
    report_frequency = Column(String(20), default="weekly",
                            comment="Frequency of reports: daily, weekly, monthly")
    email_format = Column(String(20), default="html",
                        comment="Preferred email format: html, text")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False,
                      comment="When the preferences were created")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False,
                      comment="When the preferences were last updated")

    def __repr__(self):
        return f"<UserNotificationPreferences(user_id={self.user_id})>"
