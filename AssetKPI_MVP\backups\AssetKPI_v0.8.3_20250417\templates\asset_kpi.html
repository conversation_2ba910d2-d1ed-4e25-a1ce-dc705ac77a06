{% extends "layout.html" %}

{% block title %}Asset KPIs | AssetKPI{% endblock %}

{% block styles %}
<style>
    .asset-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
    .chart-container {
        height: 300px;
        margin-bottom: 20px;
    }
    .metric-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
        height: 100%;
    }
    .metric-card:hover {
        transform: translateY(-5px);
    }
    .metric-value {
        font-size: 24px;
        font-weight: bold;
    }
    .metric-unit {
        font-size: 14px;
        color: #6c757d;
    }
    .status-badge {
        font-size: 1rem;
        padding: 5px 10px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    .status-maintenance {
        background-color: #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/assets">Assets</a></li>
                <li class="breadcrumb-item"><a href="/assets/{{ asset.assetid }}">{{ asset.assetname }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">KPIs</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Asset Header -->
<div class="asset-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>{{ asset.assetname }} - KPIs</h1>
            <p class="text-muted">{{ asset.assettype }} | {{ asset.manufacturer }} {{ asset.model }}</p>
            <div class="d-flex align-items-center mt-2">
                <span class="badge status-{{ asset.status|lower if asset.status else 'inactive' }} status-badge me-2">{{ asset.status }}</span>
                <span class="text-muted">Serial: {{ asset.serialnumber }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Asset Navigation -->
<ul class="nav nav-pills mb-4">
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}">Overview</a>
    </li>
    <li class="nav-item">
        <a class="nav-link active" href="/assets/{{ asset.assetid }}/kpi">KPIs</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/maintenance">Maintenance</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/specifications">Specifications</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/documents">Documents</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/meters">Meters</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/pm">PM Schedules</a>
    </li>
</ul>

<!-- KPI Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card metric-card">
            <div class="card-body text-center">
                <h5 class="card-title">MTBF</h5>
                <p class="metric-value">87.5</p>
                <p class="metric-unit">hours</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card">
            <div class="card-body text-center">
                <h5 class="card-title">MTTR</h5>
                <p class="metric-value">4.2</p>
                <p class="metric-unit">hours</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card">
            <div class="card-body text-center">
                <h5 class="card-title">Availability</h5>
                <p class="metric-value">95.4%</p>
                <p class="metric-unit">uptime</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card">
            <div class="card-body text-center">
                <h5 class="card-title">Reliability</h5>
                <p class="metric-value">92.8%</p>
                <p class="metric-unit">reliability</p>
            </div>
        </div>
    </div>
</div>

<!-- KPI Charts -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5>MTBF & MTTR Trend</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="mtbfMttrChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5>Availability Trend</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="availabilityChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5>Failure Rate</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="failureRateChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5>Maintenance Cost</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="maintenanceCostChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPI Data Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>KPI History</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>MTBF (hours)</th>
                                <th>MTTR (hours)</th>
                                <th>Availability (%)</th>
                                <th>Reliability (%)</th>
                                <th>Failure Rate</th>
                                <th>Maintenance Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2023-12-01</td>
                                <td>87.5</td>
                                <td>4.2</td>
                                <td>95.4%</td>
                                <td>92.8%</td>
                                <td>0.011</td>
                                <td>$1,245.00</td>
                            </tr>
                            <tr>
                                <td>2023-11-01</td>
                                <td>85.2</td>
                                <td>4.5</td>
                                <td>94.9%</td>
                                <td>91.5%</td>
                                <td>0.012</td>
                                <td>$1,320.00</td>
                            </tr>
                            <tr>
                                <td>2023-10-01</td>
                                <td>82.8</td>
                                <td>4.8</td>
                                <td>94.5%</td>
                                <td>90.2%</td>
                                <td>0.012</td>
                                <td>$1,450.00</td>
                            </tr>
                            <tr>
                                <td>2023-09-01</td>
                                <td>80.1</td>
                                <td>5.1</td>
                                <td>94.0%</td>
                                <td>89.5%</td>
                                <td>0.013</td>
                                <td>$1,520.00</td>
                            </tr>
                            <tr>
                                <td>2023-08-01</td>
                                <td>78.5</td>
                                <td>5.3</td>
                                <td>93.7%</td>
                                <td>88.9%</td>
                                <td>0.013</td>
                                <td>$1,580.00</td>
                            </tr>
                            <tr>
                                <td>2023-07-01</td>
                                <td>76.2</td>
                                <td>5.5</td>
                                <td>93.2%</td>
                                <td>88.1%</td>
                                <td>0.014</td>
                                <td>$1,620.00</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sample data for charts
        const months = ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        // MTBF & MTTR Chart
        const mtbfMttrCtx = document.getElementById('mtbfMttrChart').getContext('2d');
        const mtbfMttrChart = new Chart(mtbfMttrCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [
                    {
                        label: 'MTBF (hours)',
                        data: [76.2, 78.5, 80.1, 82.8, 85.2, 87.5],
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: 'MTTR (hours)',
                        data: [5.5, 5.3, 5.1, 4.8, 4.5, 4.2],
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'MTBF (hours)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'MTTR (hours)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });

        // Availability Chart
        const availabilityCtx = document.getElementById('availabilityChart').getContext('2d');
        const availabilityChart = new Chart(availabilityCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [
                    {
                        label: 'Availability (%)',
                        data: [93.2, 93.7, 94.0, 94.5, 94.9, 95.4],
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.4
                    },
                    {
                        label: 'Reliability (%)',
                        data: [88.1, 88.9, 89.5, 90.2, 91.5, 92.8],
                        borderColor: 'rgba(153, 102, 255, 1)',
                        backgroundColor: 'rgba(153, 102, 255, 0.2)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        min: 85,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Percentage (%)'
                        }
                    }
                }
            }
        });

        // Failure Rate Chart
        const failureRateCtx = document.getElementById('failureRateChart').getContext('2d');
        const failureRateChart = new Chart(failureRateCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [
                    {
                        label: 'Failure Rate',
                        data: [0.014, 0.013, 0.013, 0.012, 0.012, 0.011],
                        borderColor: 'rgba(255, 159, 64, 1)',
                        backgroundColor: 'rgba(255, 159, 64, 0.2)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: 'Failures per hour'
                        }
                    }
                }
            }
        });

        // Maintenance Cost Chart
        const maintenanceCostCtx = document.getElementById('maintenanceCostChart').getContext('2d');
        const maintenanceCostChart = new Chart(maintenanceCostCtx, {
            type: 'bar',
            data: {
                labels: months,
                datasets: [
                    {
                        label: 'Maintenance Cost ($)',
                        data: [1620, 1580, 1520, 1450, 1320, 1245],
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: 'Cost ($)'
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
