/**
 * Form Validation Utilities
 * 
 * This module provides utilities for validating form data.
 */

/**
 * Validation rules for common field types.
 */
const ValidationRules = {
  /**
   * Required field validation.
   * 
   * @param {any} value - Field value
   * @returns {boolean} - Whether the value is valid
   */
  required: {
    validate: (value) => {
      if (value === undefined || value === null) {
        return false;
      }
      
      if (typeof value === 'string') {
        return value.trim() !== '';
      }
      
      return true;
    },
    message: 'This field is required'
  },
  
  /**
   * Minimum length validation.
   * 
   * @param {string} value - Field value
   * @param {Object} params - Validation parameters
   * @param {number} params.min - Minimum length
   * @returns {boolean} - Whether the value is valid
   */
  minLength: {
    validate: (value, params) => {
      if (!value) {
        return true; // Skip validation if value is empty (use required rule for that)
      }
      
      return String(value).length >= params.min;
    },
    message: 'Must be at least {min} characters'
  },
  
  /**
   * Maximum length validation.
   * 
   * @param {string} value - Field value
   * @param {Object} params - Validation parameters
   * @param {number} params.max - Maximum length
   * @returns {boolean} - Whether the value is valid
   */
  maxLength: {
    validate: (value, params) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      return String(value).length <= params.max;
    },
    message: 'Must be no more than {max} characters'
  },
  
  /**
   * Numeric value validation.
   * 
   * @param {any} value - Field value
   * @returns {boolean} - Whether the value is valid
   */
  numeric: {
    validate: (value) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      return !isNaN(parseFloat(value)) && isFinite(value);
    },
    message: 'Must be a number'
  },
  
  /**
   * Minimum value validation.
   * 
   * @param {number} value - Field value
   * @param {Object} params - Validation parameters
   * @param {number} params.min - Minimum value
   * @returns {boolean} - Whether the value is valid
   */
  min: {
    validate: (value, params) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      return parseFloat(value) >= params.min;
    },
    message: 'Must be at least {min}'
  },
  
  /**
   * Maximum value validation.
   * 
   * @param {number} value - Field value
   * @param {Object} params - Validation parameters
   * @param {number} params.max - Maximum value
   * @returns {boolean} - Whether the value is valid
   */
  max: {
    validate: (value, params) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      return parseFloat(value) <= params.max;
    },
    message: 'Must be no more than {max}'
  },
  
  /**
   * Pattern validation.
   * 
   * @param {string} value - Field value
   * @param {Object} params - Validation parameters
   * @param {RegExp} params.pattern - Regular expression pattern
   * @returns {boolean} - Whether the value is valid
   */
  pattern: {
    validate: (value, params) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      return params.pattern.test(String(value));
    },
    message: 'Must match the required format'
  },
  
  /**
   * Email validation.
   * 
   * @param {string} value - Field value
   * @returns {boolean} - Whether the value is valid
   */
  email: {
    validate: (value) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      // Simple email validation pattern
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return pattern.test(String(value).toLowerCase());
    },
    message: 'Must be a valid email address'
  },
  
  /**
   * Date validation.
   * 
   * @param {string} value - Field value
   * @returns {boolean} - Whether the value is valid
   */
  date: {
    validate: (value) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      return !isNaN(Date.parse(value));
    },
    message: 'Must be a valid date'
  },
  
  /**
   * Minimum date validation.
   * 
   * @param {string} value - Field value
   * @param {Object} params - Validation parameters
   * @param {string} params.min - Minimum date
   * @returns {boolean} - Whether the value is valid
   */
  minDate: {
    validate: (value, params) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      const date = new Date(value);
      const minDate = new Date(params.min);
      
      return date >= minDate;
    },
    message: 'Must be on or after {min}'
  },
  
  /**
   * Maximum date validation.
   * 
   * @param {string} value - Field value
   * @param {Object} params - Validation parameters
   * @param {string} params.max - Maximum date
   * @returns {boolean} - Whether the value is valid
   */
  maxDate: {
    validate: (value, params) => {
      if (!value) {
        return true; // Skip validation if value is empty
      }
      
      const date = new Date(value);
      const maxDate = new Date(params.max);
      
      return date <= maxDate;
    },
    message: 'Must be on or before {max}'
  },
  
  /**
   * Equal to validation.
   * 
   * @param {any} value - Field value
   * @param {Object} params - Validation parameters
   * @param {any} params.value - Value to compare
   * @returns {boolean} - Whether the value is valid
   */
  equalTo: {
    validate: (value, params) => {
      return value === params.value;
    },
    message: 'Must be equal to {value}'
  },
  
  /**
   * Custom validation.
   * 
   * @param {any} value - Field value
   * @param {Object} params - Validation parameters
   * @param {Function} params.validate - Custom validation function
   * @returns {boolean} - Whether the value is valid
   */
  custom: {
    validate: (value, params) => {
      return params.validate(value);
    },
    message: 'Invalid value'
  }
};

/**
 * Validate a field against a set of rules.
 * 
 * @param {any} value - Field value
 * @param {Array} rules - Validation rules
 * @param {Object} formData - Form data (for cross-field validation)
 * @returns {Array} - Array of error messages (empty if valid)
 */
function validateField(value, rules, formData = {}) {
  const errors = [];
  
  for (const ruleConfig of rules) {
    const rule = ValidationRules[ruleConfig.rule];
    
    if (!rule) {
      console.warn(`Unknown validation rule: ${ruleConfig.rule}`);
      continue;
    }
    
    const isValid = rule.validate(value, ruleConfig.params, formData);
    
    if (!isValid) {
      let message = ruleConfig.message || rule.message;
      
      // Replace parameters in message
      if (ruleConfig.params) {
        for (const [key, val] of Object.entries(ruleConfig.params)) {
          message = message.replace(`{${key}}`, val);
        }
      }
      
      errors.push(message);
    }
  }
  
  return errors;
}

/**
 * Validate a form against a set of field configurations.
 * 
 * @param {Object} formData - Form data
 * @param {Array} fieldConfigs - Field configurations
 * @returns {Object} - Validation result
 * @returns {boolean} result.valid - Whether the form is valid
 * @returns {Object} result.errors - Validation errors by field
 */
function validateForm(formData, fieldConfigs) {
  const errors = {};
  
  for (const fieldConfig of fieldConfigs) {
    const fieldName = fieldConfig.name;
    const fieldValue = formData[fieldName];
    const fieldRules = fieldConfig.rules || [];
    
    const fieldErrors = validateField(fieldValue, fieldRules, formData);
    
    if (fieldErrors.length > 0) {
      errors[fieldName] = fieldErrors;
    }
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Display validation errors on a form.
 * 
 * @param {HTMLFormElement} form - Form element
 * @param {Object} errors - Validation errors by field
 * @param {Object} options - Display options
 * @param {boolean} options.showSummary - Whether to show error summary
 */
function displayErrors(form, errors, options = {}) {
  const showSummary = options.showSummary !== undefined ? options.showSummary : true;
  
  // Clear previous errors
  clearErrors(form);
  
  // Display error summary
  if (showSummary && Object.keys(errors).length > 0) {
    const errorSummary = createErrorSummary(form, errors);
    form.insertBefore(errorSummary, form.firstChild);
  }
  
  // Display field errors
  for (const [fieldName, fieldErrors] of Object.entries(errors)) {
    const fieldElement = form.querySelector(`[name="${fieldName}"]`);
    
    if (fieldElement) {
      // Add error class to field
      fieldElement.classList.add('is-invalid');
      
      // Add error message
      const errorElement = document.createElement('div');
      errorElement.className = 'invalid-feedback';
      errorElement.textContent = fieldErrors[0]; // Show first error
      
      // Add error element after field
      const formGroup = fieldElement.closest('.form-group') || fieldElement.parentNode;
      formGroup.appendChild(errorElement);
      
      // Set aria attributes
      fieldElement.setAttribute('aria-invalid', 'true');
      fieldElement.setAttribute('aria-describedby', `${fieldName}-error`);
      errorElement.id = `${fieldName}-error`;
    }
  }
  
  // Focus first field with error
  const firstErrorField = form.querySelector('.is-invalid');
  if (firstErrorField) {
    firstErrorField.focus();
  }
}

/**
 * Clear validation errors from a form.
 * 
 * @param {HTMLFormElement} form - Form element
 */
function clearErrors(form) {
  // Remove error summary
  const errorSummary = form.querySelector('.alert-danger');
  if (errorSummary) {
    errorSummary.remove();
  }
  
  // Remove field errors
  const invalidFields = form.querySelectorAll('.is-invalid');
  invalidFields.forEach(field => {
    field.classList.remove('is-invalid');
    field.removeAttribute('aria-invalid');
    field.removeAttribute('aria-describedby');
  });
  
  // Remove error messages
  const errorMessages = form.querySelectorAll('.invalid-feedback');
  errorMessages.forEach(message => {
    message.remove();
  });
}

/**
 * Create an error summary element.
 * 
 * @param {HTMLFormElement} form - Form element
 * @param {Object} errors - Validation errors by field
 * @returns {HTMLElement} - Error summary element
 */
function createErrorSummary(form, errors) {
  const errorSummary = document.createElement('div');
  errorSummary.className = 'alert alert-danger';
  errorSummary.setAttribute('role', 'alert');
  
  const heading = document.createElement('h5');
  heading.textContent = 'Please correct the following errors:';
  errorSummary.appendChild(heading);
  
  const errorList = document.createElement('ul');
  
  for (const [fieldName, fieldErrors] of Object.entries(errors)) {
    const fieldElement = form.querySelector(`[name="${fieldName}"]`);
    const fieldLabel = getFieldLabel(form, fieldName, fieldElement);
    
    for (const error of fieldErrors) {
      const errorItem = document.createElement('li');
      
      // Create link to field
      const errorLink = document.createElement('a');
      errorLink.href = '#';
      errorLink.textContent = `${fieldLabel}: ${error}`;
      
      // Add click event to focus field
      errorLink.addEventListener('click', (event) => {
        event.preventDefault();
        if (fieldElement) {
          fieldElement.focus();
        }
      });
      
      errorItem.appendChild(errorLink);
      errorList.appendChild(errorItem);
    }
  }
  
  errorSummary.appendChild(errorList);
  
  return errorSummary;
}

/**
 * Get the label for a field.
 * 
 * @param {HTMLFormElement} form - Form element
 * @param {string} fieldName - Field name
 * @param {HTMLElement} fieldElement - Field element
 * @returns {string} - Field label
 */
function getFieldLabel(form, fieldName, fieldElement) {
  // Try to find label by for attribute
  if (fieldElement) {
    const labelElement = form.querySelector(`label[for="${fieldElement.id}"]`);
    if (labelElement) {
      return labelElement.textContent;
    }
  }
  
  // Try to find label in parent form-group
  if (fieldElement) {
    const formGroup = fieldElement.closest('.form-group');
    if (formGroup) {
      const labelElement = formGroup.querySelector('label');
      if (labelElement) {
        return labelElement.textContent;
      }
    }
  }
  
  // Fallback to field name
  return fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
}

/**
 * Add validation to a form.
 * 
 * @param {HTMLFormElement} form - Form element
 * @param {Array} fieldConfigs - Field configurations
 * @param {Object} options - Validation options
 * @param {boolean} options.validateOnInput - Whether to validate on input
 * @param {boolean} options.validateOnBlur - Whether to validate on blur
 * @param {boolean} options.validateOnSubmit - Whether to validate on submit
 * @param {Function} options.onSubmit - Submit handler
 */
function addFormValidation(form, fieldConfigs, options = {}) {
  const validateOnInput = options.validateOnInput !== undefined ? options.validateOnInput : false;
  const validateOnBlur = options.validateOnBlur !== undefined ? options.validateOnBlur : true;
  const validateOnSubmit = options.validateOnSubmit !== undefined ? options.validateOnSubmit : true;
  const onSubmit = options.onSubmit || null;
  
  // Add input event listeners
  if (validateOnInput) {
    for (const fieldConfig of fieldConfigs) {
      const fieldElement = form.querySelector(`[name="${fieldConfig.name}"]`);
      
      if (fieldElement) {
        fieldElement.addEventListener('input', () => {
          const fieldValue = getFieldValue(fieldElement);
          const fieldErrors = validateField(fieldValue, fieldConfig.rules || []);
          
          if (fieldErrors.length > 0) {
            displayFieldError(fieldElement, fieldErrors[0]);
          } else {
            clearFieldError(fieldElement);
          }
        });
      }
    }
  }
  
  // Add blur event listeners
  if (validateOnBlur) {
    for (const fieldConfig of fieldConfigs) {
      const fieldElement = form.querySelector(`[name="${fieldConfig.name}"]`);
      
      if (fieldElement) {
        fieldElement.addEventListener('blur', () => {
          const fieldValue = getFieldValue(fieldElement);
          const fieldErrors = validateField(fieldValue, fieldConfig.rules || []);
          
          if (fieldErrors.length > 0) {
            displayFieldError(fieldElement, fieldErrors[0]);
          } else {
            clearFieldError(fieldElement);
          }
        });
      }
    }
  }
  
  // Add submit event listener
  if (validateOnSubmit) {
    form.addEventListener('submit', (event) => {
      // Get form data
      const formData = getFormData(form);
      
      // Validate form
      const result = validateForm(formData, fieldConfigs);
      
      if (!result.valid) {
        event.preventDefault();
        displayErrors(form, result.errors);
        return false;
      }
      
      // Call submit handler
      if (onSubmit) {
        event.preventDefault();
        onSubmit(formData);
        return false;
      }
      
      return true;
    });
  }
}

/**
 * Get the value of a field.
 * 
 * @param {HTMLElement} fieldElement - Field element
 * @returns {any} - Field value
 */
function getFieldValue(fieldElement) {
  if (fieldElement.type === 'checkbox') {
    return fieldElement.checked;
  } else if (fieldElement.type === 'radio') {
    const checkedRadio = document.querySelector(`input[name="${fieldElement.name}"]:checked`);
    return checkedRadio ? checkedRadio.value : null;
  } else if (fieldElement.type === 'select-multiple') {
    const selectedOptions = Array.from(fieldElement.options)
      .filter(option => option.selected)
      .map(option => option.value);
    return selectedOptions;
  } else {
    return fieldElement.value;
  }
}

/**
 * Get form data from a form.
 * 
 * @param {HTMLFormElement} form - Form element
 * @returns {Object} - Form data
 */
function getFormData(form) {
  const formData = {};
  
  // Get form elements
  const elements = form.elements;
  
  for (let i = 0; i < elements.length; i++) {
    const element = elements[i];
    
    // Skip elements without name
    if (!element.name) {
      continue;
    }
    
    // Skip buttons
    if (element.type === 'button' || element.type === 'submit' || element.type === 'reset') {
      continue;
    }
    
    // Handle different element types
    if (element.type === 'checkbox') {
      formData[element.name] = element.checked;
    } else if (element.type === 'radio') {
      if (element.checked) {
        formData[element.name] = element.value;
      }
    } else if (element.type === 'select-multiple') {
      const selectedOptions = Array.from(element.options)
        .filter(option => option.selected)
        .map(option => option.value);
      formData[element.name] = selectedOptions;
    } else {
      formData[element.name] = element.value;
    }
  }
  
  return formData;
}

/**
 * Display an error for a field.
 * 
 * @param {HTMLElement} fieldElement - Field element
 * @param {string} error - Error message
 */
function displayFieldError(fieldElement, error) {
  // Add error class to field
  fieldElement.classList.add('is-invalid');
  
  // Get form group
  const formGroup = fieldElement.closest('.form-group') || fieldElement.parentNode;
  
  // Remove existing error message
  const existingError = formGroup.querySelector('.invalid-feedback');
  if (existingError) {
    existingError.remove();
  }
  
  // Add error message
  const errorElement = document.createElement('div');
  errorElement.className = 'invalid-feedback';
  errorElement.textContent = error;
  
  // Add error element after field
  formGroup.appendChild(errorElement);
  
  // Set aria attributes
  fieldElement.setAttribute('aria-invalid', 'true');
  fieldElement.setAttribute('aria-describedby', `${fieldElement.name}-error`);
  errorElement.id = `${fieldElement.name}-error`;
}

/**
 * Clear an error for a field.
 * 
 * @param {HTMLElement} fieldElement - Field element
 */
function clearFieldError(fieldElement) {
  // Remove error class from field
  fieldElement.classList.remove('is-invalid');
  
  // Get form group
  const formGroup = fieldElement.closest('.form-group') || fieldElement.parentNode;
  
  // Remove error message
  const errorElement = formGroup.querySelector('.invalid-feedback');
  if (errorElement) {
    errorElement.remove();
  }
  
  // Remove aria attributes
  fieldElement.removeAttribute('aria-invalid');
  fieldElement.removeAttribute('aria-describedby');
}

// Export validation utilities
export {
  ValidationRules,
  validateField,
  validateForm,
  displayErrors,
  clearErrors,
  addFormValidation,
  getFormData
};
