/**
 * Mobile Responsiveness JavaScript
 * 
 * This file contains JavaScript functionality specifically for enhancing
 * mobile responsiveness across the AssetKPI application.
 */

/**
 * Mobile utility functions
 */
const MobileUtils = {
  /**
   * Check if the current device is mobile
   */
  isMobile: function() {
    return window.innerWidth <= 767.98;
  },

  /**
   * Check if the current device is tablet
   */
  isTablet: function() {
    return window.innerWidth > 767.98 && window.innerWidth <= 991.98;
  },

  /**
   * Check if the current device is desktop
   */
  isDesktop: function() {
    return window.innerWidth > 991.98;
  },

  /**
   * Get current breakpoint
   */
  getBreakpoint: function() {
    if (this.isMobile()) return 'mobile';
    if (this.isTablet()) return 'tablet';
    return 'desktop';
  },

  /**
   * Set body class based on current breakpoint
   */
  setBodyBreakpointClass: function() {
    const body = document.body;
    body.classList.remove('is-mobile', 'is-tablet', 'is-desktop');
    body.classList.add(`is-${this.getBreakpoint()}`);
  }
};

/**
 * Mobile navigation functionality
 */
const MobileNavigation = {
  init: function() {
    this.setupMobileToggle();
    this.setupDropdowns();
  },

  setupMobileToggle: function() {
    const toggleButton = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (toggleButton && navbarCollapse) {
      toggleButton.addEventListener('click', function() {
        navbarCollapse.classList.toggle('show');
        toggleButton.classList.toggle('active');
      });

      // Close mobile menu when clicking outside
      document.addEventListener('click', function(e) {
        if (!toggleButton.contains(e.target) && !navbarCollapse.contains(e.target)) {
          navbarCollapse.classList.remove('show');
          toggleButton.classList.remove('active');
        }
      });
    }
  },

  setupDropdowns: function() {
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
      toggle.addEventListener('click', function(e) {
        if (MobileUtils.isMobile()) {
          e.preventDefault();
          const dropdown = this.nextElementSibling;
          if (dropdown) {
            dropdown.classList.toggle('show');
          }
        }
      });
    });
  }
};

/**
 * Mobile sidebar functionality
 */
const MobileSidebar = {
  init: function() {
    this.setupSidebarToggle();
    this.setupOverlay();
  },

  setupSidebarToggle: function() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');

    if (sidebarToggle && sidebar) {
      sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('show');
        document.body.classList.toggle('sidebar-open');
        
        // Show/hide overlay
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
          overlay.classList.toggle('show');
        }
      });
    }
  },

  setupOverlay: function() {
    const overlay = document.querySelector('.sidebar-overlay');
    const sidebar = document.querySelector('.sidebar');

    if (overlay && sidebar) {
      overlay.addEventListener('click', function() {
        sidebar.classList.remove('show');
        document.body.classList.remove('sidebar-open');
        overlay.classList.remove('show');
      });
    }
  }
};

/**
 * Responsive tables functionality
 */
const ResponsiveTables = {
  init: function() {
    this.makeTablesResponsive();
    this.setupStackedTables();
  },

  makeTablesResponsive: function() {
    const tables = document.querySelectorAll('table:not(.table-mobile-stack)');
    
    tables.forEach(table => {
      if (!table.closest('.table-responsive')) {
        const wrapper = document.createElement('div');
        wrapper.className = 'table-responsive';
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
      }
    });
  },

  setupStackedTables: function() {
    const stackedTables = document.querySelectorAll('.table-mobile-stack');
    
    stackedTables.forEach(table => {
      const headers = table.querySelectorAll('thead th');
      const rows = table.querySelectorAll('tbody tr');
      
      rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, index) => {
          if (headers[index]) {
            cell.setAttribute('data-label', headers[index].textContent);
          }
        });
      });
    });
  }
};

/**
 * Touch enhancements for mobile devices
 */
const TouchEnhancements = {
  init: function() {
    this.setupTouchTargets();
    this.setupSwipeGestures();
  },

  setupTouchTargets: function() {
    // Ensure minimum touch target size
    const touchElements = document.querySelectorAll('button, .btn, a, input, select');
    
    touchElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      if (rect.width < 44 || rect.height < 44) {
        element.style.minWidth = '44px';
        element.style.minHeight = '44px';
      }
    });
  },

  setupSwipeGestures: function() {
    let startX = 0;
    let startY = 0;
    let endX = 0;
    let endY = 0;

    document.addEventListener('touchstart', function(e) {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    });

    document.addEventListener('touchend', function(e) {
      endX = e.changedTouches[0].clientX;
      endY = e.changedTouches[0].clientY;
      
      const deltaX = endX - startX;
      const deltaY = endY - startY;
      
      // Detect swipe gestures
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        if (deltaX > 0) {
          // Swipe right - could open sidebar
          document.dispatchEvent(new CustomEvent('swipeRight'));
        } else {
          // Swipe left - could close sidebar
          document.dispatchEvent(new CustomEvent('swipeLeft'));
        }
      }
    });

    // Handle swipe events for sidebar
    document.addEventListener('swipeRight', function() {
      if (MobileUtils.isMobile()) {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar && !sidebar.classList.contains('show')) {
          sidebar.classList.add('show');
          document.body.classList.add('sidebar-open');
        }
      }
    });

    document.addEventListener('swipeLeft', function() {
      if (MobileUtils.isMobile()) {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar && sidebar.classList.contains('show')) {
          sidebar.classList.remove('show');
          document.body.classList.remove('sidebar-open');
        }
      }
    });
  }
};

/**
 * Mobile chart enhancements
 */
const MobileCharts = {
  init: function() {
    this.adjustChartSizes();
    this.setupChartInteractions();
  },

  adjustChartSizes: function() {
    if (MobileUtils.isMobile()) {
      const chartContainers = document.querySelectorAll('.chart-container');
      
      chartContainers.forEach(container => {
        container.style.height = '250px';
        
        // If Chart.js is available, resize charts
        if (window.Chart) {
          const canvas = container.querySelector('canvas');
          if (canvas && canvas.chart) {
            canvas.chart.resize();
          }
        }
      });
    }
  },

  setupChartInteractions: function() {
    // Add touch-friendly chart interactions
    const charts = document.querySelectorAll('canvas');
    
    charts.forEach(chart => {
      chart.addEventListener('touchstart', function(e) {
        e.preventDefault(); // Prevent scrolling when interacting with chart
      });
    });
  }
};

/**
 * Mobile form enhancements
 */
const MobileForms = {
  init: function() {
    this.setupFormValidation();
    this.setupInputEnhancements();
  },

  setupFormValidation: function() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
      form.addEventListener('submit', function(e) {
        if (MobileUtils.isMobile()) {
          // Scroll to first error on mobile
          const firstError = form.querySelector('.is-invalid');
          if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }
      });
    });
  },

  setupInputEnhancements: function() {
    // Auto-zoom prevention for iOS
    const inputs = document.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
      if (input.style.fontSize === '' || parseFloat(input.style.fontSize) < 16) {
        input.style.fontSize = '16px';
      }
    });
  }
};

/**
 * Initialize all mobile enhancements
 */
document.addEventListener('DOMContentLoaded', function() {
  // Set body breakpoint class
  MobileUtils.setBodyBreakpointClass();
  
  // Initialize mobile components
  MobileNavigation.init();
  MobileSidebar.init();
  ResponsiveTables.init();
  TouchEnhancements.init();
  MobileCharts.init();
  MobileForms.init();
  
  // Update on resize
  window.addEventListener('resize', function() {
    MobileUtils.setBodyBreakpointClass();
    
    // Reinitialize components that need to be updated on resize
    ResponsiveTables.init();
    MobileCharts.init();
  });
});

// Export for use in other modules
window.MobileUtils = MobileUtils;
window.MobileNavigation = MobileNavigation;
window.MobileSidebar = MobileSidebar;
window.ResponsiveTables = ResponsiveTables;
window.TouchEnhancements = TouchEnhancements;
window.MobileCharts = MobileCharts;
window.MobileForms = MobileForms;
