/**
 * Mobile Responsiveness JavaScript
 * 
 * This file contains JavaScript functionality specifically for enhancing
 * mobile responsiveness across the AssetKPI application.
 */

/**
 * Mobile Responsiveness Utilities
 */
const MobileUtils = {
  /**
   * Check if the current device is mobile
   * @returns {boolean} True if the device is mobile
   */
  isMobileDevice: function() {
    return window.innerWidth < 768;
  },
  
  /**
   * Check if the device supports touch
   * @returns {boolean} True if the device supports touch
   */
  isTouchDevice: function() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },
  
  /**
   * Get the current breakpoint
   * @returns {string} Current breakpoint (xs, sm, md, lg, xl, xxl)
   */
  getCurrentBreakpoint: function() {
    const width = window.innerWidth;
    
    if (width < 576) return 'xs';
    if (width < 768) return 'sm';
    if (width < 992) return 'md';
    if (width < 1200) return 'lg';
    if (width < 1400) return 'xl';
    return 'xxl';
  },
  
  /**
   * Add a class to the body based on the current breakpoint
   */
  setBodyBreakpointClass: function() {
    const body = document.body;
    const breakpoint = this.getCurrentBreakpoint();
    
    // Remove existing breakpoint classes
    body.classList.remove('breakpoint-xs', 'breakpoint-sm', 'breakpoint-md', 'breakpoint-lg', 'breakpoint-xl', 'breakpoint-xxl');
    
    // Add current breakpoint class
    body.classList.add(`breakpoint-${breakpoint}`);
    
    // Add mobile/desktop class
    if (breakpoint === 'xs' || breakpoint === 'sm') {
      body.classList.add('is-mobile');
      body.classList.remove('is-desktop');
    } else {
      body.classList.add('is-desktop');
      body.classList.remove('is-mobile');
    }
  }
};

/**
 * Mobile Navigation
 */
const MobileNavigation = {
  /**
   * Initialize mobile navigation
   */
  init: function() {
    this.setupHamburgerMenu();
    this.setupMobileDropdowns();
  },
  
  /**
   * Setup hamburger menu functionality
   */
  setupHamburgerMenu: function() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    
    if (navbarToggler) {
      // Ensure navbar collapses when clicking outside
      document.addEventListener('click', function(event) {
        const navbar = document.querySelector('.navbar');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        
        if (navbarCollapse && navbarCollapse.classList.contains('show')) {
          // Check if click is outside navbar
          if (!navbar.contains(event.target) || event.target.classList.contains('nav-link')) {
            // Close navbar
            const bsCollapse = new bootstrap.Collapse(navbarCollapse);
            bsCollapse.hide();
          }
        }
      });
    }
  },
  
  /**
   * Setup mobile dropdown menus
   */
  setupMobileDropdowns: function() {
    // Make dropdowns work with touch
    if (MobileUtils.isTouchDevice()) {
      const dropdownToggleLinks = document.querySelectorAll('.dropdown-toggle');
      
      dropdownToggleLinks.forEach(link => {
        link.addEventListener('click', function(event) {
          if (MobileUtils.isMobileDevice()) {
            // First click shows dropdown, second click follows link
            if (!this.classList.contains('dropdown-clicked')) {
              event.preventDefault();
              this.classList.add('dropdown-clicked');
              
              // Remove class when dropdown is hidden
              const dropdown = this.closest('.dropdown');
              const dropdownMenu = dropdown.querySelector('.dropdown-menu');
              
              dropdownMenu.addEventListener('hidden.bs.dropdown', function() {
                link.classList.remove('dropdown-clicked');
              }, { once: true });
            }
          }
        });
      });
    }
  }
};

/**
 * Mobile Sidebar
 */
const MobileSidebar = {
  /**
   * Initialize mobile sidebar
   */
  init: function() {
    this.setupMobileSidebar();
  },
  
  /**
   * Setup mobile sidebar functionality
   */
  setupMobileSidebar: function() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle && sidebar) {
      // Create backdrop if it doesn't exist
      let backdrop = document.querySelector('.sidebar-backdrop');
      if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'sidebar-backdrop';
        document.body.appendChild(backdrop);
      }
      
      // Toggle sidebar
      sidebarToggle.addEventListener('click', function(event) {
        event.preventDefault();
        sidebar.classList.toggle('show');
        backdrop.classList.toggle('show');
        
        // Prevent scrolling when sidebar is open
        if (sidebar.classList.contains('show')) {
          document.body.style.overflow = 'hidden';
        } else {
          document.body.style.overflow = '';
        }
      });
      
      // Close sidebar when clicking backdrop
      backdrop.addEventListener('click', function() {
        sidebar.classList.remove('show');
        backdrop.classList.remove('show');
        document.body.style.overflow = '';
      });
      
      // Close sidebar when pressing escape
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && sidebar.classList.contains('show')) {
          sidebar.classList.remove('show');
          backdrop.classList.remove('show');
          document.body.style.overflow = '';
        }
      });
    }
  }
};

/**
 * Responsive Tables
 */
const ResponsiveTables = {
  /**
   * Initialize responsive tables
   */
  init: function() {
    this.setupCardViewTables();
    this.setupColumnToggleTables();
  },
  
  /**
   * Setup card view tables for mobile
   */
  setupCardViewTables: function() {
    const cardViewTables = document.querySelectorAll('.table-to-cards');
    
    cardViewTables.forEach(table => {
      if (MobileUtils.isMobileDevice()) {
        // Get header text
        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
        
        // Add data-label attribute to cells
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
          const cells = row.querySelectorAll('td');
          cells.forEach((cell, index) => {
            if (headers[index]) {
              cell.setAttribute('data-label', headers[index]);
            }
          });
        });
      }
    });
  },
  
  /**
   * Setup column toggle tables for mobile
   */
  setupColumnToggleTables: function() {
    const columnToggleTables = document.querySelectorAll('.table-column-toggle');
    
    columnToggleTables.forEach(table => {
      if (MobileUtils.isMobileDevice()) {
        // Create column toggle dropdown if it doesn't exist
        let toggleContainer = table.previousElementSibling;
        if (!toggleContainer || !toggleContainer.classList.contains('column-toggle-dropdown')) {
          toggleContainer = document.createElement('div');
          toggleContainer.className = 'column-toggle-dropdown dropdown';
          toggleContainer.innerHTML = `
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
              Toggle Columns
            </button>
            <div class="dropdown-menu">
              <!-- Column toggle options will be added here -->
            </div>
          `;
          table.parentNode.insertBefore(toggleContainer, table);
        }
        
        // Get dropdown menu
        const dropdownMenu = toggleContainer.querySelector('.dropdown-menu');
        dropdownMenu.innerHTML = ''; // Clear existing items
        
        // Add toggle options for each column
        const headers = table.querySelectorAll('thead th');
        headers.forEach((header, index) => {
          // Skip first column (usually ID or primary info)
          if (index === 0) return;
          
          const columnName = header.textContent.trim();
          const item = document.createElement('div');
          item.className = 'dropdown-item';
          item.innerHTML = `
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="toggle-column-${index}" ${index < 3 ? 'checked' : ''}>
              <label class="form-check-label" for="toggle-column-${index}">
                ${columnName}
              </label>
            </div>
          `;
          dropdownMenu.appendChild(item);
          
          // Add toggle functionality
          const checkbox = item.querySelector(`#toggle-column-${index}`);
          checkbox.addEventListener('change', function() {
            // Toggle column visibility
            const isChecked = this.checked;
            const columnCells = table.querySelectorAll(`tr > :nth-child(${index + 1})`);
            columnCells.forEach(cell => {
              cell.classList.toggle('toggle-column', !isChecked);
              cell.classList.toggle('show', isChecked);
            });
          });
          
          // Initialize column visibility
          if (index >= 3) {
            const columnCells = table.querySelectorAll(`tr > :nth-child(${index + 1})`);
            columnCells.forEach(cell => {
              cell.classList.add('toggle-column');
            });
          }
        });
      }
    });
  }
};

/**
 * Touch Enhancements
 */
const TouchEnhancements = {
  /**
   * Initialize touch enhancements
   */
  init: function() {
    if (MobileUtils.isTouchDevice()) {
      this.setupSwipeActions();
      this.setupPinchZoom();
      this.improveTouchTargets();
    }
  },
  
  /**
   * Setup swipe actions for mobile
   */
  setupSwipeActions: function() {
    // Example: Swipe to archive/delete in lists
    const listItems = document.querySelectorAll('.swipe-action-item');
    
    listItems.forEach(item => {
      let startX, moveX, startTime;
      const threshold = 100; // Minimum distance to trigger action
      
      item.addEventListener('touchstart', function(event) {
        startX = event.touches[0].clientX;
        startTime = new Date().getTime();
      });
      
      item.addEventListener('touchmove', function(event) {
        moveX = event.touches[0].clientX;
        const diffX = moveX - startX;
        
        // Only allow horizontal swiping
        if (Math.abs(diffX) > 10) {
          event.preventDefault();
          
          // Limit swipe distance
          const translateX = Math.min(Math.max(diffX, -150), 150);
          this.style.transform = `translateX(${translateX}px)`;
        }
      });
      
      item.addEventListener('touchend', function() {
        const endTime = new Date().getTime();
        const diffX = moveX - startX;
        const diffTime = endTime - startTime;
        
        // Reset position with animation
        this.style.transition = 'transform 0.3s ease';
        
        // Check if swipe was fast and far enough
        if (Math.abs(diffX) > threshold && diffTime < 300) {
          if (diffX > 0) {
            // Swipe right action (e.g., archive)
            this.classList.add('swiped-right');
            // Trigger action after animation
            setTimeout(() => {
              // Perform action here
              this.style.transition = '';
              this.style.transform = '';
              this.classList.remove('swiped-right');
            }, 300);
          } else {
            // Swipe left action (e.g., delete)
            this.classList.add('swiped-left');
            // Trigger action after animation
            setTimeout(() => {
              // Perform action here
              this.style.transition = '';
              this.style.transform = '';
              this.classList.remove('swiped-left');
            }, 300);
          }
        } else {
          // Reset position
          this.style.transform = '';
          setTimeout(() => {
            this.style.transition = '';
          }, 300);
        }
      });
    });
  },
  
  /**
   * Setup pinch zoom for charts and images
   */
  setupPinchZoom: function() {
    const zoomableElements = document.querySelectorAll('.zoomable');
    
    zoomableElements.forEach(element => {
      let startDist = 0;
      let scale = 1;
      let startScale = 1;
      
      element.addEventListener('touchstart', function(event) {
        if (event.touches.length === 2) {
          startDist = Math.hypot(
            event.touches[0].clientX - event.touches[1].clientX,
            event.touches[0].clientY - event.touches[1].clientY
          );
          startScale = scale;
        }
      });
      
      element.addEventListener('touchmove', function(event) {
        if (event.touches.length === 2) {
          event.preventDefault();
          
          const dist = Math.hypot(
            event.touches[0].clientX - event.touches[1].clientX,
            event.touches[0].clientY - event.touches[1].clientY
          );
          
          scale = startScale * (dist / startDist);
          scale = Math.min(Math.max(scale, 1), 3); // Limit scale between 1 and 3
          
          this.style.transform = `scale(${scale})`;
        }
      });
      
      element.addEventListener('touchend', function() {
        if (scale < 1.1) {
          // Reset scale if close to original
          scale = 1;
          this.style.transition = 'transform 0.3s ease';
          this.style.transform = `scale(${scale})`;
          setTimeout(() => {
            this.style.transition = '';
          }, 300);
        }
      });
    });
  },
  
  /**
   * Improve touch targets for better usability
   */
  improveTouchTargets: function() {
    // Add touch-friendly class to body
    document.body.classList.add('touch-friendly');
    
    // Improve checkbox and radio touch targets
    const smallControls = document.querySelectorAll('.form-check-input');
    smallControls.forEach(control => {
      const label = control.nextElementSibling;
      if (label && label.classList.contains('form-check-label')) {
        label.addEventListener('click', function(event) {
          // Expand touch target
          const rect = this.getBoundingClientRect();
          const touchPadding = 10;
          
          if (
            event.clientX < rect.left - touchPadding ||
            event.clientX > rect.right + touchPadding ||
            event.clientY < rect.top - touchPadding ||
            event.clientY > rect.bottom + touchPadding
          ) {
            // Click was outside the expanded touch area
            event.preventDefault();
          }
        });
      }
    });
  }
};

/**
 * Mobile Charts
 */
const MobileCharts = {
  /**
   * Initialize mobile chart optimizations
   */
  init: function() {
    if (MobileUtils.isMobileDevice()) {
      this.optimizeCharts();
    }
  },
  
  /**
   * Optimize charts for mobile
   */
  optimizeCharts: function() {
    // Check if Chart.js is available
    if (typeof Chart !== 'undefined') {
      // Set global defaults for mobile
      Chart.defaults.font.size = 12;
      Chart.defaults.responsive = true;
      Chart.defaults.maintainAspectRatio = false;
      
      // Find all charts
      const chartCanvases = document.querySelectorAll('canvas[data-chart-type]');
      
      chartCanvases.forEach(canvas => {
        const chartType = canvas.dataset.chartType;
        const chartInstance = Chart.getChart(canvas);
        
        if (chartInstance) {
          // Modify existing chart
          if (chartType === 'bar' || chartType === 'line') {
            // Simplify axes
            if (chartInstance.options.scales.x) {
              chartInstance.options.scales.x.ticks.maxRotation = 0;
              chartInstance.options.scales.x.ticks.autoSkip = true;
              chartInstance.options.scales.x.ticks.maxTicksLimit = 5;
            }
            
            // Hide legend or move to bottom
            if (chartInstance.options.plugins.legend) {
              chartInstance.options.plugins.legend.position = 'bottom';
              chartInstance.options.plugins.legend.labels.boxWidth = 10;
              chartInstance.options.plugins.legend.labels.padding = 10;
            }
            
            // Reduce padding
            chartInstance.options.layout = chartInstance.options.layout || {};
            chartInstance.options.layout.padding = {
              top: 10,
              right: 10,
              bottom: 10,
              left: 10
            };
            
            // Update chart
            chartInstance.update();
          } else if (chartType === 'pie' || chartType === 'doughnut') {
            // Move legend to bottom
            if (chartInstance.options.plugins.legend) {
              chartInstance.options.plugins.legend.position = 'bottom';
              chartInstance.options.plugins.legend.labels.boxWidth = 10;
              chartInstance.options.plugins.legend.labels.padding = 10;
            }
            
            // Update chart
            chartInstance.update();
          }
        }
      });
    }
  }
};

/**
 * Initialize all mobile enhancements
 */
document.addEventListener('DOMContentLoaded', function() {
  // Set body breakpoint class
  MobileUtils.setBodyBreakpointClass();
  
  // Initialize mobile components
  MobileNavigation.init();
  MobileSidebar.init();
  ResponsiveTables.init();
  TouchEnhancements.init();
  MobileCharts.init();
  
  // Update on resize
  window.addEventListener('resize', function() {
    MobileUtils.setBodyBreakpointClass();
    
    // Reinitialize components that need to be updated on resize
    ResponsiveTables.init();
    MobileCharts.init();
  });
});
