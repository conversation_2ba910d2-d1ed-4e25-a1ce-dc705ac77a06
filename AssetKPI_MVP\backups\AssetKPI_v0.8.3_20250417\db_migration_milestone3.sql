-- AssetKPI Database Migration: Milestone 3 - Work Order Enhancements

-- 3.1 Work Order Planning
CREATE TABLE IF NOT EXISTS work_order_plans (
    plan_id SERIAL PRIMARY KEY,
    plan_name VARCHAR(100) NOT NULL,
    description TEXT,
    estimated_duration INTEGER, -- in minutes
    estimated_labor_hours NUMERIC(10, 2),
    estimated_cost NUMERIC(10, 2),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Add columns to workorders table
ALTER TABLE workorders ADD COLUMN IF NOT EXISTS plan_id INTEGER REFERENCES work_order_plans(plan_id);
ALTER TABLE workorders ADD COLUMN IF NOT EXISTS priority VARCHAR(20);
ALTER TABLE workorders ADD COLUMN IF NOT EXISTS estimated_completion_date TIMESTAMP;
ALTER TABLE workorders ADD COLUMN IF NOT EXISTS actual_labor_hours NUMERIC(10, 2);

-- 3.2 Work Order Tasks
CREATE TABLE IF NOT EXISTS work_order_tasks (
    task_id SERIAL PRIMARY KEY,
    workorder_id INTEGER REFERENCES workorders(workorderid),
    task_description TEXT NOT NULL,
    sequence_number INTEGER,
    estimated_hours NUMERIC(5, 2),
    actual_hours NUMERIC(5, 2),
    status VARCHAR(30),
    assigned_to VARCHAR(100),
    completed_by VARCHAR(100),
    completed_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3.3 Work Order Labor
CREATE TABLE IF NOT EXISTS labor_resources (
    resource_id SERIAL PRIMARY KEY,
    person_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50),
    craft VARCHAR(50),
    skill_level VARCHAR(50),
    labor_rate NUMERIC(10, 2),
    availability_status VARCHAR(30),
    contact_info TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS work_order_labor (
    labor_id SERIAL PRIMARY KEY,
    workorder_id INTEGER REFERENCES workorders(workorderid),
    labor_code VARCHAR(50),
    craft VARCHAR(50),
    person_id INTEGER REFERENCES labor_resources(resource_id),
    hours_worked NUMERIC(10, 2),
    labor_cost NUMERIC(10, 2),
    work_date DATE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_workorders_plan_id ON workorders(plan_id);
CREATE INDEX IF NOT EXISTS idx_work_order_tasks_workorder_id ON work_order_tasks(workorder_id);
CREATE INDEX IF NOT EXISTS idx_work_order_labor_workorder_id ON work_order_labor(workorder_id);
CREATE INDEX IF NOT EXISTS idx_work_order_labor_person_id ON work_order_labor(person_id);

-- End of Milestone 3 migration script
