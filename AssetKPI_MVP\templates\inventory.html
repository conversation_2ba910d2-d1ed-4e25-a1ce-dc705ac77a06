{% extends "layout.html" %}

{% block title %}AssetKPI - Inventory Management{% endblock %}

{% block styles %}
<style>
    .inventory-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .inventory-card:hover {
        transform: translateY(-5px);
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .metric-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .class-a {
        background-color: rgba(255, 99, 132, 0.1);
    }
    .class-b {
        background-color: rgba(255, 159, 64, 0.1);
    }
    .class-c {
        background-color: rgba(75, 192, 192, 0.1);
    }
    .below-reorder {
        color: #dc3545;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>Inventory Management</h1>
        <p class="text-muted">Manage and optimize your spare parts inventory</p>
    </div>
    <div class="col-auto auth-required-content" data-role="ADMIN">
        <a href="/inventory-config" class="btn btn-primary mt-2">
            <i class="bi bi-gear"></i> Inventory Configuration
        </a>
    </div>
</div>

<!-- Inventory Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card inventory-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">{{ inventory_metrics.total_parts }}</div>
                <div class="metric-label">Total Parts</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card inventory-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">${{ "%.2f"|format(inventory_metrics.total_value) }}</div>
                <div class="metric-label">Total Value</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card inventory-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">{{ inventory_metrics.below_reorder }}</div>
                <div class="metric-label">Below Reorder Level</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card inventory-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">{{ inventory_metrics.class_a }} / {{ inventory_metrics.class_b }} / {{ inventory_metrics.class_c }}</div>
                <div class="metric-label">ABC Classification (A/B/C)</div>
            </div>
        </div>
    </div>
</div>

<!-- Inventory Optimization Visualization -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Inventory Optimization</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Economic Order Quantity (EOQ)</h6>
                        <p class="text-muted">EOQ helps determine the optimal order quantity that minimizes total inventory costs.</p>
                        <div class="chart-container" style="position: relative; height: 250px;">
                            <canvas id="eoqChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Safety Stock</h6>
                        <p class="text-muted">Safety stock protects against variability in demand and lead time.</p>
                        <div class="chart-container" style="position: relative; height: 250px;">
                            <canvas id="safetyStockChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <strong>Note:</strong> These charts show the top 10 parts by value. Configure optimization parameters in the <a href="/inventory-config" class="alert-link">Inventory Configuration</a> page.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Spare Parts Table -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Spare Parts Inventory</h5>
                <div class="input-group mt-2" style="max-width: 300px;">
                    <span class="input-group-text">Search</span>
                    <input type="text" id="searchInput" class="form-control" placeholder="Search parts...">
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="sparepartsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Part Number</th>
                                <th>Stock Qty</th>
                                <th>Reorder Level</th>
                                <th>ABC Class</th>
                                <th>Unit Price</th>
                                <th>Lead Time (days)</th>
                                <th>EOQ</th>
                                <th>Safety Stock</th>
                                <th>Last Restocked</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if spare_parts %}
                                {% for part in spare_parts %}
                                    <tr class="class-{{ part.abc_classification|default('', true)|lower }}">
                                        <td>{{ part.partid }}</td>
                                        <td>{{ part.partname }}</td>
                                        <td>{{ part.partnumber }}</td>
                                        <td class="{% if part.stockquantity is not none and part.reorderlevel is not none and part.stockquantity <= part.reorderlevel %}below-reorder{% endif %}">
                                            {{ part.stockquantity|default('N/A', true) }}
                                        </td>
                                        <td>{{ part.reorderlevel|default('N/A', true) }}</td>
                                        <td>{{ part.abc_classification|default('N/A', true) }}</td>
                                        <td>${{ "%.2f"|format(part.unitprice) if part.unitprice is not none else 'N/A' }}</td>
                                        <td>{{ part.leadtimedays|default('N/A', true) }}</td>
                                        <td>{{ "%.2f"|format(part.eoq) if part.eoq is not none else 'N/A' }}</td>
                                        <td>{{ "%.2f"|format(part.calculated_safety_stock) if part.calculated_safety_stock is not none else 'N/A' }}</td>
                                        <td>{{ part.lastrestocked.strftime('%Y-%m-%d') if part.lastrestocked else 'N/A' }}</td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="11" class="text-center">No spare parts available</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload form section for ENGINEER+ roles -->
<div class="row mb-4 auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Upload Spare Parts CSV</h5>
            </div>
            <div class="card-body">
                <p>Columns: partname, partnumber, stockquantity, reorderlevel, unitprice, leadtime, location, supplier</p>
                <form action="/upload/spareparts" method="post" enctype="multipart/form-data">
                    <div class="input-group">
                        <input type="file" class="form-control" name="file" accept=".csv" required>
                        <button class="btn btn-primary" type="submit">Upload File</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>

<script>
    // Chart instances
    let eoqChart;
    let safetyStockChart;

    // Function to render EOQ chart
    function renderEOQChart(parts) {
        const ctx = document.getElementById('eoqChart').getContext('2d');

        // Sort parts by EOQ value (descending) and take top 10
        const topParts = [...parts]
            .filter(part => part.eoq !== null && part.eoq > 0)
            .sort((a, b) => b.eoq - a.eoq)
            .slice(0, 10);

        // Prepare data
        const labels = topParts.map(part => part.partname);
        const eoqValues = topParts.map(part => part.eoq);
        const stockValues = topParts.map(part => part.stockquantity || 0);

        // Destroy previous chart if it exists
        if (eoqChart) {
            eoqChart.destroy();
        }

        // Create new chart
        eoqChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'EOQ',
                        data: eoqValues,
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Current Stock',
                        data: stockValues,
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantity'
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw;
                                return `${label}: ${Math.round(value)}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Function to render Safety Stock chart
    function renderSafetyStockChart(parts) {
        const ctx = document.getElementById('safetyStockChart').getContext('2d');

        // Sort parts by safety stock value (descending) and take top 10
        const topParts = [...parts]
            .filter(part => part.calculated_safety_stock !== null && part.calculated_safety_stock > 0)
            .sort((a, b) => b.calculated_safety_stock - a.calculated_safety_stock)
            .slice(0, 10);

        // Prepare data
        const labels = topParts.map(part => part.partname);
        const safetyStockValues = topParts.map(part => part.calculated_safety_stock);
        const stockValues = topParts.map(part => part.stockquantity || 0);
        const reorderValues = topParts.map(part => part.reorderlevel || 0);

        // Destroy previous chart if it exists
        if (safetyStockChart) {
            safetyStockChart.destroy();
        }

        // Create new chart
        safetyStockChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Safety Stock',
                        data: safetyStockValues,
                        backgroundColor: 'rgba(75, 192, 192, 0.5)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Current Stock',
                        data: stockValues,
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Reorder Level',
                        data: reorderValues,
                        backgroundColor: 'rgba(255, 159, 64, 0.5)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantity'
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw;
                                return `${label}: ${Math.round(value)}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const table = document.getElementById('sparepartsTable');

        if (searchInput && table) {
            searchInput.addEventListener('keyup', function() {
                const searchTerm = searchInput.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // Initialize authentication UI
        AssetKPIAuth.initAuth(updateUI);

        // Render charts with spare parts data
        const spareParts = [
            {% for part in spare_parts %}
            {
                partid: {{ part.partid }},
                partname: "{{ part.partname }}",
                stockquantity: {{ part.stockquantity|default('null', true) }},
                reorderlevel: {{ part.reorderlevel|default('null', true) }},
                eoq: {{ part.eoq|default('null', true) }},
                calculated_safety_stock: {{ part.calculated_safety_stock|default('null', true) }},
                unitprice: {{ part.unitprice|default('null', true) }}
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        renderEOQChart(spareParts);
        renderSafetyStockChart(spareParts);
    });

    // Function to update UI based on authentication
    function updateUI(user) {
        const authRequiredContent = document.querySelectorAll('.auth-required-content');

        if (user) {
            // User is authenticated, show role-specific content
            AssetKPIAuth.authenticatedFetch('/api/users/me')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('Failed to fetch user info');
                })
                .then(data => {
                    const userRole = data.role || '';

                    // Show content based on role
                    authRequiredContent.forEach(el => {
                        const requiredRoles = (el.dataset.role || '').split(',');
                        if (requiredRoles.includes(userRole)) {
                            el.style.display = 'block';
                        } else {
                            el.style.display = 'none';
                        }
                    });
                })
                .catch(error => {
                    console.error('Error fetching user info:', error);
                });
        } else {
            // User is not authenticated, hide all authenticated content
            authRequiredContent.forEach(el => {
                el.style.display = 'none';
            });
        }
    }
</script>
{% endblock %}