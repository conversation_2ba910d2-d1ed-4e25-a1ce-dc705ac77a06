# Create a new debug page
with open('templates/firebase_debug.html', 'w') as file:
    file.write('''
<!DOCTYPE html>
<html>
<head>
    <title>Firebase Debug</title>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
</head>
<body>
    <h1>Firebase Debug</h1>
    <div id="status">Checking Firebase configuration...</div>
    <pre id="config"></pre>
    
    <h2>Test Authentication</h2>
    <form id="login-form">
        <div>
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" value="TestTest">
        </div>
        <button type="button" id="login-button">Sign In</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
            authDomain: "ikios-59679.firebaseapp.com",
            projectId: "ikios-59679",
            storageBucket: "ikios-59679.appspot.com",
            messagingSenderId: "1234567890",
            appId: "1:1234567890:web:1234567890"
        };
        
        // Display the Firebase configuration
        document.getElementById('config').textContent = JSON.stringify(firebaseConfig, null, 2);
        
        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            document.getElementById('status').textContent = 'Firebase initialized successfully';
            document.getElementById('status').style.color = 'green';
        } catch (error) {
            document.getElementById('status').textContent = 'Firebase initialization error: ' + error.message;
            document.getElementById('status').style.color = 'red';
        }
        
        // Handle login
        document.getElementById('login-button').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.textContent = 'Signing in...';
                const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
                resultDiv.textContent = 'Signed in successfully as ' + userCredential.user.email;
                resultDiv.style.color = 'green';
                
                // Get the ID token
                const token = await userCredential.user.getIdToken();
                const tokenPreview = token.substring(0, 10) + '...' + token.substring(token.length - 10);
                resultDiv.textContent += '\nToken: ' + tokenPreview;
            } catch (error) {
                resultDiv.textContent = 'Error signing in: ' + error.message;
                resultDiv.style.color = 'red';
            }
        });
    </script>
</body>
</html>
''')

# Add a route to serve the debug page
with open('main.py', 'r') as file:
    content = file.read()

# Find a good place to add the route (after the login_page function)
login_page_end = '''    return templates.TemplateResponse("login.html", {
        "request": request,
        "firebase_config": firebase_config
    })'''

debug_route = '''    return templates.TemplateResponse("login.html", {
        "request": request,
        "firebase_config": firebase_config
    })

@app.get("/firebase-debug-simple", response_class=HTMLResponse, tags=["Authentication"])
async def firebase_debug_simple_page(request: Request):
    """
    Serves a simple debug page for testing Firebase Authentication.
    """
    return templates.TemplateResponse("firebase_debug.html", {"request": request})'''

content = content.replace(login_page_end, debug_route)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Created a simple debug page and added a route to serve it")
