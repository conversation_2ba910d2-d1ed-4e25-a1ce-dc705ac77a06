-- AssetKPI Database Migration: Milestone 1 - Asset Hierarchy and Classification

-- 1.1 Asset Hierarchy
CREATE TABLE IF NOT EXISTS asset_locations (
    location_id SERIAL PRIMARY KEY,
    location_name VARCHAR(100) NOT NULL,
    location_type VARCHAR(50),
    parent_location_id INTEGER REFERENCES asset_locations(location_id),
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS asset_systems (
    system_id SERIAL PRIMARY KEY,
    system_name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_system_id INTEGER REFERENCES asset_systems(system_id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add columns to assets table
ALTER TABLE assets ADD COLUMN IF NOT EXISTS system_id INTEGER REFERENCES asset_systems(system_id);
ALTER TABLE assets ADD COLUMN IF NOT EXISTS location_id INTEGER REFERENCES asset_locations(location_id);
ALTER TABLE assets ADD COLUMN IF NOT EXISTS parent_asset_id INTEGER REFERENCES assets(assetid);

-- 1.2 Asset Classification
CREATE TABLE IF NOT EXISTS asset_categories (
    category_id SERIAL PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_category_id INTEGER REFERENCES asset_categories(category_id)
);

-- Add column to assets table
ALTER TABLE assets ADD COLUMN IF NOT EXISTS category_id INTEGER REFERENCES asset_categories(category_id);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_assets_system_id ON assets(system_id);
CREATE INDEX IF NOT EXISTS idx_assets_location_id ON assets(location_id);
CREATE INDEX IF NOT EXISTS idx_assets_category_id ON assets(category_id);

-- End of Milestone 1 migration script
