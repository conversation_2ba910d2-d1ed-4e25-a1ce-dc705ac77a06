-- AssetKPI Database Migration: CMMS Expansion
-- This script expands the AssetKPI database to simulate a comprehensive CMMS like IBM Maximo

-- 1. Enhanced Asset Management

-- 1.1 Asset Hierarchy
CREATE TABLE IF NOT EXISTS asset_locations (
    location_id SERIAL PRIMARY KEY,
    location_name VARCHAR(100) NOT NULL,
    location_type VARCHAR(50),
    parent_location_id INTEGER REFERENCES asset_locations(location_id),
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS asset_systems (
    system_id SERIAL PRIMARY KEY,
    system_name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_system_id INTEGER REFERENCES asset_systems(system_id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add columns to assets table
ALTER TABLE assets ADD COLUMN IF NOT EXISTS system_id INTEGER REFERENCES asset_systems(system_id);
ALTER TABLE assets ADD COLUMN IF NOT EXISTS location_id INTEGER REFERENCES asset_locations(location_id);
ALTER TABLE assets ADD COLUMN IF NOT EXISTS parent_asset_id INTEGER REFERENCES assets(assetid);

-- 1.2 Asset Classification
CREATE TABLE IF NOT EXISTS asset_categories (
    category_id SERIAL PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_category_id INTEGER REFERENCES asset_categories(category_id)
);

-- Add column to assets table
ALTER TABLE assets ADD COLUMN IF NOT EXISTS category_id INTEGER REFERENCES asset_categories(category_id);

-- 1.3 Asset Specifications
CREATE TABLE IF NOT EXISTS asset_specifications (
    spec_id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    spec_name VARCHAR(100) NOT NULL,
    spec_value VARCHAR(255),
    spec_unit VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 1.4 Asset Warranty
CREATE TABLE IF NOT EXISTS asset_warranties (
    warranty_id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    warranty_type VARCHAR(50),
    provider VARCHAR(100),
    start_date DATE,
    end_date DATE,
    coverage_details TEXT,
    contact_info TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 2. Enhanced Work Management

-- 2.1 Work Order Planning
CREATE TABLE IF NOT EXISTS work_order_plans (
    plan_id SERIAL PRIMARY KEY,
    plan_name VARCHAR(100) NOT NULL,
    description TEXT,
    estimated_duration INTEGER, -- in minutes
    estimated_labor_hours NUMERIC(10, 2),
    estimated_cost NUMERIC(10, 2),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Add columns to workorders table
ALTER TABLE workorders ADD COLUMN IF NOT EXISTS plan_id INTEGER REFERENCES work_order_plans(plan_id);
ALTER TABLE workorders ADD COLUMN IF NOT EXISTS priority VARCHAR(20);
ALTER TABLE workorders ADD COLUMN IF NOT EXISTS estimated_completion_date TIMESTAMP;
ALTER TABLE workorders ADD COLUMN IF NOT EXISTS actual_labor_hours NUMERIC(10, 2);

-- 2.2 Work Order Tasks
CREATE TABLE IF NOT EXISTS work_order_tasks (
    task_id SERIAL PRIMARY KEY,
    workorder_id INTEGER REFERENCES workorders(workorderid),
    task_description TEXT NOT NULL,
    sequence_number INTEGER,
    estimated_hours NUMERIC(5, 2),
    actual_hours NUMERIC(5, 2),
    status VARCHAR(30),
    assigned_to VARCHAR(100),
    completed_by VARCHAR(100),
    completed_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3. Preventive Maintenance

-- 3.1 PM Schedules
CREATE TABLE IF NOT EXISTS pm_schedules (
    schedule_id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    schedule_name VARCHAR(100) NOT NULL,
    frequency_type VARCHAR(50) NOT NULL, -- Calendar, Meter, or Condition
    frequency_value INTEGER,
    frequency_unit VARCHAR(20), -- Days, Weeks, Months, Years, Hours, Cycles, etc.
    last_completed_date TIMESTAMP,
    next_due_date TIMESTAMP,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 3.2 PM Job Plans
CREATE TABLE IF NOT EXISTS pm_job_plans (
    plan_id SERIAL PRIMARY KEY,
    schedule_id INTEGER REFERENCES pm_schedules(schedule_id),
    plan_name VARCHAR(100) NOT NULL,
    description TEXT,
    estimated_duration INTEGER, -- in minutes
    safety_instructions TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3.3 PM Job Tasks
CREATE TABLE IF NOT EXISTS pm_job_tasks (
    task_id SERIAL PRIMARY KEY,
    plan_id INTEGER REFERENCES pm_job_plans(plan_id),
    task_description TEXT NOT NULL,
    sequence_number INTEGER,
    estimated_hours NUMERIC(5, 2),
    required_tools TEXT,
    required_parts TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3.4 Asset Meters
CREATE TABLE IF NOT EXISTS asset_meters (
    meter_id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    meter_name VARCHAR(100) NOT NULL,
    meter_type VARCHAR(50), -- Runtime, Cycles, Distance, etc.
    unit_of_measure VARCHAR(50),
    current_reading NUMERIC(15, 5),
    last_reading_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS meter_readings (
    reading_id SERIAL PRIMARY KEY,
    meter_id INTEGER REFERENCES asset_meters(meter_id),
    reading_value NUMERIC(15, 5),
    reading_date TIMESTAMP,
    entered_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 4. Enhanced Inventory Management

-- 4.1 Storerooms and Locations
CREATE TABLE IF NOT EXISTS storerooms (
    storeroom_id SERIAL PRIMARY KEY,
    storeroom_name VARCHAR(100) NOT NULL,
    location VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS storage_locations (
    location_id SERIAL PRIMARY KEY,
    storeroom_id INTEGER REFERENCES storerooms(storeroom_id),
    location_name VARCHAR(100) NOT NULL,
    bin VARCHAR(50),
    aisle VARCHAR(50),
    shelf VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add column to spareparts table
ALTER TABLE spareparts ADD COLUMN IF NOT EXISTS storage_location_id INTEGER REFERENCES storage_locations(location_id);

-- 4.2 Vendors
CREATE TABLE IF NOT EXISTS vendors (
    vendor_id SERIAL PRIMARY KEY,
    vendor_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(50),
    email VARCHAR(100),
    address TEXT,
    preferred BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add column to spareparts table
ALTER TABLE spareparts ADD COLUMN IF NOT EXISTS preferred_vendor_id INTEGER REFERENCES vendors(vendor_id);

-- 4.3 Purchase Orders
CREATE TABLE IF NOT EXISTS purchase_orders (
    po_id SERIAL PRIMARY KEY,
    po_number VARCHAR(50) UNIQUE NOT NULL,
    vendor_id INTEGER REFERENCES vendors(vendor_id),
    order_date DATE,
    expected_delivery_date DATE,
    status VARCHAR(30),
    total_amount NUMERIC(15, 2),
    shipping_address TEXT,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS po_items (
    item_id SERIAL PRIMARY KEY,
    po_id INTEGER REFERENCES purchase_orders(po_id),
    part_id INTEGER REFERENCES spareparts(partid),
    quantity INTEGER,
    unit_price NUMERIC(10, 2),
    line_total NUMERIC(15, 2),
    received_quantity INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 4.4 Inventory Transactions
CREATE TABLE IF NOT EXISTS inventory_transactions (
    transaction_id SERIAL PRIMARY KEY,
    part_id INTEGER REFERENCES spareparts(partid),
    transaction_type VARCHAR(50), -- Issue, Receipt, Return, Adjustment, Transfer
    quantity INTEGER,
    transaction_date TIMESTAMP,
    reference_type VARCHAR(50), -- WorkOrder, PO, Adjustment
    reference_id INTEGER,
    location_from INTEGER REFERENCES storage_locations(location_id),
    location_to INTEGER REFERENCES storage_locations(location_id),
    unit_cost NUMERIC(10, 2),
    total_cost NUMERIC(15, 2),
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 5. Resource Management

-- 5.1 Labor Resources
CREATE TABLE IF NOT EXISTS labor_resources (
    resource_id SERIAL PRIMARY KEY,
    person_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50),
    craft VARCHAR(50),
    skill_level VARCHAR(50),
    labor_rate NUMERIC(10, 2),
    availability_status VARCHAR(30),
    contact_info TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 5.2 Resource Skills
CREATE TABLE IF NOT EXISTS resource_skills (
    skill_id SERIAL PRIMARY KEY,
    resource_id INTEGER REFERENCES labor_resources(resource_id),
    skill_name VARCHAR(100) NOT NULL,
    proficiency_level VARCHAR(50),
    certification VARCHAR(100),
    expiration_date DATE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 5.3 Tools and Equipment
CREATE TABLE IF NOT EXISTS tools_equipment (
    tool_id SERIAL PRIMARY KEY,
    tool_name VARCHAR(100) NOT NULL,
    tool_type VARCHAR(50),
    serial_number VARCHAR(50),
    status VARCHAR(30),
    location VARCHAR(100),
    calibration_due_date DATE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS work_order_tools (
    id SERIAL PRIMARY KEY,
    workorder_id INTEGER REFERENCES workorders(workorderid),
    tool_id INTEGER REFERENCES tools_equipment(tool_id),
    checkout_date TIMESTAMP,
    return_date TIMESTAMP,
    checked_out_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 6. Documentation

-- 6.1 Technical Documentation
CREATE TABLE IF NOT EXISTS technical_documents (
    document_id SERIAL PRIMARY KEY,
    document_name VARCHAR(100) NOT NULL,
    document_type VARCHAR(50),
    description TEXT,
    file_path VARCHAR(255),
    version VARCHAR(50),
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS asset_documents (
    id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    document_id INTEGER REFERENCES technical_documents(document_id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 6.2 Safety Procedures
CREATE TABLE IF NOT EXISTS safety_procedures (
    procedure_id SERIAL PRIMARY KEY,
    procedure_name VARCHAR(100) NOT NULL,
    description TEXT,
    procedure_steps TEXT,
    required_ppe TEXT,
    hazards TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS work_order_procedures (
    id SERIAL PRIMARY KEY,
    workorder_id INTEGER REFERENCES workorders(workorderid),
    procedure_id INTEGER REFERENCES safety_procedures(procedure_id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 7. Work Order Labor (was missing from earlier section)
CREATE TABLE IF NOT EXISTS work_order_labor (
    labor_id SERIAL PRIMARY KEY,
    workorder_id INTEGER REFERENCES workorders(workorderid),
    labor_code VARCHAR(50),
    craft VARCHAR(50),
    person_id INTEGER REFERENCES labor_resources(resource_id),
    hours_worked NUMERIC(10, 2),
    labor_cost NUMERIC(10, 2),
    work_date DATE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_assets_system_id ON assets(system_id);
CREATE INDEX IF NOT EXISTS idx_assets_location_id ON assets(location_id);
CREATE INDEX IF NOT EXISTS idx_assets_category_id ON assets(category_id);
CREATE INDEX IF NOT EXISTS idx_asset_specs_asset_id ON asset_specifications(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_warranties_asset_id ON asset_warranties(asset_id);
CREATE INDEX IF NOT EXISTS idx_workorders_plan_id ON workorders(plan_id);
CREATE INDEX IF NOT EXISTS idx_work_order_tasks_workorder_id ON work_order_tasks(workorder_id);
CREATE INDEX IF NOT EXISTS idx_pm_schedules_asset_id ON pm_schedules(asset_id);
CREATE INDEX IF NOT EXISTS idx_pm_job_plans_schedule_id ON pm_job_plans(schedule_id);
CREATE INDEX IF NOT EXISTS idx_pm_job_tasks_plan_id ON pm_job_tasks(plan_id);
CREATE INDEX IF NOT EXISTS idx_asset_meters_asset_id ON asset_meters(asset_id);
CREATE INDEX IF NOT EXISTS idx_meter_readings_meter_id ON meter_readings(meter_id);
CREATE INDEX IF NOT EXISTS idx_storage_locations_storeroom_id ON storage_locations(storeroom_id);
CREATE INDEX IF NOT EXISTS idx_spareparts_storage_location_id ON spareparts(storage_location_id);
CREATE INDEX IF NOT EXISTS idx_spareparts_preferred_vendor_id ON spareparts(preferred_vendor_id);
CREATE INDEX IF NOT EXISTS idx_po_items_po_id ON po_items(po_id);
CREATE INDEX IF NOT EXISTS idx_po_items_part_id ON po_items(part_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_part_id ON inventory_transactions(part_id);
CREATE INDEX IF NOT EXISTS idx_resource_skills_resource_id ON resource_skills(resource_id);
CREATE INDEX IF NOT EXISTS idx_work_order_tools_workorder_id ON work_order_tools(workorder_id);
CREATE INDEX IF NOT EXISTS idx_work_order_tools_tool_id ON work_order_tools(tool_id);
CREATE INDEX IF NOT EXISTS idx_asset_documents_asset_id ON asset_documents(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_documents_document_id ON asset_documents(document_id);
CREATE INDEX IF NOT EXISTS idx_work_order_procedures_workorder_id ON work_order_procedures(workorder_id);
CREATE INDEX IF NOT EXISTS idx_work_order_labor_workorder_id ON work_order_labor(workorder_id);
CREATE INDEX IF NOT EXISTS idx_work_order_labor_person_id ON work_order_labor(person_id);

-- End of migration script
