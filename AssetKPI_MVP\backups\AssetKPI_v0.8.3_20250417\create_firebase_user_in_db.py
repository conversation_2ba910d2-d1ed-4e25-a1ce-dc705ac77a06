import os
import sys
import firebase_admin
from firebase_admin import credentials, auth
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Import the User model and UserRole enum
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from main import User, UserRole

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if already initialized
        firebase_admin.get_app()
        print("Firebase Admin SDK already initialized")
    except ValueError:
        # Initialize with service account
        try:
            SERVICE_ACCOUNT_KEY_PATH = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
            cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)
            firebase_admin.initialize_app(cred)
            print("Firebase Admin SDK initialized successfully")
        except Exception as e:
            print(f"Error initializing Firebase Admin SDK: {e}")
            sys.exit(1)

def get_user_by_email(email):
    """Get a Firebase user by email"""
    try:
        user = auth.get_user_by_email(email)
        print(f"Found Firebase user: {user.uid} ({user.email})")
        return user
    except auth.UserNotFoundError:
        print(f"Firebase user with email {email} not found")
        return None
    except Exception as e:
        print(f"Error getting Firebase user by email: {e}")
        return None

def create_user_in_db(firebase_uid, email, role, full_name=None):
    """Create a user in the PostgreSQL database"""
    try:
        # Check if user already exists in database
        existing_user = db.query(User).filter(User.user_id == firebase_uid).first()
        if existing_user:
            print(f"User with ID {firebase_uid} already exists in database.")
            return existing_user
        
        # Create new user in database
        user = User(
            user_id=firebase_uid,
            email=email,
            role=role,
            full_name=full_name,
            created_at=datetime.now(),
            last_login=None
        )
        
        # Add to database
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print(f"Created user in database: {user.email} with role {user.role}")
        return user
    
    except Exception as e:
        db.rollback()
        print(f"Error creating user in database: {e}")
        return None

def main():
    # Initialize Firebase Admin SDK
    initialize_firebase()
    
    # User email
    email = "<EMAIL>"
    
    # Get Firebase user
    firebase_user = get_user_by_email(email)
    if not firebase_user:
        print("Failed to get Firebase user")
        sys.exit(1)
    
    # Create users with different roles for testing
    roles = [UserRole.ADMIN, UserRole.MANAGER, UserRole.ENGINEER, UserRole.VIEWER]
    
    # Create main user with ADMIN role
    create_user_in_db(
        firebase_uid=firebase_user.uid,
        email=firebase_user.email,
        role=UserRole.ADMIN,
        full_name="Johan Borgulf"
    )
    
    # List all users in the database
    print("\nUsers in database:")
    users = db.query(User).all()
    for user in users:
        print(f"- {user.user_id}: {user.email} ({user.role})")

if __name__ == "__main__":
    main()
