{% extends "layout.html" %}

{% block title %}Asset Detail - {{ asset.assetname }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Asset Detail</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="/assets">Assets</a></li>
        <li class="breadcrumb-item active">{{ asset.assetname }}</li>
    </ol>

    <!-- Asset Header Card -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-cogs me-1"></i>
                {{ asset.assetname }}
            </div>
            <div>
                <span class="badge {% if asset.status == 'Active' %}bg-success{% elif asset.status == 'Inactive' %}bg-secondary{% elif asset.status == 'Under Maintenance' %}bg-warning{% else %}bg-danger{% endif %}">
                    {{ asset.status }}
                </span>
                <span class="badge {% if asset.criticality == 'Critical' %}bg-danger{% elif asset.criticality == 'High' %}bg-warning{% elif asset.criticality == 'Medium' %}bg-primary{% else %}bg-info{% endif %} ms-2">
                    {{ asset.criticality }} Criticality
                </span>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Asset ID:</th>
                            <td>{{ asset.assetid }}</td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td>{{ asset.assettype }}</td>
                        </tr>
                        <tr>
                            <th>Serial Number:</th>
                            <td>{{ asset.serialnumber }}</td>
                        </tr>
                        <tr>
                            <th>Manufacturer:</th>
                            <td>{{ asset.manufacturer }}</td>
                        </tr>
                        <tr>
                            <th>Model:</th>
                            <td>{{ asset.model }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Location:</th>
                            <td>{{ location.location_name if location else asset.location }}</td>
                        </tr>
                        <tr>
                            <th>System:</th>
                            <td>{{ system.system_name if system else 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Category:</th>
                            <td>{{ category.category_name if category else 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Lifecycle Stage:</th>
                            <td>{{ asset.lifecyclestage }}</td>
                        </tr>
                        <tr>
                            <th>Last Service:</th>
                            <td>{{ asset.lastservicedate.strftime('%Y-%m-%d') if asset.lastservicedate else 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Asset Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="assetTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link active" id="overview-tab" href="/assets/{{ asset.assetid }}">Overview</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="kpi-tab" href="/assets/{{ asset.assetid }}/kpi">KPIs</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="maintenance-tab" href="/assets/{{ asset.assetid }}/maintenance">Maintenance History</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="specs-tab" href="/assets/{{ asset.assetid }}/specifications">Specifications</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="docs-tab" href="/assets/{{ asset.assetid }}/documents">Documents</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="meters-tab" href="/assets/{{ asset.assetid }}/meters">Meters</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="pm-tab" href="/assets/{{ asset.assetid }}/pm">PM Schedules</a>
        </li>
    </ul>

    <!-- Overview Content -->
    <div class="row">
        <!-- Asset Health Card -->
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-heartbeat me-1"></i>
                    Asset Health
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="display-4 fw-bold">
                            {{ asset_health }}%
                        </div>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar {% if asset_health >= 80 %}bg-success{% elif asset_health >= 60 %}bg-warning{% else %}bg-danger{% endif %}"
                                 role="progressbar"
                                 style="width: {{ asset_health }}%;"
                                 aria-valuenow="{{ asset_health }}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                    <div class="small text-muted">
                        Last updated: {{ health_last_updated.strftime('%Y-%m-%d %H:%M') if health_last_updated else 'N/A' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Work Orders Card -->
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-tools me-1"></i>
                    Recent Work Orders
                </div>
                <div class="card-body">
                    {% if recent_work_orders %}
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for wo in recent_work_orders %}
                                <tr>
                                    <td><a href="/workorders/{{ wo.workorderid }}">{{ wo.workorderid }}</a></td>
                                    <td>{{ wo.workordertype }}</td>
                                    <td>{{ wo.description[:30] }}{% if wo.description|length > 30 %}...{% endif %}</td>
                                    <td>
                                        <span class="badge {% if wo.status == 'Completed' %}bg-success{% elif wo.status == 'In Progress' %}bg-primary{% elif wo.status == 'On Hold' %}bg-warning{% elif wo.status == 'Cancelled' %}bg-danger{% else %}bg-secondary{% endif %}">
                                            {{ wo.status }}
                                        </span>
                                    </td>
                                    <td>{{ wo.startdate.strftime('%Y-%m-%d') if wo.startdate else 'N/A' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end mt-2">
                        <a href="/assets/{{ asset.assetid }}/maintenance" class="btn btn-sm btn-primary">View All</a>
                    </div>
                    {% else %}
                    <p class="text-center">No recent work orders found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Key Metrics Card -->
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-line me-1"></i>
                    Key Metrics
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="small text-muted">MTBF</div>
                                            <div class="h5 mb-0">{{ mtbf|round(1) }} days</div>
                                        </div>
                                        <div class="text-primary">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="small text-muted">MTTR</div>
                                            <div class="h5 mb-0">{{ mttr|round(1) }} hours</div>
                                        </div>
                                        <div class="text-success">
                                            <i class="fas fa-wrench fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="small text-muted">Availability</div>
                                            <div class="h5 mb-0">{{ availability|round(1) }}%</div>
                                        </div>
                                        <div class="text-warning">
                                            <i class="fas fa-percentage fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="small text-muted">Reliability</div>
                                            <div class="h5 mb-0">{{ reliability|round(1) }}%</div>
                                        </div>
                                        <div class="text-danger">
                                            <i class="fas fa-shield-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-end mt-2">
                        <a href="/assets/{{ asset.assetid }}/kpi" class="btn btn-sm btn-primary">View All KPIs</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Maintenance Card -->
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Upcoming Maintenance
                </div>
                <div class="card-body">
                    {% if upcoming_pm %}
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Schedule</th>
                                    <th>Type</th>
                                    <th>Due Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for pm in upcoming_pm %}
                                <tr>
                                    <td>{{ pm.schedule_name }}</td>
                                    <td>{{ pm.frequency_type }}</td>
                                    <td>{{ pm.next_due_date.strftime('%Y-%m-%d') if pm.next_due_date else 'N/A' }}</td>
                                    <td>
                                        {% set days_until = (pm.next_due_date - now).days if pm.next_due_date else 0 %}
                                        <span class="badge {% if days_until < 0 %}bg-danger{% elif days_until < 7 %}bg-warning{% else %}bg-success{% endif %}">
                                            {% if days_until < 0 %}
                                                Overdue by {{ days_until|abs }} days
                                            {% elif days_until == 0 %}
                                                Due today
                                            {% else %}
                                                Due in {{ days_until }} days
                                            {% endif %}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end mt-2">
                        <a href="/assets/{{ asset.assetid }}/pm" class="btn btn-sm btn-primary">View All PM Schedules</a>
                    </div>
                    {% else %}
                    <p class="text-center">No upcoming maintenance scheduled.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Any JavaScript specific to this page
    });
</script>
{% endblock %}
