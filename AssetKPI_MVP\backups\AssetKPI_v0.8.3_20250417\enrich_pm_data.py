import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def enrich_pm_data():
    """Add more comprehensive PM and meter data to the database."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Get all assets
        cursor.execute("SELECT assetid, assetname, assettype FROM assets")
        assets = cursor.fetchall()
        
        if not assets:
            print("No assets found in the database. Please add assets first.")
            return
        
        # Sample PM schedule templates based on asset type
        pm_templates = {
            'Production': [
                # (name_suffix, frequency_type, frequency_value, frequency_unit, estimated_duration)
                ('Monthly Inspection', 'Calendar', 30, 'Days', 60),
                ('Quarterly Maintenance', 'Calendar', 90, 'Days', 120),
                ('Semi-Annual Service', 'Calendar', 180, 'Days', 240),
                ('Annual Overhaul', 'Calendar', 365, 'Days', 480)
            ],
            'HVAC': [
                ('Monthly Filter Check', 'Calendar', 30, 'Days', 30),
                ('Quarterly Inspection', 'Calendar', 90, 'Days', 90),
                ('Semi-Annual Service', 'Calendar', 180, 'Days', 180),
                ('Annual Maintenance', 'Calendar', 365, 'Days', 360)
            ],
            'Electrical': [
                ('Monthly Visual Inspection', 'Calendar', 30, 'Days', 45),
                ('Quarterly Testing', 'Calendar', 90, 'Days', 120),
                ('Annual Maintenance', 'Calendar', 365, 'Days', 240)
            ],
            'Utility': [
                ('Weekly Inspection', 'Calendar', 7, 'Days', 30),
                ('Monthly Service', 'Calendar', 30, 'Days', 60),
                ('Quarterly Maintenance', 'Calendar', 90, 'Days', 120),
                ('Annual Overhaul', 'Calendar', 365, 'Days', 360)
            ],
            'Material Handling': [
                ('Monthly Safety Inspection', 'Calendar', 30, 'Days', 45),
                ('Quarterly Service', 'Calendar', 90, 'Days', 90),
                ('Annual Certification', 'Calendar', 365, 'Days', 180)
            ],
            'Quality': [
                ('Monthly Calibration Check', 'Calendar', 30, 'Days', 60),
                ('Quarterly Verification', 'Calendar', 90, 'Days', 120),
                ('Annual Calibration', 'Calendar', 365, 'Days', 240)
            ],
            'IT': [
                ('Monthly Updates', 'Calendar', 30, 'Days', 60),
                ('Quarterly Maintenance', 'Calendar', 90, 'Days', 120),
                ('Annual Hardware Inspection', 'Calendar', 365, 'Days', 180)
            ],
            'default': [
                ('Monthly Inspection', 'Calendar', 30, 'Days', 60),
                ('Quarterly Maintenance', 'Calendar', 90, 'Days', 120),
                ('Annual Service', 'Calendar', 365, 'Days', 240)
            ]
        }
        
        # Sample meter templates based on asset type
        meter_templates = {
            'Production': [
                # (name, type, unit)
                ('Runtime', 'Runtime', 'Hours'),
                ('Cycles', 'Counter', 'Cycles'),
                ('Production Count', 'Counter', 'Units')
            ],
            'HVAC': [
                ('Runtime', 'Runtime', 'Hours'),
                ('Cycles', 'Counter', 'Cycles'),
                ('Temperature', 'Gauge', 'Celsius')
            ],
            'Electrical': [
                ('Runtime', 'Runtime', 'Hours'),
                ('Load', 'Gauge', 'kW'),
                ('Temperature', 'Gauge', 'Celsius')
            ],
            'Utility': [
                ('Runtime', 'Runtime', 'Hours'),
                ('Pressure', 'Gauge', 'PSI'),
                ('Flow', 'Gauge', 'GPM')
            ],
            'Material Handling': [
                ('Runtime', 'Runtime', 'Hours'),
                ('Distance', 'Counter', 'Miles'),
                ('Load', 'Counter', 'Tons')
            ],
            'Quality': [
                ('Usage', 'Counter', 'Hours'),
                ('Calibration Age', 'Counter', 'Days')
            ],
            'IT': [
                ('Uptime', 'Runtime', 'Hours'),
                ('Data Processed', 'Counter', 'GB'),
                ('Temperature', 'Gauge', 'Celsius')
            ],
            'default': [
                ('Runtime', 'Runtime', 'Hours'),
                ('Cycles', 'Counter', 'Cycles')
            ]
        }
        
        # Sample PM task templates
        pm_task_templates = {
            'Inspection': [
                # (description, estimated_hours, required_tools, required_parts)
                ('Visual inspection of all components', 0.5, 'Flashlight, inspection mirror', 'None'),
                ('Check for loose connections', 0.5, 'Basic hand tools', 'None'),
                ('Inspect for leaks or damage', 0.5, 'Flashlight, leak detection fluid', 'None'),
                ('Check alignment and clearances', 0.75, 'Feeler gauges, alignment tools', 'None'),
                ('Verify proper operation', 0.5, 'Test equipment', 'None')
            ],
            'Lubrication': [
                ('Lubricate bearings', 0.75, 'Grease gun, lubricants', 'Grease, oil'),
                ('Check oil levels and quality', 0.5, 'Oil sampling kit', 'Oil'),
                ('Change oil and filters', 1.5, 'Filter wrenches, drain pans', 'Oil, filters'),
                ('Grease fittings', 1.0, 'Grease gun', 'Grease'),
                ('Apply lubricant to moving parts', 0.75, 'Oil can, lubricants', 'Lubricating oil')
            ],
            'Cleaning': [
                ('Clean exterior surfaces', 0.5, 'Cleaning supplies', 'Cleaning solution'),
                ('Clean filters', 0.75, 'Compressed air, cleaning tools', 'Filter cleaner'),
                ('Remove debris and buildup', 1.0, 'Vacuum, scrapers', 'None'),
                ('Clean electrical contacts', 0.5, 'Contact cleaner, lint-free cloths', 'Contact cleaner'),
                ('Flush cooling system', 1.5, 'Flush kit, hoses', 'Coolant, cleaning solution')
            ],
            'Testing': [
                ('Test operation under load', 1.0, 'Load testing equipment', 'None'),
                ('Check safety systems', 0.75, 'Safety testing equipment', 'None'),
                ('Verify alarms and indicators', 0.5, 'Multimeter, test equipment', 'None'),
                ('Test backup systems', 0.75, 'Test equipment', 'None'),
                ('Perform diagnostic tests', 1.0, 'Diagnostic equipment, laptop', 'None')
            ],
            'Adjustment': [
                ('Adjust tension on belts/chains', 0.75, 'Tension gauges, wrenches', 'None'),
                ('Calibrate sensors and controls', 1.0, 'Calibration equipment', 'None'),
                ('Adjust clearances', 0.75, 'Feeler gauges, adjustment tools', 'None'),
                ('Balance rotating components', 1.5, 'Balancing equipment', 'Balance weights'),
                ('Tune control parameters', 1.0, 'Laptop, software', 'None')
            ],
            'Replacement': [
                ('Replace wear components', 1.5, 'Hand tools, torque wrench', 'Wear components, gaskets'),
                ('Replace filters', 0.75, 'Filter wrenches', 'Filters'),
                ('Replace seals and gaskets', 1.5, 'Hand tools, scrapers', 'Seals, gaskets, sealant'),
                ('Replace belts or chains', 1.0, 'Hand tools, tensioning tools', 'Belts, chains'),
                ('Replace lubricants', 1.0, 'Drain pans, funnels', 'Oil, grease, coolant')
            ]
        }
        
        print("Adding PM schedules and job plans...")
        
        # Track how many we've added
        schedules_added = 0
        
        # For each asset, create PM schedules
        for asset_id, asset_name, asset_type in assets:
            # Determine which template to use
            template_key = 'default'
            for key in pm_templates.keys():
                if asset_type and key.lower() in asset_type.lower():
                    template_key = key
                    break
            
            # Get the template
            templates = pm_templates[template_key]
            
            # Determine how many schedules to create (1-3)
            num_schedules = random.randint(1, min(3, len(templates)))
            
            # Select random templates
            selected_templates = random.sample(templates, num_schedules)
            
            for template in selected_templates:
                name_suffix, frequency_type, frequency_value, frequency_unit, estimated_duration = template
                
                # Create schedule name
                schedule_name = f"{asset_name} - {name_suffix}"
                
                # Calculate last completed and next due dates
                today = datetime.now()
                days_since_last = random.randint(0, frequency_value)
                last_completed_date = today - timedelta(days=days_since_last)
                next_due_date = last_completed_date + timedelta(days=frequency_value)
                
                # Insert PM schedule
                cursor.execute(
                    """
                    INSERT INTO pm_schedules
                    (asset_id, schedule_name, frequency_type, frequency_value, frequency_unit, 
                     last_completed_date, next_due_date, enabled, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING schedule_id
                    """,
                    (asset_id, schedule_name, frequency_type, frequency_value, frequency_unit, 
                     last_completed_date, next_due_date, True, datetime.now(), datetime.now())
                )
                
                schedule_id = cursor.fetchone()[0]
                
                # Create job plan
                plan_name = f"{name_suffix} Plan for {asset_name}"
                description = f"Standard {name_suffix.lower()} procedure for {asset_type if asset_type else 'equipment'}"
                safety_instructions = "Wear appropriate PPE. Follow lockout/tagout procedures. Ensure area is clear of personnel."
                
                cursor.execute(
                    """
                    INSERT INTO pm_job_plans
                    (schedule_id, plan_name, description, estimated_duration, safety_instructions, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING plan_id
                    """,
                    (schedule_id, plan_name, description, estimated_duration, safety_instructions, datetime.now())
                )
                
                plan_id = cursor.fetchone()[0]
                
                # Add tasks to the job plan
                add_pm_tasks(cursor, plan_id, pm_task_templates)
                
                schedules_added += 1
                
                if schedules_added % 10 == 0:
                    print(f"  - Added {schedules_added} PM schedules so far...")
        
        print(f"Successfully added {schedules_added} PM schedules with job plans!")
        
        # Now add meters and readings
        print("\nAdding asset meters and readings...")
        
        # Track how many we've added
        meters_added = 0
        
        # For each asset, create meters
        for asset_id, asset_name, asset_type in assets:
            # Check if asset already has meters
            cursor.execute("SELECT COUNT(*) FROM asset_meters WHERE asset_id = %s", (asset_id,))
            meter_count = cursor.fetchone()[0]
            
            if meter_count > 0:
                print(f"  - Skipping asset {asset_name} (ID: {asset_id}) - already has meters")
                continue
            
            # Determine which template to use
            template_key = 'default'
            for key in meter_templates.keys():
                if asset_type and key.lower() in asset_type.lower():
                    template_key = key
                    break
            
            # Get the template
            templates = meter_templates[template_key]
            
            # Determine how many meters to create (1-3)
            num_meters = random.randint(1, min(3, len(templates)))
            
            # Select random templates
            selected_templates = random.sample(templates, num_meters)
            
            for template in selected_templates:
                meter_name, meter_type, unit = template
                
                # Create meter name
                full_meter_name = f"{asset_name} - {meter_name}"
                
                # Generate current reading based on meter type
                if meter_type == 'Runtime':
                    current_reading = random.randint(1000, 50000)
                elif meter_type == 'Counter':
                    current_reading = random.randint(10000, 1000000)
                else:  # Gauge
                    if 'Temperature' in meter_name:
                        current_reading = random.uniform(20, 80)
                    elif 'Pressure' in meter_name:
                        current_reading = random.uniform(50, 150)
                    else:
                        current_reading = random.uniform(10, 1000)
                
                # Round to 2 decimal places
                current_reading = round(current_reading, 2)
                
                # Last reading date
                last_reading_date = datetime.now() - timedelta(days=random.randint(1, 30))
                
                # Insert meter
                cursor.execute(
                    """
                    INSERT INTO asset_meters
                    (asset_id, meter_name, meter_type, unit_of_measure, current_reading, last_reading_date, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING meter_id
                    """,
                    (asset_id, full_meter_name, meter_type, unit, current_reading, last_reading_date, datetime.now())
                )
                
                meter_id = cursor.fetchone()[0]
                
                # Add historical readings
                add_meter_readings(cursor, meter_id, meter_type, current_reading, last_reading_date)
                
                meters_added += 1
                
                if meters_added % 10 == 0:
                    print(f"  - Added {meters_added} meters so far...")
        
        # Commit the changes
        conn.commit()
        print(f"Successfully added {meters_added} meters with historical readings!")
        
    except Exception as e:
        print(f"Error enriching PM data: {e}")
        import traceback
        traceback.print_exc()
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def add_pm_tasks(cursor, plan_id, task_templates):
    """Add tasks to a PM job plan."""
    # Determine which task categories to include
    task_categories = random.sample(list(task_templates.keys()), 
                                  k=random.randint(3, len(task_templates)))
    
    sequence = 1
    for category in task_categories:
        # Pick 1-3 tasks from this category
        tasks = random.sample(task_templates[category], 
                             k=random.randint(1, min(3, len(task_templates[category]))))
        
        for task_desc, est_hours, req_tools, req_parts in tasks:
            cursor.execute(
                """
                INSERT INTO pm_job_tasks
                (plan_id, task_description, sequence_number, estimated_hours, required_tools, required_parts)
                VALUES (%s, %s, %s, %s, %s, %s)
                """,
                (plan_id, f"{category}: {task_desc}", sequence, est_hours, req_tools, req_parts)
            )
            
            sequence += 1

def add_meter_readings(cursor, meter_id, meter_type, current_reading, last_reading_date):
    """Add historical meter readings."""
    # Determine how many readings to create (5-24)
    num_readings = random.randint(5, 24)
    
    # Start with the current reading and work backwards
    reading_value = current_reading
    reading_date = last_reading_date
    
    # People who might have entered readings
    entered_by_options = ['John Smith', 'Jane Doe', 'Bob Johnson', 'Alice Brown', 'System']
    
    for i in range(num_readings):
        # Decrease the reading as we go back in time
        if meter_type == 'Runtime':
            # Runtime meters increase steadily
            reading_value -= random.uniform(50, 200)
        elif meter_type == 'Counter':
            # Counters increase by varying amounts
            reading_value -= random.uniform(500, 2000)
        else:  # Gauge
            # Gauges fluctuate around a mean
            if 'Temperature' in meter_type:
                reading_value = current_reading + random.uniform(-10, 10)
            elif 'Pressure' in meter_type:
                reading_value = current_reading + random.uniform(-20, 20)
            else:
                reading_value = current_reading + random.uniform(-100, 100)
        
        # Ensure reading is not negative
        reading_value = max(0, reading_value)
        
        # Round to 2 decimal places
        reading_value = round(reading_value, 2)
        
        # Go back in time
        reading_date -= timedelta(days=random.randint(5, 30))
        
        # Random person who entered the reading
        entered_by = random.choice(entered_by_options)
        
        cursor.execute(
            """
            INSERT INTO meter_readings
            (meter_id, reading_value, reading_date, entered_by, created_at)
            VALUES (%s, %s, %s, %s, %s)
            """,
            (meter_id, reading_value, reading_date, entered_by, datetime.now())
        )

if __name__ == "__main__":
    enrich_pm_data()
