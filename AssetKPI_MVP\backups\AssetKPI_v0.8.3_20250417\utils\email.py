"""
Email utility functions for the AssetKPI application.

This module provides functions for sending emails, including
reports and notifications to users.
"""
import os
import smtplib
import ssl
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from typing import List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Email configuration
SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.gmail.com")
SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
SMTP_USERNAME = os.getenv("SMTP_USERNAME", "")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "")
EMAIL_FROM = os.getenv("EMAIL_FROM", "<EMAIL>")
EMAIL_ENABLED = os.getenv("EMAIL_ENABLED", "false").lower() == "true"

def send_email(
    recipients: List[str],
    subject: str,
    html_content: str,
    text_content: Optional[str] = None,
    attachment_path: Optional[str] = None
) -> bool:
    """
    Send an email to the specified recipients.
    
    Args:
        recipients: List of email addresses to send to
        subject: Email subject
        html_content: HTML content of the email
        text_content: Plain text content of the email (optional)
        attachment_path: Path to a file to attach (optional)
        
    Returns:
        True if the email was sent successfully, False otherwise
    """
    if not EMAIL_ENABLED:
        print("Email sending is disabled. Set EMAIL_ENABLED=true to enable.")
        return False
    
    if not SMTP_USERNAME or not SMTP_PASSWORD:
        print("SMTP credentials not configured. Check your .env file.")
        return False
    
    if not recipients:
        print("No recipients specified.")
        return False
    
    try:
        # Create message
        msg = MIMEMultipart("alternative")
        msg["Subject"] = subject
        msg["From"] = EMAIL_FROM
        msg["To"] = ", ".join(recipients)
        
        # Add text content if provided, otherwise create from HTML
        if text_content:
            msg.attach(MIMEText(text_content, "plain"))
        else:
            # Simple HTML to text conversion
            text = html_content.replace("<br>", "\n").replace("</p>", "\n").replace("</h1>", "\n").replace("</h2>", "\n")
            # Remove HTML tags
            import re
            text = re.sub(r'<[^>]*>', '', text)
            msg.attach(MIMEText(text, "plain"))
        
        # Add HTML content
        msg.attach(MIMEText(html_content, "html"))
        
        # Add attachment if provided
        if attachment_path and os.path.exists(attachment_path):
            with open(attachment_path, "rb") as file:
                attachment = MIMEApplication(file.read(), Name=os.path.basename(attachment_path))
                attachment["Content-Disposition"] = f'attachment; filename="{os.path.basename(attachment_path)}"'
                msg.attach(attachment)
        
        # Create secure connection and send email
        context = ssl.create_default_context()
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls(context=context)
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            server.send_message(msg)
        
        print(f"Email sent successfully to {len(recipients)} recipients")
        return True
    
    except Exception as e:
        print(f"Error sending email: {e}")
        return False

def send_report_email(
    recipients: List[str],
    subject: str,
    message: str,
    attachment_path: str
) -> bool:
    """
    Send an email with a report attachment.
    
    Args:
        recipients: List of email addresses to send to
        subject: Email subject
        message: Email message (HTML format)
        attachment_path: Path to the report file
        
    Returns:
        True if the email was sent successfully, False otherwise
    """
    return send_email(
        recipients=recipients,
        subject=subject,
        html_content=message,
        attachment_path=attachment_path
    )

def send_notification_email(
    recipients: List[str],
    subject: str,
    message: str
) -> bool:
    """
    Send a notification email without attachments.
    
    Args:
        recipients: List of email addresses to send to
        subject: Email subject
        message: Email message (HTML format)
        
    Returns:
        True if the email was sent successfully, False otherwise
    """
    return send_email(
        recipients=recipients,
        subject=subject,
        html_content=message
    )
