from typing import List, Dict, Any, Optional, TypeVar, Generic, Callable
from fastapi import Query, Request
from pydantic import BaseModel, Field
from sqlalchemy.orm import Query as SQLAlchemyQuery
from math import ceil

T = TypeVar('T')

class PaginationParams:
    """
    Common pagination parameters for list endpoints.
    """
    def __init__(
        self,
        limit: int = Query(10, ge=1, le=100, description="Number of items to return per page"),
        offset: int = Query(0, ge=0, description="Number of items to skip"),
        sort_by: Optional[str] = Query(None, description="Field to sort by"),
        sort_order: str = Query("asc", description="Sort order (asc or desc)")
    ):
        self.limit = limit
        self.offset = offset
        self.sort_by = sort_by
        self.sort_order = sort_order.lower()
        
    def get_sort_direction(self):
        """
        Get the SQLAlchemy sort direction based on sort_order.
        """
        from sqlalchemy import asc, desc
        return asc if self.sort_order == "asc" else desc


class PaginatedResponse(BaseModel, Generic[T]):
    """
    Standard response format for paginated results.
    """
    items: List[T]
    total: int
    limit: int
    offset: int
    has_more: bool
    
    # Add links for next and previous pages
    next_page: Optional[str] = None
    prev_page: Optional[str] = None
    
    class Config:
        arbitrary_types_allowed = True


def paginate_query(
    query: SQLAlchemyQuery,
    pagination: PaginationParams,
    model,
    request: Request
) -> Dict[str, Any]:
    """
    Apply pagination to a SQLAlchemy query and return a paginated response.
    
    Args:
        query: The SQLAlchemy query to paginate
        pagination: The pagination parameters
        model: The SQLAlchemy model being queried
        request: The FastAPI request object for generating links
        
    Returns:
        A dictionary with pagination metadata and results
    """
    # Apply sorting if specified
    if pagination.sort_by and hasattr(model, pagination.sort_by):
        sort_column = getattr(model, pagination.sort_by)
        sort_direction = pagination.get_sort_direction()
        query = query.order_by(sort_direction(sort_column))
    
    # Get total count before applying limit/offset
    total = query.count()
    
    # Apply limit and offset
    query = query.limit(pagination.limit).offset(pagination.offset)
    
    # Execute query
    items = query.all()
    
    # Calculate if there are more items
    has_more = (pagination.offset + pagination.limit) < total
    
    # Generate links for next and previous pages
    base_url = str(request.url).split('?')[0]
    query_params = dict(request.query_params)
    
    # Next page link
    next_page = None
    if has_more:
        next_offset = pagination.offset + pagination.limit
        query_params['offset'] = str(next_offset)
        next_page = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in query_params.items()])}"
    
    # Previous page link
    prev_page = None
    if pagination.offset > 0:
        prev_offset = max(0, pagination.offset - pagination.limit)
        query_params['offset'] = str(prev_offset)
        prev_page = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in query_params.items()])}"
    
    return {
        "items": items,
        "total": total,
        "limit": pagination.limit,
        "offset": pagination.offset,
        "has_more": has_more,
        "next_page": next_page,
        "prev_page": prev_page
    }
