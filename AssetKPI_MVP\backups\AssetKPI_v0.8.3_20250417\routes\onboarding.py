"""
Onboarding routes for the AssetKPI application.

This module defines the API routes for the onboarding process.
"""

import logging
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from models.user import User as UserModel
from schemas.user import UserOnboardingUpdate

# Create a logger for this module
logger = logging.getLogger(__name__)

# Create a router for onboarding endpoints
onboarding_router = APIRouter(prefix="/api/users/onboarding", tags=["Onboarding"])

# Store dependencies
get_db_dependency = None
get_current_user_dependency = None


def init_router(
    get_db: Callable,
    get_current_user: Callable
):
    """
    Initialize the router with dependencies.
    
    Args:
        get_db: Dependency to get database session
        get_current_user: Dependency to get current user
    """
    global get_db_dependency, get_current_user_dependency
    
    get_db_dependency = get_db
    get_current_user_dependency = get_current_user


class OnboardingProgressRequest(BaseModel):
    """
    Request model for updating onboarding progress.
    """
    step: int
    data: Dict[str, Any]


class OnboardingStatusResponse(BaseModel):
    """
    Response model for onboarding status.
    """
    completed: bool
    current_step: int
    data: Optional[Dict[str, Any]] = None


@onboarding_router.get(
    "/status",
    response_model=OnboardingStatusResponse,
    summary="Get onboarding status"
)
async def get_onboarding_status(
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Get the onboarding status for the current user.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Onboarding status
    """
    try:
        # Get user from database
        user = db.query(UserModel).filter(UserModel.user_id == current_user.user_id).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Return onboarding status
        return OnboardingStatusResponse(
            completed=user.onboarding_completed,
            current_step=user.onboarding_step or 0,
            data=user.onboarding_data
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting onboarding status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting onboarding status: {str(e)}"
        )


@onboarding_router.post(
    "/progress",
    response_model=OnboardingStatusResponse,
    summary="Update onboarding progress"
)
async def update_onboarding_progress(
    request: OnboardingProgressRequest,
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Update the onboarding progress for the current user.
    
    Args:
        request: Onboarding progress request
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated onboarding status
    """
    try:
        # Get user from database
        user = db.query(UserModel).filter(UserModel.user_id == current_user.user_id).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update user onboarding data
        user.onboarding_step = request.step
        user.onboarding_data = request.data
        user.updated_at = datetime.now()
        
        db.commit()
        
        # Return updated onboarding status
        return OnboardingStatusResponse(
            completed=user.onboarding_completed,
            current_step=user.onboarding_step,
            data=user.onboarding_data
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error updating onboarding progress: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating onboarding progress: {str(e)}"
        )


@onboarding_router.post(
    "/complete",
    response_model=OnboardingStatusResponse,
    summary="Complete onboarding"
)
async def complete_onboarding(
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Mark onboarding as complete for the current user.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated onboarding status
    """
    try:
        # Get user from database
        user = db.query(UserModel).filter(UserModel.user_id == current_user.user_id).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Mark onboarding as complete
        user.onboarding_completed = True
        user.updated_at = datetime.now()
        
        # Apply user preferences from onboarding data
        if user.onboarding_data:
            # Update user profile if provided
            if 'profile' in user.onboarding_data:
                profile_data = user.onboarding_data['profile']
                
                if 'fullName' in profile_data and profile_data['fullName']:
                    user.full_name = profile_data['fullName']
            
            # Update organization if provided
            if 'organization' in user.onboarding_data:
                org_data = user.onboarding_data['organization']
                
                # In a real implementation, you would update organization settings here
                pass
            
            # Update dashboard preferences if provided
            if 'dashboard' in user.onboarding_data:
                dashboard_data = user.onboarding_data['dashboard']
                
                # In a real implementation, you would update dashboard preferences here
                pass
        
        db.commit()
        
        # Return updated onboarding status
        return OnboardingStatusResponse(
            completed=user.onboarding_completed,
            current_step=user.onboarding_step,
            data=user.onboarding_data
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error completing onboarding: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error completing onboarding: {str(e)}"
        )


@onboarding_router.post(
    "/reset",
    response_model=OnboardingStatusResponse,
    summary="Reset onboarding"
)
async def reset_onboarding(
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Reset onboarding for the current user.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated onboarding status
    """
    try:
        # Get user from database
        user = db.query(UserModel).filter(UserModel.user_id == current_user.user_id).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Reset onboarding
        user.onboarding_completed = False
        user.onboarding_step = 0
        user.onboarding_data = {}
        user.updated_at = datetime.now()
        
        db.commit()
        
        # Return updated onboarding status
        return OnboardingStatusResponse(
            completed=user.onboarding_completed,
            current_step=user.onboarding_step,
            data=user.onboarding_data
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error resetting onboarding: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resetting onboarding: {str(e)}"
        )
