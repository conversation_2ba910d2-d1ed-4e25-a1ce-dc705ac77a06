{% extends "layout.html" %}

{% block title %}Inventory Optimization Report | AssetKPI{% endblock %}

{% block styles %}
<style>
    .report-header {
        margin-bottom: 30px;
    }
    .report-section {
        margin-bottom: 30px;
    }
    .report-table {
        font-size: 0.9rem;
    }
    .savings-high {
        color: #28a745;
        font-weight: bold;
    }
    .savings-medium {
        color: #fd7e14;
        font-weight: bold;
    }
    .risk-high {
        color: #dc3545;
        font-weight: bold;
    }
    .risk-medium {
        color: #fd7e14;
        font-weight: bold;
    }
    .risk-low {
        color: #28a745;
    }
    .chart-container {
        height: 300px;
        margin-bottom: 20px;
    }
    @media print {
        .no-print {
            display: none !important;
        }
        .page-break {
            page-break-before: always;
        }
        body {
            padding: 0;
            margin: 0;
        }
        .container-fluid {
            padding: 0;
            margin: 0;
        }
        .card {
            border: none;
        }
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
            border-bottom: 1px solid #dee2e6 !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center report-header">
        <div>
            <h1 class="mt-4">Inventory Optimization Report</h1>
            <ol class="breadcrumb mb-4 no-print">
                <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/inventory">Inventory</a></li>
                <li class="breadcrumb-item active">Optimization Report</li>
            </ol>
        </div>
        <div class="no-print">
            <button id="printReportBtn" class="btn btn-primary">
                <i class="fas fa-print"></i> Print Report
            </button>
            <button id="exportPdfBtn" class="btn btn-secondary ms-2">
                <i class="fas fa-file-pdf"></i> Export PDF
            </button>
        </div>
    </div>

    <div class="card mb-4 report-section">
        <div class="card-header">
            <h5>Executive Summary</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th>Report Date:</th>
                            <td id="reportDate">-</td>
                        </tr>
                        <tr>
                            <th>Total Parts:</th>
                            <td id="totalParts">-</td>
                        </tr>
                        <tr>
                            <th>Total Inventory Value:</th>
                            <td id="totalValue">-</td>
                        </tr>
                        <tr>
                            <th>Optimal Inventory Value:</th>
                            <td id="optimalValue">-</td>
                        </tr>
                        <tr>
                            <th>Potential Savings:</th>
                            <td id="potentialSavings" class="savings-high">-</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <canvas id="summaryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4 report-section">
        <div class="card-header">
            <h5>Inventory Status</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-danger text-white mb-3">
                        <div class="card-body text-center">
                            <h3 id="stockoutRiskCount">-</h3>
                            <p class="mb-0">Parts at Risk of Stockout</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white mb-3">
                        <div class="card-body text-center">
                            <h3 id="optimalStockCount">-</h3>
                            <p class="mb-0">Parts at Optimal Level</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white mb-3">
                        <div class="card-body text-center">
                            <h3 id="overstockCount">-</h3>
                            <p class="mb-0">Overstocked Parts</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="chart-container mt-3">
                <canvas id="inventoryStatusChart"></canvas>
            </div>
        </div>
    </div>

    <div class="card mb-4 report-section">
        <div class="card-header">
            <h5>Top Savings Opportunities</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered report-table" id="savingsTable">
                    <thead>
                        <tr>
                            <th>Part ID</th>
                            <th>Part Name</th>
                            <th>Current Stock</th>
                            <th>Optimal Stock</th>
                            <th>Difference</th>
                            <th>Current Cost</th>
                            <th>Optimal Cost</th>
                            <th>Potential Savings</th>
                            <th>ABC Class</th>
                        </tr>
                    </thead>
                    <tbody id="savingsTableBody">
                        <tr>
                            <td colspan="9" class="text-center">Loading data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4 report-section page-break">
        <div class="card-header">
            <h5>Stockout Risk</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered report-table" id="stockoutTable">
                    <thead>
                        <tr>
                            <th>Part ID</th>
                            <th>Part Name</th>
                            <th>Current Stock</th>
                            <th>Reorder Point</th>
                            <th>Days of Supply</th>
                            <th>Lead Time (days)</th>
                            <th>Stockout Risk</th>
                            <th>ABC Class</th>
                        </tr>
                    </thead>
                    <tbody id="stockoutTableBody">
                        <tr>
                            <td colspan="8" class="text-center">Loading data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4 report-section">
        <div class="card-header">
            <h5>Recommendations</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered report-table" id="recommendationsTable">
                    <thead>
                        <tr>
                            <th>Part ID</th>
                            <th>Part Name</th>
                            <th>Recommendation Type</th>
                            <th>Recommendation</th>
                            <th>Priority</th>
                        </tr>
                    </thead>
                    <tbody id="recommendationsTableBody">
                        <tr>
                            <td colspan="5" class="text-center">Loading data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4 report-section page-break">
        <div class="card-header">
            <h5>ABC Analysis</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <canvas id="abcChart"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Class</th>
                                <th>Parts Count</th>
                                <th>Inventory Value</th>
                                <th>% of Total Value</th>
                            </tr>
                        </thead>
                        <tbody id="abcTableBody">
                            <tr>
                                <td colspan="4" class="text-center">Loading data...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mb-4 no-print">
        <p class="text-muted">Report generated on <span id="generatedDate">-</span></p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Add Chart.js library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize authentication UI
        AssetKPIAuth.initAuth(updateUI);
        
        // Add event listeners for buttons
        document.getElementById('printReportBtn').addEventListener('click', function() {
            window.print();
        });
        
        document.getElementById('exportPdfBtn').addEventListener('click', function() {
            alert('PDF export functionality will be implemented in a future update.');
        });
        
        // Set report date
        const now = new Date();
        document.getElementById('reportDate').textContent = now.toLocaleDateString();
        document.getElementById('generatedDate').textContent = now.toLocaleString();
        
        // Load report data
        function loadReportData() {
            AssetKPIAuth.authenticatedFetch('/api/inventory/optimization-report')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch optimization report data');
                    }
                    return response.json();
                })
                .then(data => {
                    // Update executive summary
                    updateExecutiveSummary(data);
                    
                    // Update inventory status
                    updateInventoryStatus(data);
                    
                    // Update savings opportunities
                    updateSavingsOpportunities(data);
                    
                    // Update stockout risk
                    updateStockoutRisk(data);
                    
                    // Update recommendations
                    updateRecommendations(data);
                    
                    // Update ABC analysis
                    updateABCAnalysis(data);
                })
                .catch(error => {
                    console.error('Error loading report data:', error);
                    alert(`Error loading report data: ${error.message}`);
                });
        }
        
        // Update executive summary
        function updateExecutiveSummary(data) {
            document.getElementById('totalParts').textContent = data.summary.total_parts;
            document.getElementById('totalValue').textContent = `$${data.summary.total_value.toFixed(2)}`;
            document.getElementById('optimalValue').textContent = `$${data.summary.optimal_value.toFixed(2)}`;
            document.getElementById('potentialSavings').textContent = `$${data.summary.potential_savings.toFixed(2)}`;
            
            // Create summary chart
            const ctx = document.getElementById('summaryChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Current Value', 'Optimal Value', 'Potential Savings'],
                    datasets: [{
                        label: 'Inventory Value ($)',
                        data: [
                            data.summary.total_value,
                            data.summary.optimal_value,
                            data.summary.potential_savings
                        ],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(40, 167, 69, 0.7)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(40, 167, 69, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Value ($)'
                            }
                        }
                    }
                }
            });
        }
        
        // Update inventory status
        function updateInventoryStatus(data) {
            document.getElementById('stockoutRiskCount').textContent = data.status.stockout_risk_count;
            document.getElementById('optimalStockCount').textContent = data.status.optimal_stock_count;
            document.getElementById('overstockCount').textContent = data.status.overstock_count;
            
            // Create inventory status chart
            const ctx = document.getElementById('inventoryStatusChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['At Risk of Stockout', 'Optimal Level', 'Overstocked'],
                    datasets: [{
                        data: [
                            data.status.stockout_risk_count,
                            data.status.optimal_stock_count,
                            data.status.overstock_count
                        ],
                        backgroundColor: [
                            'rgba(220, 53, 69, 0.7)',
                            'rgba(40, 167, 69, 0.7)',
                            'rgba(255, 193, 7, 0.7)'
                        ],
                        borderColor: [
                            'rgba(220, 53, 69, 1)',
                            'rgba(40, 167, 69, 1)',
                            'rgba(255, 193, 7, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        }
        
        // Update savings opportunities
        function updateSavingsOpportunities(data) {
            const tableBody = document.getElementById('savingsTableBody');
            
            if (data.savings.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="9" class="text-center">No savings opportunities found</td></tr>';
                return;
            }
            
            let html = '';
            data.savings.forEach(item => {
                const savingsClass = item.potential_savings > 100 ? 'savings-high' : 
                                    item.potential_savings > 50 ? 'savings-medium' : '';
                
                html += `
                    <tr>
                        <td>${item.part_id}</td>
                        <td>${item.part_name}</td>
                        <td>${item.current_stock}</td>
                        <td>${item.optimal_stock}</td>
                        <td>${item.stock_difference}</td>
                        <td>$${item.current_cost.toFixed(2)}</td>
                        <td>$${item.optimal_cost.toFixed(2)}</td>
                        <td class="${savingsClass}">$${item.potential_savings.toFixed(2)}</td>
                        <td>${item.abc_classification || 'N/A'}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }
        
        // Update stockout risk
        function updateStockoutRisk(data) {
            const tableBody = document.getElementById('stockoutTableBody');
            
            if (data.stockout_risk.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center">No parts at risk of stockout</td></tr>';
                return;
            }
            
            let html = '';
            data.stockout_risk.forEach(item => {
                const riskClass = item.stockout_risk > 50 ? 'risk-high' : 
                                 item.stockout_risk > 20 ? 'risk-medium' : 'risk-low';
                
                html += `
                    <tr>
                        <td>${item.part_id}</td>
                        <td>${item.part_name}</td>
                        <td>${item.current_stock}</td>
                        <td>${item.reorder_point}</td>
                        <td>${item.days_of_supply}</td>
                        <td>${item.lead_time_days}</td>
                        <td class="${riskClass}">${item.stockout_risk.toFixed(1)}%</td>
                        <td>${item.abc_classification || 'N/A'}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }
        
        // Update recommendations
        function updateRecommendations(data) {
            const tableBody = document.getElementById('recommendationsTableBody');
            
            if (data.recommendations.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No recommendations found</td></tr>';
                return;
            }
            
            let html = '';
            data.recommendations.forEach(item => {
                const priorityClass = item.priority === 1 ? 'risk-high' : 
                                     item.priority === 2 ? 'risk-medium' : '';
                
                let priorityText = '';
                switch (item.priority) {
                    case 1:
                        priorityText = 'High';
                        break;
                    case 2:
                        priorityText = 'Medium';
                        break;
                    case 3:
                        priorityText = 'Low';
                        break;
                    default:
                        priorityText = 'Unknown';
                }
                
                html += `
                    <tr>
                        <td>${item.part_id}</td>
                        <td>${item.part_name}</td>
                        <td>${item.recommendation_type}</td>
                        <td>${item.reason}</td>
                        <td class="${priorityClass}">${priorityText}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }
        
        // Update ABC analysis
        function updateABCAnalysis(data) {
            const tableBody = document.getElementById('abcTableBody');
            
            if (!data.abc_analysis || Object.keys(data.abc_analysis).length === 0) {
                tableBody.innerHTML = '<tr><td colspan="4" class="text-center">No ABC analysis data found</td></tr>';
                return;
            }
            
            let html = '';
            const classes = ['A', 'B', 'C', 'Unclassified'];
            const totalValue = classes.reduce((sum, cls) => sum + (data.abc_analysis[cls]?.value || 0), 0);
            
            classes.forEach(cls => {
                const analysis = data.abc_analysis[cls] || { count: 0, value: 0 };
                const percentage = totalValue > 0 ? (analysis.value / totalValue * 100).toFixed(1) : '0.0';
                
                html += `
                    <tr>
                        <td>${cls}</td>
                        <td>${analysis.count}</td>
                        <td>$${analysis.value.toFixed(2)}</td>
                        <td>${percentage}%</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
            
            // Create ABC chart
            const ctx = document.getElementById('abcChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: classes,
                    datasets: [{
                        data: classes.map(cls => data.abc_analysis[cls]?.value || 0),
                        backgroundColor: [
                            'rgba(220, 53, 69, 0.7)',  // A - Red
                            'rgba(255, 193, 7, 0.7)',  // B - Yellow
                            'rgba(40, 167, 69, 0.7)',  // C - Green
                            'rgba(108, 117, 125, 0.7)' // Unclassified - Gray
                        ],
                        borderColor: [
                            'rgba(220, 53, 69, 1)',
                            'rgba(255, 193, 7, 1)',
                            'rgba(40, 167, 69, 1)',
                            'rgba(108, 117, 125, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const percentage = totalValue > 0 ? (value / totalValue * 100).toFixed(1) : '0.0';
                                    return `${label}: $${value.toFixed(2)} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // Function to update UI based on authentication state
        function updateUI(user) {
            if (user) {
                // User is signed in, load data
                loadReportData();
            } else {
                // User is signed out, show message
                document.getElementById('savingsTableBody').innerHTML = 
                    '<tr><td colspan="9" class="text-center">Please sign in to view report data</td></tr>';
                document.getElementById('stockoutTableBody').innerHTML = 
                    '<tr><td colspan="8" class="text-center">Please sign in to view report data</td></tr>';
                document.getElementById('recommendationsTableBody').innerHTML = 
                    '<tr><td colspan="5" class="text-center">Please sign in to view report data</td></tr>';
                document.getElementById('abcTableBody').innerHTML = 
                    '<tr><td colspan="4" class="text-center">Please sign in to view report data</td></tr>';
            }
        }
    });
</script>
{% endblock %}
