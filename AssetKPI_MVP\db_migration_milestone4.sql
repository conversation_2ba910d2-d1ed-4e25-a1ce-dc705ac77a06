-- AssetKPI Database Migration: Milestone 4 - Preventive Maintenance

-- 4.1 PM Schedules
CREATE TABLE IF NOT EXISTS pm_schedules (
    schedule_id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    schedule_name VARCHAR(100) NOT NULL,
    frequency_type VARCHAR(50) NOT NULL, -- Calendar, Meter, or Condition
    frequency_value INTEGER,
    frequency_unit VARCHAR(20), -- Days, Weeks, Months, Years, Hours, Cycles, etc.
    last_completed_date TIMESTAMP,
    next_due_date TIMESTAMP,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 4.2 PM Job Plans
CREATE TABLE IF NOT EXISTS pm_job_plans (
    plan_id SERIAL PRIMARY KEY,
    schedule_id INTEGER REFERENCES pm_schedules(schedule_id),
    plan_name VARCHAR(100) NOT NULL,
    description TEXT,
    estimated_duration INTEGER, -- in minutes
    safety_instructions TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 4.3 PM Job Tasks
CREATE TABLE IF NOT EXISTS pm_job_tasks (
    task_id SERIAL PRIMARY KEY,
    plan_id INTEGER REFERENCES pm_job_plans(plan_id),
    task_description TEXT NOT NULL,
    sequence_number INTEGER,
    estimated_hours NUMERIC(5, 2),
    required_tools TEXT,
    required_parts TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 4.4 Asset Meters
CREATE TABLE IF NOT EXISTS asset_meters (
    meter_id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    meter_name VARCHAR(100) NOT NULL,
    meter_type VARCHAR(50), -- Runtime, Cycles, Distance, etc.
    unit_of_measure VARCHAR(50),
    current_reading NUMERIC(15, 5),
    last_reading_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS meter_readings (
    reading_id SERIAL PRIMARY KEY,
    meter_id INTEGER REFERENCES asset_meters(meter_id),
    reading_value NUMERIC(15, 5),
    reading_date TIMESTAMP,
    entered_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_pm_schedules_asset_id ON pm_schedules(asset_id);
CREATE INDEX IF NOT EXISTS idx_pm_job_plans_schedule_id ON pm_job_plans(schedule_id);
CREATE INDEX IF NOT EXISTS idx_pm_job_tasks_plan_id ON pm_job_tasks(plan_id);
CREATE INDEX IF NOT EXISTS idx_asset_meters_asset_id ON asset_meters(asset_id);
CREATE INDEX IF NOT EXISTS idx_meter_readings_meter_id ON meter_readings(meter_id);

-- End of Milestone 4 migration script
