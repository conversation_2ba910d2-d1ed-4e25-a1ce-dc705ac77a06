import time
import re
from collections import defaultdict
from typing import Dict, Tuple, Optional, Callable, List, Any, Union
from fastapi import Request, Response, Depends
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_429_TOO_MANY_REQUESTS
from enum import Enum

class UserRole(str, Enum):
    """User roles for rate limiting purposes."""
    ANONYMOUS = "ANONYMOUS"
    VIEWER = "VIEWER"
    ENGINEER = "ENGINEER"
    MANAGER = "MANAGER"
    ADMIN = "ADMIN"
    API_KEY = "API_KEY"


class EndpointTier(str, Enum):
    """Endpoint tiers for rate limiting purposes."""
    STANDARD = "STANDARD"  # Regular API endpoints
    HIGH_VOLUME = "HIGH_VOLUME"  # Endpoints that can handle more traffic
    LOW_VOLUME = "LOW_VOLUME"  # Endpoints that should be more restricted
    CRITICAL = "CRITICAL"  # Critical endpoints with highest restrictions


class RateLimitConfig:
    """Configuration for rate limiting."""
    def __init__(self):
        # Default limits by role (requests per minute)
        self.role_limits = {
            UserRole.ANONYMOUS: 60,  # 1 request per second
            UserRole.VIEWER: 100,    # ~1.7 requests per second
            UserRole.ENGINEER: 150,  # 2.5 requests per second
            UserRole.MANAGER: 180,   # 3 requests per second
            UserRole.ADMIN: 200,     # ~3.3 requests per second
            UserRole.API_KEY: 300,   # 5 requests per second
        }

        # Multipliers by endpoint tier
        self.tier_multipliers = {
            EndpointTier.STANDARD: 1.0,
            EndpointTier.HIGH_VOLUME: 2.0,
            EndpointTier.LOW_VOLUME: 0.5,
            EndpointTier.CRITICAL: 0.2,
        }

        # Endpoint patterns and their tiers
        self.endpoint_tiers = {
            r"/api/kpis/.*": EndpointTier.STANDARD,
            r"/api/assets/.*": EndpointTier.STANDARD,
            r"/api/inventory/parts": EndpointTier.HIGH_VOLUME,
            r"/api/inventory/parts/\d+": EndpointTier.STANDARD,
            r"/api/workorders": EndpointTier.HIGH_VOLUME,
            r"/api/workorders/\d+": EndpointTier.STANDARD,
            r"/api/users/.*": EndpointTier.LOW_VOLUME,
            r"/api/ingest/.*": EndpointTier.LOW_VOLUME,
            r"/api/dashboard/.*": EndpointTier.HIGH_VOLUME,
            r"/api/analytics/.*": EndpointTier.LOW_VOLUME,
            r"/api/.*/bulk/.*": EndpointTier.CRITICAL,
        }

        # Time window in seconds (default: 60 seconds)
        self.window = 60

    def get_limit_for_request(self, request: Request, role: str) -> int:
        """Get the rate limit for a specific request based on path and role."""
        path = request.url.path

        # Find matching endpoint tier
        tier = EndpointTier.STANDARD  # Default tier
        for pattern, endpoint_tier in self.endpoint_tiers.items():
            if re.match(pattern, path):
                tier = endpoint_tier
                break

        # Get base limit for role
        base_limit = self.role_limits.get(role, self.role_limits[UserRole.ANONYMOUS])

        # Apply tier multiplier
        limit = int(base_limit * self.tier_multipliers[tier])

        return max(1, limit)  # Ensure at least 1 request is allowed


class RateLimiter:
    """
    An enhanced in-memory rate limiter for FastAPI with support for
    different roles and endpoint tiers.
    """
    def __init__(self, config: Optional[RateLimitConfig] = None):
        """
        Initialize the rate limiter.

        Args:
            config: Rate limit configuration
        """
        self.config = config or RateLimitConfig()
        self.requests: Dict[str, Dict[float, int]] = defaultdict(dict)

    def _get_key(self, request: Request, role: str) -> str:
        """
        Get a unique key for the request based on client IP, role, and optional API key.

        Args:
            request: The FastAPI request object
            role: User role

        Returns:
            A string key for rate limiting
        """
        # Try to get API key from header
        api_key = request.headers.get("X-API-Key", "")

        # If API key is present, use it as the key
        if api_key:
            return f"api_key:{api_key}:{request.url.path}"

        # Otherwise use client IP and role
        client_ip = request.client.host if request.client else "unknown"
        return f"ip:{client_ip}:{role}:{request.url.path}"

    def is_rate_limited(self, request: Request, role: str = UserRole.ANONYMOUS) -> Tuple[bool, int, int, int]:
        """
        Check if a request should be rate limited.

        Args:
            request: The FastAPI request object
            role: User role

        Returns:
            A tuple of (is_limited, remaining, reset_time, limit)
        """
        # Determine the appropriate limit based on role and endpoint
        limit = self.config.get_limit_for_request(request, role)

        # Get the key for this request
        key = self._get_key(request, role)
        now = time.time()

        # Remove expired entries
        self.requests[key] = {ts: count for ts, count in self.requests[key].items()
                             if now - ts < self.config.window}

        # Count recent requests
        total_requests = sum(self.requests[key].values())

        # Calculate remaining requests and reset time
        remaining = max(0, limit - total_requests)
        reset_time = int(now + self.config.window)

        # Check if rate limited
        if total_requests >= limit:
            return True, remaining, reset_time, limit

        # Record this request
        self.requests[key][now] = self.requests[key].get(now, 0) + 1

        return False, remaining, reset_time, limit

class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Enhanced middleware for rate limiting in FastAPI with support for
    different roles and endpoint tiers.
    """
    def __init__(
        self,
        app,
        config: Optional[RateLimitConfig] = None,
        exclude_paths: Optional[List[str]] = None,
        get_response_headers: Optional[Callable] = None,
        get_user_role: Optional[Callable] = None
    ):
        """
        Initialize the rate limit middleware.

        Args:
            app: The FastAPI application
            config: Rate limit configuration
            exclude_paths: List of paths to exclude from rate limiting
            get_response_headers: Function to get custom response headers
            get_user_role: Function to get the user role from the request
        """
        super().__init__(app)
        self.limiter = RateLimiter(config)
        self.exclude_paths = exclude_paths or []
        self.get_response_headers = get_response_headers
        self.get_user_role = get_user_role

    async def dispatch(self, request: Request, call_next):
        """
        Process the request and apply rate limiting.

        Args:
            request: The FastAPI request
            call_next: The next middleware or route handler

        Returns:
            The response
        """
        # Skip rate limiting for excluded paths
        path = request.url.path
        if any(path.startswith(excluded) for excluded in self.exclude_paths):
            return await call_next(request)

        # Determine user role
        role = UserRole.ANONYMOUS
        if request.headers.get("X-API-Key"):
            role = UserRole.API_KEY
        elif self.get_user_role:
            try:
                role = await self.get_user_role(request)
            except Exception:
                # If there's an error getting the role, use ANONYMOUS
                pass

        # Check rate limit
        is_limited, remaining, reset_time, limit = self.limiter.is_rate_limited(request, role)

        # If rate limited, return 429 response
        if is_limited:
            headers = {
                "X-RateLimit-Limit": str(limit),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(reset_time),
                "Retry-After": str(reset_time - int(time.time())),
                "X-RateLimit-Role": str(role),
                "X-RateLimit-Path": path
            }

            # Add custom headers if provided
            if self.get_response_headers:
                custom_headers = self.get_response_headers(request)
                if custom_headers:
                    headers.update(custom_headers)

            return Response(
                content=f"Rate limit exceeded. Limit: {limit} requests per minute for {role} role.",
                status_code=HTTP_429_TOO_MANY_REQUESTS,
                headers=headers
            )

        # Process the request
        response = await call_next(request)

        # Add rate limit headers to the response
        response.headers["X-RateLimit-Limit"] = str(limit)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(reset_time)
        response.headers["X-RateLimit-Role"] = str(role)

        return response


# Dependency for rate limiting specific endpoints
def rate_limit(
    request: Request,
    role: str = UserRole.ANONYMOUS,
    tier: str = EndpointTier.STANDARD,
    config: Optional[RateLimitConfig] = None
):
    """
    Dependency for rate limiting specific endpoints.

    Args:
        request: The FastAPI request
        role: User role
        tier: Endpoint tier
        config: Rate limit configuration

    Raises:
        HTTPException: If the rate limit is exceeded
    """
    config = config or RateLimitConfig()
    limiter = RateLimiter(config)

    # Override the tier for this specific endpoint
    original_get_limit = config.get_limit_for_request

    def custom_get_limit(req, r):
        # Get base limit for role
        base_limit = config.role_limits.get(r, config.role_limits[UserRole.ANONYMOUS])
        # Apply tier multiplier
        limit = int(base_limit * config.tier_multipliers[tier])
        return max(1, limit)

    # Monkey patch the config temporarily
    config.get_limit_for_request = custom_get_limit

    # Check rate limit
    is_limited, remaining, reset_time, limit = limiter.is_rate_limited(request, role)

    # Restore original method
    config.get_limit_for_request = original_get_limit

    # If rate limited, raise an exception
    if is_limited:
        headers = {
            "X-RateLimit-Limit": str(limit),
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": str(reset_time),
            "Retry-After": str(reset_time - int(time.time())),
            "X-RateLimit-Role": str(role),
            "X-RateLimit-Path": request.url.path
        }

        from fastapi import HTTPException
        raise HTTPException(
            status_code=HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Rate limit exceeded. Limit: {limit} requests per minute for {role} role.",
            headers=headers
        )
