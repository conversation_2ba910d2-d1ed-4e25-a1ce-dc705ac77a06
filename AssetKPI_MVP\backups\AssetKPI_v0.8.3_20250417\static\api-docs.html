<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AssetKPI API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
            font-size: 1.3rem;
        }
        .endpoint {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
        }
        .method {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .get {
            background-color: #61affe;
        }
        .post {
            background-color: #49cc90;
        }
        .put {
            background-color: #fca130;
        }
        .delete {
            background-color: #f93e3e;
        }
        .path {
            font-family: monospace;
            font-size: 1.1rem;
        }
        .description {
            margin-top: 10px;
            margin-bottom: 10px;
        }
        .params-table {
            width: 100%;
            margin-top: 15px;
            margin-bottom: 15px;
        }
        .section {
            margin-bottom: 40px;
        }
        .tag-section {
            margin-bottom: 60px;
        }
        .version-badge {
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AssetKPI API Documentation <span class="version-badge">v0.7.1</span></h1>
        
        <div class="section">
            <p>
                AssetKPI provides a comprehensive API for asset management, inventory optimization, and KPI calculation.
                This documentation provides an overview of the available endpoints and their functionality.
            </p>
            
            <h2>Authentication</h2>
            <p>
                The API supports two authentication methods:
            </p>
            <ul>
                <li><strong>Firebase Authentication</strong>: For browser-based applications and user-specific operations</li>
                <li><strong>API Key Authentication</strong>: For server-to-server communication and automated processes</li>
            </ul>
            
            <h3>Firebase Authentication</h3>
            <p>
                To authenticate with Firebase, include the Firebase ID token in the Authorization header:
            </p>
            <pre><code>Authorization: Bearer &lt;firebase-id-token&gt;</code></pre>
            
            <h3>API Key Authentication</h3>
            <p>
                To authenticate with an API key, include the API key in the X-API-KEY header:
            </p>
            <pre><code>X-API-KEY: &lt;your-api-key&gt;</code></pre>
        </div>
        
        <div class="tag-section">
            <h2>Assets</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/assets</span>
                <div class="description">
                    Get a list of all assets. Supports pagination and filtering.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/assets/{asset_id}</span>
                <div class="description">
                    Get details for a specific asset.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="path">/api/assets</span>
                <div class="description">
                    Create a new asset.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method put">PUT</span>
                <span class="path">/api/assets/{asset_id}</span>
                <div class="description">
                    Update an existing asset.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method delete">DELETE</span>
                <span class="path">/api/assets/{asset_id}</span>
                <div class="description">
                    Delete an asset.
                </div>
            </div>
        </div>
        
        <div class="tag-section">
            <h2>Inventory</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/inventory/parts</span>
                <div class="description">
                    Get a list of all spare parts. Supports pagination and filtering.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/inventory/parts/{part_id}</span>
                <div class="description">
                    Get details for a specific spare part.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="path">/api/inventory/parts</span>
                <div class="description">
                    Create a new spare part.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method put">PUT</span>
                <span class="path">/api/inventory/parts/{part_id}</span>
                <div class="description">
                    Update an existing spare part.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method delete">DELETE</span>
                <span class="path">/api/inventory/parts/{part_id}</span>
                <div class="description">
                    Delete a spare part.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/inventory/optimization-report</span>
                <div class="description">
                    Get inventory optimization report data.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/inventory/eoq-summary</span>
                <div class="description">
                    Get summary of EOQ calculations.
                </div>
            </div>
        </div>
        
        <div class="tag-section">
            <h2>KPIs</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/kpis</span>
                <div class="description">
                    Get a list of all KPIs. Supports pagination and filtering.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/kpis/{kpi_id}</span>
                <div class="description">
                    Get details for a specific KPI.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/kpis/history/{kpi_name}</span>
                <div class="description">
                    Get historical data for a specific KPI.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/kpi/analytics</span>
                <div class="description">
                    Get KPI analytics data for visualization.
                </div>
            </div>
        </div>
        
        <div class="tag-section">
            <h2>Work Orders</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/workorders</span>
                <div class="description">
                    Get a list of all work orders. Supports pagination and filtering.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/workorders/{wo_id}</span>
                <div class="description">
                    Get details for a specific work order.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="path">/api/workorders</span>
                <div class="description">
                    Create a new work order.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method put">PUT</span>
                <span class="path">/api/workorders/{wo_id}</span>
                <div class="description">
                    Update an existing work order.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method delete">DELETE</span>
                <span class="path">/api/workorders/{wo_id}</span>
                <div class="description">
                    Delete a work order.
                </div>
            </div>
        </div>
        
        <div class="tag-section">
            <h2>Users</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/users</span>
                <div class="description">
                    Get a list of all users. Supports pagination and filtering.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/users/{user_id}</span>
                <div class="description">
                    Get details for a specific user.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/users/{user_id}/permissions</span>
                <div class="description">
                    Get permissions for a specific user.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method put">PUT</span>
                <span class="path">/api/users/{user_id}/permissions</span>
                <div class="description">
                    Update permissions for a specific user.
                </div>
            </div>
        </div>
        
        <div class="tag-section">
            <h2>Dashboard</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/dashboard/kpi/history/{kpi_name}</span>
                <div class="description">
                    Get historical data for a specific KPI for dashboard use.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/assets/count</span>
                <div class="description">
                    Get the total number of assets.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/inventory/summary</span>
                <div class="description">
                    Get inventory summary data.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/workorders/count</span>
                <div class="description">
                    Get the count of work orders, optionally filtered by status.
                </div>
            </div>
        </div>
        
        <div class="tag-section">
            <h2>Analytics</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/usage/activity</span>
                <div class="description">
                    Get user activity data for analytics.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/usage/feature-usage</span>
                <div class="description">
                    Get feature usage data for analytics.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="path">/api/usage/user-sessions</span>
                <div class="description">
                    Get user session data for analytics.
                </div>
            </div>
        </div>
        
        <div class="tag-section">
            <h2>Ingestion</h2>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="path">/api/ingest/workorder</span>
                <div class="description">
                    Ingest a single work order.
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="path">/upload/workorders</span>
                <div class="description">
                    Upload work orders via CSV file.
                </div>
            </div>
        </div>
        
        <footer class="mt-5 pt-3 border-top text-muted">
            <p>AssetKPI API Documentation v0.7.1 | &copy; 2025 AssetKPI</p>
        </footer>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
