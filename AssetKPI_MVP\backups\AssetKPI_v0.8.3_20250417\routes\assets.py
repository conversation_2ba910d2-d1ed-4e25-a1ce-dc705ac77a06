"""
Asset routes for the AssetKPI application.

This module defines the API routes for asset management.
"""

import logging
from typing import Dict, List, Any, Optional, Callable

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from models.user import User as UserModel
from models.asset import Asset as AssetModel
from schemas.asset import AssetCreate, AssetUpdate, AssetResponse, AssetListResponse
from utils.validation import validate_data, validation_error_response

# Create a logger for this module
logger = logging.getLogger(__name__)

# Create a router for asset endpoints
assets_router = APIRouter(prefix="/api/assets", tags=["Assets"])

# Store dependencies
get_db_dependency = None
get_current_user_dependency = None


def init_router(
    get_db: Callable,
    get_current_user: Callable
):
    """
    Initialize the router with dependencies.
    
    Args:
        get_db: Dependency to get database session
        get_current_user: Dependency to get current user
    """
    global get_db_dependency, get_current_user_dependency
    
    get_db_dependency = get_db
    get_current_user_dependency = get_current_user


@assets_router.post(
    "",
    response_model=AssetResponse,
    summary="Create a new asset"
)
async def create_asset(
    asset_data: dict,
    request: Request,
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Create a new asset.
    
    Args:
        asset_data: Asset data
        request: Request object
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Asset response
    """
    # Check user permissions
    if current_user.role not in ["ENGINEER", "MANAGER", "ADMIN"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to create assets"
        )
    
    try:
        # Map form field names to database field names
        field_mapping = {
            'assetName': 'assetname',
            'assetType': 'assettype',
            'assetLocation': 'location_id',
            'assetStatus': 'status',
            'assetManufacturer': 'manufacturer',
            'assetModel': 'model',
            'assetSerial': 'serialnumber',
            'assetCriticality': 'criticality',
            'assetPurchaseDate': 'purchasedate',
            'assetPurchaseCost': 'purchasecost',
            'assetDescription': 'description'
        }
        
        # Map form data to database fields
        db_data = {}
        for form_field, db_field in field_mapping.items():
            if form_field in asset_data and asset_data[form_field]:
                db_data[db_field] = asset_data[form_field]
        
        # Validate data
        validation_rules = {
            'assetname': [
                {'rule': 'required', 'message': 'Asset name is required'},
                {'rule': 'min_length', 'params': {'length': 3}, 'message': 'Asset name must be at least 3 characters'},
                {'rule': 'max_length', 'params': {'length': 100}, 'message': 'Asset name must be no more than 100 characters'}
            ],
            'assettype': [
                {'rule': 'required', 'message': 'Asset type is required'}
            ],
            'location_id': [
                {'rule': 'required', 'message': 'Location is required'}
            ],
            'status': [
                {'rule': 'required', 'message': 'Status is required'}
            ],
            'purchasecost': [
                {'rule': 'numeric', 'message': 'Purchase cost must be a number'},
                {'rule': 'min_value', 'params': {'value': 0}, 'message': 'Purchase cost must be a positive number'}
            ],
            'purchasedate': [
                {'rule': 'date', 'message': 'Please enter a valid date'}
            ]
        }
        
        errors = validate_data(db_data, validation_rules)
        if errors:
            # Map database field names back to form field names for error messages
            form_errors = {}
            for db_field, field_errors in errors.items():
                form_field = next((k for k, v in field_mapping.items() if v == db_field), db_field)
                form_errors[form_field] = field_errors
            
            return validation_error_response(form_errors)
        
        # Create asset
        new_asset = AssetModel(**db_data)
        db.add(new_asset)
        db.commit()
        db.refresh(new_asset)
        
        # Log activity
        logger.info(f"User {current_user.email} created asset {new_asset.assetid}")
        
        return {
            "success": True,
            "message": "Asset created successfully",
            "asset": new_asset
        }
    
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error creating asset: {str(e)}")
        return {
            "success": False,
            "message": "Database error creating asset",
            "errors": {"database": [str(e)]}
        }
    
    except Exception as e:
        logger.error(f"Error creating asset: {str(e)}")
        return {
            "success": False,
            "message": f"Error creating asset: {str(e)}",
            "errors": {"general": [str(e)]}
        }
