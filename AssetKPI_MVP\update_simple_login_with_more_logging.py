# Create a temporary file with the fixed code
with open('templates/simple_login.html', 'r') as file:
    content = file.read()

# Update the fetch call with more detailed logging
old_fetch = '''                // Try the debug endpoint first
                const debugResponse = await fetch('/api/debug-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({ email: userCredential.user.email })
                });
                
                if (debugResponse.ok) {
                    const debugData = await debugResponse.json();
                    console.log('Debug token response:', debugData);
                    resultDiv.textContent += '\\nDebug token verification successful. UID: ' + debugData.uid;
                } else {
                    const debugError = await debugResponse.text();
                    console.error('Debug token verification failed:', debugError);
                    resultDiv.textContent += '\\nDebug token verification failed: ' + debugResponse.statusText;
                }
                
                // Now try the regular endpoint
                const response = await fetch('/api/verify-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({ email: userCredential.user.email })
                });'''

new_fetch = '''                // Log the token for debugging
                console.log('Token first 20 chars:', token.substring(0, 20) + '...');
                
                // Check the user directly first
                try {
                    const userCheckResponse = await fetch('/api/user-check/' + userCredential.user.uid, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (userCheckResponse.ok) {
                        const userData = await userCheckResponse.json();
                        console.log('User check response:', userData);
                        resultDiv.textContent += '\\nUser found in database. UID: ' + userData.uid;
                    } else {
                        const userError = await userCheckResponse.text();
                        console.error('User check failed:', userError);
                        resultDiv.textContent += '\\nUser check failed: ' + userCheckResponse.statusText;
                    }
                } catch (error) {
                    console.error('Error checking user:', error);
                    resultDiv.textContent += '\\nError checking user: ' + error.message;
                }
                
                // Try the debug endpoint next
                try {
                    const debugResponse = await fetch('/api/debug-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + token
                        },
                        body: JSON.stringify({ email: userCredential.user.email })
                    });
                    
                    if (debugResponse.ok) {
                        const debugData = await debugResponse.json();
                        console.log('Debug token response:', debugData);
                        resultDiv.textContent += '\\nDebug token verification successful. UID: ' + debugData.uid;
                    } else {
                        const debugError = await debugResponse.text();
                        console.error('Debug token verification failed:', debugError);
                        resultDiv.textContent += '\\nDebug token verification failed: ' + debugResponse.statusText;
                        
                        // Try to parse the error
                        try {
                            const errorObj = JSON.parse(debugError);
                            console.error('Debug token error details:', errorObj);
                            resultDiv.textContent += '\\nError details: ' + errorObj.error;
                        } catch (e) {
                            console.error('Could not parse error:', debugError);
                            resultDiv.textContent += '\\nRaw error: ' + debugError;
                        }
                    }
                } catch (error) {
                    console.error('Error calling debug endpoint:', error);
                    resultDiv.textContent += '\\nError calling debug endpoint: ' + error.message;
                }
                
                // Now try the regular endpoint
                try {
                    const response = await fetch('/api/verify-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + token
                        },
                        body: JSON.stringify({ email: userCredential.user.email })
                    });'''

content = content.replace(old_fetch, new_fetch)

# Update the error handling for the regular endpoint
old_error_handling = '''                if (response.ok) {
                    const data = await response.json();
                    console.log('Token verification successful:', data);
                    resultDiv.textContent += '\\nToken verification successful. Role: ' + data.role;
                    
                    // Redirect to dashboard after successful login
                    // window.location.href = '/dashboard';
                } else {
                    const error = await response.text();
                    console.error('Token verification failed:', error);
                    resultDiv.textContent += '\\nToken verification failed: ' + response.statusText;
                }'''

new_error_handling = '''                if (response.ok) {
                    const data = await response.json();
                    console.log('Token verification successful:', data);
                    resultDiv.textContent += '\\nToken verification successful. Role: ' + data.role;
                    
                    // Redirect to dashboard after successful login
                    // window.location.href = '/dashboard';
                } else {
                    const error = await response.text();
                    console.error('Token verification failed:', error);
                    resultDiv.textContent += '\\nToken verification failed: ' + response.statusText;
                    
                    // Try to parse the error
                    try {
                        const errorObj = JSON.parse(error);
                        console.error('Token verification error details:', errorObj);
                        resultDiv.textContent += '\\nError details: ' + errorObj.error;
                    } catch (e) {
                        console.error('Could not parse error:', error);
                        resultDiv.textContent += '\\nRaw error: ' + error;
                    }
                }
                } catch (error) {
                    console.error('Error calling verify-token endpoint:', error);
                    resultDiv.textContent += '\\nError calling verify-token endpoint: ' + error.message;
                }'''

content = content.replace(old_error_handling, new_error_handling)

# Write the fixed content back to the file
with open('templates/simple_login.html', 'w') as file:
    file.write(content)

print("Updated simple_login.html with more detailed logging")
