# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Replace the problematic code
old_code = """            # --- Enhanced Safety Stock Calculation ---
            # Get configuration values from database or use defaults
            service_level_z = get_config_value(request.app, 'service_level_z', SAFETY_STOCK_SERVICE_LEVEL_Z)
            demand_variability_factor = get_config_value(request.app, 'demand_variability_factor', SAFETY_STOCK_DEMAND_VARIABILITY_FACTOR)
            lead_time_variability_factor = get_config_value(request.app, 'lead_time_variability_factor', SAFETY_STOCK_LEAD_TIME_VARIABILITY_FACTOR)

            # Calculate and store safety stock
            ss_result = calculate_and_store_safety_stock(
                app=request.app,"""

new_code = """            # --- Enhanced Safety Stock Calculation ---
            # Use default configuration values
            service_level_z = SAFETY_STOCK_SERVICE_LEVEL_Z
            demand_variability_factor = SAFETY_STOCK_DEMAND_VARIABILITY_FACTOR
            lead_time_variability_factor = SAFETY_STOCK_LEAD_TIME_VARIABILITY_FACTOR

            # Calculate and store safety stock
            ss_result = calculate_and_store_safety_stock("""

content = content.replace(old_code, new_code)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed the safety stock calculation code in main.py")
