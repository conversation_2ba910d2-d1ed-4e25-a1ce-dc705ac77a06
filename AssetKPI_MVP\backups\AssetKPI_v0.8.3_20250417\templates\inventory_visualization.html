{% extends "layout.html" %}

{% block title %}Inventory Visualization{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <h1 class="mb-4">Inventory Visualization</h1>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Inventory Metrics Overview</h5>
                </div>
                <div class="card-body">
                    <div id="loadingIndicator" style="display: none;" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p>Loading inventory data...</p>
                    </div>
                    <div id="errorMessage" style="display: none;" class="alert alert-danger"></div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="chartType">Chart Type:</label>
                                <select class="form-control" id="chartType">
                                    <option value="stockLevels">Current Stock Levels</option>
                                    <option value="optimalStock">Optimal vs. Current Stock</option>
                                    <option value="potentialSavings">Potential Savings</option>
                                    <option value="stockoutRisk">Stockout Risk</option>
                                    <option value="daysOfSupply">Days of Supply</option>
                                    <option value="abcAnalysis">ABC Analysis</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="sortBy">Sort By:</label>
                                <select class="form-control" id="sortBy">
                                    <option value="name">Part Name</option>
                                    <option value="stock">Stock Quantity</option>
                                    <option value="value">Value</option>
                                    <option value="savings">Potential Savings</option>
                                    <option value="risk">Stockout Risk</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filterBy">Filter By:</label>
                                <select class="form-control" id="filterBy">
                                    <option value="all">All Parts</option>
                                    <option value="overstock">Overstocked Parts</option>
                                    <option value="understock">Understocked Parts</option>
                                    <option value="critical">Critical Parts</option>
                                    <option value="highValue">High Value Parts</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="dateFrom">From Date:</label>
                                <input type="date" class="form-control" id="dateFrom">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="dateTo">To Date:</label>
                                <input type="date" class="form-control" id="dateTo">
                            </div>
                        </div>
                                <label for="limit">Limit:</label>
                                <select class="form-control" id="limit">
                                    <option value="10">Top 10</option>
                                    <option value="20">Top 20</option>
                                    <option value="50">Top 50</option>
                                    <option value="100">Top 100</option>
                                    <option value="all">All</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title" id="chartTitle">Current Stock Levels</h5>
                </div>
                <div class="card-body">
                    <canvas id="inventoryChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Inventory Value Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="valueDistributionChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Stockout Risk Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="stockoutRiskChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Inventory Metrics Table</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportCsv">
                            <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportPdf">
                            <i class="bi bi-file-earmark-pdf"></i> Export PDF
                        </button>
                    </div>
                </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="inventoryTable">
                            <thead>
                                <tr>
                                    <th>Part ID</th>
                                    <th>Part Name</th>
                                    <th>Current Stock</th>
                                    <th>Optimal Stock</th>
                                    <th>Difference</th>
                                    <th>Current Value</th>
                                    <th>Optimal Value</th>
                                    <th>Potential Savings</th>
                                    <th>Days of Supply</th>
                                    <th>Stockout Risk</th>
                                    <th>ABC Class</th>
                                </tr>
                            </thead>
                            <tbody id="inventoryTableBody">
                                <!-- Table data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Global variables for chart instances
    let mainChart = null;
    let valueDistributionChart = null;
    let stockoutRiskChart = null;
    
    // Function to format currency
    function formatCurrency(value) {
        return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
    }
    
    // Function to format percentage
    function formatPercentage(value) {
        return new Intl.NumberFormat('en-US', { style: 'percent', maximumFractionDigits: 1 }).format(value / 100);
    }
    
    // Function to get chart colors
    function getChartColors(count) {
        const baseColors = [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 205, 86, 0.7)',
            'rgba(201, 203, 207, 0.7)',
            'rgba(255, 99, 71, 0.7)',
            'rgba(50, 205, 50, 0.7)',
            'rgba(138, 43, 226, 0.7)'
        ];
        
        let colors = [];
        for (let i = 0; i < count; i++) {
            colors.push(baseColors[i % baseColors.length]);
        }
        return colors;
    }
    
    // Function to create or update the main chart
    function updateMainChart(data, chartType) {
        const ctx = document.getElementById('inventoryChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (mainChart) {
            mainChart.destroy();
        }
        
        // Prepare chart data based on chart type
        let chartData = {
            labels: [],
            datasets: []
        };
        
        let chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {}
                }
            },
            scales: {}
        };
        
        switch(chartType) {
            case 'stockLevels':
                chartData.labels = data.map(item => item.part_name);
                chartData.datasets = [{
                    label: 'Current Stock',
                    data: data.map(item => item.current_stock),
                    backgroundColor: getChartColors(data.length),
                    borderWidth: 1
                }];
                chartOptions.plugins.tooltip.callbacks.label = function(context) {
                    return `Stock: ${context.raw} units`;
                };
                break;
                
            case 'optimalStock':
                chartData.labels = data.map(item => item.part_name);
                chartData.datasets = [
                    {
                        label: 'Current Stock',
                        data: data.map(item => item.current_stock),
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderWidth: 1
                    },
                    {
                        label: 'Optimal Stock',
                        data: data.map(item => item.optimal_stock),
                        backgroundColor: 'rgba(255, 99, 132, 0.7)',
                        borderWidth: 1
                    }
                ];
                chartOptions.plugins.tooltip.callbacks.label = function(context) {
                    return `${context.dataset.label}: ${context.raw} units`;
                };
                break;
                
            case 'potentialSavings':
                chartData.labels = data.map(item => item.part_name);
                chartData.datasets = [{
                    label: 'Potential Savings',
                    data: data.map(item => item.potential_savings),
                    backgroundColor: getChartColors(data.length),
                    borderWidth: 1
                }];
                chartOptions.plugins.tooltip.callbacks.label = function(context) {
                    return `Savings: ${formatCurrency(context.raw)}`;
                };
                break;
                
            case 'stockoutRisk':
                chartData.labels = data.map(item => item.part_name);
                chartData.datasets = [{
                    label: 'Stockout Risk (%)',
                    data: data.map(item => item.stockout_risk),
                    backgroundColor: data.map(item => {
                        // Color based on risk level
                        if (item.stockout_risk > 50) return 'rgba(255, 99, 132, 0.7)';
                        if (item.stockout_risk > 25) return 'rgba(255, 159, 64, 0.7)';
                        return 'rgba(75, 192, 192, 0.7)';
                    }),
                    borderWidth: 1
                }];
                chartOptions.plugins.tooltip.callbacks.label = function(context) {
                    return `Risk: ${context.raw}%`;
                };
                break;
                
            case 'daysOfSupply':
                chartData.labels = data.map(item => item.part_name);
                chartData.datasets = [{
                    label: 'Days of Supply',
                    data: data.map(item => item.days_of_supply),
                    backgroundColor: getChartColors(data.length),
                    borderWidth: 1
                }];
                chartOptions.plugins.tooltip.callbacks.label = function(context) {
                    return `Days: ${context.raw}`;
                };
                break;
                
            case 'abcAnalysis':
                // Group data by ABC classification
                const abcData = {
                    'A': { count: 0, value: 0 },
                    'B': { count: 0, value: 0 },
                    'C': { count: 0, value: 0 }
                };
                
                data.forEach(item => {
                    if (item.abc_classification) {
                        abcData[item.abc_classification].count++;
                        abcData[item.abc_classification].value += (item.current_stock * item.unit_price);
                    }
                });
                
                chartData = {
                    labels: ['Class A', 'Class B', 'Class C'],
                    datasets: [
                        {
                            label: 'Item Count',
                            data: [abcData.A.count, abcData.B.count, abcData.C.count],
                            backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(75, 192, 192, 0.7)'],
                            borderWidth: 1
                        },
                        {
                            label: 'Inventory Value',
                            data: [abcData.A.value, abcData.B.value, abcData.C.value],
                            backgroundColor: ['rgba(255, 99, 132, 0.4)', 'rgba(54, 162, 235, 0.4)', 'rgba(75, 192, 192, 0.4)'],
                            borderWidth: 1,
                            yAxisID: 'y1'
                        }
                    ]
                };
                
                chartOptions.scales = {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Item Count'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Inventory Value ($)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                };
                
                chartOptions.plugins.tooltip.callbacks.label = function(context) {
                    if (context.datasetIndex === 0) {
                        return `Items: ${context.raw}`;
                    } else {
                        return `Value: ${formatCurrency(context.raw)}`;
                    }
                };
                break;
        }
        
        // Create new chart
        mainChart = new Chart(ctx, {
            type: chartType === 'abcAnalysis' ? 'bar' : 'bar',
            data: chartData,
            options: chartOptions
        });
        
        // Update chart title
        document.getElementById('chartTitle').textContent = $('#chartType option:selected').text();
    }
    
    // Function to create or update the value distribution chart (pie chart)
    function updateValueDistributionChart(data) {
        const ctx = document.getElementById('valueDistributionChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (valueDistributionChart) {
            valueDistributionChart.destroy();
        }
        
        // Group data by value ranges
        const valueRanges = {
            'Low Value (<$100)': 0,
            'Medium Value ($100-$500)': 0,
            'High Value ($500-$1000)': 0,
            'Very High Value (>$1000)': 0
        };
        
        data.forEach(item => {
            const value = item.current_stock * item.unit_price;
            if (value < 100) {
                valueRanges['Low Value (<$100)'] += value;
            } else if (value < 500) {
                valueRanges['Medium Value ($100-$500)'] += value;
            } else if (value < 1000) {
                valueRanges['High Value ($500-$1000)'] += value;
            } else {
                valueRanges['Very High Value (>$1000)'] += value;
            }
        });
        
        // Create chart data
        const chartData = {
            labels: Object.keys(valueRanges),
            datasets: [{
                data: Object.values(valueRanges),
                backgroundColor: [
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(255, 99, 132, 0.7)'
                ],
                borderWidth: 1
            }]
        };
        
        // Create new chart
        valueDistributionChart = new Chart(ctx, {
            type: 'pie',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const percentage = (value / Object.values(valueRanges).reduce((a, b) => a + b, 0) * 100).toFixed(1);
                                return `${context.label}: ${formatCurrency(value)} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Function to create or update the stockout risk chart (donut chart)
    function updateStockoutRiskChart(data) {
        const ctx = document.getElementById('stockoutRiskChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (stockoutRiskChart) {
            stockoutRiskChart.destroy();
        }
        
        // Group data by risk levels
        const riskLevels = {
            'Low Risk (<10%)': 0,
            'Medium Risk (10-25%)': 0,
            'High Risk (25-50%)': 0,
            'Critical Risk (>50%)': 0
        };
        
        data.forEach(item => {
            if (item.stockout_risk < 10) {
                riskLevels['Low Risk (<10%)']++;
            } else if (item.stockout_risk < 25) {
                riskLevels['Medium Risk (10-25%)']++;
            } else if (item.stockout_risk < 50) {
                riskLevels['High Risk (25-50%)']++;
            } else {
                riskLevels['Critical Risk (>50%)']++;
            }
        });
        
        // Create chart data
        const chartData = {
            labels: Object.keys(riskLevels),
            datasets: [{
                data: Object.values(riskLevels),
                backgroundColor: [
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(255, 99, 132, 0.7)'
                ],
                borderWidth: 1
            }]
        };
        
        // Create new chart
        stockoutRiskChart = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const percentage = (value / Object.values(riskLevels).reduce((a, b) => a + b, 0) * 100).toFixed(1);
                                return `${context.label}: ${value} parts (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Function to populate the inventory table
    function populateInventoryTable(data) {
        const tableBody = document.getElementById('inventoryTableBody');
        tableBody.innerHTML = '';
        
        data.forEach(item => {
            const row = document.createElement('tr');
            
            // Add risk class to row
            if (item.stockout_risk > 50) {
                row.classList.add('table-danger');
            } else if (item.stockout_risk > 25) {
                row.classList.add('table-warning');
            }
            
            // Create table cells
            row.innerHTML = `
                <td>${item.part_id}</td>
                <td>${item.part_name}</td>
                <td>${item.current_stock}</td>
                <td>${item.optimal_stock}</td>
                <td>${item.stock_difference}</td>
                <td>${formatCurrency(item.current_value)}</td>
                <td>${formatCurrency(item.optimal_value)}</td>
                <td>${formatCurrency(item.potential_savings)}</td>
                <td>${item.days_of_supply}</td>
                <td>${item.stockout_risk}%</td>
                <td>${item.abc_classification || 'N/A'}</td>
            `;
            
            tableBody.appendChild(row);
        });
    }
    
    // Function to fetch and process inventory data
    function fetchInventoryData() {
        const chartType = document.getElementById("chartType").value;
        const sortBy = document.getElementById("sortBy").value;
        const filterBy = document.getElementById("filterBy").value;
        const limit = document.getElementById("limit").value;
        const dateFrom = document.getElementById("dateFrom").value;
        const dateTo = document.getElementById("dateTo").value;

        // Show loading indicator
        document.getElementById("loadingIndicator").style.display = "block";

        // Check if AssetKPIAuth is defined
        if (typeof AssetKPIAuth === "undefined") {
            console.error("AssetKPIAuth is not defined");
            document.getElementById("loadingIndicator").style.display = "none";
            document.getElementById("errorMessage").textContent = "Authentication module not loaded. Please refresh the page.";
            document.getElementById("errorMessage").style.display = "block";
            return;
        }

        // Get token or use empty string if not available
        fetch(`/api/inventory/visualization?chartType=${chartType}&sortBy=${sortBy}&filterBy=${filterBy}&limit=${limit}&dateFrom=${dateFrom}&dateTo=${dateTo}`, {
            headers: {
                "Authorization": token ? `Bearer ${token}` : ""
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide loading indicator
            document.getElementById("loadingIndicator").style.display = "none";
            // Process and display data
            updateMainChart(data.items, chartType);
            updateValueDistributionChart(data.items);
            updateStockoutRiskChart(data.items);
            updateInventoryTable(data.items);
            updateSummaryMetrics(data.items);
        })
        .catch(error => {
            console.error("Error fetching inventory data:", error);
            // Hide loading indicator
            document.getElementById("loadingIndicator").style.display = "none";
            
            // Show error message
            document.getElementById("errorMessage").textContent = "Error fetching inventory data: " + error.message;
            document.getElementById("errorMessage").style.display = "block";
        });
    }

        .catch(error => {
            console.error('Error fetching inventory data:', error);
            // Hide loading indicator
            // $('#loadingIndicator').hide();
            
            // Show error message
            alert('Error fetching inventory data. Please try again later.');
        });
    }
    
    // Function to export table data to CSV
    function exportTableToCsv() {
        const data = [];
        
        // Add header row
        const headers = [];
        document.querySelectorAll("#inventoryTable thead th").forEach(th => {
            headers.push(th.textContent);
        });
        data.push(headers);
        
        // Add data rows
        document.querySelectorAll("#inventoryTable tbody tr").forEach(tr => {
            const row = [];
            tr.querySelectorAll("td").forEach(td => {
                row.push(td.textContent.trim());
            });
            data.push(row);
        });
        
        // Convert to CSV string
        let csvContent = "data:text/csv;charset=utf-8,";
        data.forEach(rowArray => {
            const row = rowArray.join(",");
            csvContent += row + "\r\n";
        });
        
        // Create download link
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `inventory_data_${new Date().toISOString().slice(0,10)}.csv`);
        document.body.appendChild(link);
        
        // Trigger download and remove link
        link.click();
        document.body.removeChild(link);
    }
    
    // Function to export table to PDF
    function exportTableToPdf() {
        // Get current date for filename
        const date = new Date().toISOString().slice(0,10);
        
        // Define PDF document properties
        const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
                { text: "Inventory Metrics Report", style: "header" },
                { text: `Generated on ${new Date().toLocaleString()}`, style: "subheader" },
                { text: "\n" },
                {
                    table: {
                        headerRows: 1,
                        widths: ["auto", "*", "auto", "auto", "auto", "auto", "auto", "auto", "auto", "auto", "auto"],
                        body: []
                    }
                }
            ],
            styles: {
                header: { fontSize: 18, bold: true, margin: [0, 0, 0, 10] },
                subheader: { fontSize: 12, italics: true, margin: [0, 0, 0, 5] },
                tableHeader: { bold: true, fillColor: "#eeeeee" }
            }
        };
        
        // Add header row
        const headerRow = [];
        document.querySelectorAll("#inventoryTable thead th").forEach(th => {
            headerRow.push({ text: th.textContent, style: "tableHeader" });
        });
        docDefinition.content[3].table.body.push(headerRow);
        
        // Add data rows
        document.querySelectorAll("#inventoryTable tbody tr").forEach(tr => {
            const row = [];
            tr.querySelectorAll("td").forEach(td => {
                row.push(td.textContent.trim());
            });
            docDefinition.content[3].table.body.push(row);
        });
        
        // Generate and download PDF
        // Note: This requires the pdfmake library which should be included in the layout.html
        pdfMake.createPdf(docDefinition).download(`inventory_report_${date}.pdf`);
    }
    // Initialize page
    $(document).ready(function() {
        // Set up event listeners for filter controls
        $("#chartType, #sortBy, #filterBy, #limit, #dateFrom, #dateTo").change(function() {
            fetchInventoryData();
        });
        $('#chartType, #sortBy, #filterBy, #limit').change(function() {
            fetchInventoryData();
        });
        
        // Set up event listeners for export buttons
        $("#exportCsv").click(exportTableToCsv);
        $("#exportPdf").click(exportTableToPdf);

        // Initial data fetch
        fetchInventoryData();
    });
</script>
{% endblock %}
