# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Add a test endpoint after the user check endpoint
test_endpoint = '''

@app.get("/api/test-auth", tags=["Authentication"])
async def test_auth():
    """
    Test endpoint for authentication.
    """
    try:
        # Get all users from the database
        db = SessionLocal()
        try:
            # Query the users table for all users
            print("Querying database for all users")
            query = text("SELECT * FROM users")
            print(f"SQL Query: {query}")
            
            result = db.execute(query).fetchall()
            
            if result:
                print(f"Found {len(result)} users in database")
                # Return user information
                users = []
                for row in result:
                    user_info = {
                        "uid": row[0],
                        "email": row[1],
                        "role": row[2],
                        "full_name": row[3]
                    }
                    users.append(user_info)
                print(f"Returning {len(users)} users")
                return {"users": users}
            else:
                print("No users found in database")
                return JSONResponse(
                    status_code=404,
                    content={"error": "No users found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error testing auth: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": f"Error testing auth: {str(e)}"}
        )
'''

# Find a good place to add the test endpoint (after the user check endpoint)
user_check_end = '''    except Exception as e:
        print(f"Error checking user: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": f"Error checking user: {str(e)}"}
        )'''

content = content.replace(user_check_end, user_check_end + test_endpoint)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Added test endpoint to main.py")
