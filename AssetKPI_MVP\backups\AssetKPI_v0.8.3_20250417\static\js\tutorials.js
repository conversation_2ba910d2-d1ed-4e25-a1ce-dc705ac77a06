/**
 * Tutorial Initialization Script
 * 
 * This script initializes the tutorial system for the AssetKPI application.
 */

import TutorialEngine from './components/tutorials/TutorialEngine.js';
import TutorialMenu from './components/tutorials/TutorialMenu.js';

// Initialize tutorial engine when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create tutorial container
    const tutorialContainer = document.createElement('div');
    tutorialContainer.id = 'tutorial-container';
    document.body.appendChild(tutorialContainer);
    
    // Create tutorial menu container
    const tutorialMenuContainer = document.createElement('div');
    tutorialMenuContainer.id = 'tutorial-menu-container';
    document.body.appendChild(tutorialMenuContainer);
    
    // Initialize tutorial engine
    const tutorialEngine = new TutorialEngine({
        containerId: 'tutorial-container',
        autoStart: true,
        onComplete: (tutorialId) => {
            console.log(`Tutorial completed: ${tutorialId}`);
            // Show completion message
            showCompletionMessage(tutorialId);
        }
    });
    
    tutorialEngine.init();
    
    // Initialize tutorial menu
    const tutorialMenu = new TutorialMenu({
        containerId: 'tutorial-menu-container',
        onTutorialSelect: (tutorialId) => {
            tutorialEngine.startTutorial(tutorialId);
        }
    });
    
    tutorialMenu.init();
    
    // Add to window for debugging
    window.tutorialEngine = tutorialEngine;
    window.tutorialMenu = tutorialMenu;
});

/**
 * Show a completion message for a tutorial.
 * 
 * @param {string} tutorialId - ID of the completed tutorial
 */
function showCompletionMessage(tutorialId) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast
    const toastId = `tutorial-toast-${Date.now()}`;
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = 'toast';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    // Get tutorial title
    let tutorialTitle = 'Tutorial';
    switch (tutorialId) {
        case 'asset-management':
            tutorialTitle = 'Asset Management Tutorial';
            break;
        case 'inventory-management':
            tutorialTitle = 'Inventory Management Tutorial';
            break;
        default:
            tutorialTitle = 'Tutorial';
    }
    
    // Set toast content
    toast.innerHTML = `
        <div class="toast-header">
            <strong class="me-auto">Tutorial Completed</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            <p>Congratulations! You've completed the ${tutorialTitle}.</p>
            <p>You can access more tutorials from the tutorial menu.</p>
        </div>
    `;
    
    // Add toast to container
    toastContainer.appendChild(toast);
    
    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
