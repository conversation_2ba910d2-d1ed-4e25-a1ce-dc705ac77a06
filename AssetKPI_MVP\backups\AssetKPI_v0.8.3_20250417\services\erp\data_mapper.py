"""
ERP data mapping utilities.

This module provides utilities for mapping data between AssetKPI and ERP systems.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime, date

# Create a logger for this module
logger = logging.getLogger(__name__)


class DataTransformer:
    """
    Utility for transforming data between different formats.
    
    This class provides methods for transforming data between AssetKPI and ERP systems.
    """
    
    @staticmethod
    def transform_string(value: str, params: Dict[str, Any]) -> str:
        """
        Transform a string value.
        
        Args:
            value: String value to transform
            params: Transformation parameters
            
        Returns:
            Transformed string
        """
        if not value:
            return value
        
        operation = params.get("operation")
        
        if operation == "uppercase":
            return value.upper()
        elif operation == "lowercase":
            return value.lower()
        elif operation == "capitalize":
            return value.capitalize()
        elif operation == "trim":
            return value.strip()
        elif operation == "replace":
            old_value = params.get("old_value", "")
            new_value = params.get("new_value", "")
            return value.replace(old_value, new_value)
        elif operation == "substring":
            start = params.get("start", 0)
            end = params.get("end")
            return value[start:end] if end else value[start:]
        elif operation == "prefix":
            prefix = params.get("prefix", "")
            return f"{prefix}{value}"
        elif operation == "suffix":
            suffix = params.get("suffix", "")
            return f"{value}{suffix}"
        else:
            return value
    
    @staticmethod
    def transform_number(value: Union[int, float], params: Dict[str, Any]) -> Union[int, float]:
        """
        Transform a numeric value.
        
        Args:
            value: Numeric value to transform
            params: Transformation parameters
            
        Returns:
            Transformed number
        """
        if value is None:
            return value
        
        operation = params.get("operation")
        
        if operation == "add":
            operand = params.get("operand", 0)
            return value + operand
        elif operation == "subtract":
            operand = params.get("operand", 0)
            return value - operand
        elif operation == "multiply":
            operand = params.get("operand", 1)
            return value * operand
        elif operation == "divide":
            operand = params.get("operand", 1)
            if operand == 0:
                logger.warning("Division by zero attempted")
                return value
            return value / operand
        elif operation == "round":
            decimals = params.get("decimals", 0)
            return round(value, decimals)
        elif operation == "floor":
            import math
            return math.floor(value)
        elif operation == "ceiling":
            import math
            return math.ceil(value)
        else:
            return value
    
    @staticmethod
    def transform_date(value: Union[str, datetime, date], params: Dict[str, Any]) -> str:
        """
        Transform a date value.
        
        Args:
            value: Date value to transform
            params: Transformation parameters
            
        Returns:
            Transformed date string
        """
        if not value:
            return value
        
        # Convert string to datetime if needed
        if isinstance(value, str):
            try:
                input_format = params.get("input_format", "%Y-%m-%d")
                value = datetime.strptime(value, input_format)
            except ValueError:
                logger.warning(f"Could not parse date string: {value}")
                return value
        
        # Convert date to datetime if needed
        if isinstance(value, date) and not isinstance(value, datetime):
            value = datetime.combine(value, datetime.min.time())
        
        operation = params.get("operation")
        
        if operation == "format":
            output_format = params.get("output_format", "%Y-%m-%d")
            return value.strftime(output_format)
        elif operation == "add_days":
            from datetime import timedelta
            days = params.get("days", 0)
            new_date = value + timedelta(days=days)
            output_format = params.get("output_format", "%Y-%m-%d")
            return new_date.strftime(output_format)
        elif operation == "subtract_days":
            from datetime import timedelta
            days = params.get("days", 0)
            new_date = value - timedelta(days=days)
            output_format = params.get("output_format", "%Y-%m-%d")
            return new_date.strftime(output_format)
        else:
            output_format = params.get("output_format", "%Y-%m-%d")
            return value.strftime(output_format)
    
    @staticmethod
    def transform_boolean(value: Union[bool, str, int], params: Dict[str, Any]) -> Union[bool, str, int]:
        """
        Transform a boolean value.
        
        Args:
            value: Boolean value to transform
            params: Transformation parameters
            
        Returns:
            Transformed boolean
        """
        if value is None:
            return value
        
        # Convert to boolean if needed
        if isinstance(value, str):
            value = value.lower() in ("true", "t", "yes", "y", "1")
        elif isinstance(value, int):
            value = value != 0
        
        operation = params.get("operation")
        
        if operation == "invert":
            return not value
        elif operation == "to_string":
            true_value = params.get("true_value", "true")
            false_value = params.get("false_value", "false")
            return true_value if value else false_value
        elif operation == "to_integer":
            true_value = params.get("true_value", 1)
            false_value = params.get("false_value", 0)
            return true_value if value else false_value
        else:
            return value
    
    @staticmethod
    def transform_value(
        value: Any,
        rule: Dict[str, Any],
        data_type: Optional[str] = None
    ) -> Any:
        """
        Transform a value using a transformation rule.
        
        Args:
            value: Value to transform
            rule: Transformation rule
            data_type: Optional data type hint
            
        Returns:
            Transformed value
        """
        if value is None:
            return value
        
        rule_type = rule.get("rule_type")
        params = rule.get("rule_params", {})
        
        # Determine data type if not provided
        if not data_type:
            if isinstance(value, str):
                data_type = "string"
            elif isinstance(value, (int, float)):
                data_type = "number"
            elif isinstance(value, (datetime, date)):
                data_type = "date"
            elif isinstance(value, bool):
                data_type = "boolean"
            else:
                data_type = "string"
        
        # Apply transformation based on data type
        if data_type == "string":
            return DataTransformer.transform_string(str(value), params)
        elif data_type == "number":
            try:
                num_value = float(value)
                result = DataTransformer.transform_number(num_value, params)
                return int(result) if isinstance(value, int) and result.is_integer() else result
            except (ValueError, TypeError):
                logger.warning(f"Could not convert value to number: {value}")
                return value
        elif data_type == "date":
            return DataTransformer.transform_date(value, params)
        elif data_type == "boolean":
            return DataTransformer.transform_boolean(value, params)
        else:
            logger.warning(f"Unsupported data type: {data_type}")
            return value


class DataMapper:
    """
    Utility for mapping data between AssetKPI and ERP systems.
    
    This class provides methods for mapping data between different systems.
    """
    
    def __init__(self, field_mappings: List[Dict[str, Any]], transformation_rules: Optional[List[Dict[str, Any]]] = None):
        """
        Initialize the data mapper.
        
        Args:
            field_mappings: List of field mappings
            transformation_rules: Optional list of transformation rules
        """
        self.field_mappings = field_mappings
        self.transformation_rules = transformation_rules or []
        
        # Create lookup dictionaries for faster access
        self.assetkpi_to_erp = {
            mapping["assetkpi_field"]: mapping["erp_field"]
            for mapping in field_mappings
        }
        
        self.erp_to_assetkpi = {
            mapping["erp_field"]: mapping["assetkpi_field"]
            for mapping in field_mappings
        }
        
        self.field_data_types = {
            mapping["assetkpi_field"]: mapping.get("data_type")
            for mapping in field_mappings
            if "data_type" in mapping
        }
        
        self.required_fields = {
            mapping["assetkpi_field"]: mapping.get("is_required", False)
            for mapping in field_mappings
        }
        
        self.default_values = {
            mapping["assetkpi_field"]: mapping.get("default_value")
            for mapping in field_mappings
            if "default_value" in mapping
        }
        
        # Create lookup dictionary for transformation rules
        self.transformation_rules_by_field = {}
        for rule in self.transformation_rules:
            field = rule.get("field")
            if field:
                if field not in self.transformation_rules_by_field:
                    self.transformation_rules_by_field[field] = []
                self.transformation_rules_by_field[field].append(rule)
    
    def map_to_erp(self, assetkpi_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Map data from AssetKPI to ERP format.
        
        Args:
            assetkpi_data: Data in AssetKPI format
            
        Returns:
            Data in ERP format
        """
        erp_data = {}
        
        for assetkpi_field, value in assetkpi_data.items():
            if assetkpi_field in self.assetkpi_to_erp:
                erp_field = self.assetkpi_to_erp[assetkpi_field]
                
                # Apply transformations if any
                if assetkpi_field in self.transformation_rules_by_field:
                    for rule in self.transformation_rules_by_field[assetkpi_field]:
                        data_type = self.field_data_types.get(assetkpi_field)
                        value = DataTransformer.transform_value(value, rule, data_type)
                
                erp_data[erp_field] = value
        
        return erp_data
    
    def map_to_assetkpi(self, erp_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Map data from ERP to AssetKPI format.
        
        Args:
            erp_data: Data in ERP format
            
        Returns:
            Data in AssetKPI format
        """
        assetkpi_data = {}
        
        # First, apply default values for all fields
        for assetkpi_field, default_value in self.default_values.items():
            if default_value is not None:
                assetkpi_data[assetkpi_field] = default_value
        
        # Then, map ERP data
        for erp_field, value in erp_data.items():
            if erp_field in self.erp_to_assetkpi:
                assetkpi_field = self.erp_to_assetkpi[erp_field]
                
                # Apply transformations if any
                if assetkpi_field in self.transformation_rules_by_field:
                    for rule in self.transformation_rules_by_field[assetkpi_field]:
                        data_type = self.field_data_types.get(assetkpi_field)
                        value = DataTransformer.transform_value(value, rule, data_type)
                
                assetkpi_data[assetkpi_field] = value
        
        # Check for required fields
        for field, is_required in self.required_fields.items():
            if is_required and field not in assetkpi_data:
                logger.warning(f"Required field {field} is missing")
                
                # Use default value if available
                if field in self.default_values:
                    assetkpi_data[field] = self.default_values[field]
        
        return assetkpi_data
    
    def validate_mapping(self, data: Dict[str, Any], direction: str = "to_erp") -> List[str]:
        """
        Validate data mapping.
        
        Args:
            data: Data to validate
            direction: Mapping direction ("to_erp" or "to_assetkpi")
            
        Returns:
            List of validation errors
        """
        errors = []
        
        if direction == "to_erp":
            # Validate AssetKPI data for mapping to ERP
            for field, is_required in self.required_fields.items():
                if is_required and field not in data and field not in self.default_values:
                    errors.append(f"Required field {field} is missing")
        
        elif direction == "to_assetkpi":
            # Validate ERP data for mapping to AssetKPI
            required_erp_fields = [
                self.assetkpi_to_erp[field]
                for field, is_required in self.required_fields.items()
                if is_required and field not in self.default_values
            ]
            
            for field in required_erp_fields:
                if field not in data:
                    errors.append(f"Required ERP field {field} is missing")
        
        return errors
