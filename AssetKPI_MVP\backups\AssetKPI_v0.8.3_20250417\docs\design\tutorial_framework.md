# Tutorial Framework Design

This document outlines the design for the AssetKPI interactive tutorial framework, which will guide users through key features of the application.

## Goals

- Provide interactive guidance for new users
- Highlight key features and workflows
- Reduce learning curve for complex features
- Improve user engagement and feature adoption
- Support multiple tutorial types for different user needs

## Architecture

The tutorial framework consists of the following components:

1. **Tutorial Engine**: Core class that manages tutorial state and flow
2. **Tutorial UI Components**: Visual elements for displaying tutorial content
3. **Tutorial Data**: JSON-based tutorial definitions
4. **Tutorial API**: Backend endpoints for tracking tutorial progress

### Component Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                     Tutorial Framework                       │
│                                                             │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────┐│
│  │ Tutorial      │    │ Tutorial      │    │ Tutorial      ││
│  │ Engine        │◄───┤ Data          │    │ API           ││
│  │               │    │               │    │               ││
│  └───────┬───────┘    └───────────────┘    └───────┬───────┘│
│          │                                         │        │
│          ▼                                         │        │
│  ┌───────────────┐                                 │        │
│  │ Tutorial      │                                 │        │
│  │ UI Components │◄────────────────────────────────┘        │
│  │               │                                          │
│  └───────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
```

## Tutorial Engine

The Tutorial Engine is responsible for:

- Loading tutorial definitions
- Managing tutorial state (current step, progress)
- Navigating between steps
- Targeting and highlighting UI elements
- Handling user interactions
- Tracking tutorial completion

### State Management

The Tutorial Engine maintains the following state:

- Current tutorial ID
- Current step index
- Tutorial progress (completed steps)
- User preferences (e.g., tutorial speed, auto-advance)

## Tutorial UI Components

The framework includes the following UI components:

### Tooltip/Popover

- Displays step content (text, images, videos)
- Shows navigation controls (next, previous, skip)
- Supports different positions (top, right, bottom, left)
- Automatically positions based on screen space

### Highlight/Focus

- Highlights target elements
- Dims the rest of the UI
- Supports different highlight styles (outline, spotlight)
- Handles scrolling to ensure target is visible

### Progress Indicator

- Shows overall tutorial progress
- Displays step numbers and titles
- Allows jumping to specific steps

### Tutorial Menu

- Lists available tutorials
- Shows completion status
- Allows restarting or continuing tutorials

## Tutorial Data Structure

Tutorials are defined using a JSON structure:

```json
{
  "id": "asset-management",
  "title": "Asset Management Tutorial",
  "description": "Learn how to manage assets in AssetKPI",
  "steps": [
    {
      "id": "step1",
      "title": "Navigate to Assets",
      "content": "Click on the Assets menu to view your assets.",
      "target": "#assetsDropdown",
      "position": "bottom",
      "highlight": true,
      "action": {
        "type": "click",
        "element": "#assetsDropdown"
      },
      "waitForElement": ".dropdown-menu"
    },
    {
      "id": "step2",
      "title": "View Asset List",
      "content": "Click on Asset List to see all your assets.",
      "target": "a[href='/assets']",
      "position": "right",
      "highlight": true,
      "action": {
        "type": "click",
        "element": "a[href='/assets']"
      },
      "waitForPage": "/assets"
    }
  ]
}
```

### Step Properties

- **id**: Unique identifier for the step
- **title**: Step title
- **content**: Step content (text, HTML)
- **target**: CSS selector for the target element
- **position**: Tooltip position (top, right, bottom, left)
- **highlight**: Whether to highlight the target element
- **action**: Action to perform (click, input, etc.)
- **waitForElement**: Element to wait for before proceeding
- **waitForPage**: Page URL to wait for before proceeding
- **condition**: Condition to check before showing the step

## Tutorial Types

The framework supports different types of tutorials:

### Feature Tours

- Brief overview of a specific feature
- Highlights key UI elements and actions
- Typically 3-5 steps

### Workflow Tutorials

- Guides users through a complete workflow
- Includes user interactions and form inputs
- Typically 5-10 steps

### Interactive Demos

- Demonstrates a feature with simulated data
- Includes animations and interactive elements
- Can run automatically without user input

## User Experience

### Starting Tutorials

Tutorials can be started in several ways:

- From the help menu
- From contextual help icons
- Automatically for first-time users
- From the dashboard

### Navigation

Users can navigate tutorials using:

- Next/Previous buttons
- Skip button to skip the current step
- Exit button to exit the tutorial
- Progress indicator to jump to specific steps

### Persistence

The framework saves tutorial progress to allow users to:

- Continue tutorials where they left off
- Track which tutorials have been completed
- Receive recommendations for tutorials based on usage

## Technical Implementation

### Frontend

- JavaScript class for the Tutorial Engine
- React components for UI elements
- Local storage for persisting state
- CSS for styling and animations

### Backend

- API endpoints for tracking tutorial progress
- Database schema for storing user tutorial data
- Analytics for measuring tutorial effectiveness

## Success Metrics

- Tutorial completion rate
- Feature adoption after tutorial completion
- Time spent in tutorials
- User feedback ratings
- Reduction in support requests

## Future Enhancements

- Personalized tutorial recommendations
- A/B testing of tutorial content
- User-created tutorials
- Tutorial analytics dashboard
- Multi-language support
