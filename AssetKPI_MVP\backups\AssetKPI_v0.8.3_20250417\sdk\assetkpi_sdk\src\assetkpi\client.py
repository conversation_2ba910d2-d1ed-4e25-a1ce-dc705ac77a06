"""
AssetKPI API Client

This module provides the main client class for interacting with the AssetKPI API.
"""

import json
import time
import random
import logging
from typing import Dict, List, Optional, Union, Any

import requests
from requests.exceptions import RequestException, Timeout, ConnectionError

from .exceptions import (
    AssetKPIError,
    AuthenticationError,
    RateLimitError,
    APIError,
    ValidationError,
    ResourceNotFoundError,
    ServerError,
)

# Configure logging
logger = logging.getLogger("assetkpi")


class AssetKPIClient:
    """
    Client for the AssetKPI API.
    
    This class provides methods for interacting with the AssetKPI API,
    including authentication, error handling, and retry logic.
    """
    
    def __init__(
        self,
        base_url: str,
        firebase_id_token: Optional[str] = None,
        api_key: Optional[str] = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_backoff_factor: float = 0.5,
        retry_status_codes: List[int] = None,
    ):
        """
        Initialize the AssetKPI API client.
        
        Args:
            base_url: The base URL for the API (e.g., "http://localhost:8000/api")
            firebase_id_token: Firebase ID token for authentication
            api_key: API key for authentication
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_backoff_factor: Backoff factor for retries
            retry_status_codes: HTTP status codes to retry
        """
        self.base_url = base_url.rstrip("/")
        self.firebase_id_token = firebase_id_token
        self.api_key = api_key
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_backoff_factor = retry_backoff_factor
        self.retry_status_codes = retry_status_codes or [429, 500, 502, 503, 504]
        
        # Validate authentication
        if not firebase_id_token and not api_key:
            logger.warning("No authentication provided. API calls may fail.")
    
    def get_headers(self) -> Dict[str, str]:
        """
        Get headers for API requests.
        
        Returns:
            Dictionary of headers
        """
        headers = {"Content-Type": "application/json"}
        
        if self.firebase_id_token:
            headers["Authorization"] = f"Bearer {self.firebase_id_token}"
        elif self.api_key:
            headers["X-API-Key"] = self.api_key
        
        return headers
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request to the API with retry logic.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint
            params: Query parameters
            data: Request body data
            files: Files to upload
            headers: Additional headers
            
        Returns:
            API response as a dictionary
            
        Raises:
            AuthenticationError: If authentication fails
            RateLimitError: If rate limit is exceeded
            APIError: If the API returns an error
            ValidationError: If request validation fails
            ResourceNotFoundError: If resource is not found
            ServerError: If server returns a 5xx error
            AssetKPIError: For other errors
        """
        url = f"{self.base_url}{endpoint}"
        request_headers = self.get_headers()
        
        if headers:
            request_headers.update(headers)
        
        # Convert data to JSON if it's a dictionary
        json_data = None
        if data and not files:
            json_data = data
            data = None
        
        retries = 0
        while True:
            try:
                response = requests.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json_data,
                    data=data,
                    files=files,
                    headers=request_headers,
                    timeout=self.timeout,
                )
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 1))
                    
                    if retries < self.max_retries:
                        # Calculate backoff with jitter
                        backoff = min(
                            60,
                            retry_after * (2 ** retries) + random.uniform(0, 1)
                        )
                        
                        logger.warning(
                            f"Rate limit exceeded. Retrying after {backoff:.2f} seconds"
                        )
                        time.sleep(backoff)
                        retries += 1
                        continue
                    else:
                        raise RateLimitError(
                            "Rate limit exceeded and max retries reached",
                            retry_after=retry_after,
                        )
                
                # Handle server errors
                if response.status_code >= 500 and response.status_code in self.retry_status_codes:
                    if retries < self.max_retries:
                        backoff = self.retry_backoff_factor * (2 ** retries) + random.uniform(0, 1)
                        logger.warning(
                            f"Server error {response.status_code}. "
                            f"Retrying after {backoff:.2f} seconds"
                        )
                        time.sleep(backoff)
                        retries += 1
                        continue
                    else:
                        raise ServerError(
                            f"Server error {response.status_code} and max retries reached"
                        )
                
                # Handle other errors
                if response.status_code >= 400:
                    self._handle_error_response(response)
                
                # Parse response
                if response.content:
                    try:
                        return response.json()
                    except ValueError:
                        return {"message": response.text}
                return {}
                
            except (ConnectionError, Timeout) as e:
                if retries < self.max_retries:
                    backoff = self.retry_backoff_factor * (2 ** retries) + random.uniform(0, 1)
                    logger.warning(
                        f"Connection error: {str(e)}. "
                        f"Retrying after {backoff:.2f} seconds"
                    )
                    time.sleep(backoff)
                    retries += 1
                    continue
                raise AssetKPIError(f"Connection error after {self.max_retries} retries: {str(e)}")
            
            except RequestException as e:
                raise AssetKPIError(f"Request error: {str(e)}")
    
    def _handle_error_response(self, response: requests.Response) -> None:
        """
        Handle error responses from the API.
        
        Args:
            response: The HTTP response
            
        Raises:
            AuthenticationError: If authentication fails
            ValidationError: If request validation fails
            ResourceNotFoundError: If resource is not found
            ServerError: If server returns a 5xx error
            APIError: For other errors
        """
        status_code = response.status_code
        error_message = "Unknown error"
        
        try:
            error_data = response.json()
            if isinstance(error_data, dict) and "detail" in error_data:
                error_message = error_data["detail"]
            elif isinstance(error_data, dict) and "message" in error_data:
                error_message = error_data["message"]
            else:
                error_message = str(error_data)
        except (ValueError, KeyError):
            error_message = response.text or f"HTTP {status_code}"
        
        if status_code == 401:
            raise AuthenticationError(f"Authentication failed: {error_message}")
        elif status_code == 403:
            raise AuthenticationError(f"Permission denied: {error_message}")
        elif status_code == 404:
            raise ResourceNotFoundError(f"Resource not found: {error_message}")
        elif status_code == 422:
            raise ValidationError(f"Validation error: {error_message}")
        elif status_code >= 500:
            raise ServerError(f"Server error: {error_message}")
        else:
            raise APIError(error_message, status_code=status_code, response=response)
    
    def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make a GET request to the API.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            headers: Additional headers
            
        Returns:
            API response as a dictionary
        """
        return self._make_request("GET", endpoint, params=params, headers=headers)
    
    def post(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make a POST request to the API.
        
        Args:
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            files: Files to upload
            headers: Additional headers
            
        Returns:
            API response as a dictionary
        """
        return self._make_request(
            "POST", endpoint, params=params, data=data, files=files, headers=headers
        )
    
    def put(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make a PUT request to the API.
        
        Args:
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            headers: Additional headers
            
        Returns:
            API response as a dictionary
        """
        return self._make_request(
            "PUT", endpoint, params=params, data=data, headers=headers
        )
    
    def patch(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make a PATCH request to the API.
        
        Args:
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            headers: Additional headers
            
        Returns:
            API response as a dictionary
        """
        return self._make_request(
            "PATCH", endpoint, params=params, data=data, headers=headers
        )
    
    def delete(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make a DELETE request to the API.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            headers: Additional headers
            
        Returns:
            API response as a dictionary
        """
        return self._make_request("DELETE", endpoint, params=params, headers=headers)
    
    # --- Authentication Methods ---
    
    def get_current_user(self) -> Dict[str, Any]:
        """
        Get the current authenticated user.
        
        Returns:
            User information
        """
        return self.get("/users/me")
    
    def refresh_token(self, refresh_token: str) -> Dict[str, str]:
        """
        Refresh the Firebase ID token.
        
        Args:
            refresh_token: Firebase refresh token
            
        Returns:
            Dictionary with new ID token
        """
        # This is a placeholder. In a real implementation, you would use
        # Firebase Admin SDK or Firebase Auth REST API to refresh the token.
        raise NotImplementedError(
            "Token refresh is not implemented in this version of the SDK."
        )
