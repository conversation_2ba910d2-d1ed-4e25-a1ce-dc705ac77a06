# AssetKPI API Use Cases

This document provides detailed examples for common use cases when working with the AssetKPI API.

## Use Case 1: Monitoring Inventory Levels

### Scenario
You need to monitor inventory levels and identify parts that need to be reordered.

### API Workflow

1. **Get all spare parts**

```bash
curl -X GET "http://localhost:8000/api/inventory/parts" \
  -H "X-API-Key: your-api-key"
```

2. **Filter parts below reorder level**

In your application code:

```python
# Python example
import requests

api_key = "your-api-key"
response = requests.get(
    "http://localhost:8000/api/inventory/parts",
    headers={"X-API-Key": api_key}
)

parts = response.json()
parts_to_reorder = [p for p in parts if p['stockquantity'] <= p['reorderlevel']]

for part in parts_to_reorder:
    print(f"Part {part['partname']} needs to be reordered. Current stock: {part['stockquantity']}, Reorder level: {part['reorderlevel']}")
```

```javascript
// JavaScript example
fetch('http://localhost:8000/api/inventory/parts', {
  headers: {
    'X-API-Key': 'your-api-key'
  }
})
.then(response => response.json())
.then(parts => {
  const partsToReorder = parts.filter(p => p.stockquantity <= p.reorderlevel);
  
  partsToReorder.forEach(part => {
    console.log(`Part ${part.partname} needs to be reordered. Current stock: ${part.stockquantity}, Reorder level: ${part.reorderlevel}`);
  });
});
```

3. **Get detailed analysis for a specific part**

```bash
curl -X GET "http://localhost:8000/api/inventory/analysis/1" \
  -H "X-API-Key: your-api-key"
```

4. **Update stock quantity after receiving new inventory**

```bash
curl -X PUT "http://localhost:8000/api/inventory/parts/1" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "stockquantity": 25,
    "lastrestocked": "2023-04-16"
  }'
```

### Integration Example

Here's a complete Python script that monitors inventory levels and sends notifications for parts that need to be reordered:

```python
import requests
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000/api"
API_KEY = "your-api-key"
EMAIL_FROM = "<EMAIL>"
EMAIL_TO = "<EMAIL>"
SMTP_SERVER = "smtp.example.com"
SMTP_PORT = 587
SMTP_USERNAME = "smtp-username"
SMTP_PASSWORD = "smtp-password"

def get_parts_to_reorder():
    response = requests.get(
        f"{API_BASE_URL}/inventory/parts",
        headers={"X-API-Key": API_KEY}
    )
    
    if response.status_code != 200:
        print(f"Error fetching parts: {response.status_code}")
        return []
    
    parts = response.json()
    return [p for p in parts if p['stockquantity'] <= p['reorderlevel']]

def get_part_analysis(part_id):
    response = requests.get(
        f"{API_BASE_URL}/inventory/analysis/{part_id}",
        headers={"X-API-Key": API_KEY}
    )
    
    if response.status_code != 200:
        print(f"Error fetching analysis for part {part_id}: {response.status_code}")
        return None
    
    return response.json()

def send_notification(parts_to_reorder):
    if not parts_to_reorder:
        print("No parts to reorder. No notification sent.")
        return
    
    # Create email content
    subject = f"Inventory Alert: {len(parts_to_reorder)} parts need to be reordered"
    
    html_content = f"""
    <html>
    <head>
        <style>
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
        </style>
    </head>
    <body>
        <h2>Inventory Reorder Alert</h2>
        <p>The following parts need to be reordered:</p>
        <table>
            <tr>
                <th>Part ID</th>
                <th>Part Name</th>
                <th>Current Stock</th>
                <th>Reorder Level</th>
                <th>EOQ</th>
                <th>Lead Time (days)</th>
            </tr>
    """
    
    for part in parts_to_reorder:
        html_content += f"""
            <tr>
                <td>{part['partid']}</td>
                <td>{part['partname']}</td>
                <td>{part['stockquantity']}</td>
                <td>{part['reorderlevel']}</td>
                <td>{part.get('eoq', 'N/A')}</td>
                <td>{part.get('leadtimedays', 'N/A')}</td>
            </tr>
        """
    
    html_content += """
        </table>
        <p>Please take appropriate action to reorder these parts.</p>
        <p>This is an automated message from the AssetKPI system.</p>
    </body>
    </html>
    """
    
    # Create email message
    msg = MIMEMultipart()
    msg['From'] = EMAIL_FROM
    msg['To'] = EMAIL_TO
    msg['Subject'] = subject
    msg.attach(MIMEText(html_content, 'html'))
    
    # Send email
    try:
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
        server.starttls()
        server.login(SMTP_USERNAME, SMTP_PASSWORD)
        server.send_message(msg)
        server.quit()
        print(f"Notification sent to {EMAIL_TO}")
    except Exception as e:
        print(f"Error sending notification: {e}")

def main():
    print(f"Running inventory check at {datetime.now()}")
    
    # Get parts that need to be reordered
    parts_to_reorder = get_parts_to_reorder()
    print(f"Found {len(parts_to_reorder)} parts that need to be reordered")
    
    # Get detailed analysis for each part
    for part in parts_to_reorder:
        analysis = get_part_analysis(part['partid'])
        if analysis:
            part['analysis'] = analysis
            print(f"Part {part['partname']} (ID: {part['partid']})")
            print(f"  Current Stock: {part['stockquantity']}, Reorder Level: {part['reorderlevel']}")
            if 'analysis' in part and 'inventory_analysis' in part['analysis']:
                inv_analysis = part['analysis']['inventory_analysis']
                print(f"  Stockout Risk: {inv_analysis.get('stockout_risk', 'N/A')}%")
                print(f"  Days of Supply: {inv_analysis.get('days_of_supply', 'N/A')}")
    
    # Send notification
    send_notification(parts_to_reorder)

if __name__ == "__main__":
    main()
```

## Use Case 2: Automated Work Order Creation

### Scenario
You have a system that needs to automatically create work orders based on sensor data or scheduled maintenance.

### API Workflow

1. **Create a new work order**

```bash
curl -X POST "http://localhost:8000/api/ingest/workorder" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "assetId": 5,
    "workOrderType": "Preventive",
    "description": "Quarterly maintenance check",
    "status": "OPEN",
    "assignedTo": "John Smith",
    "downtimeMinutes": 60,
    "startDate": "2023-04-16T09:00:00Z"
  }'
```

2. **Add parts to the work order**

```bash
curl -X POST "http://localhost:8000/api/workorders/25/parts" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "parts": [
      {
        "partid": 1,
        "quantityused": 1
      },
      {
        "partid": 15,
        "quantityused": 2
      }
    ]
  }'
```

3. **Update the work order when completed**

```bash
curl -X PUT "http://localhost:8000/api/workorders/25" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "CLOSED",
    "endDate": "2023-04-16T14:30:00Z",
    "repairTimeMinutes": 150,
    "maintenanceCost": 520.00
  }'
```

### Integration Example

Here's a Python script that creates work orders based on a maintenance schedule:

```python
import requests
import json
from datetime import datetime, timedelta

# Configuration
API_BASE_URL = "http://localhost:8000/api"
API_KEY = "your-api-key"

def get_assets():
    response = requests.get(
        f"{API_BASE_URL}/assets",
        headers={"X-API-Key": API_KEY}
    )
    
    if response.status_code != 200:
        print(f"Error fetching assets: {response.status_code}")
        return []
    
    return response.json()

def get_maintenance_schedule():
    # In a real system, this would come from a database or external system
    # For this example, we'll use a hardcoded schedule
    today = datetime.now().date()
    
    schedule = [
        {
            "asset_id": 5,
            "maintenance_type": "Quarterly",
            "due_date": (today + timedelta(days=1)).isoformat(),
            "description": "Quarterly maintenance check",
            "estimated_downtime": 60,
            "assigned_to": "John Smith",
            "parts_required": [
                {"part_id": 1, "quantity": 1},
                {"part_id": 15, "quantity": 2}
            ]
        },
        {
            "asset_id": 8,
            "maintenance_type": "Monthly",
            "due_date": today.isoformat(),
            "description": "Monthly lubrication",
            "estimated_downtime": 30,
            "assigned_to": "Jane Doe",
            "parts_required": [
                {"part_id": 22, "quantity": 1}
            ]
        }
    ]
    
    return schedule

def create_work_order(maintenance_task):
    # Create work order
    work_order_data = {
        "assetId": maintenance_task["asset_id"],
        "workOrderType": "Preventive",
        "description": maintenance_task["description"],
        "status": "OPEN",
        "assignedTo": maintenance_task["assigned_to"],
        "downtimeMinutes": maintenance_task["estimated_downtime"],
        "startDate": f"{maintenance_task['due_date']}T09:00:00Z"
    }
    
    response = requests.post(
        f"{API_BASE_URL}/ingest/workorder",
        headers={
            "X-API-Key": API_KEY,
            "Content-Type": "application/json"
        },
        json=work_order_data
    )
    
    if response.status_code != 201:
        print(f"Error creating work order: {response.status_code}")
        print(response.text)
        return None
    
    result = response.json()
    work_order_id = result.get("workorder_id")
    
    if not work_order_id:
        print("No work order ID returned")
        return None
    
    print(f"Created work order {work_order_id} for asset {maintenance_task['asset_id']}")
    
    # Add parts to work order
    if maintenance_task.get("parts_required"):
        parts_data = {
            "parts": [
                {
                    "partid": part["part_id"],
                    "quantityused": part["quantity"]
                }
                for part in maintenance_task["parts_required"]
            ]
        }
        
        response = requests.post(
            f"{API_BASE_URL}/workorders/{work_order_id}/parts",
            headers={
                "X-API-Key": API_KEY,
                "Content-Type": "application/json"
            },
            json=parts_data
        )
        
        if response.status_code != 200:
            print(f"Error adding parts to work order: {response.status_code}")
            print(response.text)
        else:
            print(f"Added {len(maintenance_task['parts_required'])} parts to work order {work_order_id}")
    
    return work_order_id

def main():
    print(f"Running maintenance scheduler at {datetime.now()}")
    
    # Get assets
    assets = get_assets()
    print(f"Found {len(assets)} assets")
    
    # Get maintenance schedule
    schedule = get_maintenance_schedule()
    print(f"Found {len(schedule)} scheduled maintenance tasks")
    
    # Create work orders for due tasks
    today = datetime.now().date()
    due_tasks = [task for task in schedule if task["due_date"] <= today.isoformat()]
    
    print(f"Found {len(due_tasks)} tasks due today or earlier")
    
    for task in due_tasks:
        work_order_id = create_work_order(task)
        if work_order_id:
            print(f"Successfully created work order {work_order_id} for {task['description']}")

if __name__ == "__main__":
    main()
```

## Use Case 3: KPI Dashboard Integration

### Scenario
You want to integrate AssetKPI data into an external dashboard or reporting system.

### API Workflow

1. **Get latest KPIs**

```bash
curl -X GET "http://localhost:8000/api/kpis/latest" \
  -H "X-API-Key: your-api-key"
```

2. **Get KPI history for trend analysis**

```bash
curl -X GET "http://localhost:8000/api/kpis/history/MTTR_Calculated?start_date=2023-01-01&end_date=2023-04-15" \
  -H "X-API-Key: your-api-key"
```

3. **Get inventory summary**

```bash
curl -X GET "http://localhost:8000/api/inventory/summary" \
  -H "X-API-Key: your-api-key"
```

4. **Get work order counts**

```bash
curl -X GET "http://localhost:8000/api/workorders/count?status=OPEN" \
  -H "X-API-Key: your-api-key"
```

### Integration Example

Here's a JavaScript example that fetches data for a dashboard:

```javascript
// dashboard.js
class AssetKPIDashboard {
  constructor(apiBaseUrl, apiKey) {
    this.apiBaseUrl = apiBaseUrl;
    this.apiKey = apiKey;
    this.dashboardData = {};
  }

  async fetchData() {
    try {
      // Fetch all data in parallel
      const [
        latestKPIs,
        mttrHistory,
        mtbfHistory,
        inventorySummary,
        openWorkOrders,
        closedWorkOrders
      ] = await Promise.all([
        this.fetchLatestKPIs(),
        this.fetchKPIHistory('MTTR_Calculated'),
        this.fetchKPIHistory('MTBF_Calculated'),
        this.fetchInventorySummary(),
        this.fetchWorkOrderCount('OPEN'),
        this.fetchWorkOrderCount('CLOSED')
      ]);

      // Combine all data
      this.dashboardData = {
        kpis: latestKPIs,
        trends: {
          mttr: mttrHistory,
          mtbf: mtbfHistory
        },
        inventory: inventorySummary,
        workOrders: {
          open: openWorkOrders,
          closed: closedWorkOrders
        },
        lastUpdated: new Date()
      };

      return this.dashboardData;
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }

  async fetchLatestKPIs() {
    const response = await fetch(`${this.apiBaseUrl}/kpis/latest`, {
      headers: {
        'X-API-Key': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching latest KPIs: ${response.status}`);
    }

    return response.json();
  }

  async fetchKPIHistory(kpiName, startDate = '2023-01-01', endDate = new Date().toISOString().split('T')[0]) {
    const response = await fetch(
      `${this.apiBaseUrl}/kpis/history/${kpiName}?start_date=${startDate}&end_date=${endDate}`,
      {
        headers: {
          'X-API-Key': this.apiKey
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Error fetching KPI history: ${response.status}`);
    }

    return response.json();
  }

  async fetchInventorySummary() {
    const response = await fetch(`${this.apiBaseUrl}/inventory/summary`, {
      headers: {
        'X-API-Key': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching inventory summary: ${response.status}`);
    }

    return response.json();
  }

  async fetchWorkOrderCount(status) {
    const response = await fetch(`${this.apiBaseUrl}/workorders/count?status=${status}`, {
      headers: {
        'X-API-Key': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching work order count: ${response.status}`);
    }

    return response.json();
  }

  renderDashboard() {
    if (!this.dashboardData.kpis) {
      console.error('No dashboard data available. Call fetchData() first.');
      return;
    }

    // Update KPI cards
    document.getElementById('mttr-value').textContent = this.dashboardData.kpis.mttr.toFixed(2);
    document.getElementById('mtbf-value').textContent = this.dashboardData.kpis.mtbf.toFixed(2);
    document.getElementById('failure-rate-value').textContent = this.dashboardData.kpis.failure_rate.toFixed(2);
    
    // Update inventory summary
    document.getElementById('total-parts').textContent = this.dashboardData.inventory.total_parts;
    document.getElementById('total-value').textContent = `$${this.dashboardData.inventory.total_value.toFixed(2)}`;
    document.getElementById('below-reorder').textContent = this.dashboardData.inventory.below_reorder;
    
    // Update work order counts
    document.getElementById('open-work-orders').textContent = this.dashboardData.workOrders.open.count;
    document.getElementById('closed-work-orders').textContent = this.dashboardData.workOrders.closed.count;
    
    // Update last updated timestamp
    document.getElementById('last-updated').textContent = 
      `Last updated: ${this.dashboardData.lastUpdated.toLocaleString()}`;
    
    // Render charts (using Chart.js in this example)
    this.renderMTTRChart();
    this.renderMTBFChart();
  }

  renderMTTRChart() {
    const ctx = document.getElementById('mttr-chart').getContext('2d');
    
    // Extract data for chart
    const labels = this.dashboardData.trends.mttr.map(item => 
      new Date(item.calculation_date).toLocaleDateString()
    );
    
    const data = this.dashboardData.trends.mttr.map(item => item.kpi_value);
    
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'MTTR (hours)',
          data: data,
          borderColor: 'rgb(75, 192, 192)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: false
          }
        }
      }
    });
  }

  renderMTBFChart() {
    const ctx = document.getElementById('mtbf-chart').getContext('2d');
    
    // Extract data for chart
    const labels = this.dashboardData.trends.mtbf.map(item => 
      new Date(item.calculation_date).toLocaleDateString()
    );
    
    const data = this.dashboardData.trends.mtbf.map(item => item.kpi_value);
    
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'MTBF (hours)',
          data: data,
          borderColor: 'rgb(153, 102, 255)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: false
          }
        }
      }
    });
  }
}

// Usage
document.addEventListener('DOMContentLoaded', async () => {
  const dashboard = new AssetKPIDashboard(
    'http://localhost:8000/api',
    'your-api-key'
  );
  
  try {
    await dashboard.fetchData();
    dashboard.renderDashboard();
    
    // Refresh data every 5 minutes
    setInterval(async () => {
      await dashboard.fetchData();
      dashboard.renderDashboard();
    }, 5 * 60 * 1000);
  } catch (error) {
    console.error('Error initializing dashboard:', error);
    document.getElementById('error-message').textContent = 
      `Error loading dashboard data: ${error.message}`;
    document.getElementById('error-container').style.display = 'block';
  }
});
```

## Use Case 4: Bulk Data Import

### Scenario
You need to import a large number of work orders from another system.

### API Workflow

1. **Prepare the data in the correct format**

```json
{
  "workorders": [
    {
      "assetId": 5,
      "workOrderType": "Corrective",
      "description": "Replace motor coupling",
      "status": "CLOSED",
      "assignedTo": "Jane Doe",
      "failureCode": "MECH-002",
      "failureType": "Mechanical",
      "downtimeMinutes": 180,
      "repairTimeMinutes": 120,
      "maintenanceCost": 450.00,
      "startDate": "2023-04-16T09:00:00Z",
      "endDate": "2023-04-16T14:30:00Z"
    },
    {
      "assetId": 8,
      "workOrderType": "Preventive",
      "description": "Lubricate bearings",
      "status": "CLOSED",
      "assignedTo": "John Smith",
      "failureCode": null,
      "failureType": null,
      "downtimeMinutes": 60,
      "repairTimeMinutes": 45,
      "maintenanceCost": 150.00,
      "startDate": "2023-04-17T10:00:00Z",
      "endDate": "2023-04-17T11:00:00Z"
    }
  ]
}
```

2. **Send the bulk import request**

```bash
curl -X POST "http://localhost:8000/api/ingest/workorders/bulk" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d @workorders.json
```

3. **Check the import results**

```bash
curl -X GET "http://localhost:8000/api/workorders?limit=10&offset=0&sort=workorderid&order=desc" \
  -H "X-API-Key: your-api-key"
```

### Integration Example

Here's a Python script that imports work orders from a CSV file:

```python
import csv
import json
import requests
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000/api"
API_KEY = "your-api-key"
CSV_FILE_PATH = "work_orders.csv"

def parse_date(date_str):
    if not date_str:
        return None
    
    try:
        # Try different date formats
        for fmt in ["%Y-%m-%d", "%m/%d/%Y", "%d-%m-%Y"]:
            try:
                dt = datetime.strptime(date_str, fmt)
                return dt.strftime("%Y-%m-%dT%H:%M:%SZ")
            except ValueError:
                continue
        
        # If it already has time component
        if "T" in date_str:
            return date_str
        
        print(f"Could not parse date: {date_str}")
        return None
    except Exception as e:
        print(f"Error parsing date {date_str}: {e}")
        return None

def parse_number(num_str):
    if not num_str:
        return None
    
    try:
        return float(num_str)
    except ValueError:
        print(f"Could not parse number: {num_str}")
        return None

def read_work_orders_from_csv():
    work_orders = []
    
    with open(CSV_FILE_PATH, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        
        for row in reader:
            # Map CSV columns to API fields
            work_order = {
                "assetId": int(row.get("Asset ID", 0)),
                "workOrderType": row.get("Work Order Type", ""),
                "description": row.get("Description", ""),
                "status": row.get("Status", ""),
                "assignedTo": row.get("Assigned To", ""),
                "failureCode": row.get("Failure Code") or None,
                "failureType": row.get("Failure Type") or None,
                "downtimeMinutes": parse_number(row.get("Downtime (minutes)")),
                "repairTimeMinutes": parse_number(row.get("Repair Time (minutes)")),
                "maintenanceCost": parse_number(row.get("Maintenance Cost")),
                "startDate": parse_date(row.get("Start Date")),
                "endDate": parse_date(row.get("End Date"))
            }
            
            # Remove None values to avoid validation errors
            work_order = {k: v for k, v in work_order.items() if v is not None}
            
            work_orders.append(work_order)
    
    return work_orders

def import_work_orders(work_orders):
    if not work_orders:
        print("No work orders to import")
        return
    
    print(f"Importing {len(work_orders)} work orders...")
    
    # Split into batches of 100 to avoid request size limits
    batch_size = 100
    for i in range(0, len(work_orders), batch_size):
        batch = work_orders[i:i+batch_size]
        
        response = requests.post(
            f"{API_BASE_URL}/ingest/workorders/bulk",
            headers={
                "X-API-Key": API_KEY,
                "Content-Type": "application/json"
            },
            json={"workorders": batch}
        )
        
        if response.status_code != 200:
            print(f"Error importing batch {i//batch_size + 1}: {response.status_code}")
            print(response.text)
        else:
            result = response.json()
            print(f"Batch {i//batch_size + 1}: Imported {result.get('imported_count', 0)} work orders")
            
            if result.get('errors'):
                print(f"  Errors: {len(result['errors'])}")
                for error in result['errors'][:5]:  # Show first 5 errors
                    print(f"  - {error}")
                
                if len(result['errors']) > 5:
                    print(f"  ... and {len(result['errors']) - 5} more errors")

def main():
    print(f"Starting work order import at {datetime.now()}")
    
    # Read work orders from CSV
    work_orders = read_work_orders_from_csv()
    print(f"Read {len(work_orders)} work orders from CSV")
    
    # Import work orders
    import_work_orders(work_orders)
    
    print(f"Import completed at {datetime.now()}")

if __name__ == "__main__":
    main()
```

Example CSV format:

```
Asset ID,Work Order Type,Description,Status,Assigned To,Failure Code,Failure Type,Downtime (minutes),Repair Time (minutes),Maintenance Cost,Start Date,End Date
5,Corrective,Replace motor coupling,CLOSED,Jane Doe,MECH-002,Mechanical,180,120,450.00,2023-04-16,2023-04-16
8,Preventive,Lubricate bearings,CLOSED,John Smith,,,60,45,150.00,2023-04-17,2023-04-17
12,Corrective,Fix oil leak,CLOSED,John Smith,HYDR-001,Hydraulic,240,180,350.00,2023-04-18,2023-04-18
```
