# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Add a user check endpoint after the firebase check endpoint
user_check_endpoint = '''

@app.get("/api/user-check/{uid}", tags=["Authentication"])
async def user_check(uid: str):
    """
    Check if a user with the given UID exists in the database.
    """
    try:
        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            print(f"Querying database for user with UID: {uid}")
            query = text("SELECT * FROM users WHERE uid = :uid")
            print(f"SQL Query: {query}")
            print(f"Parameters: {{'uid': {uid}}}")
            
            result = db.execute(query, {"uid": uid}).fetchone()

            if result:
                print(f"Found user in database: {result}")
                # Return user information
                user_info = {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
                print(f"Returning user info: {user_info}")
                return user_info
            else:
                print(f"User with UID {uid} not found in database")
                # Let's check what users are in the database
                all_users = db.execute(text("SELECT uid, email FROM users")).fetchall()
                print(f"All users in database: {all_users}")
                
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error checking user: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": f"Error checking user: {str(e)}"}
        )
'''

# Find a good place to add the user check endpoint (after the firebase check endpoint)
firebase_check_end = '''    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error checking Firebase Admin SDK: {str(e)}"}
        )'''

content = content.replace(firebase_check_end, firebase_check_end + user_check_endpoint)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Added user check endpoint to main.py")
