"""
ERP Integration models for the AssetKPI application.

This module defines the database models for ERP connections and data mapping configurations.
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean, ForeignKey, JSON, Text, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from database import Base
import enum


class ERPSystemType(enum.Enum):
    """Enum for supported ERP system types."""
    SAP = "sap"
    ORACLE = "oracle"
    MICROSOFT_DYNAMICS = "microsoft_dynamics"
    INFOR = "infor"
    NETSUITE = "netsuite"
    SAGE = "sage"
    CUSTOM = "custom"


class ERPConnectionStatus(enum.Enum):
    """Enum for ERP connection status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"


class ERPConnection(Base):
    """
    Model for ERP system connections.
    
    This model stores information about connections to external ERP systems,
    including connection details and authentication.
    """
    __tablename__ = "erp_connections"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # ERP system details
    system_type = Column(String(50), nullable=False)
    version = Column(String(50), nullable=True)
    
    # Connection details
    connection_url = Column(String(500), nullable=False)
    connection_params = Column(JSON, nullable=True)
    
    # Authentication
    auth_type = Column(String(50), nullable=False)  # "basic", "oauth", "api_key", etc.
    auth_credentials = Column(String(500), nullable=True)  # Encrypted credentials
    
    # Status
    status = Column(String(50), default=ERPConnectionStatus.INACTIVE.value)
    last_sync_time = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # User who created the connection
    created_by = Column(String(255))
    
    # Relationships
    data_mappings = relationship("ERPDataMapping", back_populates="connection")
    sync_logs = relationship("ERPSyncLog", back_populates="connection")


class ERPDataMapping(Base):
    """
    Model for ERP data mappings.
    
    This model stores information about how data is mapped between AssetKPI and ERP systems.
    """
    __tablename__ = "erp_data_mappings"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("erp_connections.id"))
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Entity type in AssetKPI
    assetkpi_entity = Column(String(50), nullable=False)  # "asset", "inventory", "workorder", etc.
    
    # Entity type in ERP system
    erp_entity = Column(String(50), nullable=False)
    
    # Field mappings (JSON object mapping AssetKPI fields to ERP fields)
    field_mappings = Column(JSON, nullable=False)
    
    # Transformation rules (optional JSON object with transformation rules)
    transformation_rules = Column(JSON, nullable=True)
    
    # Sync direction
    sync_direction = Column(String(20), nullable=False)  # "import", "export", "bidirectional"
    
    # Sync schedule (cron expression)
    sync_schedule = Column(String(50), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    connection = relationship("ERPConnection", back_populates="data_mappings")


class ERPSyncLog(Base):
    """
    Model for ERP synchronization logs.
    
    This model stores logs of synchronization operations between AssetKPI and ERP systems.
    """
    __tablename__ = "erp_sync_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("erp_connections.id"))
    mapping_id = Column(Integer, ForeignKey("erp_data_mappings.id"), nullable=True)
    
    # Operation details
    operation_type = Column(String(50), nullable=False)  # "import", "export", "test", etc.
    entity_type = Column(String(50), nullable=False)  # "asset", "inventory", "workorder", etc.
    
    # Status
    status = Column(String(50), nullable=False)  # "success", "partial", "failure"
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=True)
    
    # Results
    records_processed = Column(Integer, default=0)
    records_succeeded = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)
    
    # Error details
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Relationships
    connection = relationship("ERPConnection", back_populates="sync_logs")
