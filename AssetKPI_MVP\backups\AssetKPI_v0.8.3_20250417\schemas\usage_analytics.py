from pydantic import BaseModel, <PERSON>, validator, ConfigDict
from typing import Optional, Dict, Any, Union, List
from datetime import datetime
import json

class UserActivityLogBase(BaseModel):
    """Base Pydantic model for user activity logs with common fields"""
    user_id: Optional[str] = Field(
        None,
        description="Firebase UID of the user, null for anonymous users"
    )
    event_type: str = Field(
        ...,
        description="Type of activity: PAGE_VIEW, FEATURE_USE, API_CALL, REPORT_GENERATED, etc."
    )
    details: Optional[Dict[str, Any]] = Field(
        None,
        description="JSON details about the activity (page path, feature name, parameters, etc.)"
    )
    session_id: Optional[str] = Field(
        None,
        description="Browser session ID to track user journey"
    )

    # Enhanced tracking fields
    duration_seconds: Optional[float] = Field(
        None,
        description="Duration of the activity in seconds (e.g., time spent on page)"
    )
    component_id: Optional[str] = Field(
        None,
        description="ID of the UI component interacted with"
    )
    previous_page: Optional[str] = Field(
        None,
        description="Previous page in the user journey"
    )
    browser_info: Optional[Dict[str, Any]] = Field(
        None,
        description="Browser and device information"
    )
    interaction_depth: Optional[int] = Field(
        None,
        description="Depth of interaction (e.g., clicks, scrolls)"
    )
    conversion_event: Optional[bool] = Field(
        False,
        description="Whether this activity represents a conversion event"
    )
    user_role: Optional[str] = Field(
        None,
        description="Role of the user at the time of the activity"
    )

    @validator('event_type')
    def validate_event_type(cls, v):
        """Validate that event_type is one of the allowed values"""
        allowed_types = [
            'PAGE_VIEW',
            'FEATURE_USE',
            'API_CALL',
            'REPORT_GENERATED',
            'LOGIN',
            'LOGOUT',
            'SEARCH',
            'FILTER_APPLIED',
            'EXPORT',
            'ERROR',
            'COMPONENT_INTERACTION',
            'FORM_SUBMISSION',
            'MODAL_OPEN',
            'MODAL_CLOSE',
            'DROPDOWN_SELECT',
            'BUTTON_CLICK',
            'LINK_CLICK',
            'CHART_INTERACTION',
            'TABLE_INTERACTION',
            'CONVERSION'
        ]
        if v not in allowed_types:
            raise ValueError(f"event_type must be one of: {', '.join(allowed_types)}")
        return v

class UserActivityLogCreate(UserActivityLogBase):
    """Pydantic model for creating a new user activity log entry"""
    # All fields inherited from base class
    # timestamp is auto-generated by the database
    pass

class UserActivityLogRead(UserActivityLogBase):
    """Pydantic model for reading user activity log entries"""
    id: int = Field(..., description="Unique identifier for the activity log entry")
    timestamp: datetime = Field(..., description="When the activity occurred")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "user_id": "firebase-user-id-123",
                "timestamp": "2023-07-15T14:30:00Z",
                "event_type": "PAGE_VIEW",
                "details": {
                    "path": "/dashboard",
                    "referrer": "/login",
                    "user_agent": "Mozilla/5.0...",
                    "ip_address": "***********",
                    "response_time_ms": 120.5
                },
                "session_id": "sess_123456789",
                "duration_seconds": 45.2,
                "component_id": "dashboard-kpi-chart",
                "previous_page": "/login",
                "browser_info": {
                    "browser": "Chrome",
                    "browser_version": "92.0.4515.159",
                    "os": "Windows",
                    "os_version": "10",
                    "device_type": "Desktop",
                    "is_mobile": False
                },
                "interaction_depth": 3,
                "conversion_event": False,
                "user_role": "ADMIN"
            }
        }
    )

class UserActivityLogFilter(BaseModel):
    """Pydantic model for filtering user activity logs"""
    user_id: Optional[str] = None
    event_type: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    session_id: Optional[str] = None
    component_id: Optional[str] = None
    conversion_event: Optional[bool] = None
    user_role: Optional[str] = None
    min_duration: Optional[float] = None
    max_duration: Optional[float] = None
    browser: Optional[str] = None
    os: Optional[str] = None
    device_type: Optional[str] = None
    is_mobile: Optional[bool] = None

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "user_id": "firebase-user-id-123",
                "event_type": "PAGE_VIEW",
                "start_date": "2023-07-01T00:00:00Z",
                "end_date": "2023-07-31T23:59:59Z",
                "component_id": "dashboard-kpi-chart",
                "conversion_event": True,
                "user_role": "ADMIN",
                "min_duration": 10.0,
                "max_duration": 60.0,
                "browser": "Chrome",
                "os": "Windows",
                "device_type": "Desktop",
                "is_mobile": False
            }
        }
    )

# Scheduled Report Schemas
class ScheduledReportBase(BaseModel):
    """Base Pydantic model for scheduled reports"""
    report_type: str = Field(..., description="Type of report: usage_analytics, inventory, kpi, etc.")
    period: str = Field(..., description="Period covered by the report: daily, weekly, monthly, custom")
    start_date: datetime = Field(..., description="Start date of the report period")
    end_date: datetime = Field(..., description="End date of the report period")
    format: str = Field(..., description="Format of the report: csv, excel, pdf, etc.")

class ScheduledReportCreate(ScheduledReportBase):
    """Pydantic model for creating a new scheduled report"""
    pass

class ScheduledReportRead(ScheduledReportBase):
    """Pydantic model for reading scheduled report entries"""
    id: int = Field(..., description="Unique identifier for the report")
    file_path: Optional[str] = Field(None, description="Path to the generated report file")
    created_at: datetime = Field(..., description="When the report was generated")
    sent_to: Optional[List[str]] = Field(None, description="List of email addresses the report was sent to")
    status: str = Field(..., description="Status of the report: pending, generated, sent, failed")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "report_type": "usage_analytics",
                "period": "weekly",
                "start_date": "2023-07-01T00:00:00Z",
                "end_date": "2023-07-07T23:59:59Z",
                "format": "excel",
                "file_path": "/reports/usage_report_weekly_20230708_120000.xlsx",
                "created_at": "2023-07-08T12:00:00Z",
                "sent_to": ["<EMAIL>", "<EMAIL>"],
                "status": "sent"
            }
        }
    )

# User Notification Preferences Schemas
class UserNotificationPreferencesBase(BaseModel):
    """Base Pydantic model for user notification preferences"""
    receive_usage_reports: bool = Field(False, description="Whether the user wants to receive usage reports")
    receive_inventory_reports: bool = Field(False, description="Whether the user wants to receive inventory reports")
    receive_kpi_reports: bool = Field(False, description="Whether the user wants to receive KPI reports")
    report_frequency: str = Field("weekly", description="Frequency of reports: daily, weekly, monthly")
    email_format: str = Field("html", description="Preferred email format: html, text")

class UserNotificationPreferencesCreate(UserNotificationPreferencesBase):
    """Pydantic model for creating user notification preferences"""
    user_id: str = Field(..., description="Firebase UID of the user")

class UserNotificationPreferencesUpdate(UserNotificationPreferencesBase):
    """Pydantic model for updating user notification preferences"""
    pass

class UserNotificationPreferencesRead(UserNotificationPreferencesBase):
    """Pydantic model for reading user notification preferences"""
    user_id: str = Field(..., description="Firebase UID of the user")
    created_at: datetime = Field(..., description="When the preferences were created")
    updated_at: datetime = Field(..., description="When the preferences were last updated")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "user_id": "firebase-user-id-123",
                "receive_usage_reports": True,
                "receive_inventory_reports": False,
                "receive_kpi_reports": True,
                "report_frequency": "weekly",
                "email_format": "html",
                "created_at": "2023-07-01T12:00:00Z",
                "updated_at": "2023-07-15T14:30:00Z"
            }
        }
    )
