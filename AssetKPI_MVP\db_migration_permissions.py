"""
Database migration script to add user permissions table and update users table.
"""
import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Import permission constants directly
try:
    # First try to import from the permissions package
    try:
        from permissions.constants import (  # type: ignore
            PermissionScope,
            DEFAULT_VIEWER_PERMISSIONS,
            DEFAULT_ENGINEER_PERMISSIONS,
            DEFAULT_MANAGER_PERMISSIONS,
            DEFAULT_ADMIN_PERMISSIONS
        )
        print("INFO:     Successfully imported permission constants from package")
    except ImportError:
        # Fall back to direct import
        from permission_constants import (  # type: ignore
            PermissionScope,
            DEFAULT_VIEWER_PERMISSIONS,
            DEFAULT_ENGINEER_PERMISSIONS,
            DEFAULT_MANAGER_PERMISSIONS,
            DEFAULT_ADMIN_PERMISSIONS
        )
        print("INFO:     Successfully imported permission constants from file")
except ImportError as e:
    print(f"ERROR:    Could not import permission constants: {e}")
    print("ERROR:    Make sure permission_constants.py is in the same directory as this script")
    sys.exit(1)

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

def parse_db_url(url):
    """Parse database URL into connection parameters."""
    # Remove postgresql:// prefix
    url = url.replace("postgresql://", "")

    # Split user:password@host:port/dbname
    user_pass, host_port_db = url.split("@")
    user, password = user_pass.split(":")

    # Split host:port/dbname
    host_port, dbname = host_port_db.split("/")

    # Check if port is specified
    if ":" in host_port:
        host, port = host_port.split(":")
    else:
        host = host_port
        port = "5432"  # Default PostgreSQL port

    return {
        "user": user,
        "password": password,
        "host": host,
        "port": port,
        "dbname": dbname
    }

def connect_to_db():
    """Connect to the database."""
    params = parse_db_url(DATABASE_URL)
    conn = psycopg2.connect(
        user=params["user"],
        password=params["password"],
        host=params["host"],
        port=params["port"],
        dbname=params["dbname"]
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    return conn

def check_if_table_exists(conn, table_name):
    """Check if a table exists in the database."""
    with conn.cursor() as cursor:
        cursor.execute(
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = %s)",
            (table_name,)
        )
        return cursor.fetchone()[0]

def check_if_column_exists(conn, table_name, column_name):
    """Check if a column exists in a table."""
    with conn.cursor() as cursor:
        cursor.execute(
            "SELECT EXISTS (SELECT FROM information_schema.columns WHERE table_name = %s AND column_name = %s)",
            (table_name, column_name)
        )
        return cursor.fetchone()[0]

def create_user_permissions_table(conn):
    """Create the user_permissions table if it doesn't exist."""
    with conn.cursor() as cursor:
        if not check_if_table_exists(conn, "user_permissions"):
            print("Creating user_permissions table...")
            cursor.execute("""
                CREATE TABLE user_permissions (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
                    permission VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(user_id, permission)
                );
                CREATE INDEX idx_user_permissions_user_id ON user_permissions(user_id);
                CREATE INDEX idx_user_permissions_permission ON user_permissions(permission);
            """)
            print("user_permissions table created successfully.")
        else:
            print("user_permissions table already exists.")

def add_api_columns_to_users_table(conn):
    """Add API-related columns to the users table if they don't exist."""
    with conn.cursor() as cursor:
        # Add api_access_enabled column
        if not check_if_column_exists(conn, "users", "api_access_enabled"):
            print("Adding api_access_enabled column to users table...")
            cursor.execute("""
                ALTER TABLE users
                ADD COLUMN api_access_enabled BOOLEAN DEFAULT TRUE;
            """)
            print("api_access_enabled column added successfully.")
        else:
            print("api_access_enabled column already exists.")

        # Add api_key_id column
        if not check_if_column_exists(conn, "users", "api_key_id"):
            print("Adding api_key_id column to users table...")
            cursor.execute("""
                ALTER TABLE users
                ADD COLUMN api_key_id VARCHAR(255) UNIQUE;
                CREATE INDEX idx_users_api_key_id ON users(api_key_id);
            """)
            print("api_key_id column added successfully.")
        else:
            print("api_key_id column already exists.")

def assign_default_permissions(conn):
    """Assign default permissions to existing users based on their roles."""
    with conn.cursor() as cursor:
        # Get all users
        cursor.execute("SELECT user_id, role FROM users")
        users = cursor.fetchall()

        for user_id, role in users:
            print(f"Assigning default permissions to user {user_id} with role {role}...")

            # Determine permissions based on role
            if role == "ADMIN":
                permissions = DEFAULT_ADMIN_PERMISSIONS
            elif role == "MANAGER":
                permissions = DEFAULT_MANAGER_PERMISSIONS
            elif role == "ENGINEER":
                permissions = DEFAULT_ENGINEER_PERMISSIONS
            else:  # VIEWER or any other role
                permissions = DEFAULT_VIEWER_PERMISSIONS

            # Insert permissions for the user
            for permission in permissions:
                try:
                    # Convert PermissionScope enum to string
                    permission_str = str(permission)
                    cursor.execute(
                        "INSERT INTO user_permissions (user_id, permission) VALUES (%s, %s) ON CONFLICT (user_id, permission) DO NOTHING",
                        (user_id, permission_str)
                    )
                except Exception as e:
                    print(f"Error assigning permission {permission} to user {user_id}: {e}")

            print(f"Assigned {len(permissions)} permissions to user {user_id}.")

def main():
    """Main function to run the migration."""
    try:
        conn = connect_to_db()
        print(f"Connected to database: {DATABASE_URL}")

        # Create user_permissions table
        create_user_permissions_table(conn)

        # Add API columns to users table
        add_api_columns_to_users_table(conn)

        # Assign default permissions to existing users
        assign_default_permissions(conn)

        print("Migration completed successfully.")
    except Exception as e:
        print(f"Error during migration: {e}")
    finally:
        if conn:
            conn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    main()
