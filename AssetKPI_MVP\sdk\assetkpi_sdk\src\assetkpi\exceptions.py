"""
Exceptions for the AssetKPI SDK.
"""

class AssetKPIError(Exception):
    """Base exception for all AssetKPI SDK errors."""
    pass


class AuthenticationError(AssetKPIError):
    """Raised when authentication fails."""
    pass


class RateLimitError(AssetKPIError):
    """Raised when API rate limit is exceeded."""
    def __init__(self, message, retry_after=None):
        super().__init__(message)
        self.retry_after = retry_after


class APIError(AssetKPIError):
    """Raised when the API returns an error."""
    def __init__(self, message, status_code=None, response=None):
        super().__init__(message)
        self.status_code = status_code
        self.response = response


class ValidationError(AssetKPIError):
    """Raised when request validation fails."""
    pass


class ResourceNotFoundError(AssetKPIError):
    """Raised when a requested resource is not found."""
    pass


class ServerError(AssetKPIError):
    """Raised when the server returns a 5xx error."""
    pass
