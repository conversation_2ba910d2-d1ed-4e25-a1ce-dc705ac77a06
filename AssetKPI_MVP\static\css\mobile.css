/**
 * Mobile Responsiveness Styles
 * 
 * This file contains styles specifically for enhancing mobile responsiveness
 * across the AssetKPI application.
 */

/* ===== Global Mobile Styles ===== */

/* Base mobile styles */
@media (max-width: 767.98px) {
  /* Adjust base font size for mobile */
  html {
    font-size: 14px;
  }
  
  /* Ensure proper viewport behavior */
  body {
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
  
  /* Improve touch targets */
  button, 
  .btn, 
  a, 
  input, 
  select, 
  textarea {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Adjust container padding */
  .container,
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}

/* ===== Navigation ===== */

/* Mobile navigation styles */
@media (max-width: 767.98px) {
  /* Navbar adjustments */
  .navbar {
    padding: 0.5rem 1rem;
  }
  
  .navbar-brand {
    font-size: 1.1rem;
  }
  
  /* Mobile menu toggle */
  .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
  }
  
  .navbar-toggler:focus {
    box-shadow: none;
  }
  
  /* Collapsible navbar content */
  .navbar-collapse {
    margin-top: 0.5rem;
    border-top: 1px solid rgba(0,0,0,0.1);
    padding-top: 0.5rem;
  }
  
  .navbar-nav .nav-link {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
  }
  
  .navbar-nav .nav-link:last-child {
    border-bottom: none;
  }
}

/* ===== Sidebar ===== */

/* Mobile sidebar styles */
@media (max-width: 767.98px) {
  /* Hide sidebar by default on mobile */
  .sidebar {
    position: fixed;
    top: 0;
    left: -250px;
    width: 250px;
    height: 100vh;
    background: #fff;
    z-index: 1050;
    transition: left 0.3s ease;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  }
  
  /* Show sidebar when active */
  .sidebar.show {
    left: 0;
  }
  
  /* Sidebar overlay */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1040;
    display: none;
  }
  
  .sidebar-overlay.show {
    display: block;
  }
  
  /* Adjust main content when sidebar is hidden */
  .main-content {
    margin-left: 0;
    width: 100%;
  }
}

/* ===== Forms ===== */

/* Mobile form styles */
@media (max-width: 767.98px) {
  /* Stack form elements */
  .form-row {
    flex-direction: column;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  /* Full width form controls */
  .form-control,
  .form-select,
  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  /* Adjust input groups */
  .input-group {
    flex-direction: column;
  }
  
  .input-group .form-control {
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
  }
  
  .input-group-text {
    border-radius: 0.25rem;
    text-align: center;
    margin-bottom: 0.5rem;
  }
  
  /* Form button groups */
  .btn-group {
    flex-direction: column;
    width: 100%;
  }
  
  .btn-group .btn {
    border-radius: 0.25rem;
    margin-bottom: 0.25rem;
  }
}

/* ===== Tables ===== */

/* Mobile table styles */
@media (max-width: 767.98px) {
  /* Make tables horizontally scrollable */
  .table-responsive {
    border: none;
  }
  
  /* Alternative: Stack table data */
  .table-mobile-stack {
    border: none;
  }
  
  .table-mobile-stack thead {
    display: none;
  }
  
  .table-mobile-stack tbody,
  .table-mobile-stack tr,
  .table-mobile-stack td {
    display: block;
    width: 100%;
  }
  
  .table-mobile-stack tr {
    border: 1px solid #dee2e6;
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
  }
  
  .table-mobile-stack td {
    border: none;
    padding: 0.25rem 0;
    text-align: left;
  }
  
  .table-mobile-stack td:before {
    content: attr(data-label) ": ";
    font-weight: bold;
    display: inline-block;
    width: 40%;
  }
}

/* ===== Dashboard ===== */

/* Responsive dashboard */
@media (max-width: 767.98px) {
  /* Stack dashboard widgets */
  .dashboard-widget {
    margin-bottom: 1rem;
  }
  
  /* Simplify charts */
  .chart-container {
    height: 250px !important;
  }
  
  /* Adjust KPI cards */
  .kpi-card {
    margin-bottom: 1rem;
  }
  
  /* Stack dashboard actions */
  .dashboard-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .dashboard-actions .btn,
  .dashboard-actions .dropdown {
    width: 100%;
  }
}

/* ===== Utilities ===== */

/* Mobile-specific utility classes */
@media (max-width: 767.98px) {
  /* Hide elements on mobile */
  .d-mobile-none {
    display: none !important;
  }
  
  /* Show elements only on mobile */
  .d-mobile-block {
    display: block !important;
  }
  
  /* Full width on mobile */
  .w-mobile-100 {
    width: 100% !important;
  }
  
  /* Center text on mobile */
  .text-mobile-center {
    text-align: center !important;
  }
  
  /* Stack flex items on mobile */
  .flex-mobile-column {
    flex-direction: column !important;
  }
  
  /* Adjust spacing on mobile */
  .mb-mobile-3 {
    margin-bottom: 1rem !important;
  }
  
  .p-mobile-2 {
    padding: 0.5rem !important;
  }
}
