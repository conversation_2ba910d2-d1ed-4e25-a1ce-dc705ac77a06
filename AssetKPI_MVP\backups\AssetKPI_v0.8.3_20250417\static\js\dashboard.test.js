/**
 * Unit tests for dashboard functions
 * 
 * These tests use Jest-like syntax but can be run in the browser
 * with a simple test runner.
 */

// Mock chart.js
class MockChart {
    constructor(ctx, config) {
        this.ctx = ctx;
        this.config = config;
        this.destroyed = false;
    }
    
    destroy() {
        this.destroyed = true;
    }
    
    update() {
        // Mock update method
    }
}

// Store original Chart constructor
const OriginalChart = window.Chart;

// Mock elements
const mockElements = {
    'mttrTrendChart': document.createElement('canvas'),
    'mtbfTrendChart': document.createElement('canvas'),
    'chartErrorMsg': document.createElement('div'),
    'startDateInput': document.createElement('input'),
    'endDateInput': document.createElement('input')
};

// Mock document.getElementById
const originalGetElementById = document.getElementById;
document.getElementById = function(id) {
    if (mockElements[id]) {
        return mockElements[id];
    }
    return originalGetElementById.call(document, id);
};

// Mock fetch
const mockFetch = async (url, options) => {
    const response = {
        ok: true,
        status: 200,
        json: async () => {
            if (url.includes('MTTR_Calculated')) {
                return [
                    { calculation_timestamp: '2023-01-01T00:00:00', kpi_value: 5.2 },
                    { calculation_timestamp: '2023-01-02T00:00:00', kpi_value: 4.8 }
                ];
            } else if (url.includes('MTBF_Calculated')) {
                return [
                    { calculation_timestamp: '2023-01-01T00:00:00', kpi_value: 120.5 },
                    { calculation_timestamp: '2023-01-02T00:00:00', kpi_value: 130.2 }
                ];
            } else if (url.includes('unauthorized')) {
                throw new Error('Unauthorized');
            } else {
                return [];
            }
        }
    };
    
    // Simulate 401 for specific URLs
    if (url.includes('unauthorized')) {
        response.ok = false;
        response.status = 401;
        response.json = async () => ({ detail: 'Unauthorized' });
    }
    
    return response;
};

// Setup and teardown
function beforeEach() {
    // Reset mocks
    window.Chart = MockChart;
    window.fetch = mockFetch;
    
    // Reset chart instances
    window.chartInstances = {};
    
    // Set up mock elements
    for (const id in mockElements) {
        if (id.includes('Chart')) {
            // Add parent element with no-data-msg for chart elements
            const parent = document.createElement('div');
            const noDataMsg = document.createElement('div');
            noDataMsg.className = 'no-data-msg';
            parent.appendChild(mockElements[id]);
            parent.appendChild(noDataMsg);
            mockElements[id].parentElement = parent;
        }
    }
    
    // Mock AssetKPIAuth
    window.AssetKPIAuth = {
        authenticatedFetch: mockFetch,
        isAuthenticated: () => true,
        getCurrentUser: () => ({ getIdToken: async () => 'mock-token' })
    };
}

function afterEach() {
    // Restore original functions
    window.Chart = OriginalChart;
}

// Test suite
const tests = [
    {
        name: 'renderKpiChart should render chart with data',
        run: async function() {
            beforeEach();
            
            // Call the function
            await renderKpiChart('MTTR_Calculated', 'mttrTrendChart', 'MTTR (Hours)');
            
            // Verify
            assert(chartInstances['mttrTrendChart'], 'Chart instance should be created');
            assert(chartInstances['mttrTrendChart'].config.data.datasets.length > 0, 'Chart should have datasets');
            assert(chartInstances['mttrTrendChart'].config.data.datasets[0].data.length === 2, 'Chart should have 2 data points');
            
            afterEach();
        }
    },
    {
        name: 'renderKpiChart should handle empty data',
        run: async function() {
            beforeEach();
            
            // Mock fetch to return empty array
            window.fetch = async () => ({
                ok: true,
                json: async () => []
            });
            window.AssetKPIAuth.authenticatedFetch = window.fetch;
            
            // Call the function
            await renderKpiChart('MTTR_Calculated', 'mttrTrendChart', 'MTTR (Hours)');
            
            // Verify
            assert(mockElements['mttrTrendChart'].parentElement.querySelector('.no-data-msg').style.display === 'block', 'No data message should be displayed');
            
            afterEach();
        }
    },
    {
        name: 'renderKpiChart should handle authentication errors',
        run: async function() {
            beforeEach();
            
            // Mock isAuthenticated to return false
            window.AssetKPIAuth.isAuthenticated = () => false;
            
            // Call the function
            await renderKpiChart('MTTR_Calculated', 'mttrTrendChart', 'MTTR (Hours)');
            
            // Verify
            assert(mockElements['chartErrorMsg'].textContent.includes('Authentication required'), 'Error message should indicate authentication required');
            
            afterEach();
        }
    },
    {
        name: 'renderKpiChart should handle 401 errors',
        run: async function() {
            beforeEach();
            
            // Mock fetch to return 401
            window.fetch = async () => ({
                ok: false,
                status: 401,
                json: async () => ({ detail: 'Unauthorized' })
            });
            window.AssetKPIAuth.authenticatedFetch = window.fetch;
            
            // Call the function
            await renderKpiChart('MTTR_Calculated', 'mttrTrendChart', 'MTTR (Hours)');
            
            // Verify
            assert(mockElements['chartErrorMsg'].textContent.includes('Authentication error'), 'Error message should indicate authentication error');
            
            afterEach();
        }
    },
    {
        name: 'renderKpiChart should handle other API errors',
        run: async function() {
            beforeEach();
            
            // Mock fetch to return 500
            window.fetch = async () => ({
                ok: false,
                status: 500,
                json: async () => ({ detail: 'Server error' })
            });
            window.AssetKPIAuth.authenticatedFetch = window.fetch;
            
            // Call the function
            await renderKpiChart('MTTR_Calculated', 'mttrTrendChart', 'MTTR (Hours)');
            
            // Verify
            assert(mockElements['chartErrorMsg'].textContent.includes('Error loading data'), 'Error message should indicate data loading error');
            
            afterEach();
        }
    },
    {
        name: 'handleRecommendationAction should handle successful action',
        run: async function() {
            beforeEach();
            
            // Create mock elements for recommendation action
            mockElements['rec-action-message'] = document.createElement('div');
            
            // Mock fetch to return success
            window.fetch = async () => ({
                ok: true,
                json: async () => ({ message: 'Recommendation updated successfully' })
            });
            window.AssetKPIAuth.authenticatedFetch = window.fetch;
            
            // Call the function
            await handleRecommendationAction('123', 'ACCEPTED');
            
            // Verify
            assert(mockElements['rec-action-message'].textContent.includes('updated successfully'), 'Success message should be displayed');
            assert(mockElements['rec-action-message'].className.includes('text-success'), 'Success class should be applied');
            
            afterEach();
        }
    },
    {
        name: 'handleRecommendationAction should handle authentication errors',
        run: async function() {
            beforeEach();
            
            // Create mock elements for recommendation action
            mockElements['rec-action-message'] = document.createElement('div');
            
            // Mock isAuthenticated to return false
            window.AssetKPIAuth.isAuthenticated = () => false;
            
            // Call the function
            await handleRecommendationAction('123', 'ACCEPTED');
            
            // Verify
            assert(mockElements['rec-action-message'].textContent.includes('Authentication required'), 'Error message should indicate authentication required');
            assert(mockElements['rec-action-message'].className.includes('text-danger'), 'Error class should be applied');
            
            afterEach();
        }
    },
    {
        name: 'handleRecommendationAction should handle 401 errors',
        run: async function() {
            beforeEach();
            
            // Create mock elements for recommendation action
            mockElements['rec-action-message'] = document.createElement('div');
            
            // Mock fetch to return 401
            window.fetch = async () => ({
                ok: false,
                status: 401,
                json: async () => ({ detail: 'Unauthorized' })
            });
            window.AssetKPIAuth.authenticatedFetch = window.fetch;
            
            // Call the function
            await handleRecommendationAction('123', 'ACCEPTED');
            
            // Verify
            assert(mockElements['rec-action-message'].textContent.includes('Authentication error'), 'Error message should indicate authentication error');
            assert(mockElements['rec-action-message'].className.includes('text-danger'), 'Error class should be applied');
            
            afterEach();
        }
    }
];

// Simple assertion function
function assert(condition, message) {
    if (!condition) {
        console.error('ASSERTION FAILED:', message);
        throw new Error(message);
    }
}

// Run all tests
function runTests() {
    console.log('Running dashboard.js tests...');
    
    let passed = 0;
    let failed = 0;
    
    tests.forEach(test => {
        try {
            console.log(`Running test: ${test.name}`);
            test.run();
            console.log(`✅ PASSED: ${test.name}`);
            passed++;
        } catch (error) {
            console.error(`❌ FAILED: ${test.name}`);
            console.error(error);
            failed++;
        }
    });
    
    console.log(`\nTest Results: ${passed} passed, ${failed} failed`);
    
    return {
        passed,
        failed,
        total: tests.length
    };
}

// Export for use in HTML test runner
window.runDashboardTests = runTests;
