{% extends "layout.html" %}

{% block title %}Asset Specifications | AssetKPI{% endblock %}

{% block styles %}
<style>
    .asset-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
    .status-badge {
        font-size: 1rem;
        padding: 5px 10px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    .status-maintenance {
        background-color: #ffc107;
    }
    .spec-card {
        transition: transform 0.3s;
    }
    .spec-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    .warranty-card {
        border-left: 4px solid #0d6efd;
    }
    .warranty-expired {
        border-left: 4px solid #dc3545;
    }
    .warranty-active {
        border-left: 4px solid #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/assets">Assets</a></li>
                <li class="breadcrumb-item"><a href="/assets/{{ asset.assetid }}">{{ asset.assetname }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Specifications</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Asset Header -->
<div class="asset-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>{{ asset.assetname }} - Specifications</h1>
            <p class="text-muted">{{ asset.assettype }} | {{ asset.manufacturer }} {{ asset.model }}</p>
            <div class="d-flex align-items-center mt-2">
                <span class="badge status-{{ asset.status|lower if asset.status else 'inactive' }} status-badge me-2">{{ asset.status }}</span>
                <span class="text-muted">Serial: {{ asset.serialnumber }}</span>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <i class="fas fa-plus"></i> Add Specification
            </button>
        </div>
    </div>
</div>

<!-- Asset Navigation -->
<ul class="nav nav-pills mb-4">
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}">Overview</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/kpi">KPIs</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/maintenance">Maintenance</a>
    </li>
    <li class="nav-item">
        <a class="nav-link active" href="/assets/{{ asset.assetid }}/specifications">Specifications</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/documents">Documents</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/meters">Meters</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/pm">PM Schedules</a>
    </li>
</ul>

<!-- Specifications and Warranties -->
<div class="row">
    <!-- Technical Specifications -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Technical Specifications</h5>
                <div>
                    <button class="btn btn-sm btn-outline-secondary" id="toggleView">
                        <i class="fas fa-th"></i> Toggle View
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Table View (Default) -->
                <div id="tableView">
                    {% if specifications %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Specification</th>
                                        <th>Value</th>
                                        <th>Unit</th>
                                        <th>Last Updated</th>
                                        <th class="auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for spec in specifications %}
                                        <tr>
                                            <td>{{ spec.spec_name }}</td>
                                            <td>{{ spec.spec_value }}</td>
                                            <td>{{ spec.spec_unit }}</td>
                                            <td>{{ spec.updated_at.strftime('%Y-%m-%d') }}</td>
                                            <td class="auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                                                <button class="btn btn-sm btn-primary">Edit</button>
                                                <button class="btn btn-sm btn-danger">Delete</button>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-center">No specifications found for this asset.</p>
                    {% endif %}
                </div>

                <!-- Card View (Hidden by Default) -->
                <div id="cardView" class="d-none">
                    {% if specifications %}
                        <div class="row">
                            {% for spec in specifications %}
                                <div class="col-md-4 mb-3">
                                    <div class="card spec-card h-100">
                                        <div class="card-header">
                                            <h6 class="mb-0">{{ spec.spec_name }}</h6>
                                        </div>
                                        <div class="card-body">
                                            <h4 class="text-center mb-3">{{ spec.spec_value }} <small class="text-muted">{{ spec.spec_unit }}</small></h4>
                                            <p class="text-muted text-center mb-0">Last updated: {{ spec.updated_at.strftime('%Y-%m-%d') }}</p>
                                        </div>
                                        <div class="card-footer text-center auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                                            <button class="btn btn-sm btn-primary">Edit</button>
                                            <button class="btn btn-sm btn-danger">Delete</button>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-center">No specifications found for this asset.</p>
                    {% endif %}
                </div>
            </div>
            <div class="card-footer text-end auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Specification
                </button>
            </div>
        </div>
    </div>

    <!-- Warranty Information -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5>Warranty Information</h5>
            </div>
            <div class="card-body">
                {% if warranties %}
                    {% for warranty in warranties %}
                        {% set is_active = warranty.end_date and warranty.end_date > now.date() %}
                        <div class="card mb-3 {% if is_active %}warranty-active{% else %}warranty-expired{% endif %}">
                            <div class="card-body">
                                <h5 class="card-title">{{ warranty.warranty_type }}</h5>
                                <h6 class="card-subtitle mb-2 text-muted">Provider: {{ warranty.provider }}</h6>
                                <p class="card-text">
                                    <strong>Coverage:</strong> {{ warranty.coverage_details }}
                                </p>
                                <p class="card-text">
                                    <strong>Valid:</strong> {{ warranty.start_date.strftime('%Y-%m-%d') }} to {{ warranty.end_date.strftime('%Y-%m-%d') }}
                                </p>
                                <p class="card-text">
                                    <strong>Status:</strong>
                                    {% if is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Expired</span>
                                    {% endif %}
                                </p>
                                <p class="card-text">
                                    <strong>Contact:</strong> {{ warranty.contact_info }}
                                </p>
                            </div>
                            <div class="card-footer text-end auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                                <button class="btn btn-sm btn-primary">Edit</button>
                                <button class="btn btn-sm btn-danger">Delete</button>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-center">No warranty information found for this asset.</p>
                {% endif %}
            </div>
            <div class="card-footer text-end auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Warranty
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Additional Information -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5>Additional Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Manufacturer Details</h6>
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Manufacturer:</th>
                                <td>{{ asset.manufacturer }}</td>
                            </tr>
                            <tr>
                                <th>Model:</th>
                                <td>{{ asset.model }}</td>
                            </tr>
                            <tr>
                                <th>Serial Number:</th>
                                <td>{{ asset.serialnumber }}</td>
                            </tr>
                            <tr>
                                <th>Purchase Date:</th>
                                <td>{{ asset.purchasedate.strftime('%Y-%m-%d') if asset.purchasedate else 'N/A' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Asset Classification</h6>
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Type:</th>
                                <td>{{ asset.assettype }}</td>
                            </tr>
                            <tr>
                                <th>Category:</th>
                                <td>{{ category.category_name if category else 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>System:</th>
                                <td>{{ system.system_name if system else 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Criticality:</th>
                                <td>{{ asset.criticality }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between table and card view
        const toggleViewBtn = document.getElementById('toggleView');
        const tableView = document.getElementById('tableView');
        const cardView = document.getElementById('cardView');

        toggleViewBtn.addEventListener('click', function() {
            if (tableView.classList.contains('d-none')) {
                tableView.classList.remove('d-none');
                cardView.classList.add('d-none');
                toggleViewBtn.innerHTML = '<i class="fas fa-th"></i> Toggle View';
            } else {
                tableView.classList.add('d-none');
                cardView.classList.remove('d-none');
                toggleViewBtn.innerHTML = '<i class="fas fa-table"></i> Toggle View';
            }
        });
    });
</script>
{% endblock %}
