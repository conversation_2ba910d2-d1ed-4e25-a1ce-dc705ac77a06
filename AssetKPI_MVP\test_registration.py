import requests
import json
import sys

# Base URL
base_url = "http://127.0.0.1:8000"

def test_registration(token):
    """
    Test the registration endpoint with a real Firebase token.
    """
    print("Testing registration endpoint with real token...")
    headers = {
        "Authorization": f"Bearer {token}"
    }
    response = requests.post(f"{base_url}/api/register", headers=headers)
    print(f"Status code: {response.status_code}")
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except:
        print(f"Response: {response.text}")
    print()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_registration.py <token>")
        sys.exit(1)
    
    token = sys.argv[1]
    test_registration(token)
