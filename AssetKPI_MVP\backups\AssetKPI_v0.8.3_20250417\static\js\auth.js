/**
 * Firebase Authentication Utility for AssetKPI
 *
 * This module provides functions for handling Firebase authentication
 * and token management across the application.
 */

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
    authDomain: "ikios-59679.firebaseapp.com",
    projectId: "ikios-59679",
    storageBucket: "ikios-59679.appspot.com",
    messagingSenderId: "1045286122604",
    appId: "1:1045286122604:web:c9e9c9b9b9b9b9b9b9b9b9"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();

// Auth state variable
let currentUser = null;
let currentIdToken = null;

/**
 * Sign in with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} - User credential object
 */
async function signIn(email, password) {
    try {
        const userCredential = await auth.signInWithEmailAndPassword(email, password);
        currentUser = userCredential.user;

        // Get and store the ID token
        currentIdToken = await currentUser.getIdToken();
        localStorage.setItem('firebaseIdToken', currentIdToken);

        // Also set a cookie for server-side authentication
        document.cookie = `firebaseIdToken=${currentIdToken}; path=/; max-age=3600; SameSite=Strict`;
        console.log('Token stored in cookie for server-side auth');

        return {
            success: true,
            user: currentUser,
            token: currentIdToken
        };
    } catch (error) {
        console.error('Sign in error:', error);
        return {
            success: false,
            error: error
        };
    }
}

/**
 * Sign out the current user
 * @returns {Promise<Object>} - Result object
 */
async function signOut() {
    try {
        await auth.signOut();
        currentUser = null;
        currentIdToken = null;
        localStorage.removeItem('firebaseIdToken');

        return {
            success: true
        };
    } catch (error) {
        console.error('Sign out error:', error);
        return {
            success: false,
            error: error
        };
    }
}

/**
 * Get the current user's ID token
 * @param {boolean} forceRefresh - Whether to force a token refresh
 * @returns {Promise<string|null>} - The ID token or null if not authenticated
 */
async function getIdToken(forceRefresh = false) {
    if (!currentUser) {
        // Try to get token from localStorage
        const storedToken = localStorage.getItem('firebaseIdToken');
        if (storedToken) {
            console.log('Retrieved token from localStorage');
            return storedToken;
        }
        console.log('No user and no token in localStorage');
        return null;
    }

    try {
        console.log('Getting fresh token from Firebase');
        currentIdToken = await currentUser.getIdToken(forceRefresh);
        localStorage.setItem('firebaseIdToken', currentIdToken);
        return currentIdToken;
    } catch (error) {
        console.error('Error getting ID token:', error);
        // Try to fall back to stored token if available
        const storedToken = localStorage.getItem('firebaseIdToken');
        if (storedToken) {
            console.log('Falling back to stored token after error');
            return storedToken;
        }
        return null;
    }
}

/**
 * Check if the user is authenticated
 * @returns {boolean} - Whether the user is authenticated
 */
function isAuthenticated() {
    return !!currentUser || !!localStorage.getItem('firebaseIdToken');
}

/**
 * Get the current user
 * @returns {Object|null} - The current user or null if not authenticated
 */
function getCurrentUser() {
    return currentUser;
}

/**
 * Initialize the authentication state
 * @param {Function} callback - Callback function to run when auth state changes
 * @returns {Function} - Unsubscribe function
 */
function initAuth(callback) {
    console.log('Initializing Firebase Auth state');

    // Check if we have a token in localStorage
    const storedToken = localStorage.getItem('firebaseIdToken');
    if (storedToken) {
        console.log('Found token in localStorage during init');
        // Also set a cookie for server-side authentication if we have a token in localStorage
        document.cookie = `firebaseIdToken=${storedToken}; path=/; max-age=3600; SameSite=Strict`;
        console.log('Token from localStorage also stored in cookie for server-side auth');

        // Verify the token is still valid
        try {
            const parts = storedToken.split('.');
            if (parts.length === 3) {
                // Try to decode the payload to check expiration
                const payload = JSON.parse(atob(parts[1]));
                const now = Math.floor(Date.now() / 1000);

                if (payload.exp && payload.exp < now) {
                    console.log('Stored token is expired, will refresh if user is logged in');
                }
            }
        } catch (e) {
            console.error('Error checking token expiration:', e);
        }
    }

    return auth.onAuthStateChanged(async (user) => {
        console.log('Auth state changed:', user ? `User: ${user.email}` : 'No user');
        currentUser = user;

        if (user) {
            try {
                console.log('Getting fresh token during auth state change');
                currentIdToken = await user.getIdToken(true); // Force refresh
                localStorage.setItem('firebaseIdToken', currentIdToken);
                console.log('Token stored in localStorage');

                // Also set a cookie for server-side authentication
                document.cookie = `firebaseIdToken=${currentIdToken}; path=/; max-age=3600; SameSite=Strict`;
                console.log('Token stored in cookie for server-side auth');

                // Set up a token refresh interval
                // Refresh token every 50 minutes (tokens typically last 1 hour)
                const tokenRefreshInterval = setInterval(async () => {
                    try {
                        if (currentUser) {
                            console.log('Refreshing token on interval');
                            currentIdToken = await currentUser.getIdToken(true);
                            localStorage.setItem('firebaseIdToken', currentIdToken);
                            document.cookie = `firebaseIdToken=${currentIdToken}; path=/; max-age=3600; SameSite=Strict`;
                            console.log('Token refreshed successfully');
                        } else {
                            console.log('No current user, clearing token refresh interval');
                            clearInterval(tokenRefreshInterval);
                        }
                    } catch (error) {
                        console.error('Error refreshing token on interval:', error);
                    }
                }, 50 * 60 * 1000); // 50 minutes

            } catch (error) {
                console.error('Error getting ID token during init:', error);
            }
        } else {
            console.log('No user, clearing token');
            currentIdToken = null;
            localStorage.removeItem('firebaseIdToken');
            // Clear the cookie as well
            document.cookie = 'firebaseIdToken=; path=/; max-age=0; SameSite=Strict';
            console.log('Token cookie cleared');
        }

        if (callback && typeof callback === 'function') {
            callback(user);
        }
    });
}

/**
 * Make an authenticated API request
 * @param {string} url - The API endpoint URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} - Fetch response
 */
async function authenticatedFetch(url, options = {}) {
    // Try to get token from current user first, then from localStorage as fallback
    let token = null;

    // First try to get a fresh token from the current user
    if (currentUser) {
        try {
            // Force refresh token to ensure it's valid
            token = await currentUser.getIdToken(true);
            // Update stored token
            localStorage.setItem('firebaseIdToken', token);
            // Also update the cookie for server-side authentication
            document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;
            console.log('Using fresh token from current user');
        } catch (error) {
            console.error('Error refreshing token:', error);
        }
    }

    // If we couldn't get a token from the current user, try localStorage
    if (!token) {
        token = localStorage.getItem('firebaseIdToken');
        if (token) {
            console.log('Using token from localStorage');
            // Also update the cookie for server-side authentication
            document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;
        }
    }

    // If we still don't have a token, try to get it from the cookie
    if (!token) {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('firebaseIdToken=')) {
                token = cookie.substring('firebaseIdToken='.length, cookie.length);
                console.log('Using token from cookie');
                break;
            }
        }
    }

    if (!token) {
        console.error('No authentication token available');
        throw new Error('Not authenticated');
    }

    // Log token info for debugging (first and last 10 chars only for security)
    const tokenPreview = token.length > 20 ?
        `${token.substring(0, 10)}...${token.substring(token.length - 10)}` :
        '[token too short]';
    console.log(`Using token: ${tokenPreview}`);

    // Check if token is valid JWT format (should have 3 parts separated by dots)
    const parts = token.split('.');
    if (parts.length !== 3) {
        console.error('Token is not in valid JWT format (should have 3 parts):', parts.length);
        // Try to clean up the token - sometimes line breaks or other characters get added
        token = token.trim().replace(/\s+/g, '');
        console.log('Cleaned token parts:', token.split('.').length);
    }

    const headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`
    };

    console.log(`Making authenticated request to: ${url}`);

    // For debugging - log the full Authorization header
    console.log(`Authorization header: Bearer ${token.substring(0, 10)}...`);

    try {
        const response = await fetch(url, {
            ...options,
            headers
        });

        // If we get a 401 Unauthorized error, try to refresh the token and retry the request
        if (response.status === 401 && currentUser) {
            console.log('Got 401 response, trying to refresh token and retry');
            try {
                // Force refresh token
                token = await currentUser.getIdToken(true);
                // Update stored token
                localStorage.setItem('firebaseIdToken', token);
                // Also update the cookie for server-side authentication
                document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;

                // Retry the request with the new token
                headers.Authorization = `Bearer ${token}`;
                return fetch(url, {
                    ...options,
                    headers
                });
            } catch (refreshError) {
                console.error('Error refreshing token on 401:', refreshError);
                return response; // Return the original 401 response if refresh fails
            }
        }

        return response;
    } catch (error) {
        console.error(`Network error during fetch: ${error.message}`);
        throw error;
    }
}

// Export the auth functions
const AssetKPIAuth = {
    signIn,
    signOut,
    getIdToken,
    isAuthenticated,
    getCurrentUser,
    initAuth,
    authenticatedFetch
};

// Make it available globally
window.AssetKPIAuth = AssetKPIAuth;
