"""
Webhook models for the AssetKPI application.

This module defines the database models for webhook subscriptions and webhook events.
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from database import Base
import enum


class WebhookEventType(enum.Enum):
    """Enum for webhook event types."""
    # Inventory events
    INVENTORY_UPDATED = "inventory.updated"
    INVENTORY_CREATED = "inventory.created"
    INVENTORY_DELETED = "inventory.deleted"
    INVENTORY_THRESHOLD_REACHED = "inventory.threshold_reached"
    
    # Work order events
    WORKORDER_CREATED = "workorder.created"
    WORKORDER_UPDATED = "workorder.updated"
    WORKORDER_COMPLETED = "workorder.completed"
    WORKORDER_DELETED = "workorder.deleted"
    
    # Asset events
    ASSET_CREATED = "asset.created"
    ASSET_UPDATED = "asset.updated"
    ASSET_DELETED = "asset.deleted"
    
    # KPI events
    KPI_CALCULATED = "kpi.calculated"
    KPI_THRESHOLD_REACHED = "kpi.threshold_reached"
    
    # Recommendation events
    RECOMMENDATION_CREATED = "recommendation.created"
    RECOMMENDATION_UPDATED = "recommendation.updated"
    
    # User events
    USER_CREATED = "user.created"
    USER_UPDATED = "user.updated"
    USER_DELETED = "user.deleted"
    USER_LOGIN = "user.login"
    
    # System events
    SYSTEM_BACKUP_COMPLETED = "system.backup_completed"
    SYSTEM_ERROR = "system.error"


class WebhookSubscription(Base):
    """
    Model for webhook subscriptions.
    
    This model stores information about webhook subscriptions, including the URL,
    event types, and authentication details.
    """
    __tablename__ = "webhook_subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    url = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    
    # Event types to subscribe to (stored as JSON array)
    event_types = Column(JSON, nullable=False)
    
    # Authentication
    auth_type = Column(String(50), nullable=True)  # "basic", "bearer", "api_key", etc.
    auth_credentials = Column(String(500), nullable=True)  # Encrypted credentials
    
    # Status
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # User who created the subscription
    created_by = Column(String(255), ForeignKey("users.user_id"))
    user = relationship("User", back_populates="webhook_subscriptions")
    
    # Delivery settings
    retry_count = Column(Integer, default=3)
    retry_interval = Column(Integer, default=60)  # seconds
    timeout = Column(Integer, default=30)  # seconds
    
    # Delivery history
    delivery_success_count = Column(Integer, default=0)
    delivery_failure_count = Column(Integer, default=0)
    last_delivery_status = Column(String(50), nullable=True)
    last_delivery_time = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    events = relationship("WebhookEvent", back_populates="subscription")


class WebhookEvent(Base):
    """
    Model for webhook events.
    
    This model stores information about webhook events, including the event type,
    payload, and delivery status.
    """
    __tablename__ = "webhook_events"
    
    id = Column(Integer, primary_key=True, index=True)
    subscription_id = Column(Integer, ForeignKey("webhook_subscriptions.id"))
    event_type = Column(String(50), nullable=False)
    payload = Column(JSON, nullable=False)
    
    # Delivery status
    status = Column(String(50), default="pending")  # pending, success, failure
    attempts = Column(Integer, default=0)
    next_attempt_time = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    
    # Response details
    response_status_code = Column(Integer, nullable=True)
    response_body = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Relationships
    subscription = relationship("WebhookSubscription", back_populates="events")
