{% extends "layout.html" %}

{% block title %}AssetKPI - Dashboard{% endblock %}

{% block styles %}
<style>
    /* Dashboard-specific styles */
    .dashboard-section {
        margin-bottom: 2rem;
    }
    .kpi-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .kpi-card:hover {
        transform: translateY(-5px);
    }
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 1rem;
    }
    .no-data-msg {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #6c757d;
    }
    .upload-section, .kpi-section, .recommendations-section, .quality-section, .chart-section, .inventory-summary-section {
        margin-bottom: 30px;
        padding: 25px;
        border: 1px solid #eee;
        border-radius: 8px;
        background-color: #fff;
        box-shadow: 0 3px 6px rgba(0,0,0,0.04);
    }
    .recommendations-table td:first-child, .recommendations-table th:first-child {
        text-align: center;
    }
    .priority-1 {
        font-weight: bold;
        color: #d9534f;
    }
    .priority-2 {
        color: #f0ad4e;
    }
    .priority-3 {
        color: #5bc0de;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>Dashboard</h1>
        <p class="text-muted">Overview of KPIs, inventory status, and recommendations</p>
    </div>
</div>

<!-- Dashboard Widgets for authenticated users -->
<div class="auth-required-content" data-role="VIEWER,ENGINEER,MANAGER,ADMIN">
    {% include "dashboard_widgets.html" %}
</div>

<!-- KPIs Section -->
<div class="kpi-section">
    <h2>Latest KPIs</h2>
    <ul>
         <!-- Display Latest STORED Calculated KPIs -->
        <li>MTTR (Latest Calculated):
            {% if latest_stored is mapping and latest_stored.MTTR_Calculated and latest_stored.MTTR_Calculated.kpi_value is not none %}
                {{ "%.2f"|format(latest_stored.MTTR_Calculated.kpi_value) }} hours
                <small>(as of {{ latest_stored.MTTR_Calculated.calculation_timestamp.strftime('%Y-%m-%d %H:%M:%S') }})</small>
            {% else %} N/A <small>(Pending)</small> {% endif %}
        </li>
        <li>MTBF (Latest Calculated):
            {% if latest_stored is mapping and latest_stored.MTBF_Calculated and latest_stored.MTBF_Calculated.kpi_value is not none %}
                {{ "%.2f"|format(latest_stored.MTBF_Calculated.kpi_value) }} hours
                <small>(as of {{ latest_stored.MTBF_Calculated.calculation_timestamp.strftime('%Y-%m-%d %H:%M:%S') }})</small>
            {% else %} N/A <small>(Pending)</small> {% endif %}
        </li>
         <li>Failure Rate (Latest Calculated):
            {% if latest_stored is mapping and latest_stored.FailureRate_Calculated and latest_stored.FailureRate_Calculated.kpi_value is not none %}
                {{ "%.3f"|format(latest_stored.FailureRate_Calculated.kpi_value) }} failures/year
                <small>(as of {{ latest_stored.FailureRate_Calculated.calculation_timestamp.strftime('%Y-%m-%d %H:%M:%S') }})</small>
            {% else %} N/A <small>(Pending)</small> {% endif %}
        </li>

         <!-- Display KPIs from kpireports table if available -->
        {% if kpis_report %}
            <hr style="margin: 15px 0;">
            <li><i>From KPI Report Table ({{ kpis_report.reportdate }}):</i></li>
            <li>MTTR (Report): {{ kpis_report.mttr }}</li>
            <li>MTBF (Report): {{ kpis_report.mtbf }}</li>
            <li>Avg Downtime: {{ kpis_report.avgdowntime }}</li>
            <li>Maintenance Cost: {{ kpis_report.maintenancecost }}</li>
            <li>Spare Part Usage: {{ kpis_report.sparepartusage }}</li>
            <li>OEE: {{ kpis_report.oee }}</li>
            <li>Open Work Orders: {{ kpis_report.openworkorders }}</li>
            <li>Closed Work Orders: {{ kpis_report.closedworkorders }}</li>
            <li>Preventive Ratio: {{ kpis_report.preventiveratio }}</li>
        {% endif %}
         {% if not kpis_report and (not latest_stored is mapping or not latest_stored.MTTR_Calculated) %}
            <p>No KPI data available yet.</p>
         {% endif %}
    </ul>
</div>

<!-- Chart Section -->
 <div class="chart-section">
    <h3>KPI Trends</h3>

    <!-- ADD Date Range Filter -->
    <div class="filter-controls" style="margin-bottom: 15px;">
        <label for="startDateInput">Start Date:</label>
        <input type="date" id="startDateInput" name="start_date">
        <label for="endDateInput" style="margin-left: 10px;">End Date:</label>
        <input type="date" id="endDateInput" name="end_date">
        <button id="filterButton" style="margin-left: 10px;">Apply Filter</button>
        <span id="chartErrorMsg" style="margin-left: 15px; color: red;"></span>
    </div>
    <!-- END Date Range Filter -->

    {# MTTR Chart #}
    <h4>MTTR Trend (Calculated)</h4>
    <div class="chart-container">
         <canvas id="mttrTrendChart"></canvas>
         <p class="no-data-msg" style="display: none; text-align: center;">No historical MTTR data available for selected period.</p>
     </div>

     {# ADD MTBF Chart Canvas #}
     <hr style="margin: 25px 0;">
     <h4>MTBF Trend (Calculated)</h4>
     <div class="chart-container">
         <canvas id="mtbfTrendChart"></canvas>
          <p class="no-data-msg" style="display: none; text-align: center;">No historical MTBF data available for selected period.</p>
     </div>
     {# END MTBF Chart Canvas #}
 </div>

<!-- Inventory Summary Section -->
<div class="inventory-summary-section">
    <h2>Spare Parts Overview</h2>
    {% if spare_parts %}
    <table class="inventory-summary-table">
         <thead>
             <tr>
                 <th>ID</th>
                 <th>Name</th>
                 <th>Part Number</th>
                 <th>Stock Qty</th>
                 <th>Reorder Lvl</th>
                 <th>Avg Mthly Cons.</th>
                 <th>ABC Class</th>
                 <th>Unit Price</th>
                 <th>Lead Time (d)</th>
                 <th>EOQ (Calc)</th>
                 <th>Safety Stock (Calc)</th>
                 <th>Last Restocked</th>
                 <th>Metrics Updated</th>
             </tr>
         </thead>
         <tbody>
         {% for part in spare_parts %}
             <tr>
                 <td>{{ part.partid }}</td>
                 <td>{{ part.partname }}</td>
                 <td>{{ part.partnumber }}</td>
                 <td>{{ part.stockquantity }}</td>
                 <td>{{ part.reorderlevel }}</td>
                 <td>{{ "%.2f"|format(part.avg_monthly_consumption) if part.avg_monthly_consumption is not none else 'N/A' }}</td>
                 <td><b>{{ part.abc_classification if part.abc_classification else '-' }}</b></td>
                 <td>{{ "%.2f"|format(part.unitprice) if part.unitprice is not none else 'N/A' }}</td>
                 <td>{{ part.leadtimedays if part.leadtimedays is not none else 'N/A' }}</td>
                 <td>{{ "%.0f"|format(part.eoq) if part.eoq is not none else 'N/A' }}</td>
                 <td>{{ "%.0f"|format(part.calculated_safety_stock) if part.calculated_safety_stock is not none else 'N/A' }}</td>
                 <td>{{ part.lastrestocked.strftime('%Y-%m-%d') if part.lastrestocked else 'N/A' }}</td>
                 <td><small>{{ part.inventory_metrics_last_updated.strftime('%Y-%m-%d %H:%M') if part.inventory_metrics_last_updated else 'Never' }}</small></td>
             </tr>
         {% endfor %}
         </tbody>
    </table>
    {% else %}
    <p>No spare parts data available.</p>
    {% endif %}
</div>

<!-- Inventory Recommendations Section -->
<div class="recommendations-section">
    <h2>Inventory Recommendations</h2>
    {% if recommendations %}
    <table class="recommendations-table" id="recommendationsTable">
        <thead>
            <tr>
                <th>Priority</th>
                <th>Type</th>
                <th>Part Details</th>
                <th>Reason</th>
                <th>Generated At</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        {% for rec, partid, partname, partnumber in recommendations %}
            <tr id="rec-row-{{ rec.id }}">
                <td class="priority-{{ rec.priority }}">{{ rec.priority }}</td>
                <td>{{ rec.recommendation_type }}</td>
                <td>({{ partid }}) {{ partname }} [{{ partnumber }}]</td>
                <td>{{ rec.reason }}</td>
                <td>{{ rec.generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>
                    <button class="action-button" data-rec-id="{{ rec.id }}" data-action="ACKNOWLEDGED">Ack</button>
                    <button class="action-button" data-rec-id="{{ rec.id }}" data-action="DISMISSED">Dismiss</button>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No active inventory recommendations.</p>
    {% endif %}
    <div id="rec-action-message" style="margin-top: 10px; color: green;"></div>
</div>

<!-- Data Quality Section -->
<div class="quality-section">
    <h2>Work Order Data Quality Issues</h2>
    {% if workorder_quality and workorder_quality.total_wos > 0 %}
        <p>Based on {{ workorder_quality.total_wos }} total work orders analyzed:</p>
        <ul>
            {% set dq_mrt = latest_stored.get('DQ_MissingRepairTime') if latest_stored is mapping else None %}
            <li>Missing/Invalid Repair Time: {{ workorder_quality.get('missing_repairtime', 0) }}
                ({{ "%.1f"|format(workorder_quality.get('missing_repairtime', 0) * 100 / workorder_quality.total_wos if workorder_quality.total_wos else 0) }}%)
                 <small>(Affects MTTR) {% if dq_mrt %} - Last Recorded: {{ "%.0f"|format(dq_mrt.kpi_value) }} on {{ dq_mrt.calculation_timestamp.strftime('%Y-%m-%d %H:%M') }}{% endif %}</small>
            </li>
            <li>Missing Start Date: {{ workorder_quality.get('missing_startdate', 0) }}
                ({{ "%.1f"|format(workorder_quality.get('missing_startdate', 0) * 100 / workorder_quality.total_wos if workorder_quality.total_wos else 0) }}%)
                 <small>(Affects MTBF/FR)</small>
            </li>
            <li>Missing End Date: {{ workorder_quality.get('missing_enddate', 0) }}
                ({{ "%.1f"|format(workorder_quality.get('missing_enddate', 0) * 100 / workorder_quality.total_wos if workorder_quality.total_wos else 0) }}%)
                 <small>(Affects MTBF/FR)</small>
            </li>
            {% set corrective_count = workorder_quality.get('corrective_wos', 0) %}
            {% if corrective_count > 0 %}
                {% set invalid_intervals = workorder_quality.get('invalid_corrective_intervals', 0) %}
                 {% set dq_inv = latest_stored.get('DQ_InvalidIntervals') if latest_stored is mapping else None %}
                <li>Invalid Corrective Intervals (Start <= Previous End): {{ invalid_intervals }}
                    (out of {{ corrective_count }} WOs analyzed - {{ "%.1f"|format(invalid_intervals * 100 / corrective_count if corrective_count else 0) }}%)
                     <small>(Affects MTBF/FR) {% if dq_inv %} - Last Recorded: {{ "%.0f"|format(dq_inv.kpi_value) }} on {{ dq_inv.calculation_timestamp.strftime('%Y-%m-%d %H:%M') }}{% endif %}</small>
                </li>
            {% else %} <li>No corrective work orders found to analyze intervals.</li> {% endif %}
        </ul>
    {% elif workorder_quality %} <p>No work orders found to analyze.</p>
    {% else %} <p>Could not calculate data quality metrics.</p> {% endif %}
</div>

<!-- Upload form section -->
<div class="upload-section">
    <h2>Upload Work Orders CSV</h2>
    <p>Columns: assetid, workordertype, description, status, assignedto, failurecode, failuretype, downtimeminutes, repairtimeminutes, maintenancecost, startdate, enddate.</p>
    <form action="/upload/workorders" method="post" enctype="multipart/form-data">
        <input type="file" name="file" accept=".csv" required>
        <button type="submit">Upload File</button>
    </form>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>

<script>
  const chartInstances = {}; // Store chart instances to destroy them before re-rendering

  // Generic function to fetch data and render/update a KPI chart
  async function renderKpiChart(kpiName, canvasId, yAxisLabel) {
    const chartElement = document.getElementById(canvasId);
    const chartContainer = chartElement?.parentElement;
    const noDataElement = chartContainer?.querySelector('.no-data-msg');
    const errorMsgElement = document.getElementById('chartErrorMsg'); // General error display

    if (!chartElement || !chartContainer || !noDataElement || !errorMsgElement) {
      console.error(`Chart rendering setup failed for canvas ID: ${canvasId}`);
      return;
    }

    // Clear previous errors/no-data messages for this specific chart
    errorMsgElement.textContent = '';
    noDataElement.style.display = 'none'; // Hide no-data message initially

    const ctx = chartElement.getContext('2d');

    // Destroy previous chart instance for this canvas if it exists
    if (chartInstances[canvasId]) {
        chartInstances[canvasId].destroy();
    }

    try {
      // Get date values
      const startDate = document.getElementById('startDateInput').value;
      const endDate = document.getElementById('endDateInput').value;

      // Construct API URL with query parameters if dates are provided
      let apiUrl = `/api/kpi/history/${kpiName}?limit=1000`; // Fetch more points if filtering
      if (startDate) {
        apiUrl += `&start_date=${encodeURIComponent(startDate)}`;
      }
      if (endDate) {
        apiUrl += `&end_date=${encodeURIComponent(endDate)}`;
      }

      // Fetch data
      const response = await fetch(apiUrl);
      if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: `HTTP error! status: ${response.status}`}));
          throw new Error(errorData.detail || `Failed to fetch data: ${response.status}`);
      }
      const historyData = await response.json();

      // Check if data is empty AFTER fetch
       if (!historyData || historyData.length === 0) {
          noDataElement.style.display = 'block'; // Show no-data message
          return; // Don't try to render empty chart
      }


      // Prepare data
      const labels = historyData.map(point => new Date(point.timestamp).toLocaleString([], { year: 'numeric', month: 'numeric', day: 'numeric', hour: '2-digit', minute: '2-digit' }) );
      const dataPoints = historyData.map(point => point.value);

      // Basic color mapping for different KPIs
      const colors = {
          'MTTR_Calculated': 'rgb(75, 192, 192)',
          'MTBF_Calculated': 'rgb(255, 99, 132)',
          'default': 'rgb(54, 162, 235)'
      };
      const borderColor = colors[kpiName] || colors['default'];

      // Chart config (similar to before)
      const chartConfig = {
        type: 'line',
        data: { labels: labels, datasets: [{ label: `${yAxisLabel}`, data: dataPoints, borderColor: borderColor, tension: 0.1, fill: false, pointRadius: 3, pointHoverRadius: 5 }] },
        options: { responsive: true, maintainAspectRatio: false, scales: { y: { beginAtZero: false, title: { display: true, text: yAxisLabel } }, x: { title: { display: true, text: 'Calculation Timestamp' }, ticks: { maxRotation: 70, minRotation: 45, autoSkip: true, maxTicksLimit: 25 } } }, plugins: { legend: { display: true }, tooltip: { mode: 'index', intersect: false } }, hover: { mode: 'nearest', intersect: true } }
      };

      // Create and store chart instance
      chartInstances[canvasId] = new Chart(ctx, chartConfig);

    } catch (error) {
      console.error(`Error fetching or rendering chart (${kpiName}):`, error);
      errorMsgElement.textContent = `Error loading ${kpiName} chart: ${error.message}`; // Show error
       noDataElement.style.display = 'none'; // Hide no-data msg if error occurred
    }
  }

  // Function to update both charts based on filter
  function updateCharts() {
      console.log("Updating charts based on date filter...");
      // Clear general error message before attempting updates
      const errorMsgElement = document.getElementById('chartErrorMsg');
      if(errorMsgElement) errorMsgElement.textContent = '';

      renderKpiChart('MTTR_Calculated', 'mttrTrendChart', 'MTTR (Hours)');
      renderKpiChart('MTBF_Calculated', 'mtbfTrendChart', 'MTBF (Hours)');
  }

  // Add event listener to the filter button
  document.addEventListener('DOMContentLoaded', function() {
      const filterButton = document.getElementById('filterButton');
      if (filterButton) {
          filterButton.addEventListener('click', updateCharts);
      } else {
          console.error("Filter button not found!");
      }

      // Initial chart render on page load
      updateCharts();
  });
</script>

<!-- Dashboard Widgets Script -->
<script>
    // Function to load dashboard widget data
    function loadDashboardWidgetData() {
        // Load asset count
        AssetKPIAuth.authenticatedFetch('/api/assets/count')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch asset count');
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('totalAssetsCount').textContent = data.count;
            })
            .catch(error => {
                console.error('Error loading asset count:', error);
                document.getElementById('totalAssetsCount').textContent = 'Error';
            });

        // Load inventory value
        AssetKPIAuth.authenticatedFetch('/api/inventory/summary')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch inventory summary');
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('inventoryValueCount').textContent = `$${data.total_value.toLocaleString()}`;
            })
            .catch(error => {
                console.error('Error loading inventory value:', error);
                document.getElementById('inventoryValueCount').textContent = 'Error';
            });

        // Load potential savings
        AssetKPIAuth.authenticatedFetch('/api/inventory/optimization-report')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch optimization report');
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('potentialSavingsCount').textContent = `$${data.summary.potential_savings.toLocaleString()}`;

                // Render inventory status chart
                renderInventoryStatusChart(data.status);

                // Render ABC analysis chart
                renderABCAnalysisChart(data.abc_analysis);
            })
            .catch(error => {
                console.error('Error loading potential savings:', error);
                document.getElementById('potentialSavingsCount').textContent = 'Error';
            });

        // Load open work orders count
        AssetKPIAuth.authenticatedFetch('/api/workorders/count?status=OPEN')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch open work orders count');
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('openWorkOrdersCount').textContent = data.count;
            })
            .catch(error => {
                console.error('Error loading open work orders count:', error);
                document.getElementById('openWorkOrdersCount').textContent = 'Error';
            });
    }

    // Function to render inventory status chart
    function renderInventoryStatusChart(statusData) {
        const ctx = document.getElementById('inventoryStatusChart').getContext('2d');

        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['At Risk of Stockout', 'Optimal Level', 'Overstocked'],
                datasets: [{
                    data: [
                        statusData.stockout_risk_count,
                        statusData.optimal_stock_count,
                        statusData.overstock_count
                    ],
                    backgroundColor: [
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)'
                    ],
                    borderColor: [
                        'rgba(220, 53, 69, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }

    // Function to render ABC analysis chart
    function renderABCAnalysisChart(abcData) {
        const ctx = document.getElementById('abcAnalysisChart').getContext('2d');

        const classes = ['A', 'B', 'C', 'Unclassified'];
        const values = classes.map(cls => abcData[cls]?.value || 0);

        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: classes,
                datasets: [{
                    data: values,
                    backgroundColor: [
                        'rgba(220, 53, 69, 0.7)',  // A - Red
                        'rgba(255, 193, 7, 0.7)',  // B - Yellow
                        'rgba(40, 167, 69, 0.7)',  // C - Green
                        'rgba(108, 117, 125, 0.7)' // Unclassified - Gray
                    ],
                    borderColor: [
                        'rgba(220, 53, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(108, 117, 125, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }

    // Recommendation Action Script
    document.addEventListener('DOMContentLoaded', function() {
        const recommendationsTableBody = document.querySelector('#recommendationsTable tbody');
        const actionMessageDiv = document.getElementById('rec-action-message');

        if (recommendationsTableBody) {
            recommendationsTableBody.addEventListener('click', async (event) => {
                // Check if the clicked element is one of our action buttons
                if (event.target.classList.contains('action-button')) {
                    const button = event.target;
                    const recId = button.dataset.recId;
                    const newStatus = button.dataset.action;

                    // Disable button to prevent double-clicks
                    button.disabled = true;
                    button.textContent = '...'; // Indicate processing
                    actionMessageDiv.textContent = ''; // Clear previous messages

                    try {
                        // Use authenticated fetch
                        const response = await AssetKPIAuth.authenticatedFetch(`/api/recommendations/${recId}/status`, {
                            method: 'PATCH',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ new_status: newStatus })
                        });

                        const result = await response.json(); // Try to parse JSON response

                        if (!response.ok) {
                            // Display error from API response if available, otherwise generic error
                            throw new Error(result.detail || `Failed to update status: ${response.status}`);
                        }

                        // Success!
                        actionMessageDiv.textContent = result.message || `Recommendation ${recId} updated to ${newStatus}.`;
                        actionMessageDiv.style.color = 'green';

                        // Since we only show 'NEW' items, remove the row from the table
                        const rowToRemove = document.getElementById(`rec-row-${recId}`);
                        if (rowToRemove) {
                            rowToRemove.remove();
                        }
                         // Check if table body is now empty
                        if (recommendationsTableBody.childElementCount === 0) {
                            const table = document.getElementById('recommendationsTable');
                            const noDataDiv = document.createElement('p');
                            noDataDiv.textContent = 'No active inventory recommendations.';
                            table.parentElement.insertBefore(noDataDiv, table.nextSibling); // Add msg after table
                            table.remove(); // Remove empty table
                        }
                    } catch (error) {
                        console.error("Error updating recommendation status:", error);
                        actionMessageDiv.textContent = `Error: ${error.message}`;
                        actionMessageDiv.style.color = 'red';
                        // Re-enable button on failure
                        button.disabled = false;
                        button.textContent = newStatus === 'ACKNOWLEDGED' ? 'Ack' : 'Dismiss';
                    }
                }
            });
        }

        // Initialize authentication UI
        AssetKPIAuth.initAuth(updateUI);
    });

    // Function to update UI based on authentication
    function updateUI(user) {
        const authRequiredContent = document.querySelectorAll('.auth-required-content');

        if (user) {
            // User is authenticated, show role-specific content
            AssetKPIAuth.authenticatedFetch('/api/users/me')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('Failed to fetch user info');
                })
                .then(data => {
                    const userRole = data.role || '';

                    // Show content based on role
                    authRequiredContent.forEach(el => {
                        const requiredRoles = (el.dataset.role || '').split(',');
                        if (requiredRoles.includes(userRole)) {
                            el.style.display = 'block';
                        } else {
                            el.style.display = 'none';
                        }
                    });

                    // Load dashboard widget data if user is authenticated
                    loadDashboardWidgetData();
                })
                .catch(error => {
                    console.error('Error fetching user info:', error);
                });
        } else {
            // User is not authenticated, hide all authenticated content
            authRequiredContent.forEach(el => {
                el.style.display = 'none';
            });
        }
    }
</script>
{% endblock %}
