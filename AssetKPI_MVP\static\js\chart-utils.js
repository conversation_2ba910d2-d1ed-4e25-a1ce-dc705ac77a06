/**
 * AssetKPI Chart Utilities
 *
 * A collection of reusable chart configurations and helper functions
 * for creating consistent and responsive charts throughout the application.
 */

const AssetKPICharts = {
    /**
     * Default chart colors
     */
    colors: {
        primary: '#0d6efd',
        secondary: '#6c757d',
        success: '#198754',
        danger: '#dc3545',
        warning: '#ffc107',
        info: '#0dcaf0',
        light: '#f8f9fa',
        dark: '#212529',
        primaryLight: '#cfe2ff',
        secondaryLight: '#e2e3e5',
        successLight: '#d1e7dd',
        dangerLight: '#f8d7da',
        warningLight: '#fff3cd',
        infoLight: '#cff4fc',
        // Additional colors for multiple datasets
        chart1: '#4e73df',
        chart2: '#1cc88a',
        chart3: '#36b9cc',
        chart4: '#f6c23e',
        chart5: '#e74a3b',
        chart6: '#858796',
        chart7: '#5a5c69',
        chart8: '#2e59d9',
        chart9: '#17a673',
        chart10: '#2c9faf'
    },

    /**
     * Default chart options
     */
    defaultOptions: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    boxWidth: 12,
                    usePointStyle: true,
                    pointStyle: 'circle'
                }
            },
            tooltip: {
                mode: 'index',
                intersect: false,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: 'rgba(0, 0, 0, 0.1)',
                borderWidth: 1,
                padding: 10,
                displayColors: true,
                callbacks: {
                    // Default callbacks can be overridden
                }
            },
            title: {
                display: true,
                text: 'Chart Title',
                font: {
                    size: 16,
                    weight: 'bold'
                },
                padding: {
                    top: 10,
                    bottom: 20
                }
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    maxRotation: 45,
                    minRotation: 0
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)'
                },
                ticks: {
                    precision: 0
                }
            }
        },
        animation: {
            duration: 1000,
            easing: 'easeOutQuart'
        },
        elements: {
            line: {
                tension: 0.4
            },
            point: {
                radius: 3,
                hoverRadius: 5
            }
        },
        layout: {
            padding: {
                left: 10,
                right: 25,
                top: 25,
                bottom: 10
            }
        }
    },

    /**
     * Create a line chart with the given data and options
     *
     * @param {string} canvasId - The ID of the canvas element
     * @param {Array} labels - The labels for the x-axis
     * @param {Array} datasets - The datasets to display
     * @param {Object} customOptions - Custom options to override defaults
     * @returns {Chart} The created chart instance
     */
    createLineChart: function(canvasId, labels, datasets, customOptions = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element with ID '${canvasId}' not found`);
            return null;
        }

        // Destroy existing chart if it exists
        this.destroyChart(canvasId);

        // Merge default options with custom options
        const options = this.mergeOptions(this.defaultOptions, customOptions);

        // Create the chart
        return new Chart(canvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: datasets
            },
            options: options
        });
    },

    /**
     * Create a bar chart with the given data and options
     *
     * @param {string} canvasId - The ID of the canvas element
     * @param {Array} labels - The labels for the x-axis
     * @param {Array} datasets - The datasets to display
     * @param {Object} customOptions - Custom options to override defaults
     * @returns {Chart} The created chart instance
     */
    createBarChart: function(canvasId, labels, datasets, customOptions = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element with ID '${canvasId}' not found`);
            return null;
        }

        // Destroy existing chart if it exists
        this.destroyChart(canvasId);

        // Merge default options with custom options
        const options = this.mergeOptions(this.defaultOptions, customOptions);

        // Create the chart
        return new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: datasets
            },
            options: options
        });
    },

    /**
     * Create a pie chart with the given data and options
     *
     * @param {string} canvasId - The ID of the canvas element
     * @param {Array} labels - The labels for the segments
     * @param {Array} data - The data values
     * @param {Array} backgroundColor - The background colors for the segments
     * @param {Object} customOptions - Custom options to override defaults
     * @returns {Chart} The created chart instance
     */
    createPieChart: function(canvasId, labels, data, backgroundColor, customOptions = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element with ID '${canvasId}' not found`);
            return null;
        }

        // Destroy existing chart if it exists
        this.destroyChart(canvasId);

        // Default pie chart options
        const pieDefaults = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        boxWidth: 12,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '0%'
        };

        // Merge pie defaults with custom options
        const options = this.mergeOptions(pieDefaults, customOptions);

        // Create the chart
        return new Chart(canvas, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColor || Object.values(this.colors).slice(0, data.length)
                }]
            },
            options: options
        });
    },

    /**
     * Create a doughnut chart with the given data and options
     *
     * @param {string} canvasId - The ID of the canvas element
     * @param {Array} labels - The labels for the segments
     * @param {Array} data - The data values
     * @param {Array} backgroundColor - The background colors for the segments
     * @param {Object} customOptions - Custom options to override defaults
     * @returns {Chart} The created chart instance
     */
    createDoughnutChart: function(canvasId, labels, data, backgroundColor, customOptions = {}) {
        // Use the pie chart function with a cutout
        const doughnutDefaults = {
            cutout: '70%'
        };
        const options = this.mergeOptions(doughnutDefaults, customOptions);
        return this.createPieChart(canvasId, labels, data, backgroundColor, options);
    },

    /**
     * Create a radar chart with the given data and options
     *
     * @param {string} canvasId - The ID of the canvas element
     * @param {Array} labels - The labels for the axes
     * @param {Array} datasets - The datasets to display
     * @param {Object} customOptions - Custom options to override defaults
     * @returns {Chart} The created chart instance
     */
    createRadarChart: function(canvasId, labels, datasets, customOptions = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element with ID '${canvasId}' not found`);
            return null;
        }

        // Destroy existing chart if it exists
        this.destroyChart(canvasId);

        // Default radar chart options
        const radarDefaults = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    angleLines: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    suggestedMin: 0,
                    ticks: {
                        backdropColor: 'rgba(0, 0, 0, 0)'
                    }
                }
            }
        };

        // Merge radar defaults with custom options
        const options = this.mergeOptions(radarDefaults, customOptions);

        // Create the chart
        return new Chart(canvas, {
            type: 'radar',
            data: {
                labels: labels,
                datasets: datasets
            },
            options: options
        });
    },

    /**
     * Export a chart as a PNG image
     *
     * @param {string} canvasId - The ID of the canvas element
     * @param {string} fileName - The name of the file to download
     */
    exportChartAsPNG: function(canvasId, fileName = 'chart') {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element with ID '${canvasId}' not found`);
            return;
        }

        // Create a temporary link element
        const link = document.createElement('a');
        link.download = `${fileName}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
    },

    /**
     * Export a chart as a PDF document
     *
     * @param {string} canvasId - The ID of the canvas element
     * @param {string} title - The title of the PDF document
     * @param {string} fileName - The name of the file to download
     */
    exportChartAsPDF: function(canvasId, title = 'Chart', fileName = 'chart') {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element with ID '${canvasId}' not found`);
            return;
        }

        // Get the chart image as a data URL
        const chartImage = canvas.toDataURL('image/png');

        // Create the PDF document definition
        const docDefinition = {
            pageSize: 'A4',
            pageOrientation: 'landscape',
            content: [
                { text: title, style: 'header' },
                { text: `Generated on ${new Date().toLocaleString()}`, style: 'subheader' },
                { image: chartImage, width: 700, alignment: 'center' }
            ],
            styles: {
                header: {
                    fontSize: 18,
                    bold: true,
                    margin: [0, 0, 0, 10]
                },
                subheader: {
                    fontSize: 12,
                    italics: true,
                    margin: [0, 0, 0, 20]
                }
            }
        };

        // Generate and download the PDF
        pdfMake.createPdf(docDefinition).download(`${fileName}.pdf`);
    },

    /**
     * Destroy a chart instance
     *
     * @param {string} canvasId - The ID of the canvas element
     */
    destroyChart: function(canvasId) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            return;
        }

        // Get the chart instance from the canvas
        const chartInstance = Chart.getChart(canvas);
        if (chartInstance) {
            chartInstance.destroy();
        }
    },

    /**
     * Merge default options with custom options
     *
     * @param {Object} defaults - The default options
     * @param {Object} custom - The custom options to override defaults
     * @returns {Object} The merged options
     */
    mergeOptions: function(defaults, custom) {
        // Deep clone the defaults to avoid modifying the original
        const merged = JSON.parse(JSON.stringify(defaults));

        // Recursively merge custom options
        function merge(target, source) {
            for (const key in source) {
                if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
                    merge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        }

        merge(merged, custom);
        return merged;
    },

    /**
     * Create a responsive chart that adapts to different screen sizes
     *
     * @param {string} canvasId - The ID of the canvas element
     * @param {Function} chartCreator - Function that creates the chart
     */
    createResponsiveChart: function(canvasId, chartCreator) {
        let chart = null;
        const canvas = document.getElementById(canvasId);

        if (!canvas) {
            console.error(`Canvas element with ID '${canvasId}' not found`);
            return;
        }

        // Create the chart initially
        chart = chartCreator();

        // Update the chart when the window is resized
        const resizeHandler = () => {
            if (chart) {
                chart.resize();
            }
        };

        // Add resize event listener
        window.addEventListener('resize', resizeHandler);

        // Return a function to clean up the event listener
        return function cleanup() {
            window.removeEventListener('resize', resizeHandler);
            if (chart) {
                chart.destroy();
            }
        };
    },

    /**
     * Format a number as a percentage
     *
     * @param {number} value - The value to format
     * @param {number} decimals - The number of decimal places
     * @returns {string} The formatted percentage
     */
    formatPercentage: function(value, decimals = 1) {
        return value.toFixed(decimals) + '%';
    },

    /**
     * Format a number with a unit
     *
     * @param {number} value - The value to format
     * @param {string} unit - The unit to append
     * @param {number} decimals - The number of decimal places
     * @returns {string} The formatted value with unit
     */
    formatWithUnit: function(value, unit, decimals = 1) {
        return value.toFixed(decimals) + ' ' + unit;
    },

    /**
     * Get standard chart options with customizations
     *
     * @param {Object} customOptions - Custom options to override defaults
     * @returns {Object} The chart options
     */
    getChartOptions: function(customOptions = {}) {
        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: customOptions.title || 'Chart Title',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: {
                        top: 10,
                        bottom: 20
                    }
                },
                legend: {
                    position: 'top',
                    labels: {
                        boxWidth: 12,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    },
                    title: {
                        display: customOptions.xAxisTitle ? true : false,
                        text: customOptions.xAxisTitle || '',
                        font: {
                            weight: 'bold'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        precision: 0
                    },
                    title: {
                        display: customOptions.yAxisTitle ? true : false,
                        text: customOptions.yAxisTitle || '',
                        font: {
                            weight: 'bold'
                        }
                    },
                    suggestedMin: customOptions.suggestedMin,
                    suggestedMax: customOptions.suggestedMax
                }
            }
        };

        // Apply indexAxis if specified (for horizontal bar charts)
        if (customOptions.indexAxis) {
            options.indexAxis = customOptions.indexAxis;
        }

        return options;
    },

    /**
     * Format a currency value
     *
     * @param {number} value - The value to format
     * @param {string} currency - The currency code (default: USD)
     * @param {string} locale - The locale to use for formatting (default: en-US)
     * @returns {string} The formatted currency value
     */
    formatCurrency: function(value, currency = 'USD', locale = 'en-US') {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency
        }).format(value);
    }
};
