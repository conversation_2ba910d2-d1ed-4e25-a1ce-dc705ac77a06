# Authentication for AssetKPI

This document provides comprehensive details about the authentication system implemented in AssetKPI, covering both Firebase and test user authentication methods.

## Overview

The AssetKPI authentication system supports both Firebase authentication for production use and a simplified test user authentication for development and testing purposes. A unified interface is provided on the frontend to handle both methods.

## Authentication Methods

### Firebase Authentication

Firebase authentication is the primary and recommended authentication method for AssetKPI. It leverages Firebase's secure authentication services for user management.

Key features:
- Secure user authentication using Firebase.
- Firebase Admin SDK integration on the server-side for token verification.
- User registration endpoint (`/api/register`) for automatic user creation (assigns VIEWER role by default).
- Token refresh mechanism to handle token expiration.
- Enhanced error messages for authentication failures.
- Detailed user information in error responses.
- Updated last login timestamp tracking.
- Comprehensive token handling in the frontend.
- Improved security with SameSite cookie attributes.
- Client-side token storage and refresh.

### Test User Authentication

Test user authentication is a simplified method designed *strictly* for development and testing. It uses hardcoded test tokens and should **never** be used in a production environment due to security risks.

Available test users and tokens:

| Role     | Token              | User ID          |
|----------|--------------------|------------------|
| Admin    | `test-admin-token` | `test-admin-uid` |
| Manager  | `test-manager-token` | `test-manager-uid` |
| Engineer | `test-engineer-token` | `test-engineer-uid` |
| Viewer   | `test-viewer-token` | `test-viewer-uid` |

## Implementation Details

### Frontend Implementation

The frontend authentication system is implemented in the `static/js` directory:

- `static/js/auth_simple.js`: Handles Firebase authentication logic.
- `static/js/test_auth.js`: Handles test user authentication logic.
- `static/js/combined_auth.js`: Provides a unified interface (`CombinedAuth` object) that automatically detects and uses the appropriate authentication method based on the token format.

Example usage of `CombinedAuth`:

```javascript
// Sign in with Firebase
CombinedAuth.signInWithEmailAndPassword('<EMAIL>', 'password');

// Sign in with test token
CombinedAuth.signInWithTestToken('test-admin-token');

// Get the current token
CombinedAuth.getToken();

// Check if authenticated
CombinedAuth.isAuthenticated();

// Sign out
CombinedAuth.signOut();
```

The login page (`/login`) includes a "Test Auth" tab to switch between Firebase and test user authentication UI.

### Backend Implementation

The backend handles token verification and user management:

- **Firebase Admin SDK**: Used for server-side verification of Firebase ID tokens.
- **Role-Based Access Control (RBAC)**: Four user roles (ADMIN, MANAGER, ENGINEER, VIEWER) with different permission levels are implemented.
- **Authentication Endpoints**:
    - `/api/register`: Registers a new user.
    - `/api/debug-token`: Debug endpoint for token verification.
    - `/api/user-check/{user_id}`: Checks if a user exists in the database.
    - `/api/test-user-auth`: Endpoint for test user authentication.
- **Token Refresh Mechanism**: Automatic token refresh is implemented.
- **Test User Authentication Module (`test_user_auth.py`)**: Maps test tokens to user IDs and queries the database for user information. Raises 404 if the user is not found or 401 if the token is invalid.

### Testing Tools

- Frontend authentication tests at `/auth-tests`.
- Firebase login test page at `/firebase-login-test`.
- Debug token endpoint at `/api/debug-token`.
- User check endpoint at `/api/user-check/{user_id}`.
- Utility scripts:
    - `add_firebase_test_user.py`: Add a test user to the database.
    - `test_firebase_token.py`: Test token verification.
    - `test_auth.py`: Test authentication endpoints.

### Error Handling

Detailed error messages are provided for authentication failures. Helpful registration instructions are shown if a user exists in Firebase but not in the local database.

## Security Considerations

**Test user authentication is for development and testing ONLY.** The hardcoded test tokens are not secure and must not be used for real users or in production environments.

To disable test user authentication in a production environment:
1. Remove or disable the `/api/test-user-auth` endpoint in `main.py`.
2. Remove or disable the `test_user_auth.py` module.
3. Remove or hide the "Test Auth" UI elements from the login page template.
4. Consider using environment variables or a configuration file to control the availability of test authentication features.

## Future Improvements (from test_auth_README.md)

- Add support for custom test tokens.
- Add support for test user creation.
- Add support for test user deletion.
- Add support for test user role changes.