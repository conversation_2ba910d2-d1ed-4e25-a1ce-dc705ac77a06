"""
Validation utilities for the AssetKPI application.

This module provides utilities for validating data on the server side.
"""

import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Union


class ValidationError(Exception):
    """
    Exception raised for validation errors.
    """
    def __init__(self, errors: Dict[str, List[str]]):
        self.errors = errors
        super().__init__(str(errors))


class Validator:
    """
    Validator class for validating data.
    """
    
    @staticmethod
    def required(value: Any, message: str = "This field is required") -> Optional[str]:
        """
        Validate that a value is not empty.
        
        Args:
            value: Value to validate
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None:
            return message
        
        if isinstance(value, str) and not value.strip():
            return message
        
        if isinstance(value, (list, dict)) and not value:
            return message
        
        return None
    
    @staticmethod
    def min_length(value: str, min_length: int, message: str = "Must be at least {min_length} characters") -> Optional[str]:
        """
        Validate that a string has a minimum length.
        
        Args:
            value: Value to validate
            min_length: Minimum length
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        if len(str(value)) < min_length:
            return message.format(min_length=min_length)
        
        return None
    
    @staticmethod
    def max_length(value: str, max_length: int, message: str = "Must be no more than {max_length} characters") -> Optional[str]:
        """
        Validate that a string has a maximum length.
        
        Args:
            value: Value to validate
            max_length: Maximum length
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        if len(str(value)) > max_length:
            return message.format(max_length=max_length)
        
        return None
    
    @staticmethod
    def numeric(value: Any, message: str = "Must be a number") -> Optional[str]:
        """
        Validate that a value is numeric.
        
        Args:
            value: Value to validate
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        try:
            float(value)
            return None
        except (ValueError, TypeError):
            return message
    
    @staticmethod
    def min_value(value: Union[int, float], min_value: Union[int, float], message: str = "Must be at least {min_value}") -> Optional[str]:
        """
        Validate that a numeric value is at least a minimum value.
        
        Args:
            value: Value to validate
            min_value: Minimum value
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        try:
            if float(value) < min_value:
                return message.format(min_value=min_value)
        except (ValueError, TypeError):
            return "Must be a number"
        
        return None
    
    @staticmethod
    def max_value(value: Union[int, float], max_value: Union[int, float], message: str = "Must be no more than {max_value}") -> Optional[str]:
        """
        Validate that a numeric value is no more than a maximum value.
        
        Args:
            value: Value to validate
            max_value: Maximum value
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        try:
            if float(value) > max_value:
                return message.format(max_value=max_value)
        except (ValueError, TypeError):
            return "Must be a number"
        
        return None
    
    @staticmethod
    def pattern(value: str, pattern: str, message: str = "Must match the required format") -> Optional[str]:
        """
        Validate that a string matches a regular expression pattern.
        
        Args:
            value: Value to validate
            pattern: Regular expression pattern
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        if not re.match(pattern, str(value)):
            return message
        
        return None
    
    @staticmethod
    def email(value: str, message: str = "Must be a valid email address") -> Optional[str]:
        """
        Validate that a string is a valid email address.
        
        Args:
            value: Value to validate
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        # Simple email validation pattern
        pattern = r"^[^\s@]+@[^\s@]+\.[^\s@]+$"
        if not re.match(pattern, str(value)):
            return message
        
        return None
    
    @staticmethod
    def date(value: str, message: str = "Must be a valid date") -> Optional[str]:
        """
        Validate that a string is a valid date.
        
        Args:
            value: Value to validate
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        try:
            datetime.fromisoformat(value)
            return None
        except (ValueError, TypeError):
            return message
    
    @staticmethod
    def min_date(value: str, min_date: str, message: str = "Must be on or after {min_date}") -> Optional[str]:
        """
        Validate that a date is on or after a minimum date.
        
        Args:
            value: Value to validate
            min_date: Minimum date
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        try:
            date_value = datetime.fromisoformat(value)
            min_date_value = datetime.fromisoformat(min_date)
            
            if date_value < min_date_value:
                return message.format(min_date=min_date)
        except (ValueError, TypeError):
            return "Must be a valid date"
        
        return None
    
    @staticmethod
    def max_date(value: str, max_date: str, message: str = "Must be on or before {max_date}") -> Optional[str]:
        """
        Validate that a date is on or before a maximum date.
        
        Args:
            value: Value to validate
            max_date: Maximum date
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value is None or value == "":
            return None  # Skip validation if value is empty
        
        try:
            date_value = datetime.fromisoformat(value)
            max_date_value = datetime.fromisoformat(max_date)
            
            if date_value > max_date_value:
                return message.format(max_date=max_date)
        except (ValueError, TypeError):
            return "Must be a valid date"
        
        return None
    
    @staticmethod
    def equal_to(value: Any, target_value: Any, message: str = "Must be equal to {target_value}") -> Optional[str]:
        """
        Validate that a value is equal to a target value.
        
        Args:
            value: Value to validate
            target_value: Target value
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if value != target_value:
            return message.format(target_value=target_value)
        
        return None
    
    @staticmethod
    def custom(value: Any, validator: Callable[[Any], bool], message: str = "Invalid value") -> Optional[str]:
        """
        Validate a value using a custom validator function.
        
        Args:
            value: Value to validate
            validator: Custom validator function
            message: Error message
            
        Returns:
            Error message if validation fails, None otherwise
        """
        if not validator(value):
            return message
        
        return None


def validate_data(data: Dict[str, Any], rules: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[str]]:
    """
    Validate data against a set of rules.
    
    Args:
        data: Data to validate
        rules: Validation rules
        
    Returns:
        Dictionary of validation errors by field
    """
    errors = {}
    
    for field_name, field_rules in rules.items():
        field_value = data.get(field_name)
        field_errors = []
        
        for rule in field_rules:
            rule_name = rule.get("rule")
            
            if not hasattr(Validator, rule_name):
                continue
            
            validator = getattr(Validator, rule_name)
            params = rule.get("params", {})
            message = rule.get("message", None)
            
            # Call validator with appropriate parameters
            if rule_name == "required":
                error = validator(field_value, message=message) if message else validator(field_value)
            elif rule_name in ["min_length", "max_length"]:
                length = params.get("length")
                error = validator(field_value, length, message=message) if message else validator(field_value, length)
            elif rule_name in ["min_value", "max_value"]:
                value = params.get("value")
                error = validator(field_value, value, message=message) if message else validator(field_value, value)
            elif rule_name == "pattern":
                pattern = params.get("pattern")
                error = validator(field_value, pattern, message=message) if message else validator(field_value, pattern)
            elif rule_name in ["min_date", "max_date"]:
                date = params.get("date")
                error = validator(field_value, date, message=message) if message else validator(field_value, date)
            elif rule_name == "equal_to":
                target_value = params.get("value")
                error = validator(field_value, target_value, message=message) if message else validator(field_value, target_value)
            elif rule_name == "custom":
                validator_func = params.get("validator")
                error = validator(field_value, validator_func, message=message) if message else validator(field_value, validator_func)
            else:
                error = validator(field_value, message=message) if message else validator(field_value)
            
            if error:
                field_errors.append(error)
        
        if field_errors:
            errors[field_name] = field_errors
    
    return errors


def validate_or_error(data: Dict[str, Any], rules: Dict[str, List[Dict[str, Any]]]) -> None:
    """
    Validate data against a set of rules and raise ValidationError if validation fails.
    
    Args:
        data: Data to validate
        rules: Validation rules
        
    Raises:
        ValidationError: If validation fails
    """
    errors = validate_data(data, rules)
    
    if errors:
        raise ValidationError(errors)


def validation_error_response(errors: Dict[str, List[str]]) -> Dict[str, Any]:
    """
    Create a validation error response.
    
    Args:
        errors: Validation errors
        
    Returns:
        Validation error response
    """
    return {
        "success": False,
        "errors": errors
    }
