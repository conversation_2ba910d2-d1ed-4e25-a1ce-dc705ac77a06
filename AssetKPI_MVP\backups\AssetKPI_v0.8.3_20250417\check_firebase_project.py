import json
import os
import base64
import jwt
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def decode_token_without_verification(token):
    """
    Decode a Firebase ID token without verifying the signature.
    This is just to extract the payload for debugging purposes.
    """
    try:
        # Split the token into parts
        parts = token.split('.')
        if len(parts) != 3:
            print("ERROR: Token does not have three parts")
            return None
        
        # Decode the payload (middle part)
        payload_b64 = parts[1]
        # Add padding if needed
        payload_b64 += '=' * (4 - len(payload_b64) % 4) if len(payload_b64) % 4 != 0 else ''
        
        try:
            payload = base64.b64decode(payload_b64)
            return json.loads(payload)
        except Exception as e:
            print(f"ERROR: Failed to decode token payload: {e}")
            return None
    except Exception as e:
        print(f"ERROR: Failed to decode token: {e}")
        return None

def check_service_account_key():
    """Check the service account key file"""
    try:
        # Get service account key path
        service_account_key_path = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
        print(f"Service account key path: {service_account_key_path}")
        
        # Check if file exists
        if not os.path.exists(service_account_key_path):
            print(f"ERROR: Service account key file not found at {service_account_key_path}")
            return None
        
        # Read the file
        with open(service_account_key_path, 'r') as f:
            service_account_data = json.load(f)
            print(f"Service account key file loaded successfully")
            project_id = service_account_data.get('project_id')
            print(f"Project ID from service account key: {project_id}")
            return project_id
    except Exception as e:
        print(f"ERROR: Failed to check service account key: {e}")
        return None

def check_token_project():
    """Check the project ID in the token"""
    try:
        # Read token from file
        with open("firebase_id_token.txt", "r") as f:
            token = f.read().strip()
            print(f"Read token from file: {token[:20]}...{token[-20:]}")
        
        # Decode token
        payload = decode_token_without_verification(token)
        if not payload:
            return None
        
        # Extract project information
        issuer = payload.get('iss')
        audience = payload.get('aud')
        
        print(f"Token issuer: {issuer}")
        print(f"Token audience: {audience}")
        
        # Extract project ID from issuer or audience
        project_id = None
        if issuer and 'securetoken.google.com/' in issuer:
            project_id = issuer.split('securetoken.google.com/')[1]
        elif audience:
            # Audience is usually the project ID
            project_id = audience
        
        print(f"Project ID from token: {project_id}")
        return project_id
    except Exception as e:
        print(f"ERROR: Failed to check token project: {e}")
        return None

def main():
    print("Firebase Project Checker")
    print("=======================")
    
    # Check service account key
    print("\nChecking service account key...")
    service_account_project_id = check_service_account_key()
    
    # Check token
    print("\nChecking token...")
    token_project_id = check_token_project()
    
    # Compare project IDs
    print("\nResults:")
    if service_account_project_id and token_project_id:
        if service_account_project_id == token_project_id:
            print("✅ SUCCESS: Service account key and token belong to the same project!")
            print(f"Project ID: {service_account_project_id}")
        else:
            print("❌ ERROR: Service account key and token belong to different projects!")
            print(f"Service account project ID: {service_account_project_id}")
            print(f"Token project ID: {token_project_id}")
            print("\nYou need to:")
            print("1. Download a new service account key from the correct Firebase project")
            print("2. OR get a new token from the correct Firebase project")
    else:
        print("❌ ERROR: Could not determine project IDs")
        if not service_account_project_id:
            print("- Service account key could not be read or is invalid")
        if not token_project_id:
            print("- Token could not be decoded or is invalid")

if __name__ == "__main__":
    main()
