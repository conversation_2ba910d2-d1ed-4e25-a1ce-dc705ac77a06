# Onboarding Wizard Design

This document outlines the design for the AssetKPI onboarding wizard, which will guide new users through the initial setup process.

## Goals

- Provide a smooth introduction to AssetKPI for new users
- Guide users through essential setup steps
- Reduce time-to-value for new users
- Increase user engagement and retention
- Collect necessary information for personalized experience

## User Flow

The onboarding wizard will consist of the following steps:

1. **Welcome Screen**
   - Brief introduction to AssetKPI
   - Overview of the onboarding process
   - Option to skip onboarding (for experienced users)

2. **Profile Setup**
   - User name and role confirmation
   - Department/team information
   - Preferences (e.g., notification settings, theme)

3. **Organization Setup**
   - Organization name
   - Industry selection
   - Number of assets (approximate)
   - Number of users (approximate)

4. **Feature Selection**
   - Select key features to focus on initially
   - Options: Asset Management, Inventory Management, Work Orders, KPI Tracking
   - Explanation of each feature's benefits

5. **Data Import Options**
   - Options for importing existing data
   - Supported file formats
   - Sample templates
   - Skip option (start from scratch)

6. **Dashboard Customization**
   - Select default dashboard widgets
   - Arrange dashboard layout
   - Set default filters

7. **Completion**
   - Summary of selections
   - Next steps
   - Option to view tutorial or go directly to dashboard

## UI Components

### Progress Indicator
- Horizontal progress bar showing all steps
- Current step highlighted
- Completed steps marked with checkmark

### Navigation Controls
- "Next" button to proceed to next step
- "Back" button to return to previous step
- "Skip" button to skip current step (where applicable)
- "Exit" button to exit wizard entirely

### Content Area
- Clear heading for each step
- Concise instructions
- Form fields or selection options
- Help text and tooltips

## Persistence

The wizard will save progress at each step, allowing users to:
- Continue from where they left off if they close the browser
- Skip completed steps when returning
- Edit previous selections

## Technical Implementation

### State Management
- Store wizard state in browser localStorage
- Save completed steps to user profile in database
- Track wizard completion status in user profile

### Routing
- Use dedicated routes for each wizard step (e.g., `/onboarding/step1`)
- Redirect to appropriate step based on completion status
- Prevent access to wizard after completion

### API Endpoints
- `POST /api/users/onboarding/progress` - Update onboarding progress
- `GET /api/users/onboarding/status` - Get current onboarding status
- `POST /api/users/onboarding/complete` - Mark onboarding as complete

## Mockups

(Mockups will be created separately in a design tool)

## User Testing Plan

1. Create prototype of onboarding wizard
2. Conduct usability testing with 5-7 users
3. Collect feedback on:
   - Clarity of instructions
   - Ease of navigation
   - Time to complete
   - Overall satisfaction
4. Iterate based on feedback
5. Conduct final validation testing

## Success Metrics

- Completion rate: % of users who complete the wizard
- Time to complete: Average time to complete the wizard
- Feature adoption: % of users who use features introduced in the wizard
- User satisfaction: Survey results after completing the wizard
