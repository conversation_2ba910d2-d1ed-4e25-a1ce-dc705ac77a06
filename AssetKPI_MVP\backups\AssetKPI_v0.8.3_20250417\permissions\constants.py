"""
Permission constants for the AssetKPI application.
This module contains constants used by both the permissions module and the database migration script.
"""
from enum import Enum

# Permission scopes
class PermissionScope(str, Enum):
    # Asset permissions
    ASSETS_READ = "assets:read"
    ASSETS_WRITE = "assets:write"
    
    # Inventory permissions
    INVENTORY_READ = "inventory:read"
    INVENTORY_WRITE = "inventory:write"
    INVENTORY_OPTIMIZE = "inventory:optimize"
    
    # Work order permissions
    WORKORDERS_READ = "workorders:read"
    WORKORDERS_WRITE = "workorders:write"
    
    # KPI permissions
    KPI_READ = "kpi:read"
    KPI_WRITE = "kpi:write"
    
    # User permissions
    USERS_READ = "users:read"
    USERS_WRITE = "users:write"
    
    # API permissions
    API_READ = "api:read"
    API_WRITE = "api:write"
    
    # Wildcard permissions
    WILDCARD = "*:*"

# Default permission sets for each role
DEFAULT_VIEWER_PERMISSIONS = {
    PermissionScope.ASSETS_READ,
    PermissionScope.INVENTORY_READ,
    PermissionScope.WORKORDERS_READ,
    PermissionScope.KPI_READ,
}

DEFAULT_ENGINEER_PERMISSIONS = {
    *DEFAULT_VIEWER_PERMISSIONS,
    PermissionScope.WORKORDERS_WRITE,
}

DEFAULT_MANAGER_PERMISSIONS = {
    *DEFAULT_ENGINEER_PERMISSIONS,
    PermissionScope.ASSETS_WRITE,
    PermissionScope.INVENTORY_WRITE,
    PermissionScope.INVENTORY_OPTIMIZE,
    PermissionScope.KPI_WRITE,
}

DEFAULT_ADMIN_PERMISSIONS = {
    *DEFAULT_MANAGER_PERMISSIONS,
    PermissionScope.USERS_READ,
    PermissionScope.USERS_WRITE,
    PermissionScope.API_READ,
    PermissionScope.API_WRITE,
}
