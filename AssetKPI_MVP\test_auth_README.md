# Test User Authentication for AssetKPI

This module provides a way to authenticate test users without requiring Firebase authentication. This is useful for testing and development purposes.

## Overview

The `test_user_auth.py` module provides functions to handle test user tokens and authenticate test users. It maps test tokens to user IDs in the database.

## Test Users

The following test users are available:

| Role | Token | User ID |
|------|-------|---------|
| Admin | `test-admin-token` | `test-admin-uid` |
| Manager | `test-manager-token` | `test-manager-uid` |
| Engineer | `test-engineer-token` | `test-engineer-uid` |
| Viewer | `test-viewer-token` | `test-viewer-uid` |

## Usage

### API Endpoint

You can use the `/api/test-user-auth` endpoint to test authentication with a test token:

```
GET /api/test-user-auth?token=test-admin-token
```

This will return a JSON response with the user's information if the token is valid:

```json
{
  "message": "Test user authentication successful",
  "user": "<EMAIL>",
  "role": "ADMIN",
  "user_id": "test-admin-uid"
}
```

If the token is invalid, it will return an error message:

```json
{
  "message": "Test user authentication failed",
  "error": "Invalid test user token or user not found"
}
```

### Programmatic Usage

You can use the `handle_test_user_token` function to authenticate a test user:

```python
from test_user_auth import handle_test_user_token

# Authenticate a test user
user = handle_test_user_token('test-admin-token', db)

# Check if a token is a test token
is_test = is_test_user_token('test-admin-token')  # Returns True
```

## Implementation Details

The module uses a mapping of test tokens to user IDs to authenticate test users. It queries the database to find the user with the corresponding user ID.

If the token is valid but the user is not found in the database, it will raise a 404 error. If the token is invalid, it will raise a 401 error.

## Security Considerations

This module is intended for testing and development purposes only. It should not be used in production environments.

The test tokens are hardcoded in the module and are not secure. They should not be used to authenticate real users.

## Frontend Implementation

The frontend implementation consists of two JavaScript files:

- `static/js/test_auth.js` - Handles test user authentication.
- `static/js/combined_auth.js` - Provides a unified interface for both Firebase and test user authentication.

The login page has been updated to include a "Test Auth" tab where users can enter a test token.

### Using the Test Auth UI

1. Go to the login page at `/login`.
2. Click on the "Test Auth" tab.
3. Enter one of the test tokens (e.g., `test-admin-token`).
4. Click "Sign In with Test Token".

## Future Improvements

- Add support for custom test tokens
- Add support for test user creation
- Add support for test user deletion
- Add support for test user role changes
