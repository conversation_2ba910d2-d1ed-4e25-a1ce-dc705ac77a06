    // Render Availability trend chart
    renderAvailabilityChart: function() {
        const chartId = 'availabilityTrendChart';
        const ctx = document.getElementById(chartId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
        }

        // Check if we have data
        if (!this.kpiData.availability.history || this.kpiData.availability.history.length === 0) {
            const noDataMsg = ctx.parentElement.querySelector('.no-data-msg');
            if (noDataMsg) {
                noDataMsg.style.display = 'block';
            }
            return;
        }

        // Prepare data
        const labels = this.kpiData.availability.history.map(item =>
            new Date(item.timestamp).toLocaleDateString());
        const data = this.kpiData.availability.history.map(item => item.value);

        // Create chart configuration
        const config = {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Availability (%)',
                    data: data,
                    borderColor: 'rgb(0, 123, 255)',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: AssetKPICharts.getChartOptions({
                title: 'Asset Availability Trend',
                yAxisTitle: 'Percentage (%)',
                suggestedMin: 0,
                suggestedMax: 100
            })
        };

        // Create chart
        this.charts[chartId] = new Chart(ctx, config);
    },
