# User Management API Endpoints

This document provides detailed information about the user management-related API endpoints in the AssetKPI system.

## User Information

### Get Current User

Retrieves information about the currently authenticated user.

**Endpoint:** `GET /api/users/me`

**Authentication Required:** Yes

**Permissions Required:** Any authenticated user

**Response:**

```json
{
  "uid": "firebase-test-admin-uid",
  "email": "johan<PERSON><PERSON>@gmail.com",
  "role": "ADMIN",
  "full_name": "<PERSON>",
  "created_at": "2023-03-15T10:30:00Z"
}
```

### Get All Users

Retrieves a list of all users.

**Endpoint:** `GET /api/users`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**Response:**

```json
[
  {
    "uid": "firebase-test-admin-uid",
    "email": "johan<PERSON><PERSON>@gmail.com",
    "role": "ADMIN",
    "full_name": "<PERSON>",
    "created_at": "2023-03-15T10:30:00Z"
  },
  {
    "uid": "firebase-test-manager-uid",
    "email": "<EMAIL>",
    "role": "MANA<PERSON>R",
    "full_name": "Manager User",
    "created_at": "2023-03-16T11:45:00Z"
  },
  ...
]
```

### Get User by ID

Retrieves information about a specific user.

**Endpoint:** `GET /api/users/{user_id}`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | string | Yes | Firebase UID of the user to retrieve |

**Response:**

```json
{
  "uid": "firebase-test-manager-uid",
  "email": "<EMAIL>",
  "role": "MANAGER",
  "full_name": "Manager User",
  "created_at": "2023-03-16T11:45:00Z"
}
```

## User Management

### Create User

Creates a new user in the system.

**Endpoint:** `POST /api/users`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "role": "ENGINEER",
  "full_name": "New User"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "User created successfully",
  "uid": "new-firebase-uid"
}
```

### Update User

Updates an existing user.

**Endpoint:** `PUT /api/users/{user_id}`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | string | Yes | Firebase UID of the user to update |

**Request Body:**

```json
{
  "role": "MANAGER",
  "full_name": "Updated User Name"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "User updated successfully"
}
```

### Delete User

Deletes a user from the system.

**Endpoint:** `DELETE /api/users/{user_id}`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | string | Yes | Firebase UID of the user to delete |

**Response:**

```json
{
  "status": "success",
  "message": "User deleted successfully"
}
```

## User Roles

### Get Available Roles

Retrieves a list of available user roles.

**Endpoint:** `GET /api/users/roles`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**Response:**

```json
[
  {
    "role": "VIEWER",
    "description": "Can view data but cannot make changes"
  },
  {
    "role": "ENGINEER",
    "description": "Can view data and create/update work orders"
  },
  {
    "role": "MANAGER",
    "description": "Can view data, create/update work orders, and run optimization jobs"
  },
  {
    "role": "ADMIN",
    "description": "Full access to all system features"
  }
]
```

### Update User Role

Updates the role of an existing user.

**Endpoint:** `PUT /api/users/{user_id}/role`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | string | Yes | Firebase UID of the user to update |

**Request Body:**

```json
{
  "role": "MANAGER"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "User role updated successfully"
}
```

## Authentication

### Login

Authenticates a user and returns a Firebase ID token.

**Endpoint:** `POST /api/auth/login`

**Authentication Required:** No

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "userPassword123"
}
```

**Response:**

```json
{
  "status": "success",
  "token": "firebase-id-token",
  "user": {
    "uid": "firebase-user-uid",
    "email": "<EMAIL>",
    "role": "ENGINEER",
    "full_name": "Example User"
  }
}
```

### Refresh Token

Refreshes an expired Firebase ID token.

**Endpoint:** `POST /api/auth/refresh`

**Authentication Required:** Yes (using refresh token)

**Request Body:**

```json
{
  "refresh_token": "firebase-refresh-token"
}
```

**Response:**

```json
{
  "status": "success",
  "token": "new-firebase-id-token"
}
```

### Logout

Invalidates the current Firebase ID token.

**Endpoint:** `POST /api/auth/logout`

**Authentication Required:** Yes

**Response:**

```json
{
  "status": "success",
  "message": "Logged out successfully"
}
```

## API Keys

### Get API Keys

Retrieves a list of API keys for the current user.

**Endpoint:** `GET /api/users/api-keys`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**Response:**

```json
[
  {
    "key_id": "key1",
    "name": "Production API Key",
    "prefix": "c5e52b",
    "created_at": "2023-03-15T10:30:00Z",
    "last_used": "2023-04-15T14:25:00Z",
    "permissions": ["READ", "WRITE"]
  },
  {
    "key_id": "key2",
    "name": "Read-Only API Key",
    "prefix": "a7f91c",
    "created_at": "2023-03-20T15:45:00Z",
    "last_used": "2023-04-14T09:10:00Z",
    "permissions": ["READ"]
  }
]
```

### Create API Key

Creates a new API key.

**Endpoint:** `POST /api/users/api-keys`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**Request Body:**

```json
{
  "name": "New API Key",
  "permissions": ["READ", "WRITE"]
}
```

**Response:**

```json
{
  "status": "success",
  "message": "API key created successfully",
  "key": "c5e52be8-9b1c-4fcd-8457-741c91ef5c85",
  "key_id": "key3",
  "name": "New API Key",
  "prefix": "c5e52b",
  "created_at": "2023-04-16T12:30:00Z",
  "permissions": ["READ", "WRITE"]
}
```

### Delete API Key

Deletes an API key.

**Endpoint:** `DELETE /api/users/api-keys/{key_id}`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| key_id | string | Yes | ID of the API key to delete |

**Response:**

```json
{
  "status": "success",
  "message": "API key deleted successfully"
}
```
