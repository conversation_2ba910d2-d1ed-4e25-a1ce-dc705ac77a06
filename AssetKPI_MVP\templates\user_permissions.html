{% extends "layout.html" %}

{% block title %}User Permissions - AssetKPI{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">User Permissions Management</h4>
                    <p class="card-category">Manage API permissions for users</p>
                </div>
                <div class="card-body">
                    {% if current_user and current_user.role == "ADMIN" %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="userSelect">Select User</label>
                                <select class="form-control" id="userSelect">
                                    <option value="">Select a user...</option>
                                    {% for user in users %}
                                    <option value="{{ user.user_id }}">{{ user.email }} ({{ user.role }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="apiAccessToggle">API Access</label>
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="apiAccessToggle" disabled>
                                    <label class="custom-control-label" for="apiAccessToggle">Enable API Access</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <h5>Permission Scopes</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Category</th>
                                            <th>Permission</th>
                                            <th>Description</th>
                                            <th>Enabled</th>
                                        </tr>
                                    </thead>
                                    <tbody id="permissionsTable">
                                        <!-- Permissions will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <button id="savePermissionsBtn" class="btn btn-primary" disabled>Save Changes</button>
                            <button id="resetPermissionsBtn" class="btn btn-secondary ml-2" disabled>Reset to Default</button>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div id="apiKeySection" style="display: none;">
                                <h5>API Key Management</h5>
                                <div class="form-group">
                                    <label for="apiKeyDisplay">API Key</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="apiKeyDisplay" readonly>
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" id="copyApiKeyBtn">Copy</button>
                                        </div>
                                    </div>
                                </div>
                                <button id="generateApiKeyBtn" class="btn btn-warning">Generate New API Key</button>
                                <button id="revokeApiKeyBtn" class="btn btn-danger ml-2">Revoke API Key</button>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <p>You need administrator privileges to manage user permissions.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    // Auth helper object
    const AssetKPIAuth = {
        getToken: function() {
            return localStorage.getItem('assetKpiToken') || '';
        },
        isAuthenticated: function() {
            return !!this.getToken();
        }
    };
    // Permission categories and descriptions
    const permissionDescriptions = {
        'assets:read': 'View asset information',
        'assets:write': 'Create, update, and delete assets',
        'inventory:read': 'View inventory information',
        'inventory:write': 'Create, update, and delete inventory items',
        'inventory:optimize': 'Run inventory optimization jobs',
        'workorders:read': 'View work order information',
        'workorders:write': 'Create, update, and delete work orders',
        'kpi:read': 'View KPI information',
        'kpi:write': 'Create and update KPI calculations',
        'users:read': 'View user information',
        'users:write': 'Create, update, and delete users',
        'api:read': 'View API information',
        'api:write': 'Manage API settings'
    };

    // Group permissions by category
    const permissionCategories = {
        'Assets': ['assets:read', 'assets:write'],
        'Inventory': ['inventory:read', 'inventory:write', 'inventory:optimize'],
        'Work Orders': ['workorders:read', 'workorders:write'],
        'KPIs': ['kpi:read', 'kpi:write'],
        'Users': ['users:read', 'users:write'],
        'API': ['api:read', 'api:write']
    };

    // Default permissions by role
    const defaultPermissions = {
        'VIEWER': ['assets:read', 'inventory:read', 'workorders:read', 'kpi:read'],
        'ENGINEER': ['assets:read', 'inventory:read', 'workorders:read', 'workorders:write', 'kpi:read'],
        'MANAGER': ['assets:read', 'assets:write', 'inventory:read', 'inventory:write', 'inventory:optimize', 'workorders:read', 'workorders:write', 'kpi:read', 'kpi:write'],
        'ADMIN': ['assets:read', 'assets:write', 'inventory:read', 'inventory:write', 'inventory:optimize', 'workorders:read', 'workorders:write', 'kpi:read', 'kpi:write', 'users:read', 'users:write', 'api:read', 'api:write']
    };

    // Current user data
    let currentUserId = '';
    let currentUserRole = '';
    let currentUserPermissions = [];
    let currentApiKeyId = '';
    let hasUnsavedChanges = false;

    // Initialize the page
    $(document).ready(function() {
        // User selection change
        $('#userSelect').change(function() {
            const userId = $(this).val();
            if (userId) {
                loadUserPermissions(userId);
            } else {
                resetForm();
            }
        });

        // API access toggle
        $('#apiAccessToggle').change(function() {
            const isEnabled = $(this).prop('checked');
            updateApiKeySection(isEnabled);
            hasUnsavedChanges = true;
            updateButtons();
        });

        // Save permissions button
        $('#savePermissionsBtn').click(function() {
            saveUserPermissions();
        });

        // Reset permissions button
        $('#resetPermissionsBtn').click(function() {
            resetToDefaultPermissions();
        });

        // Generate API key button
        $('#generateApiKeyBtn').click(function() {
            generateApiKey();
        });

        // Revoke API key button
        $('#revokeApiKeyBtn').click(function() {
            revokeApiKey();
        });

        // Copy API key button
        $('#copyApiKeyBtn').click(function() {
            copyApiKey();
        });

        // Permission checkbox change
        $(document).on('change', '.permission-checkbox', function() {
            hasUnsavedChanges = true;
            updateButtons();
        });
    });

    // Load user permissions
    function loadUserPermissions(userId) {
        // Reset form
        resetForm();

        // Set current user ID
        currentUserId = userId;

        // Get user data from the selected option
        const userOption = $('#userSelect option:selected');
        const userEmail = userOption.text().split(' (')[0];
        currentUserRole = userOption.text().split('(')[1].replace(')', '');

        // Enable form controls
        $('#apiAccessToggle').prop('disabled', false);

        // Fetch user permissions from API
        fetch(`/api/users/${userId}/permissions`, {
            headers: {
                'Authorization': `Bearer ${AssetKPIAuth.getToken()}`
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Set current user permissions
                currentUserPermissions = data.permissions || [];

                // Set API access toggle
                $('#apiAccessToggle').prop('checked', data.api_access_enabled);
                updateApiKeySection(data.api_access_enabled);

                // Set API key if available
                if (data.api_key_id) {
                    currentApiKeyId = data.api_key_id;
                    $('#apiKeyDisplay').val(data.api_key || '********');
                }

                // Populate permissions table
                populatePermissionsTable();
            })
            .catch(error => {
                console.error('Error loading user permissions:', error);
                alert('Error loading user permissions. Please try again.');
            });
    }

    // Populate permissions table
    function populatePermissionsTable() {
        const tableBody = $('#permissionsTable');
        tableBody.empty();

        // Add rows for each permission category
        Object.keys(permissionCategories).forEach(category => {
            const permissions = permissionCategories[category];

            permissions.forEach((permission, index) => {
                const isChecked = currentUserPermissions.includes(permission);
                const description = permissionDescriptions[permission] || '';

                const row = `
                    <tr>
                        ${index === 0 ? `<td rowspan="${permissions.length}">${category}</td>` : ''}
                        <td>${permission}</td>
                        <td>${description}</td>
                        <td>
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input permission-checkbox"
                                    id="perm_${permission.replace(':', '_')}"
                                    data-permission="${permission}"
                                    ${isChecked ? 'checked' : ''}>
                                <label class="custom-control-label" for="perm_${permission.replace(':', '_')}"></label>
                            </div>
                        </td>
                    </tr>
                `;

                tableBody.append(row);
            });
        });
    }

    // Update API key section visibility
    function updateApiKeySection(isEnabled) {
        if (isEnabled) {
            $('#apiKeySection').show();
        } else {
            $('#apiKeySection').hide();
        }
    }

    // Update buttons state
    function updateButtons() {
        $('#savePermissionsBtn').prop('disabled', !hasUnsavedChanges);
        $('#resetPermissionsBtn').prop('disabled', !currentUserId);
    }

    // Reset form
    function resetForm() {
        currentUserId = '';
        currentUserRole = '';
        currentUserPermissions = [];
        currentApiKeyId = '';
        hasUnsavedChanges = false;

        $('#apiAccessToggle').prop('disabled', true).prop('checked', false);
        $('#apiKeyDisplay').val('');
        $('#apiKeySection').hide();
        $('#permissionsTable').empty();

        updateButtons();
    }

    // Reset to default permissions
    function resetToDefaultPermissions() {
        if (!currentUserRole || !defaultPermissions[currentUserRole]) {
            return;
        }

        // Set default permissions for the current role
        currentUserPermissions = [...defaultPermissions[currentUserRole]];

        // Update checkboxes
        $('.permission-checkbox').each(function() {
            const permission = $(this).data('permission');
            $(this).prop('checked', currentUserPermissions.includes(permission));
        });

        hasUnsavedChanges = true;
        updateButtons();
    }

    // Save user permissions
    function saveUserPermissions() {
        if (!currentUserId) {
            return;
        }

        // Get selected permissions
        const selectedPermissions = [];
        $('.permission-checkbox:checked').each(function() {
            selectedPermissions.push($(this).data('permission'));
        });

        // Get API access status
        const apiAccessEnabled = $('#apiAccessToggle').prop('checked');

        // Prepare data
        const data = {
            permissions: selectedPermissions,
            api_access_enabled: apiAccessEnabled
        };

        // Save permissions
        fetch(`/api/users/${currentUserId}/permissions`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${AssetKPIAuth.getToken()}`
            },
            body: JSON.stringify(data)
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to save permissions');
                }
                return response.json();
            })
            .then(data => {
                alert('Permissions saved successfully');
                hasUnsavedChanges = false;
                updateButtons();

                // Reload user permissions
                loadUserPermissions(currentUserId);
            })
            .catch(error => {
                console.error('Error saving permissions:', error);
                alert('Error saving permissions. Please try again.');
            });
    }

    // Generate API key
    function generateApiKey() {
        if (!currentUserId) {
            return;
        }

        if (currentApiKeyId && !confirm('This will invalidate the existing API key. Are you sure?')) {
            return;
        }

        fetch(`/api/users/${currentUserId}/api-key`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${AssetKPIAuth.getToken()}`
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to generate API key');
                }
                return response.json();
            })
            .then(data => {
                currentApiKeyId = data.api_key_id;
                $('#apiKeyDisplay').val(data.api_key);
                alert('API key generated successfully');
            })
            .catch(error => {
                console.error('Error generating API key:', error);
                alert('Error generating API key. Please try again.');
            });
    }

    // Revoke API key
    function revokeApiKey() {
        if (!currentUserId || !currentApiKeyId) {
            return;
        }

        if (!confirm('This will revoke the API key. Are you sure?')) {
            return;
        }

        fetch(`/api/users/${currentUserId}/api-key`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${AssetKPIAuth.getToken()}`
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to revoke API key');
                }
                return response.json();
            })
            .then(data => {
                currentApiKeyId = '';
                $('#apiKeyDisplay').val('');
                alert('API key revoked successfully');
            })
            .catch(error => {
                console.error('Error revoking API key:', error);
                alert('Error revoking API key. Please try again.');
            });
    }

    // Copy API key to clipboard
    function copyApiKey() {
        const apiKeyInput = document.getElementById('apiKeyDisplay');
        const apiKey = apiKeyInput.value;

        // Use modern Clipboard API if available, fallback to older method
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(apiKey)
                .then(() => {
                    alert('API key copied to clipboard');
                })
                .catch(err => {
                    console.error('Failed to copy API key: ', err);
                    // Fallback to older method
                    apiKeyInput.select();
                    document.execCommand('copy');
                    alert('API key copied to clipboard');
                });
        } else {
            // Fallback for browsers that don't support Clipboard API
            apiKeyInput.select();
            document.execCommand('copy');
            alert('API key copied to clipboard');
        }
    }
</script>
{% endblock %}
