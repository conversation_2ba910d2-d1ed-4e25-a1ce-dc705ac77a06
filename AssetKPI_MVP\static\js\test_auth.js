/**
 * Test User Authentication Utility for AssetKPI
 *
 * This module provides functions for handling test user authentication
 * during development and testing without requiring Firebase.
 */

// Test token map - must match the server-side tokens
const TEST_TOKENS = {
    'test-admin-token': 'admin',
    'test-manager-token': 'manager',
    'test-engineer-token': 'engineer',
    'test-viewer-token': 'viewer',
    'johan-token': 'admin'  // <PERSON>'s token
};

// Create the TestAuth object
const TestAuth = {
    /**
     * Check if a token is a test token
     * @param {string} token - The token to check
     * @returns {boolean} - Whether the token is a test token
     */
    isTestToken: function(token) {
        return token in TEST_TOKENS;
    },

    /**
     * Get the role for a test token
     * @param {string} token - The test token
     * @returns {string|null} - The role for the token, or null if not a test token
     */
    getRoleForToken: function(token) {
        return TEST_TOKENS[token] || null;
    },

    /**
     * Authenticate with a test token
     * @param {string} token - The test token
     * @returns {Promise<Object>} - Authentication result
     */
    authenticateWithTestToken: async function(token) {
        try {
            // Check if it's a valid test token
            if (!this.isTestToken(token)) {
                return {
                    success: false,
                    error: 'Invalid test token'
                };
            }

            // Call the test user auth endpoint
            const response = await fetch(`/api/test-user-auth?token=${token}`);
            const data = await response.json();

            if (response.ok) {
                // Store the token
                localStorage.setItem('testUserToken', token);
                document.cookie = `testUserToken=${token}; path=/; max-age=3600; SameSite=Strict`;

                return {
                    success: true,
                    user: {
                        email: data.user,
                        role: data.role,
                        uid: data.user_id
                    },
                    token: token
                };
            } else {
                return {
                    success: false,
                    error: data.error || 'Authentication failed'
                };
            }
        } catch (error) {
            console.error('Test authentication error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    },

    /**
     * Sign out the test user
     * @returns {Promise<Object>} - Sign out result
     */
    signOut: async function() {
        try {
            // Remove the test token from storage
            localStorage.removeItem('testUserToken');
            document.cookie = 'testUserToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';

            return {
                success: true
            };
        } catch (error) {
            console.error('Test sign out error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    },

    /**
     * Get the current test token
     * @returns {string|null} - The current test token, or null if not authenticated
     */
    getCurrentToken: function() {
        return localStorage.getItem('testUserToken');
    },

    /**
     * Check if authenticated with a test token
     * @returns {boolean} - Whether authenticated with a test token
     */
    isAuthenticated: function() {
        return !!this.getCurrentToken();
    },

    /**
     * Make an authenticated API request using the test token
     * @param {string} url - The API endpoint URL
     * @param {Object} options - Fetch options
     * @returns {Promise<Response>} - Fetch response
     */
    authenticatedFetch: async function(url, options = {}) {
        const token = this.getCurrentToken();
        if (!token) {
            throw new Error('Not authenticated with a test token');
        }

        // Add the Authorization header
        const headers = {
            ...options.headers,
            'Authorization': `Bearer ${token}`
        };

        // Make the request
        return fetch(url, {
            ...options,
            headers
        });
    }
};

// Export the TestAuth object
window.TestAuth = TestAuth;
console.log('TestAuth object created and attached to window object');
