# API Reference

Welcome to the AssetKPI API Reference. This reference provides detailed information about the AssetKPI API endpoints, including request and response formats, authentication, and error handling.

## Table of Contents

- [Authentication](./authentication.md)
- [Webhooks API](./webhooks.md)
- [ERP Integration API](./erp.md)
- [Assets API](./assets.md)
- [Inventory API](./inventory.md)
- [KPI API](./kpi.md)
- [Work Orders API](./workorders.md)
- [Users API](./users.md)
- [Analytics API](./analytics.md)
- [Recommendations API](./recommendations.md)
- [SDK Reference](./sdk.md)
- [Error Codes](./error-codes.md)

## Authentication

Learn about authentication methods for the AssetKPI API, including API keys and Firebase authentication.

[Read more](./authentication.md)

## Webhooks API

Detailed reference for the webhooks API, including endpoints for managing webhook subscriptions and events.

[Read more](./webhooks.md)

## ERP Integration API

Detailed reference for the ERP integration API, including endpoints for managing ERP connections, data mappings, and synchronization.

[Read more](./erp.md)

## Assets API

Detailed reference for the assets API, including endpoints for managing assets.

[Read more](./assets.md)

## Inventory API

Detailed reference for the inventory API, including endpoints for managing spare parts and inventory optimization.

[Read more](./inventory.md)

## KPI API

Detailed reference for the KPI API, including endpoints for retrieving KPI data and analytics.

[Read more](./kpi.md)

## Work Orders API

Detailed reference for the work orders API, including endpoints for managing work orders.

[Read more](./workorders.md)

## Users API

Detailed reference for the users API, including endpoints for managing users and permissions.

[Read more](./users.md)

## Analytics API

Detailed reference for the analytics API, including endpoints for retrieving usage analytics data.

[Read more](./analytics.md)

## Recommendations API

Detailed reference for the recommendations API, including endpoints for retrieving and managing recommendations.

[Read more](./recommendations.md)

## SDK Reference

Detailed reference for the AssetKPI Python SDK, including client classes and methods.

[Read more](./sdk.md)

## Error Codes

Detailed reference for AssetKPI API error codes and error handling.

[Read more](./error-codes.md)
