{% extends "layout.html" %}

{% block title %}Asset PM Schedules | AssetKPI{% endblock %}

{% block styles %}
<style>
    .asset-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
    .status-badge {
        font-size: 1rem;
        padding: 5px 10px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    .status-maintenance {
        background-color: #ffc107;
    }
    .pm-card {
        transition: transform 0.3s;
    }
    .pm-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    .task-list {
        list-style-type: none;
        padding-left: 0;
    }
    .task-list li {
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
    }
    .task-list li:last-child {
        border-bottom: none;
    }
    .task-number {
        display: inline-block;
        width: 25px;
        height: 25px;
        line-height: 25px;
        text-align: center;
        background-color: #0d6efd;
        color: white;
        border-radius: 50%;
        margin-right: 10px;
    }
    .calendar-view {
        background-color: white;
        border-radius: 5px;
        padding: 15px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 5px;
    }
    .calendar-day {
        aspect-ratio: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 5px;
    }
    .calendar-day-header {
        font-weight: bold;
        text-align: center;
        padding: 5px;
    }
    .calendar-day.has-pm {
        background-color: #e6f7ff;
        border-color: #0d6efd;
    }
    .calendar-day.today {
        background-color: #f8f9fa;
        border-color: #0d6efd;
        border-width: 2px;
    }
    .calendar-day-number {
        font-weight: bold;
    }
    .calendar-day-content {
        font-size: 0.8rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/assets">Assets</a></li>
                <li class="breadcrumb-item"><a href="/assets/{{ asset.assetid }}">{{ asset.assetname }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">PM Schedules</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Asset Header -->
<div class="asset-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>{{ asset.assetname }} - PM Schedules</h1>
            <p class="text-muted">{{ asset.assettype }} | {{ asset.manufacturer }} {{ asset.model }}</p>
            <div class="d-flex align-items-center mt-2">
                <span class="badge status-{{ asset.status|lower if asset.status else 'inactive' }} status-badge me-2">{{ asset.status }}</span>
                <span class="text-muted">Serial: {{ asset.serialnumber }}</span>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <i class="fas fa-plus"></i> Create PM Schedule
            </button>
        </div>
    </div>
</div>

<!-- Asset Navigation -->
<ul class="nav nav-pills mb-4">
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}">Overview</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/kpi">KPIs</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/maintenance">Maintenance</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/specifications">Specifications</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/documents">Documents</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/meters">Meters</a>
    </li>
    <li class="nav-item">
        <a class="nav-link active" href="/assets/{{ asset.assetid }}/pm">PM Schedules</a>
    </li>
</ul>

<!-- View Toggle -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="btn-group w-100" role="group" aria-label="View Toggle">
                    <input type="radio" class="btn-check" name="viewToggle" id="listView" autocomplete="off" checked>
                    <label class="btn btn-outline-primary" for="listView">List View</label>

                    <input type="radio" class="btn-check" name="viewToggle" id="calendarView" autocomplete="off">
                    <label class="btn btn-outline-primary" for="calendarView">Calendar View</label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- List View (Default) -->
<div id="pmListView">
    <div class="row">
        {% if pm_schedules %}
            {% for schedule in pm_schedules %}
                <div class="col-md-6 mb-4">
                    <div class="card pm-card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ schedule.schedule_name }}</h5>
                            <div class="form-check form-switch">
                                <input class="form-check-input auth-required-content" type="checkbox" id="enableSwitch{{ schedule.schedule_id }}" {% if schedule.enabled %}checked{% endif %} data-role="ENGINEER,MANAGER,ADMIN">
                                <label class="form-check-label" for="enableSwitch{{ schedule.schedule_id }}">
                                    {% if schedule.enabled %}Enabled{% else %}Disabled{% endif %}
                                </label>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>Frequency:</strong> {{ schedule.frequency_value }} {{ schedule.frequency_unit }}</p>
                                    <p><strong>Type:</strong> {{ schedule.frequency_type }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Last Completed:</strong> {{ schedule.last_completed_date.strftime('%Y-%m-%d') if schedule.last_completed_date else 'Never' }}</p>
                                    <p>
                                        <strong>Next Due:</strong> {{ schedule.next_due_date.strftime('%Y-%m-%d') if schedule.next_due_date else 'N/A' }}
                                        {% if schedule.next_due_date %}
                                            {% set days_until = (schedule.next_due_date - now).days %}
                                            {% if days_until < 0 %}
                                                <span class="badge bg-danger">Overdue by {{ days_until|abs }} days</span>
                                            {% elif days_until == 0 %}
                                                <span class="badge bg-warning">Due today</span>
                                            {% elif days_until < 7 %}
                                                <span class="badge bg-warning">Due in {{ days_until }} days</span>
                                            {% else %}
                                                <span class="badge bg-success">Due in {{ days_until }} days</span>
                                            {% endif %}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>

                            <!-- Job Plans -->
                            {% if job_plans[schedule.schedule_id] %}
                                <div class="accordion" id="planAccordion{{ schedule.schedule_id }}">
                                    {% for plan in job_plans[schedule.schedule_id] %}
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="planHeading{{ plan.plan_id }}">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#planCollapse{{ plan.plan_id }}" aria-expanded="false" aria-controls="planCollapse{{ plan.plan_id }}">
                                                    {{ plan.plan_name }} ({{ plan.estimated_duration }} min)
                                                </button>
                                            </h2>
                                            <div id="planCollapse{{ plan.plan_id }}" class="accordion-collapse collapse" aria-labelledby="planHeading{{ plan.plan_id }}" data-bs-parent="#planAccordion{{ schedule.schedule_id }}">
                                                <div class="accordion-body">
                                                    <p>{{ plan.description }}</p>

                                                    {% if plan.safety_instructions %}
                                                        <div class="alert alert-warning">
                                                            <strong>Safety Instructions:</strong> {{ plan.safety_instructions }}
                                                        </div>
                                                    {% endif %}

                                                    {% if job_tasks[plan.plan_id] %}
                                                        <h6>Tasks:</h6>
                                                        <ul class="task-list">
                                                            {% for task in job_tasks[plan.plan_id] %}
                                                                <li>
                                                                    <span class="task-number">{{ task.sequence_number }}</span>
                                                                    {{ task.task_description }}
                                                                    {% if task.estimated_hours %}
                                                                        <span class="text-muted">(Est. {{ task.estimated_hours }} hrs)</span>
                                                                    {% endif %}
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    {% else %}
                                                        <p class="text-muted">No tasks defined for this job plan.</p>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="text-muted">No job plans defined for this schedule.</p>
                            {% endif %}
                        </div>
                        <div class="card-footer d-flex justify-content-between">
                            <button class="btn btn-success auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                                <i class="fas fa-tools"></i> Generate Work Order
                            </button>
                            <div>
                                <button class="btn btn-primary auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-danger auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info">
                    No PM schedules found for this asset. Please create a PM schedule to ensure regular maintenance.
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Calendar View (Hidden by Default) -->
<div id="pmCalendarView" class="d-none">
    <div class="row mb-4">
        <div class="col-12">
            <div class="calendar-view">
                <div class="calendar-header">
                    <button class="btn btn-outline-secondary" id="prevMonth">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <h4 id="calendarTitle">June 2023</h4>
                    <button class="btn btn-outline-secondary" id="nextMonth">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <div class="calendar-grid">
                    <!-- Day headers -->
                    <div class="calendar-day-header">Sun</div>
                    <div class="calendar-day-header">Mon</div>
                    <div class="calendar-day-header">Tue</div>
                    <div class="calendar-day-header">Wed</div>
                    <div class="calendar-day-header">Thu</div>
                    <div class="calendar-day-header">Fri</div>
                    <div class="calendar-day-header">Sat</div>

                    <!-- Calendar days will be generated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- PM Schedule Legend -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>PM Schedule Legend</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if pm_schedules %}
                            {% for schedule in pm_schedules %}
                                <div class="col-md-4 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div style="width: 20px; height: 20px; background-color: {{ ['#0d6efd', '#dc3545', '#28a745', '#ffc107', '#6f42c1', '#fd7e14'][loop.index0 % 6] }}; border-radius: 3px; margin-right: 10px;"></div>
                                        <span>{{ schedule.schedule_name }}</span>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-12">
                                <p class="text-muted">No PM schedules to display.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create PM Schedule Modal -->
<div class="modal fade" id="createPMModal" tabindex="-1" aria-labelledby="createPMModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPMModalLabel">Create PM Schedule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createPMForm">
                    <div class="mb-3">
                        <label for="scheduleName" class="form-label">Schedule Name</label>
                        <input type="text" class="form-control" id="scheduleName" required>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="frequencyType" class="form-label">Frequency Type</label>
                            <select class="form-select" id="frequencyType" required>
                                <option value="">Select Type</option>
                                <option value="Calendar">Calendar</option>
                                <option value="Meter">Meter</option>
                                <option value="Condition">Condition</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="frequencyValue" class="form-label">Frequency Value</label>
                            <input type="number" class="form-control" id="frequencyValue" min="1" required>
                        </div>
                        <div class="col-md-4">
                            <label for="frequencyUnit" class="form-label">Frequency Unit</label>
                            <select class="form-select" id="frequencyUnit" required>
                                <option value="">Select Unit</option>
                                <option value="Days">Days</option>
                                <option value="Weeks">Weeks</option>
                                <option value="Months">Months</option>
                                <option value="Years">Years</option>
                                <option value="Hours">Hours</option>
                                <option value="Cycles">Cycles</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="nextDueDate" class="form-label">Next Due Date</label>
                        <input type="date" class="form-control" id="nextDueDate" required>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="enabledCheck" checked>
                        <label class="form-check-label" for="enabledCheck">Enabled</label>
                    </div>

                    <h5 class="mt-4">Job Plan</h5>
                    <div class="mb-3">
                        <label for="planName" class="form-label">Plan Name</label>
                        <input type="text" class="form-control" id="planName" required>
                    </div>
                    <div class="mb-3">
                        <label for="planDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="planDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="estimatedDuration" class="form-label">Estimated Duration (minutes)</label>
                        <input type="number" class="form-control" id="estimatedDuration" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="safetyInstructions" class="form-label">Safety Instructions</label>
                        <textarea class="form-control" id="safetyInstructions" rows="3"></textarea>
                    </div>

                    <h5 class="mt-4">Tasks</h5>
                    <div id="taskContainer">
                        <div class="task-item mb-3 p-3 border rounded">
                            <div class="row mb-2">
                                <div class="col-md-9">
                                    <label class="form-label">Task Description</label>
                                    <textarea class="form-control task-description" rows="2" required></textarea>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Sequence</label>
                                    <input type="number" class="form-control task-sequence" value="1" min="1" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">Estimated Hours</label>
                                    <input type="number" class="form-control task-hours" step="0.25" min="0">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Required Tools</label>
                                    <input type="text" class="form-control task-tools">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Required Parts</label>
                                    <input type="text" class="form-control task-parts">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mb-3">
                        <button type="button" class="btn btn-outline-primary" id="addTaskBtn">
                            <i class="fas fa-plus"></i> Add Task
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePMBtn">Save PM Schedule</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // View toggle functionality
        const listViewBtn = document.getElementById('listView');
        const calendarViewBtn = document.getElementById('calendarView');
        const pmListView = document.getElementById('pmListView');
        const pmCalendarView = document.getElementById('pmCalendarView');

        listViewBtn.addEventListener('change', function() {
            if (this.checked) {
                pmListView.classList.remove('d-none');
                pmCalendarView.classList.add('d-none');
            }
        });

        calendarViewBtn.addEventListener('change', function() {
            if (this.checked) {
                pmListView.classList.add('d-none');
                pmCalendarView.classList.remove('d-none');
                renderCalendar();
            }
        });

        // Calendar functionality
        let currentDate = new Date();
        const calendarTitle = document.getElementById('calendarTitle');
        const calendarGrid = document.querySelector('.calendar-grid');
        const prevMonthBtn = document.getElementById('prevMonth');
        const nextMonthBtn = document.getElementById('nextMonth');

        function renderCalendar() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();

            // Set calendar title
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
            calendarTitle.textContent = `${monthNames[month]} ${year}`;

            // Clear previous days
            const dayElements = document.querySelectorAll('.calendar-day');
            dayElements.forEach(day => day.remove());

            // Get first day of month and total days
            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();

            // Create empty cells for days before first day of month
            for (let i = 0; i < firstDay; i++) {
                const emptyDay = document.createElement('div');
                emptyDay.className = 'calendar-day empty';
                calendarGrid.appendChild(emptyDay);
            }

            // Create cells for each day of the month
            const today = new Date();
            for (let day = 1; day <= daysInMonth; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';

                // Check if this day is today
                if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
                    dayElement.classList.add('today');
                }

                // Check if this day has PM schedules
                const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

                {% if pm_schedules %}
                    {% for schedule in pm_schedules %}
                        {% if schedule.next_due_date %}
                            if ('{{ schedule.next_due_date.strftime("%Y-%m-%d") }}' === dateStr) {
                                dayElement.classList.add('has-pm');
                                dayElement.setAttribute('data-bs-toggle', 'tooltip');
                                dayElement.setAttribute('data-bs-placement', 'top');
                                dayElement.setAttribute('title', '{{ schedule.schedule_name }}');
                            }
                        {% endif %}
                    {% endfor %}
                {% endif %}

                const dayNumber = document.createElement('div');
                dayNumber.className = 'calendar-day-number';
                dayNumber.textContent = day;

                const dayContent = document.createElement('div');
                dayContent.className = 'calendar-day-content';

                dayElement.appendChild(dayNumber);
                dayElement.appendChild(dayContent);
                calendarGrid.appendChild(dayElement);
            }

            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        prevMonthBtn.addEventListener('click', function() {
            currentDate.setMonth(currentDate.getMonth() - 1);
            renderCalendar();
        });

        nextMonthBtn.addEventListener('click', function() {
            currentDate.setMonth(currentDate.getMonth() + 1);
            renderCalendar();
        });

        // Create PM Schedule Modal
        const createPMBtn = document.querySelector('.btn-primary[data-role="ENGINEER,MANAGER,ADMIN"]');
        const createPMModal = new bootstrap.Modal(document.getElementById('createPMModal'));

        if (createPMBtn && createPMBtn.innerHTML.includes('Create PM Schedule')) {
            createPMBtn.addEventListener('click', function() {
                createPMModal.show();
            });
        }

        // Add Task Button
        const addTaskBtn = document.getElementById('addTaskBtn');
        const taskContainer = document.getElementById('taskContainer');

        if (addTaskBtn) {
            addTaskBtn.addEventListener('click', function() {
                const taskItems = document.querySelectorAll('.task-item');
                const newTaskItem = taskItems[0].cloneNode(true);

                // Update sequence number
                const sequenceInput = newTaskItem.querySelector('.task-sequence');
                sequenceInput.value = taskItems.length + 1;

                // Clear other inputs
                newTaskItem.querySelectorAll('textarea, input').forEach(input => {
                    if (!input.classList.contains('task-sequence')) {
                        input.value = '';
                    }
                });

                taskContainer.appendChild(newTaskItem);
            });
        }

        // Save PM Schedule Button
        const savePMBtn = document.getElementById('savePMBtn');
        if (savePMBtn) {
            savePMBtn.addEventListener('click', function() {
                // Implement save PM schedule functionality here
                alert('Save PM schedule functionality would be implemented here');
                createPMModal.hide();
            });
        }

        // Enable/disable PM schedule toggle
        const enableSwitches = document.querySelectorAll('.form-check-input[id^="enableSwitch"]');
        enableSwitches.forEach(switchEl => {
            switchEl.addEventListener('change', function() {
                const label = this.nextElementSibling;
                if (this.checked) {
                    label.textContent = 'Enabled';
                } else {
                    label.textContent = 'Disabled';
                }

                // Implement enable/disable functionality here
                // alert(`PM Schedule ${this.checked ? 'enabled' : 'disabled'}`);
            });
        });
    });
</script>
{% endblock %}
