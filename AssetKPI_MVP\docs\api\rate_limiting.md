# Rate Limiting in AssetKPI API

This document provides detailed information about the rate limiting implementation in the AssetKPI API.

## Overview

Rate limiting is a technique used to control the amount of incoming and outgoing traffic to or from a network, application, or service. In the context of the AssetKPI API, rate limiting is used to:

1. Protect the API from abuse and DoS attacks
2. Ensure fair usage among all clients
3. Maintain system stability and performance
4. Prevent excessive resource consumption

## Rate Limit Configuration

The AssetKPI API implements different rate limits based on the authentication method:

| Authentication Method | Rate Limit | Time Window |
|-----------------------|------------|-------------|
| Firebase Token        | 100 requests | 60 seconds |
| API Key               | 200 requests | 60 seconds |

## Excluded Paths

Certain paths are excluded from rate limiting to ensure that basic functionality remains accessible:

- `/static/*` - Static files (CSS, JavaScript, images)
- `/` - Home page
- `/login` - Login page
- `/logout` - Logout page
- `/dashboard` - Dashboard page

## Rate Limit Headers

All API responses include rate limit headers to help you monitor your usage:

| Header | Description |
|--------|-------------|
| `X-RateLimit-Limit` | The maximum number of requests allowed in the current time window |
| `X-RateLimit-Remaining` | The number of requests remaining in the current time window |
| `X-RateLimit-Reset` | The time at which the current rate limit window resets (Unix timestamp) |

Example:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1620000000
```

## Rate Limit Exceeded Response

If you exceed the rate limit, the API will respond with:

- HTTP Status Code: `429 Too Many Requests`
- Response Body: `Rate limit exceeded`
- Additional Headers:
  - `Retry-After`: The number of seconds to wait before making another request
  - `X-RateLimit-Limit`: The maximum number of requests allowed
  - `X-RateLimit-Remaining`: 0
  - `X-RateLimit-Reset`: The time at which the rate limit will reset

Example:
```
Status: 429 Too Many Requests
Retry-After: 30
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1620000000
```

## Rate Limiting Algorithm

The AssetKPI API uses a sliding window algorithm for rate limiting:

1. Each client is identified by their API key or IP address (if no API key is provided)
2. Requests are tracked within a sliding time window (e.g., 60 seconds)
3. When a new request arrives, the system checks if the client has exceeded their limit
4. If the limit is exceeded, the request is rejected with a 429 response
5. If the limit is not exceeded, the request is processed and the count is incremented

## Best Practices for Handling Rate Limits

To avoid hitting rate limits and to handle them gracefully when they occur:

### 1. Cache Responses

Cache API responses when appropriate to reduce the number of API calls:

```python
import requests
import time

class CachedAPIClient:
    def __init__(self, base_url, api_key, cache_ttl=300):
        self.base_url = base_url
        self.api_key = api_key
        self.cache = {}
        self.cache_ttl = cache_ttl
    
    def get(self, endpoint, params=None):
        # Create a cache key from the endpoint and params
        cache_key = f"{endpoint}:{str(params)}"
        
        # Check if we have a cached response
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                print(f"Using cached response for {endpoint}")
                return cached_data
        
        # Make the API request
        response = requests.get(
            f"{self.base_url}{endpoint}",
            headers={"X-API-Key": self.api_key},
            params=params
        )
        
        # Handle rate limiting
        if response.status_code == 429:
            retry_after = int(response.headers.get("Retry-After", 60))
            print(f"Rate limit exceeded. Retrying after {retry_after} seconds")
            time.sleep(retry_after)
            return self.get(endpoint, params)
        
        # Cache the response
        if response.status_code == 200:
            data = response.json()
            self.cache[cache_key] = (data, time.time())
            return data
        
        # Handle other errors
        response.raise_for_status()
```

### 2. Batch Operations

Instead of making many small requests, batch operations when possible:

```python
# Instead of this:
for part_id in part_ids:
    client.get(f"/inventory/parts/{part_id}")

# Do this:
client.get("/inventory/parts", {"ids": ",".join(map(str, part_ids))})
```

### 3. Implement Exponential Backoff

When you receive a 429 response, use exponential backoff for retries:

```python
import requests
import time
import random

def make_request_with_backoff(url, headers, max_retries=5):
    retries = 0
    while retries < max_retries:
        response = requests.get(url, headers=headers)
        
        if response.status_code != 429:
            return response
        
        # Get retry time from header or use default
        retry_after = int(response.headers.get("Retry-After", 1))
        
        # Calculate exponential backoff with jitter
        backoff = min(60, retry_after * (2 ** retries) + random.uniform(0, 1))
        
        print(f"Rate limit exceeded. Retrying after {backoff:.2f} seconds")
        time.sleep(backoff)
        retries += 1
    
    # If we've exhausted retries, return the last response
    return response
```

### 4. Monitor Your Usage

Monitor your API usage by checking the rate limit headers in responses:

```python
response = requests.get(url, headers=headers)

limit = int(response.headers.get("X-RateLimit-Limit", 0))
remaining = int(response.headers.get("X-RateLimit-Remaining", 0))
reset = int(response.headers.get("X-RateLimit-Reset", 0))

# Calculate percentage of limit used
usage_percent = ((limit - remaining) / limit) * 100 if limit > 0 else 0

print(f"API Usage: {usage_percent:.2f}% ({remaining}/{limit} requests remaining)")
print(f"Rate limit resets at: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(reset))}")

# Alert if usage is high
if usage_percent > 80:
    print("WARNING: API usage is high!")
```

### 5. Distribute Requests Over Time

If you need to make many requests, distribute them over time:

```python
import time

def process_items_with_rate_limiting(items, process_func, rate_limit=100, window=60):
    """
    Process items with rate limiting.
    
    Args:
        items: List of items to process
        process_func: Function to process each item
        rate_limit: Maximum number of requests per window
        window: Time window in seconds
    """
    # Calculate delay between requests to stay under the rate limit
    delay = window / rate_limit
    
    for i, item in enumerate(items):
        # Process the item
        process_func(item)
        
        # Print progress
        print(f"Processed item {i+1}/{len(items)}")
        
        # Sleep to stay under rate limit (except for the last item)
        if i < len(items) - 1:
            time.sleep(delay)
```

## Requesting Higher Rate Limits

If you need higher rate limits for your application, please contact the AssetKPI support team with the following information:

1. Your use case and why you need higher limits
2. The specific endpoints you need higher limits for
3. The volume of requests you expect to make
4. Your contact information

Higher rate limits may be granted on a case-by-case basis.
