# Work Order API Endpoints

This document provides detailed information about the work order-related API endpoints in the AssetKPI system.

## Work Order Management

### Get Work Orders

Retrieves a list of work orders.

**Endpoint:** `GET /api/workorders`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Query Parameters:**

| Parameter | Type | Required | Description | Default |
|-----------|------|----------|-------------|---------|
| status | string | No | Filter by status (e.g., `OPEN`, `CLOSED`) | All statuses |
| type | string | No | Filter by work order type (e.g., `Corrective`, `Preventive`) | All types |
| asset_id | integer | No | Filter by asset ID | All assets |
| limit | integer | No | Maximum number of results to return | 100 |
| offset | integer | No | Number of results to skip | 0 |
| sort | string | No | Field to sort by | workorderid |
| order | string | No | Sort order, either `asc` or `desc` | desc |

**Response:**

```json
[
  {
    "workorderid": 1,
    "assetid": 5,
    "workordertype": "Corrective",
    "description": "Replace bearing assembly",
    "status": "OPEN",
    "assignedto": "John <PERSON>",
    "failurecode": "MECH-001",
    "failuretype": "Mechanical",
    "downtimeminutes": 120,
    "repairtimeminutes": 90,
    "maintenancecost": 350.00,
    "startdate": "2023-04-10T08:00:00Z",
    "enddate": null
  },
  ...
]
```

### Get Work Order Details

Retrieves details for a specific work order.

**Endpoint:** `GET /api/workorders/{workorder_id}`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| workorder_id | integer | Yes | ID of the work order to retrieve |

**Response:**

```json
{
  "workorderid": 1,
  "assetid": 5,
  "workordertype": "Corrective",
  "description": "Replace bearing assembly",
  "status": "OPEN",
  "assignedto": "John Smith",
  "failurecode": "MECH-001",
  "failuretype": "Mechanical",
  "downtimeminutes": 120,
  "repairtimeminutes": 90,
  "maintenancecost": 350.00,
  "startdate": "2023-04-10T08:00:00Z",
  "enddate": null,
  "parts": [
    {
      "partid": 1,
      "partname": "Bearing Assembly",
      "quantityused": 1,
      "unitprice": 120.50,
      "totalcost": 120.50
    },
    {
      "partid": 15,
      "partname": "Seal Kit",
      "quantityused": 2,
      "unitprice": 45.75,
      "totalcost": 91.50
    }
  ],
  "asset": {
    "assetid": 5,
    "assetname": "Pump Station 1",
    "location": "Building A",
    "department": "Production"
  }
}
```

### Create Work Order

Creates a new work order.

**Endpoint:** `POST /api/ingest/workorder`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher, or valid API key

**Request Body:**

```json
{
  "assetId": 5,
  "workOrderType": "Corrective",
  "description": "Replace motor coupling",
  "status": "OPEN",
  "assignedTo": "Jane Doe",
  "failureCode": "MECH-002",
  "failureType": "Mechanical",
  "downtimeMinutes": 180,
  "repairTimeMinutes": 120,
  "maintenanceCost": 450.00,
  "startDate": "2023-04-16T09:00:00Z"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Work order created successfully",
  "workorder_id": 25
}
```

### Update Work Order

Updates an existing work order.

**Endpoint:** `PUT /api/workorders/{workorder_id}`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| workorder_id | integer | Yes | ID of the work order to update |

**Request Body:**

```json
{
  "status": "CLOSED",
  "endDate": "2023-04-16T14:30:00Z",
  "repairTimeMinutes": 150,
  "maintenanceCost": 520.00
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Work order updated successfully"
}
```

### Delete Work Order

Deletes a work order.

**Endpoint:** `DELETE /api/workorders/{workorder_id}`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| workorder_id | integer | Yes | ID of the work order to delete |

**Response:**

```json
{
  "status": "success",
  "message": "Work order deleted successfully"
}
```

## Work Order Parts

### Add Parts to Work Order

Adds parts to an existing work order.

**Endpoint:** `POST /api/workorders/{workorder_id}/parts`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| workorder_id | integer | Yes | ID of the work order to update |

**Request Body:**

```json
{
  "parts": [
    {
      "partid": 1,
      "quantityused": 1
    },
    {
      "partid": 15,
      "quantityused": 2
    }
  ]
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Parts added to work order successfully",
  "updated_maintenance_cost": 520.00
}
```

### Update Work Order Parts

Updates parts for an existing work order.

**Endpoint:** `PUT /api/workorders/{workorder_id}/parts/{part_id}`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| workorder_id | integer | Yes | ID of the work order to update |
| part_id | integer | Yes | ID of the part to update |

**Request Body:**

```json
{
  "quantityused": 3
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Work order part updated successfully",
  "updated_maintenance_cost": 565.75
}
```

### Remove Part from Work Order

Removes a part from a work order.

**Endpoint:** `DELETE /api/workorders/{workorder_id}/parts/{part_id}`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| workorder_id | integer | Yes | ID of the work order to update |
| part_id | integer | Yes | ID of the part to remove |

**Response:**

```json
{
  "status": "success",
  "message": "Part removed from work order successfully",
  "updated_maintenance_cost": 450.00
}
```

## Bulk Operations

### Bulk Import Work Orders

Imports multiple work orders at once.

**Endpoint:** `POST /api/ingest/workorders/bulk`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher, or valid API key

**Request Body:**

```json
{
  "workorders": [
    {
      "assetId": 5,
      "workOrderType": "Corrective",
      "description": "Replace motor coupling",
      "status": "CLOSED",
      "assignedTo": "Jane Doe",
      "failureCode": "MECH-002",
      "failureType": "Mechanical",
      "downtimeMinutes": 180,
      "repairTimeMinutes": 120,
      "maintenanceCost": 450.00,
      "startDate": "2023-04-16T09:00:00Z",
      "endDate": "2023-04-16T14:30:00Z"
    },
    {
      "assetId": 8,
      "workOrderType": "Preventive",
      "description": "Lubricate bearings",
      "status": "CLOSED",
      "assignedTo": "John Smith",
      "failureCode": null,
      "failureType": null,
      "downtimeMinutes": 60,
      "repairTimeMinutes": 45,
      "maintenanceCost": 150.00,
      "startDate": "2023-04-17T10:00:00Z",
      "endDate": "2023-04-17T11:00:00Z"
    }
  ]
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Work orders imported successfully",
  "imported_count": 2,
  "workorder_ids": [26, 27]
}
```

### Upload Work Orders CSV

Uploads work orders from a CSV file.

**Endpoint:** `POST /upload/workorders`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher

**Request Body:**

Form data with a CSV file attachment. The CSV file should have the following headers:
- assetId
- workOrderType
- description
- status
- assignedTo
- failureCode
- failureType
- downtimeMinutes
- repairTimeMinutes
- maintenanceCost
- startDate
- endDate

**Response:**

```json
{
  "status": "success",
  "message": "Work orders imported successfully",
  "imported_count": 5,
  "errors": [],
  "workorder_ids": [28, 29, 30, 31, 32]
}
```
