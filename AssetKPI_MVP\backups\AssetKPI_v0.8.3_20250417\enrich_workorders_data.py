import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')

    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def enrich_workorders_data():
    """Add more comprehensive work order data to the database."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()

        # Get all assets
        cursor.execute("SELECT assetid, assetname, assettype FROM assets")
        assets = cursor.fetchall()

        if not assets:
            print("No assets found in the database. Please add assets first.")
            return

        # Get all work order plans
        cursor.execute("SELECT plan_id, plan_name FROM work_order_plans")
        plans = cursor.fetchall()

        if not plans:
            print("No work order plans found. Please run the work order enhancements migration first.")
            return

        # Get all labor resources
        cursor.execute("SELECT resource_id, person_name FROM labor_resources")
        resources = cursor.fetchall()

        if not resources:
            print("No labor resources found. Please run the work order enhancements migration first.")
            return

        # Sample work order types
        work_order_types = ['Preventive', 'Corrective', 'Emergency', 'Inspection', 'Installation', 'Modification']

        # Sample statuses
        statuses = ['New', 'Assigned', 'In Progress', 'On Hold', 'Completed', 'Closed', 'Cancelled']

        # Sample failure codes
        failure_codes = {
            'Mechanical': ['MECH-001', 'MECH-002', 'MECH-003', 'MECH-004', 'MECH-005'],
            'Electrical': ['ELEC-001', 'ELEC-002', 'ELEC-003', 'ELEC-004', 'ELEC-005'],
            'Hydraulic': ['HYD-001', 'HYD-002', 'HYD-003'],
            'Pneumatic': ['PNEU-001', 'PNEU-002', 'PNEU-003'],
            'Instrumentation': ['INST-001', 'INST-002', 'INST-003'],
            'Structural': ['STRUCT-001', 'STRUCT-002'],
            'Software': ['SW-001', 'SW-002', 'SW-003'],
            'Operator Error': ['OP-001', 'OP-002'],
            'Unknown': ['UNK-001']
        }

        # Sample failure types
        failure_types = list(failure_codes.keys())

        # Sample descriptions for different work order types
        descriptions = {
            'Preventive': [
                'Routine preventive maintenance',
                'Scheduled inspection and service',
                'Regular maintenance check',
                'Periodic maintenance service',
                'Scheduled lubrication and inspection'
            ],
            'Corrective': [
                'Repair of broken component',
                'Fix equipment malfunction',
                'Repair after unexpected failure',
                'Address performance issue',
                'Fix operational problem'
            ],
            'Emergency': [
                'Critical failure requiring immediate attention',
                'Emergency repair of safety-critical component',
                'Urgent fix for production-stopping issue',
                'Emergency response to equipment breakdown',
                'Critical system failure'
            ],
            'Inspection': [
                'Regulatory compliance inspection',
                'Safety inspection',
                'Quality control inspection',
                'Pre-startup inspection',
                'Post-maintenance inspection'
            ],
            'Installation': [
                'New equipment installation',
                'Component replacement installation',
                'System upgrade installation',
                'New feature installation',
                'Replacement part installation'
            ],
            'Modification': [
                'Equipment modification for performance improvement',
                'System upgrade',
                'Configuration change',
                'Enhancement installation',
                'Modification for compliance'
            ]
        }

        # Generate work orders for each asset
        print("Generating work orders for assets...")

        # Track how many work orders we've added
        work_orders_added = 0

        # Generate work orders for the past 2 years
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365*2)

        # For each asset, create multiple work orders
        for asset_id, asset_name, asset_type in assets:
            # Determine how many work orders to create for this asset (5-15)
            num_work_orders = random.randint(5, 15)

            for _ in range(num_work_orders):
                # Randomly select work order type with weighted probability
                # Preventive should be more common than emergency
                wo_type = random.choices(
                    work_order_types,
                    weights=[0.4, 0.3, 0.05, 0.1, 0.1, 0.05],
                    k=1
                )[0]

                # Select a description based on the work order type
                description = random.choice(descriptions[wo_type])

                # Determine status with weighted probability
                # Most work orders should be completed
                status = random.choices(
                    statuses,
                    weights=[0.05, 0.1, 0.15, 0.05, 0.5, 0.1, 0.05],
                    k=1
                )[0]

                # Assign to a random resource
                assigned_resource = random.choice(resources)
                assigned_to = assigned_resource[1]

                # Determine failure information (only for corrective and emergency)
                failure_type = None
                failure_code = None
                if wo_type in ['Corrective', 'Emergency']:
                    failure_type = random.choice(failure_types)
                    failure_code = random.choice(failure_codes[failure_type])

                # Determine dates
                # Work orders should be distributed over the past 2 years
                days_ago = random.randint(0, 365*2)
                start_date_wo = end_date - timedelta(days=days_ago)

                # End date depends on status
                end_date_wo = None
                if status in ['Completed', 'Closed']:
                    # Completed work orders have an end date
                    # Duration depends on work order type
                    if wo_type == 'Emergency':
                        # Emergency work orders are shorter
                        duration_hours = random.randint(1, 8)
                    elif wo_type == 'Preventive':
                        # Preventive work orders are predictable
                        duration_hours = random.randint(2, 8)
                    elif wo_type == 'Installation':
                        # Installations take longer
                        duration_hours = random.randint(8, 40)
                    else:
                        # Other types have variable duration
                        duration_hours = random.randint(2, 24)

                    end_date_wo = start_date_wo + timedelta(hours=duration_hours)

                # Determine downtime and repair time
                downtime_minutes = None
                repair_time_minutes = None
                if wo_type in ['Corrective', 'Emergency'] and status in ['Completed', 'Closed']:
                    # Only corrective and emergency work orders have downtime
                    if wo_type == 'Emergency':
                        downtime_minutes = random.randint(60, 480)  # 1-8 hours
                    else:
                        downtime_minutes = random.randint(30, 240)  # 30 min - 4 hours

                    # Repair time is usually less than or equal to downtime
                    repair_time_minutes = random.randint(max(15, downtime_minutes // 2), downtime_minutes)

                # Determine maintenance cost
                maintenance_cost = None
                if status in ['Completed', 'Closed']:
                    # Base cost depends on work order type
                    if wo_type == 'Preventive':
                        base_cost = random.randint(100, 500)
                    elif wo_type == 'Corrective':
                        base_cost = random.randint(200, 1000)
                    elif wo_type == 'Emergency':
                        base_cost = random.randint(500, 2000)
                    elif wo_type == 'Installation':
                        base_cost = random.randint(1000, 5000)
                    else:
                        base_cost = random.randint(100, 300)

                    # Add some randomness
                    maintenance_cost = base_cost * random.uniform(0.8, 1.2)

                # Find a matching plan based on work order type
                matching_plans = [p_id for p_id, p_name in plans if wo_type.lower() in p_name.lower()]
                plan_id = random.choice(matching_plans) if matching_plans else random.choice([p[0] for p in plans])

                # Assign a priority
                priority = random.choice(['Low', 'Medium', 'High', 'Critical', 'Emergency'])

                # Calculate estimated completion date
                est_completion_date = None
                if start_date_wo:
                    # Get the plan's estimated duration
                    cursor.execute("SELECT estimated_duration FROM work_order_plans WHERE plan_id = %s", (plan_id,))
                    plan_result = cursor.fetchone()
                    if plan_result:
                        est_duration = plan_result[0]
                        est_completion_date = start_date_wo + timedelta(minutes=est_duration)

                # Calculate actual labor hours
                actual_labor_hours = None
                if end_date_wo and start_date_wo:
                    # Get the plan's estimated labor hours
                    cursor.execute("SELECT estimated_labor_hours FROM work_order_plans WHERE plan_id = %s", (plan_id,))
                    plan_result = cursor.fetchone()
                    if plan_result:
                        est_labor_hours = plan_result[0]
                        # Random actual hours between 80% and 120% of estimated
                        actual_labor_hours = round(float(est_labor_hours) * random.uniform(0.8, 1.2), 2)

                # Insert the work order
                cursor.execute(
                    """
                    INSERT INTO workorders
                    (assetid, workordertype, description, status, assignedto, failurecode, failuretype,
                     downtimeminutes, repairtimeminutes, maintenancecost, startdate, enddate,
                     plan_id, priority, estimated_completion_date, actual_labor_hours)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING workorderid
                    """,
                    (asset_id, wo_type, description, status, assigned_to, failure_code, failure_type,
                     downtime_minutes, repair_time_minutes, maintenance_cost, start_date_wo, end_date_wo,
                     plan_id, priority, est_completion_date, actual_labor_hours)
                )

                wo_id = cursor.fetchone()[0]
                work_orders_added += 1

                # Add tasks for this work order
                add_work_order_tasks(cursor, wo_id, wo_type, status, start_date_wo, end_date_wo, resources)

                # Add labor entries for completed work orders
                if status in ['Completed', 'Closed'] and end_date_wo:
                    add_work_order_labor(cursor, wo_id, actual_labor_hours, start_date_wo, end_date_wo, resources)

                # Add parts usage for some work orders
                if random.random() < 0.7 and status in ['Completed', 'Closed']:
                    add_work_order_parts(cursor, wo_id)

                if work_orders_added % 10 == 0:
                    print(f"  - Added {work_orders_added} work orders so far...")

        # Commit the changes
        conn.commit()
        print(f"Successfully added {work_orders_added} new work orders!")

    except Exception as e:
        print(f"Error enriching work orders data: {e}")
        import traceback
        traceback.print_exc()
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def add_work_order_tasks(cursor, wo_id, wo_type, status, start_date, end_date, resources):
    """Add tasks for a work order."""
    # Determine how many tasks to create (2-6)
    num_tasks = random.randint(2, 6)

    # Task templates based on work order type
    task_templates = {
        'Preventive': [
            'Inspect equipment for wear and damage',
            'Check fluid levels and top up as needed',
            'Lubricate moving parts',
            'Clean filters and components',
            'Test operation and performance',
            'Check safety systems',
            'Calibrate sensors and controls',
            'Replace consumable parts'
        ],
        'Corrective': [
            'Diagnose problem',
            'Disassemble equipment',
            'Replace damaged components',
            'Repair mechanical parts',
            'Fix electrical connections',
            'Reassemble equipment',
            'Test and verify repair',
            'Clean work area'
        ],
        'Emergency': [
            'Perform emergency shutdown',
            'Assess damage',
            'Implement temporary fix',
            'Replace critical component',
            'Restore operation',
            'Document failure mode',
            'Test safety systems',
            'Verify operation'
        ],
        'Inspection': [
            'Visual inspection',
            'Operational test',
            'Safety check',
            'Measurement and recording',
            'Documentation review',
            'Compliance verification',
            'Photo documentation',
            'Report preparation'
        ],
        'Installation': [
            'Site preparation',
            'Unpack and inspect equipment',
            'Position and secure equipment',
            'Connect utilities',
            'Install accessories',
            'Configure settings',
            'Perform initial testing',
            'Train operators'
        ],
        'Modification': [
            'Review modification plan',
            'Prepare equipment',
            'Remove old components',
            'Install new components',
            'Update documentation',
            'Test modification',
            'Train users on changes',
            'Update asset records'
        ]
    }

    # Get tasks for this work order type
    tasks = task_templates.get(wo_type, task_templates['Preventive'])

    # Ensure we don't try to create more tasks than we have templates
    num_tasks = min(num_tasks, len(tasks))

    # Select random tasks
    selected_tasks = random.sample(tasks, num_tasks)

    # Determine task status based on work order status
    task_statuses = {
        'New': ['Planned'],
        'Assigned': ['Planned', 'Assigned'],
        'In Progress': ['Planned', 'In Progress', 'Completed'],
        'On Hold': ['Planned', 'In Progress', 'On Hold'],
        'Completed': ['Completed'],
        'Closed': ['Completed'],
        'Cancelled': ['Cancelled']
    }

    # Insert tasks
    for i, task_desc in enumerate(selected_tasks):
        sequence = i + 1

        # Determine task status
        if status in task_statuses:
            if status in ['Completed', 'Closed']:
                task_status = 'Completed'
            elif status == 'Cancelled':
                task_status = 'Cancelled'
            else:
                # For in-progress work orders, tasks can have different statuses
                possible_statuses = task_statuses[status]

                # Just pick a random status from the possible ones
                task_status = random.choice(possible_statuses)
        else:
            task_status = 'Planned'

        # Assign to a random resource
        assigned_resource = random.choice(resources)
        assigned_to = assigned_resource[1]

        # Estimate hours (0.5-4 hours)
        est_hours = round(random.uniform(0.5, 4.0), 2)

        # Actual hours only for completed tasks
        actual_hours = round(est_hours * random.uniform(0.8, 1.2), 2) if task_status == 'Completed' else None

        # Completed by and date only for completed tasks
        completed_by = assigned_to if task_status == 'Completed' else None

        # For completed tasks, calculate a completion date
        completed_date = None
        if task_status == 'Completed' and start_date and end_date:
            # Task completion should be between work order start and end
            task_duration = (end_date - start_date).total_seconds()
            # Earlier tasks complete earlier in the work order timeframe
            progress_factor = (i + 1) / (num_tasks + 1)
            task_completion_seconds = task_duration * progress_factor
            completed_date = start_date + timedelta(seconds=task_completion_seconds)

        cursor.execute(
            """
            INSERT INTO work_order_tasks
            (workorder_id, task_description, sequence_number, estimated_hours, actual_hours,
             status, assigned_to, completed_by, completed_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """,
            (wo_id, task_desc, sequence, est_hours, actual_hours, task_status, assigned_to, completed_by, completed_date)
        )

def add_work_order_labor(cursor, wo_id, total_labor_hours, start_date, end_date, resources):
    """Add labor entries for a work order."""
    # Determine how many labor entries to create (1-3)
    num_labor = random.randint(1, 3)

    # If we don't have total_labor_hours, estimate it
    if total_labor_hours is None:
        # Estimate based on work order duration
        duration_hours = (end_date - start_date).total_seconds() / 3600
        total_labor_hours = duration_hours * random.uniform(0.5, 0.9)  # Labor is usually less than total duration

    # Distribute total labor hours among entries
    remaining_hours = total_labor_hours

    for i in range(num_labor):
        # For the last entry, use all remaining hours
        if i == num_labor - 1:
            hours_worked = remaining_hours
        else:
            # Randomly distribute hours
            hours_worked = round(remaining_hours * random.uniform(0.3, 0.7), 2)
            remaining_hours -= hours_worked

        # Pick a random resource
        resource_id, person_name = random.choice(resources)

        # Determine craft based on resource ID (simplified)
        crafts = ['Mechanical', 'Electrical', 'HVAC', 'Plumbing', 'Instrumentation', 'General']
        craft = crafts[resource_id % len(crafts)]

        # Determine labor rate based on craft
        labor_rates = {
            'Mechanical': random.uniform(35, 55),
            'Electrical': random.uniform(40, 60),
            'HVAC': random.uniform(38, 58),
            'Plumbing': random.uniform(35, 55),
            'Instrumentation': random.uniform(45, 65),
            'General': random.uniform(30, 50)
        }
        labor_rate = labor_rates.get(craft, random.uniform(30, 60))

        # Calculate labor cost
        labor_cost = round(hours_worked * labor_rate, 2)

        # Work date (random date between start and end)
        if start_date and end_date:
            delta = (end_date - start_date).total_seconds()
            random_seconds = random.randint(0, int(delta))
            work_date = start_date + timedelta(seconds=random_seconds)
        else:
            work_date = datetime.now().date()

        cursor.execute(
            """
            INSERT INTO work_order_labor
            (workorder_id, labor_code, craft, person_id, hours_worked, labor_cost, work_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """,
            (wo_id, f"LC{i+1}", craft, resource_id, hours_worked, labor_cost, work_date)
        )

def add_work_order_parts(cursor, wo_id):
    """Add parts usage for a work order."""
    # Get available parts
    cursor.execute("SELECT partid, partname, unitprice FROM spareparts")
    parts = cursor.fetchall()

    if not parts:
        return

    # Determine how many different parts to use (1-3)
    num_parts = random.randint(1, 3)

    # Select random parts
    selected_parts = random.sample(parts, min(num_parts, len(parts)))

    for part_id, part_name, unit_price in selected_parts:
        # Determine quantity used (1-5)
        quantity = random.randint(1, 5)

        cursor.execute(
            """
            INSERT INTO workorderparts
            (workorderid, partid, quantityused)
            VALUES (%s, %s, %s)
            """,
            (wo_id, part_id, quantity)
        )

if __name__ == "__main__":
    enrich_workorders_data()
