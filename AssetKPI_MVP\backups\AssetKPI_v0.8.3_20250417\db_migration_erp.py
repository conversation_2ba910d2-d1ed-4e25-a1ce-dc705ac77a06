"""
Database migration script for ERP integration tables.

This script creates the erp_connections, erp_data_mappings, and erp_sync_logs tables in the database.
"""

import os
import sys
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, ForeignKey, JSON, Text, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)

# Create base class for declarative models
Base = declarative_base()

# Define models for migration
class ERPConnection(Base):
    """
    Model for ERP system connections.
    
    This model stores information about connections to external ERP systems,
    including connection details and authentication.
    """
    __tablename__ = "erp_connections"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # ERP system details
    system_type = Column(String(50), nullable=False)
    version = Column(String(50), nullable=True)
    
    # Connection details
    connection_url = Column(String(500), nullable=False)
    connection_params = Column(JSON, nullable=True)
    
    # Authentication
    auth_type = Column(String(50), nullable=False)  # "basic", "oauth", "api_key", etc.
    auth_credentials = Column(String(500), nullable=True)  # Encrypted credentials
    
    # Status
    status = Column(String(50), default="inactive")
    last_sync_time = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # User who created the connection
    created_by = Column(String(255))


class ERPDataMapping(Base):
    """
    Model for ERP data mappings.
    
    This model stores information about how data is mapped between AssetKPI and ERP systems.
    """
    __tablename__ = "erp_data_mappings"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("erp_connections.id"))
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Entity type in AssetKPI
    assetkpi_entity = Column(String(50), nullable=False)  # "asset", "inventory", "workorder", etc.
    
    # Entity type in ERP system
    erp_entity = Column(String(50), nullable=False)
    
    # Field mappings (JSON object mapping AssetKPI fields to ERP fields)
    field_mappings = Column(JSON, nullable=False)
    
    # Transformation rules (optional JSON object with transformation rules)
    transformation_rules = Column(JSON, nullable=True)
    
    # Sync direction
    sync_direction = Column(String(20), nullable=False)  # "import", "export", "bidirectional"
    
    # Sync schedule (cron expression)
    sync_schedule = Column(String(50), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ERPSyncLog(Base):
    """
    Model for ERP synchronization logs.
    
    This model stores logs of synchronization operations between AssetKPI and ERP systems.
    """
    __tablename__ = "erp_sync_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("erp_connections.id"))
    mapping_id = Column(Integer, ForeignKey("erp_data_mappings.id"), nullable=True)
    
    # Operation details
    operation_type = Column(String(50), nullable=False)  # "import", "export", "test", etc.
    entity_type = Column(String(50), nullable=False)  # "asset", "inventory", "workorder", etc.
    
    # Status
    status = Column(String(50), nullable=False)  # "success", "partial", "failure"
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=True)
    
    # Results
    records_processed = Column(Integer, default=0)
    records_succeeded = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)
    
    # Error details
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)


def create_tables():
    """Create the ERP integration tables in the database."""
    try:
        # Create tables
        Base.metadata.create_all(engine)
        print("ERP integration tables created successfully.")
    except Exception as e:
        print(f"Error creating ERP integration tables: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print(f"Creating ERP integration tables in database: {DATABASE_URL}")
    create_tables()
    print("Database migration completed successfully.")
