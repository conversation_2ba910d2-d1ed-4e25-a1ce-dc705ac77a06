"""
Webhook schemas for the AssetKPI application.

This module defines the Pydantic models for webhook subscriptions and webhook events.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field, HttpUrl, validator
from enum import Enum


class WebhookEventTypeEnum(str, Enum):
    """Enum for webhook event types."""
    # Inventory events
    INVENTORY_UPDATED = "inventory.updated"
    INVENTORY_CREATED = "inventory.created"
    INVENTORY_DELETED = "inventory.deleted"
    INVENTORY_THRESHOLD_REACHED = "inventory.threshold_reached"
    
    # Work order events
    WORKORDER_CREATED = "workorder.created"
    WORKORDER_UPDATED = "workorder.updated"
    WORKORDER_COMPLETED = "workorder.completed"
    WORKORDER_DELETED = "workorder.deleted"
    
    # Asset events
    ASSET_CREATED = "asset.created"
    ASSET_UPDATED = "asset.updated"
    ASSET_DELETED = "asset.deleted"
    
    # KPI events
    KPI_CALCULATED = "kpi.calculated"
    KPI_THRESHOLD_REACHED = "kpi.threshold_reached"
    
    # Recommendation events
    RECOMMENDATION_CREATED = "recommendation.created"
    RECOMMENDATION_UPDATED = "recommendation.updated"
    
    # User events
    USER_CREATED = "user.created"
    USER_UPDATED = "user.updated"
    USER_DELETED = "user.deleted"
    USER_LOGIN = "user.login"
    
    # System events
    SYSTEM_BACKUP_COMPLETED = "system.backup_completed"
    SYSTEM_ERROR = "system.error"


class WebhookAuthTypeEnum(str, Enum):
    """Enum for webhook authentication types."""
    NONE = "none"
    BASIC = "basic"
    BEARER = "bearer"
    API_KEY = "api_key"


class WebhookSubscriptionBase(BaseModel):
    """Base model for webhook subscriptions."""
    name: str = Field(..., min_length=3, max_length=100, description="Name of the webhook subscription")
    url: HttpUrl = Field(..., description="URL to send webhook events to")
    description: Optional[str] = Field(None, description="Description of the webhook subscription")
    event_types: List[WebhookEventTypeEnum] = Field(..., min_items=1, description="Event types to subscribe to")
    auth_type: WebhookAuthTypeEnum = Field(WebhookAuthTypeEnum.NONE, description="Authentication type")
    auth_credentials: Optional[str] = Field(None, description="Authentication credentials (encrypted)")
    retry_count: int = Field(3, ge=0, le=10, description="Number of retry attempts")
    retry_interval: int = Field(60, ge=10, le=3600, description="Retry interval in seconds")
    timeout: int = Field(30, ge=5, le=300, description="Request timeout in seconds")
    
    @validator('event_types')
    def validate_event_types(cls, v):
        """Validate that event types are unique."""
        if len(v) != len(set(v)):
            raise ValueError("Event types must be unique")
        return v


class WebhookSubscriptionCreate(WebhookSubscriptionBase):
    """Model for creating a webhook subscription."""
    pass


class WebhookSubscriptionUpdate(BaseModel):
    """Model for updating a webhook subscription."""
    name: Optional[str] = Field(None, min_length=3, max_length=100, description="Name of the webhook subscription")
    url: Optional[HttpUrl] = Field(None, description="URL to send webhook events to")
    description: Optional[str] = Field(None, description="Description of the webhook subscription")
    event_types: Optional[List[WebhookEventTypeEnum]] = Field(None, min_items=1, description="Event types to subscribe to")
    auth_type: Optional[WebhookAuthTypeEnum] = Field(None, description="Authentication type")
    auth_credentials: Optional[str] = Field(None, description="Authentication credentials (encrypted)")
    is_active: Optional[bool] = Field(None, description="Whether the webhook subscription is active")
    retry_count: Optional[int] = Field(None, ge=0, le=10, description="Number of retry attempts")
    retry_interval: Optional[int] = Field(None, ge=10, le=3600, description="Retry interval in seconds")
    timeout: Optional[int] = Field(None, ge=5, le=300, description="Request timeout in seconds")
    
    @validator('event_types')
    def validate_event_types(cls, v):
        """Validate that event types are unique."""
        if v is not None and len(v) != len(set(v)):
            raise ValueError("Event types must be unique")
        return v


class WebhookSubscriptionInDB(WebhookSubscriptionBase):
    """Model for a webhook subscription in the database."""
    id: int
    created_by: str
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    delivery_success_count: int
    delivery_failure_count: int
    last_delivery_status: Optional[str] = None
    last_delivery_time: Optional[datetime] = None
    
    class Config:
        orm_mode = True


class WebhookEventBase(BaseModel):
    """Base model for webhook events."""
    event_type: WebhookEventTypeEnum = Field(..., description="Type of the event")
    payload: Dict[str, Any] = Field(..., description="Event payload")


class WebhookEventCreate(WebhookEventBase):
    """Model for creating a webhook event."""
    subscription_id: int = Field(..., description="ID of the webhook subscription")


class WebhookEventInDB(WebhookEventBase):
    """Model for a webhook event in the database."""
    id: int
    subscription_id: int
    status: str
    attempts: int
    next_attempt_time: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    response_status_code: Optional[int] = None
    response_body: Optional[str] = None
    error_message: Optional[str] = None
    
    class Config:
        orm_mode = True


class WebhookDeliveryStatus(BaseModel):
    """Model for webhook delivery status."""
    event_id: int
    subscription_id: int
    event_type: WebhookEventTypeEnum
    status: str
    attempts: int
    last_attempt_time: Optional[datetime] = None
    next_attempt_time: Optional[datetime] = None
    response_status_code: Optional[int] = None
    error_message: Optional[str] = None
