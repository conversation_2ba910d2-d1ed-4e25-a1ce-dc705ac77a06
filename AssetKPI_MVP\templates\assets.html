{% extends "layout.html" %}

{% block title %}Assets | AssetKPI{% endblock %}

{% block styles %}
<style>
    .asset-card {
        transition: transform 0.3s;
        height: 100%;
    }
    .asset-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    .filter-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .status-badge {
        font-size: 0.8rem;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    .status-maintenance {
        background-color: #ffc107;
    }
    .criticality-high {
        border-left: 4px solid #dc3545;
    }
    .criticality-medium {
        border-left: 4px solid #ffc107;
    }
    .criticality-low {
        border-left: 4px solid #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4 align-items-center">
    <div class="col-12 col-md">
        <h1 class="h2 mb-1">Assets</h1>
        <p class="text-muted mb-md-0">View and manage all assets in the system</p>
    </div>
    <div class="col-12 col-md-auto mt-2 mt-md-0">
        <button class="btn btn-primary w-100 w-md-auto auth-required-content" data-role="ENGINEER,MANAGER,ADMIN" data-bs-toggle="modal" data-bs-target="#assetFormModal">
            <i class="bi bi-plus-circle me-2"></i> Add New Asset
        </button>
    </div>
</div>

<!-- Filters Section -->
<div class="filter-section mb-4 p-3 bg-light rounded">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>Filters</h5>
        <button class="btn btn-sm btn-link d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
            <i class="bi bi-chevron-down"></i> <span class="filter-toggle-text">Show Filters</span>
        </button>
    </div>

    <div class="collapse d-md-block" id="filterCollapse">
        <div class="row g-2">
            <div class="col-12 mb-3">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" id="assetSearch" class="form-control" placeholder="Search assets...">
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-3 mb-2">
                <label for="locationFilter" class="form-label">Location</label>
                <select class="form-select" id="locationFilter">
                    <option value="">All Locations</option>
                    {% for location in asset_locations %}
                    <option value="{{ location.location_id }}">{{ location.location_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-12 col-sm-6 col-md-3 mb-2">
                <label for="systemFilter" class="form-label">System</label>
                <select class="form-select" id="systemFilter">
                    <option value="">All Systems</option>
                    {% for system in asset_systems %}
                    <option value="{{ system.system_id }}">{{ system.system_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-12 col-sm-6 col-md-3 mb-2">
                <label for="categoryFilter" class="form-label">Category</label>
                <select class="form-select" id="categoryFilter">
                    <option value="">All Categories</option>
                    {% for category in asset_categories %}
                    <option value="{{ category.category_id }}">{{ category.category_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-12 col-sm-6 col-md-3 mb-2">
                <label for="statusFilter" class="form-label">Status</label>
                <select class="form-select" id="statusFilter">
                    <option value="">All Statuses</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                    <option value="Maintenance">Maintenance</option>
                </select>
            </div>
        </div>

        <div class="d-flex justify-content-end mt-3">
            <button class="btn btn-outline-secondary me-2" id="resetFilters">
                <i class="bi bi-x-circle me-1"></i> Reset
            </button>
            <button class="btn btn-primary" id="applyFilters">
                <i class="bi bi-check2-circle me-1"></i> Apply Filters
            </button>
        </div>
    </div>
</div>

<!-- Filter toggle script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const filterToggle = document.querySelector('[data-bs-target="#filterCollapse"]');
        const filterToggleText = document.querySelector('.filter-toggle-text');
        const filterCollapse = document.getElementById('filterCollapse');

        if (filterToggle && filterCollapse) {
            filterCollapse.addEventListener('show.bs.collapse', function () {
                filterToggleText.textContent = 'Hide Filters';
                filterToggle.querySelector('i').classList.remove('bi-chevron-down');
                filterToggle.querySelector('i').classList.add('bi-chevron-up');
            });

            filterCollapse.addEventListener('hide.bs.collapse', function () {
                filterToggleText.textContent = 'Show Filters';
                filterToggle.querySelector('i').classList.remove('bi-chevron-up');
                filterToggle.querySelector('i').classList.add('bi-chevron-down');
            });
        }
    });
</script>

<!-- Assets Grid View -->
<div class="row" id="assetsGrid">
    {% if assets %}
        {% for asset in assets %}
            <div class="col-12 col-sm-6 col-lg-4 mb-4 asset-item"
                 data-location="{{ asset.location_id }}"
                 data-system="{{ asset.system_id }}"
                 data-category="{{ asset.category_id }}"
                 data-status="{{ asset.status }}"
                 data-name="{{ asset.assetname }}"
                 data-id="{{ asset.assetid }}">
                <div class="card h-100 asset-card criticality-{{ asset.criticality|lower if asset.criticality else 'medium' }}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 text-truncate" style="max-width: 70%;">{{ asset.assetname }}</h5>
                        <span class="badge status-{{ asset.status|lower if asset.status else 'inactive' }}">{{ asset.status }}</span>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6">
                                <p class="card-text mb-2"><strong>Type:</strong><br class="d-md-none"> {{ asset.assettype }}</p>
                            </div>
                            <div class="col-6">
                                <p class="card-text mb-2"><strong>Location:</strong><br class="d-md-none"> {{ asset.location }}</p>
                            </div>
                            <div class="col-6">
                                <p class="card-text mb-2"><strong>Manufacturer:</strong><br class="d-md-none"> {{ asset.manufacturer or 'N/A' }}</p>
                            </div>
                            <div class="col-6">
                                <p class="card-text mb-2"><strong>Model:</strong><br class="d-md-none"> {{ asset.model or 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex flex-column flex-sm-row gap-2">
                            <a href="/assets/{{ asset.assetid }}" class="btn btn-primary btn-sm flex-grow-1">
                                <i class="bi bi-eye me-1"></i> View Details
                            </a>
                            <a href="/assets/{{ asset.assetid }}/maintenance" class="btn btn-outline-secondary btn-sm flex-grow-1">
                                <i class="bi bi-tools me-1"></i> Maintenance
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i> No assets found. Please add assets to the system.
            </div>
        </div>
    {% endif %}
</div>

<!-- List View Toggle Button -->
<div class="row mb-4">
    <div class="col text-center">
        <button class="btn btn-outline-secondary" id="toggleView">
            <i class="bi bi-list me-1"></i> <span class="toggle-view-text">Switch to List View</span>
        </button>
    </div>
</div>

<!-- Asset Form Modal -->
<div class="modal fade" id="assetFormModal" tabindex="-1" aria-labelledby="assetFormModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assetFormModalLabel">Add New Asset</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="assetForm" class="needs-validation" novalidate>
                    <div id="formErrors" class="alert alert-danger d-none" role="alert"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group required mb-3">
                                <label for="assetName" class="form-label">Asset Name</label>
                                <input type="text" class="form-control" id="assetName" name="assetName" required>
                                <div class="invalid-feedback">Please enter an asset name.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group required mb-3">
                                <label for="assetType" class="form-label">Asset Type</label>
                                <select class="form-select" id="assetType" name="assetType" required>
                                    <option value="">Select asset type</option>
                                    <option value="Equipment">Equipment</option>
                                    <option value="Machinery">Machinery</option>
                                    <option value="Vehicle">Vehicle</option>
                                    <option value="Building">Building</option>
                                    <option value="IT">IT</option>
                                    <option value="Other">Other</option>
                                </select>
                                <div class="invalid-feedback">Please select an asset type.</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group required mb-3">
                                <label for="assetLocation" class="form-label">Location</label>
                                <select class="form-select" id="assetLocation" name="assetLocation" required>
                                    <option value="">Select location</option>
                                    {% for location in asset_locations %}
                                    <option value="{{ location.location_id }}">{{ location.location_name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">Please select a location.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group required mb-3">
                                <label for="assetStatus" class="form-label">Status</label>
                                <select class="form-select" id="assetStatus" name="assetStatus" required>
                                    <option value="">Select status</option>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                    <option value="Maintenance">Maintenance</option>
                                </select>
                                <div class="invalid-feedback">Please select a status.</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="assetManufacturer" class="form-label">Manufacturer</label>
                                <input type="text" class="form-control" id="assetManufacturer" name="assetManufacturer">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="assetModel" class="form-label">Model</label>
                                <input type="text" class="form-control" id="assetModel" name="assetModel">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="assetSerial" class="form-label">Serial Number</label>
                                <input type="text" class="form-control" id="assetSerial" name="assetSerial">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="assetCriticality" class="form-label">Criticality</label>
                                <select class="form-select" id="assetCriticality" name="assetCriticality">
                                    <option value="">Select criticality</option>
                                    <option value="High">High</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Low">Low</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="assetPurchaseDate" class="form-label">Purchase Date</label>
                                <input type="date" class="form-control" id="assetPurchaseDate" name="assetPurchaseDate">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="assetPurchaseCost" class="form-label">Purchase Cost</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="assetPurchaseCost" name="assetPurchaseCost" min="0" step="0.01">
                                    <div class="invalid-feedback">Please enter a valid purchase cost.</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="assetDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="assetDescription" name="assetDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveAssetBtn">Save Asset</button>
            </div>
        </div>
    </div>
</div>

<!-- Assets List View (Hidden by Default) -->
<div class="row d-none" id="assetsList">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-0 p-md-3">
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-to-cards">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th class="d-none d-md-table-cell">Criticality</th>
                                <th class="d-none d-lg-table-cell">Manufacturer</th>
                                <th class="d-none d-lg-table-cell">Model</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if assets %}
                                {% for asset in assets %}
                                    <tr class="asset-item"
                                        data-location="{{ asset.location_id }}"
                                        data-system="{{ asset.system_id }}"
                                        data-category="{{ asset.category_id }}"
                                        data-status="{{ asset.status }}"
                                        data-name="{{ asset.assetname }}"
                                        data-id="{{ asset.assetid }}">
                                        <td data-label="ID">{{ asset.assetid }}</td>
                                        <td data-label="Name">{{ asset.assetname }}</td>
                                        <td data-label="Type">{{ asset.assettype }}</td>
                                        <td data-label="Location">{{ asset.location }}</td>
                                        <td data-label="Status"><span class="badge status-{{ asset.status|lower if asset.status else 'inactive' }}">{{ asset.status }}</span></td>
                                        <td data-label="Criticality" class="d-none d-md-table-cell">{{ asset.criticality }}</td>
                                        <td data-label="Manufacturer" class="d-none d-lg-table-cell">{{ asset.manufacturer }}</td>
                                        <td data-label="Model" class="d-none d-lg-table-cell">{{ asset.model }}</td>
                                        <td data-label="Actions">
                                            <div class="d-flex flex-column flex-sm-row gap-2">
                                                <a href="/assets/{{ asset.assetid }}" class="btn btn-primary btn-sm">
                                                    <i class="bi bi-eye me-1"></i> <span class="d-none d-sm-inline">View</span>
                                                </a>
                                                <a href="/assets/{{ asset.assetid }}/maintenance" class="btn btn-outline-secondary btn-sm">
                                                    <i class="bi bi-tools me-1"></i> <span class="d-none d-sm-inline">History</span>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">No assets found</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script type="module">
    import { validateField, validateForm, displayErrors, clearErrors, getFormData } from '/static/js/utils/validation.js';

    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between grid and list view
        const toggleViewBtn = document.getElementById('toggleView');
        const toggleViewText = document.querySelector('.toggle-view-text');
        const assetsGrid = document.getElementById('assetsGrid');
        const assetsList = document.getElementById('assetsList');

        // Initialize responsive tables
        if (window.ResponsiveTables) {
            ResponsiveTables.setupCardViewTables();
        }

        toggleViewBtn.addEventListener('click', function() {
            if (assetsGrid.classList.contains('d-none')) {
                // Switch to grid view
                assetsGrid.classList.remove('d-none');
                assetsList.classList.add('d-none');
                toggleViewBtn.querySelector('i').classList.remove('bi-grid');
                toggleViewBtn.querySelector('i').classList.add('bi-list');
                toggleViewText.textContent = 'Switch to List View';
            } else {
                // Switch to list view
                assetsGrid.classList.add('d-none');
                assetsList.classList.remove('d-none');
                toggleViewBtn.querySelector('i').classList.remove('bi-list');
                toggleViewBtn.querySelector('i').classList.add('bi-grid');
                toggleViewText.textContent = 'Switch to Grid View';

                // Setup responsive tables when switching to list view
                if (window.ResponsiveTables) {
                    ResponsiveTables.setupCardViewTables();
                }
            }
        });

        // Check if we should start in list view on small screens
        if (window.innerWidth < 768 && window.localStorage.getItem('preferredView') === 'list') {
            toggleViewBtn.click();
        }

        // Save view preference
        window.addEventListener('beforeunload', function() {
            const currentView = assetsGrid.classList.contains('d-none') ? 'list' : 'grid';
            window.localStorage.setItem('preferredView', currentView);
        });

        // Filter functionality
        const assetSearch = document.getElementById('assetSearch');
        const locationFilter = document.getElementById('locationFilter');
        const systemFilter = document.getElementById('systemFilter');
        const categoryFilter = document.getElementById('categoryFilter');
        const statusFilter = document.getElementById('statusFilter');
        const resetFiltersBtn = document.getElementById('resetFilters');
        const applyFiltersBtn = document.getElementById('applyFilters');
        const assetItems = document.querySelectorAll('.asset-item');

        function applyFilters() {
            const searchTerm = assetSearch.value.toLowerCase();
            const locationValue = locationFilter.value;
            const systemValue = systemFilter.value;
            const categoryValue = categoryFilter.value;
            const statusValue = statusFilter.value;

            assetItems.forEach(item => {
                const name = item.dataset.name.toLowerCase();
                const location = item.dataset.location;
                const system = item.dataset.system;
                const category = item.dataset.category;
                const status = item.dataset.status;

                const matchesSearch = name.includes(searchTerm);
                const matchesLocation = !locationValue || location === locationValue;
                const matchesSystem = !systemValue || system === systemValue;
                const matchesCategory = !categoryValue || category === categoryValue;
                const matchesStatus = !statusValue || status === statusValue;

                if (matchesSearch && matchesLocation && matchesSystem && matchesCategory && matchesStatus) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        applyFiltersBtn.addEventListener('click', applyFilters);

        resetFiltersBtn.addEventListener('click', function() {
            assetSearch.value = '';
            locationFilter.value = '';
            systemFilter.value = '';
            categoryFilter.value = '';
            statusFilter.value = '';

            assetItems.forEach(item => {
                item.style.display = '';
            });
        });

        // Quick search functionality
        assetSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            assetItems.forEach(item => {
                const name = item.dataset.name.toLowerCase();

                if (name.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // Asset Form Modal
        const addAssetBtn = document.querySelector('.btn-primary[data-role="ENGINEER,MANAGER,ADMIN"]');
        const assetFormModal = new bootstrap.Modal(document.getElementById('assetFormModal'));
        const assetForm = document.getElementById('assetForm');
        const saveAssetBtn = document.getElementById('saveAssetBtn');
        const formErrorsContainer = document.getElementById('formErrors');

        // Asset form field validation rules
        const assetFormValidationRules = [
            {
                name: 'assetName',
                rules: [
                    { rule: 'required', message: 'Asset name is required' },
                    { rule: 'minLength', params: { min: 3 }, message: 'Asset name must be at least 3 characters' },
                    { rule: 'maxLength', params: { max: 100 }, message: 'Asset name must be no more than 100 characters' }
                ]
            },
            {
                name: 'assetType',
                rules: [
                    { rule: 'required', message: 'Asset type is required' }
                ]
            },
            {
                name: 'assetLocation',
                rules: [
                    { rule: 'required', message: 'Location is required' }
                ]
            },
            {
                name: 'assetStatus',
                rules: [
                    { rule: 'required', message: 'Status is required' }
                ]
            },
            {
                name: 'assetPurchaseCost',
                rules: [
                    { rule: 'numeric', message: 'Purchase cost must be a number' },
                    { rule: 'min', params: { min: 0 }, message: 'Purchase cost must be a positive number' }
                ]
            },
            {
                name: 'assetPurchaseDate',
                rules: [
                    { rule: 'date', message: 'Please enter a valid date' },
                    { rule: 'maxDate', params: { max: new Date().toISOString().split('T')[0] }, message: 'Purchase date cannot be in the future' }
                ]
            }
        ];

        // Open asset form modal
        addAssetBtn.addEventListener('click', function() {
            // Clear form
            assetForm.reset();
            clearErrors(assetForm);
            formErrorsContainer.classList.add('d-none');
            formErrorsContainer.innerHTML = '';

            // Show modal
            assetFormModal.show();
        });

        // Form validation on input
        assetForm.querySelectorAll('input, select, textarea').forEach(field => {
            field.addEventListener('blur', function() {
                const fieldConfig = assetFormValidationRules.find(config => config.name === field.name);
                if (fieldConfig) {
                    const fieldErrors = validateField(field.value, fieldConfig.rules);
                    if (fieldErrors.length > 0) {
                        field.classList.add('is-invalid');
                        const feedbackEl = field.nextElementSibling;
                        if (feedbackEl && feedbackEl.classList.contains('invalid-feedback')) {
                            feedbackEl.textContent = fieldErrors[0];
                        }
                    } else {
                        field.classList.remove('is-invalid');
                        field.classList.add('is-valid');
                    }
                }
            });
        });

        // Save asset
        saveAssetBtn.addEventListener('click', function() {
            // Get form data
            const formData = getFormData(assetForm);

            // Validate form
            const validationResult = validateForm(formData, assetFormValidationRules);

            if (!validationResult.valid) {
                // Show validation errors
                displayFormErrors(validationResult.errors);
                return;
            }

            // Clear errors
            clearErrors(assetForm);
            formErrorsContainer.classList.add('d-none');

            // Submit form data
            submitAssetForm(formData);
        });

        // Display form errors
        function displayFormErrors(errors) {
            // Clear previous errors
            clearErrors(assetForm);

            // Show error summary
            formErrorsContainer.classList.remove('d-none');
            formErrorsContainer.innerHTML = '<h5>Please correct the following errors:</h5><ul></ul>';
            const errorList = formErrorsContainer.querySelector('ul');

            // Add errors to summary and highlight fields
            for (const [fieldName, fieldErrors] of Object.entries(errors)) {
                const field = assetForm.querySelector(`[name="${fieldName}"]`);
                if (field) {
                    // Add error to field
                    field.classList.add('is-invalid');
                    const feedbackEl = field.nextElementSibling;
                    if (feedbackEl && feedbackEl.classList.contains('invalid-feedback')) {
                        feedbackEl.textContent = fieldErrors[0];
                    }

                    // Add error to summary
                    const errorItem = document.createElement('li');
                    errorItem.textContent = `${getFieldLabel(fieldName)}: ${fieldErrors[0]}`;
                    errorList.appendChild(errorItem);
                }
            }

            // Focus first invalid field
            const firstInvalidField = assetForm.querySelector('.is-invalid');
            if (firstInvalidField) {
                firstInvalidField.focus();
            }
        }

        // Get field label
        function getFieldLabel(fieldName) {
            const labelMap = {
                'assetName': 'Asset Name',
                'assetType': 'Asset Type',
                'assetLocation': 'Location',
                'assetStatus': 'Status',
                'assetManufacturer': 'Manufacturer',
                'assetModel': 'Model',
                'assetSerial': 'Serial Number',
                'assetCriticality': 'Criticality',
                'assetPurchaseDate': 'Purchase Date',
                'assetPurchaseCost': 'Purchase Cost',
                'assetDescription': 'Description'
            };

            return labelMap[fieldName] || fieldName;
        }

        // Submit asset form
        function submitAssetForm(formData) {
            // Show loading state
            saveAssetBtn.disabled = true;
            saveAssetBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';

            // Send form data to server
            fetch('/api/assets', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showAlert('success', 'Asset created successfully!');

                    // Close modal
                    assetFormModal.hide();

                    // Reload page to show new asset
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    if (data.errors) {
                        displayFormErrors(data.errors);
                    } else {
                        showAlert('danger', data.message || 'An error occurred while creating the asset.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'An error occurred while creating the asset.');
            })
            .finally(() => {
                // Reset button state
                saveAssetBtn.disabled = false;
                saveAssetBtn.innerHTML = 'Save Asset';
            });
        }

        // Show alert
        function showAlert(type, message) {
            const alertContainer = document.createElement('div');
            alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
            alertContainer.setAttribute('role', 'alert');
            alertContainer.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Add alert to page
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertContainer, container.firstChild);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                alertContainer.classList.remove('show');
                setTimeout(() => {
                    alertContainer.remove();
                }, 150);
            }, 5000);
        }
    });
</script>
{% endblock %}
