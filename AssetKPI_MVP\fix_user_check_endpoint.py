# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Fix the user check endpoint
old_function = '''@app.get("/api/user-check/{uid}", tags=["Authentication"])
async def user_check(uid: str):
    """
    Check if a user with the given UID exists in the database.
    """
    try:
        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            print(f"Querying database for user with UID: {uid}")
            query = text("SELECT * FROM users WHERE uid = :uid")
            print(f"SQL Query: {query}")
            print(f"Parameters: {{'uid': {uid}}}")
            
            result = db.execute(query, {"uid": uid}).fetchone()

            if result:
                print(f"Found user in database: {result}")
                # Return user information
                user_info = {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
                print(f"Returning user info: {user_info}")
                return user_info
            else:
                print(f"User with UID {uid} not found in database")
                # Let's check what users are in the database
                all_users = db.execute(text("SELECT uid, email FROM users")).fetchall()
                print(f"All users in database: {all_users}")
                
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error checking user: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": f"Error checking user: {str(e)}"}
        )'''

new_function = '''@app.get("/api/user-check/{uid}", tags=["Authentication"])
async def user_check(uid: str):
    """
    Check if a user with the given UID exists in the database.
    """
    try:
        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            print(f"Querying database for user with UID: {uid}")
            query = text("SELECT * FROM users WHERE user_id = :uid")
            print(f"SQL Query: {query}")
            print(f"Parameters: {{'uid': {uid}}}")
            
            result = db.execute(query, {"uid": uid}).fetchone()

            if result:
                print(f"Found user in database: {result}")
                # Return user information
                user_info = {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
                print(f"Returning user info: {user_info}")
                return user_info
            else:
                print(f"User with UID {uid} not found in database")
                # Let's check what users are in the database
                all_users = db.execute(text("SELECT user_id, email FROM users")).fetchall()
                print(f"All users in database: {all_users}")
                
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error checking user: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": f"Error checking user: {str(e)}"}
        )'''

content = content.replace(old_function, new_function)

# Fix the verify_token function
old_verify_token = '''@app.post("/api/verify-token", tags=["Authentication"])
async def verify_token(request: Request):
    """
    Verifies a Firebase ID token and returns user information.
    """
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]

        # Verify the token with Firebase Admin SDK
        try:
            decoded_token = auth.verify_id_token(token)
            uid = decoded_token['uid']
            print(f"Successfully verified token for UID: {uid}")
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )

        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            print(f"Querying database for user with UID: {uid}")
            result = db.execute(text("SELECT * FROM users WHERE uid = :uid"), {"uid": uid}).fetchone()

            if result:
                print(f"Found user in database: {result}")
                # Return user information
                return {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
            else:
                print(f"User with UID {uid} not found in database")
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error verifying token: {e}")
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

new_verify_token = '''@app.post("/api/verify-token", tags=["Authentication"])
async def verify_token(request: Request):
    """
    Verifies a Firebase ID token and returns user information.
    """
    print("=== Token Verification Request ===")
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        print(f"Authorization header: {auth_header[:20]}..." if auth_header else "No Authorization header")
        
        if not auth_header or not auth_header.startswith('Bearer '):
            print("No valid authorization header provided")
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        print(f"Token extracted: {token[:20]}...")

        # Verify the token with Firebase Admin SDK
        try:
            print("Attempting to verify token with Firebase Admin SDK...")
            decoded_token = auth.verify_id_token(token)
            uid = decoded_token['uid']
            print(f"Successfully verified token for UID: {uid}")
            print(f"Decoded token claims: {decoded_token}")
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            print(f"Token that failed verification: {token[:30]}...")
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )

        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            print(f"Querying database for user with UID: {uid}")
            query = text("SELECT * FROM users WHERE user_id = :uid")
            print(f"SQL Query: {query}")
            print(f"Parameters: {{'uid': {uid}}}")
            
            result = db.execute(query, {"uid": uid}).fetchone()

            if result:
                print(f"Found user in database: {result}")
                # Return user information
                user_info = {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
                print(f"Returning user info: {user_info}")
                return user_info
            else:
                print(f"User with UID {uid} not found in database")
                # Let's check what users are in the database
                all_users = db.execute(text("SELECT user_id, email FROM users")).fetchall()
                print(f"All users in database: {all_users}")
                
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error verifying token: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

content = content.replace(old_verify_token, new_verify_token)

# Fix the debug_token function
old_debug_token = '''@app.post("/api/debug-token", tags=["Authentication"])
async def debug_token(request: Request):
    """
    Debug endpoint for token verification.
    """
    print("=== Debug Token Request ===")
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        print(f"Authorization header: {auth_header[:20]}..." if auth_header else "No Authorization header")
        
        if not auth_header or not auth_header.startswith('Bearer '):
            print("No valid authorization header provided")
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        print(f"Token extracted: {token[:20]}...")

        # Verify the token with Firebase Admin SDK
        try:
            print("Attempting to verify token with Firebase Admin SDK...")
            decoded_token = auth.verify_id_token(token)
            uid = decoded_token['uid']
            print(f"Successfully verified token for UID: {uid}")
            print(f"Decoded token claims: {decoded_token}")
            
            # Return the decoded token
            return {
                "success": True,
                "uid": uid,
                "decoded_token": decoded_token
            }
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            print(f"Token that failed verification: {token[:30]}...")
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )
    except Exception as e:
        print(f"Error debugging token: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

new_debug_token = '''@app.post("/api/debug-token", tags=["Authentication"])
async def debug_token(request: Request):
    """
    Debug endpoint for token verification.
    """
    print("=== Debug Token Request ===")
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        print(f"Authorization header: {auth_header[:20]}..." if auth_header else "No Authorization header")
        
        if not auth_header or not auth_header.startswith('Bearer '):
            print("No valid authorization header provided")
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        print(f"Token extracted: {token[:20]}...")

        # Verify the token with Firebase Admin SDK
        try:
            print("Attempting to verify token with Firebase Admin SDK...")
            decoded_token = auth.verify_id_token(token)
            uid = decoded_token['uid']
            print(f"Successfully verified token for UID: {uid}")
            print(f"Decoded token claims: {decoded_token}")
            
            # Check if user exists in database
            db = SessionLocal()
            try:
                # Query the users table for the user with this UID
                print(f"Querying database for user with UID: {uid}")
                query = text("SELECT * FROM users WHERE user_id = :uid")
                print(f"SQL Query: {query}")
                print(f"Parameters: {{'uid': {uid}}}")
                
                result = db.execute(query, {"uid": uid}).fetchone()
                
                if result:
                    print(f"Found user in database: {result}")
                    user_exists = True
                else:
                    print(f"User with UID {uid} not found in database")
                    # Let's check what users are in the database
                    all_users = db.execute(text("SELECT user_id, email FROM users")).fetchall()
                    print(f"All users in database: {all_users}")
                    user_exists = False
            finally:
                db.close()
            
            # Return the decoded token
            return {
                "success": True,
                "uid": uid,
                "decoded_token": decoded_token,
                "user_exists_in_db": user_exists
            }
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            print(f"Token that failed verification: {token[:30]}...")
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )
    except Exception as e:
        print(f"Error debugging token: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

content = content.replace(old_debug_token, new_debug_token)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed user check endpoint, verify_token, and debug_token functions in main.py")
