.user-info {
    display: flex;
    align-items: center;
    color: #fff;
    margin-right: 15px;
    padding: 5px 10px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
}

.user-info .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #0d6efd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    color: white;
    font-weight: bold;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.user-info .user-details {
    display: flex;
    flex-direction: column;
}

@media (min-width: 576px) {
    .user-info .user-details {
        flex-direction: row;
        align-items: center;
    }
}

#logout-button {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.5);
    color: white;
    transition: all 0.3s ease;
}

#logout-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.auth-required, .auth-not-required {
    display: none;
}

.auth-required[style="display: block;"], .auth-not-required[style="display: block;"] {
    display: block !important;
}
