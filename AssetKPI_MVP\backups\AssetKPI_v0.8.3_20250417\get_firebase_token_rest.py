import requests
import json
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_firebase_token(email, password, api_key):
    """
    Get a Firebase ID token using the REST API
    """
    url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={api_key}"
    payload = {
        "email": email,
        "password": password,
        "returnSecureToken": True
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        data = response.json()
        id_token = data.get("idToken")
        
        if id_token:
            return id_token
        else:
            print("ID token not found in response")
            return None
    except Exception as e:
        print(f"Error getting Firebase token: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def main():
    # Get Firebase API key
    api_key = input("Enter your Firebase Web API Key: ")
    
    # User credentials
    email = "<EMAIL>"
    password = "TestTest"
    
    # Get token
    print(f"Getting token for {email}...")
    token = get_firebase_token(email, password, api_key)
    
    if token:
        print(f"Token: {token[:20]}...{token[-20:]}")
        
        # Save token to file
        with open("firebase_id_token.txt", "w") as f:
            f.write(token)
        print("Token saved to firebase_id_token.txt")
        
        # Create curl command
        curl_command = f'curl -X GET "{os.getenv("BASE_URL", "http://localhost:8000")}/api/kpi/history/MTTR_Calculated" -H "Authorization: Bearer {token}"'
        with open("test_curl_command.sh", "w") as f:
            f.write(curl_command)
        print("Curl command saved to test_curl_command.sh")
    else:
        print("Failed to get token")

if __name__ == "__main__":
    main()
