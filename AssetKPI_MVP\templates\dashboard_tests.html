<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Tests - AssetKPI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', path='css/style.css') }}" rel="stylesheet">
    <style>
        .test-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .test-success {
            border-left-color: #28a745;
        }
        .test-error {
            border-left-color: #dc3545;
        }
        .test-warning {
            border-left-color: #ffc107;
        }
        .test-result {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-controls {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>AssetKPI
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/auth-tests">Auth Tests</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard-tests">Dashboard Tests</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if current_user %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ current_user.email }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><span class="dropdown-item-text">Role: {{ current_user.role }}</span></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout">Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="/login">Login</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-vial me-2"></i>Dashboard API Tests</h1>
                <p class="text-muted">Test dashboard API endpoints and functionality</p>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="test-controls">
                    <h5><i class="fas fa-play me-2"></i>Test Controls</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-primary me-2" onclick="runAllTests()">
                                <i class="fas fa-play me-1"></i>Run All Tests
                            </button>
                            <button class="btn btn-secondary me-2" onclick="clearResults()">
                                <i class="fas fa-trash me-1"></i>Clear Results
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                <label class="form-check-label" for="autoRefresh">
                                    Auto-refresh every 30 seconds
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="card test-card" id="assets-test">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Assets API Test</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="testAssetsAPI()">
                            <i class="fas fa-play me-1"></i>Test
                        </button>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tests the assets API endpoint for retrieving asset data.</p>
                        <div class="test-result" id="assets-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card test-card" id="assets-count-test">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>Assets Count Test</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="testAssetsCount()">
                            <i class="fas fa-play me-1"></i>Test
                        </button>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tests the assets count API endpoint.</p>
                        <div class="test-result" id="assets-count-result" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="card test-card" id="inventory-test">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-boxes me-2"></i>Inventory Summary Test</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="testInventorySummary()">
                            <i class="fas fa-play me-1"></i>Test
                        </button>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tests the inventory summary API endpoint.</p>
                        <div class="test-result" id="inventory-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card test-card" id="workorders-test">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-wrench me-2"></i>Work Orders Count Test</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="testWorkOrdersCount()">
                            <i class="fas fa-play me-1"></i>Test
                        </button>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tests the work orders count API endpoint.</p>
                        <div class="test-result" id="workorders-result" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="card test-card" id="kpi-test">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>KPI Analytics Test</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="testKPIAnalytics()">
                            <i class="fas fa-play me-1"></i>Test
                        </button>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tests the KPI analytics API endpoint.</p>
                        <div class="test-result" id="kpi-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card test-card" id="performance-test">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Asset Performance Test</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="testAssetPerformance()">
                            <i class="fas fa-play me-1"></i>Test
                        </button>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tests the asset performance API endpoint.</p>
                        <div class="test-result" id="performance-result" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Test Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success" id="passed-count">0</h4>
                                    <small class="text-muted">Passed</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-danger" id="failed-count">0</h4>
                                    <small class="text-muted">Failed</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning" id="warning-count">0</h4>
                                    <small class="text-muted">Warnings</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info" id="total-count">6</h4>
                                    <small class="text-muted">Total Tests</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', path='js/dashboard_tests.js') }}"></script>
</body>
</html>
