{% extends "layout.html" %}

{% block title %}EOQ Configuration | AssetKPI{% endblock %}

{% block styles %}
<style>
    .config-card {
        margin-bottom: 20px;
    }
    .form-group {
        margin-bottom: 15px;
    }
    .parameter-description {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 5px;
    }
    .parameter-value {
        font-weight: bold;
    }
    .parameter-updated {
        font-size: 0.8rem;
        color: #6c757d;
    }
    .auth-required[data-role="ADMIN"] .admin-only {
        display: block;
    }
    .admin-only {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mt-4">EOQ Configuration</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/inventory">Inventory</a></li>
                <li class="breadcrumb-item active">EOQ Configuration</li>
            </ol>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card config-card">
                <div class="card-header">
                    <h5>Global EOQ Parameters</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">These parameters are used for all EOQ calculations unless overridden for specific parts.</p>
                    
                    <div id="globalParametersContainer">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-only mt-4">
                        <button id="editGlobalParamsBtn" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Global Parameters
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card config-card">
                <div class="card-header">
                    <h5>EOQ Formula</h5>
                </div>
                <div class="card-body">
                    <p>The Economic Order Quantity (EOQ) is calculated using the following formula:</p>
                    
                    <div class="text-center my-3">
                        <img src="/static/img/eoq_formula.png" alt="EOQ Formula" class="img-fluid" style="max-width: 300px;">
                    </div>
                    
                    <p><strong>Where:</strong></p>
                    <ul>
                        <li><strong>D</strong> = Annual demand</li>
                        <li><strong>S</strong> = Ordering cost per order</li>
                        <li><strong>H</strong> = Annual holding cost per unit</li>
                    </ul>
                    
                    <p><strong>Annual holding cost per unit (H)</strong> is calculated as:</p>
                    <p class="text-center">H = Unit Cost × Holding Cost Percentage</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card config-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Part-Specific EOQ Parameters</h5>
                    <div class="admin-only">
                        <button id="addPartSpecificBtn" class="btn btn-sm btn-success">
                            <i class="fas fa-plus"></i> Add Override
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted">These parameters override the global parameters for specific parts.</p>
                    
                    <div id="partSpecificContainer">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card config-card">
                <div class="card-header">
                    <h5>EOQ Calculation Results</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Summary of EOQ calculation results across all parts.</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3 id="totalParts">-</h3>
                                    <p class="mb-0">Total Parts</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3 id="partsWithEOQ">-</h3>
                                    <p class="mb-0">Parts with EOQ</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <button id="recalculateBtn" class="btn btn-primary">
                            <i class="fas fa-sync-alt"></i> Recalculate All EOQs
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Global Parameters Modal -->
<div class="modal fade" id="editGlobalParamsModal" tabindex="-1" aria-labelledby="editGlobalParamsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editGlobalParamsModalLabel">Edit Global EOQ Parameters</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="globalParamsForm">
                    <div class="form-group mb-3">
                        <label for="orderingCost">Ordering Cost ($)</label>
                        <input type="number" class="form-control" id="orderingCost" name="ordering_cost" step="0.01" min="0" required>
                        <small class="form-text text-muted">The cost of placing an order, including administrative costs.</small>
                    </div>
                    <div class="form-group mb-3">
                        <label for="holdingCostPercent">Holding Cost Percentage (%)</label>
                        <input type="number" class="form-control" id="holdingCostPercent" name="holding_cost_percent" step="0.01" min="0" max="100" required>
                        <small class="form-text text-muted">The annual cost of holding inventory as a percentage of item value.</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveGlobalParamsBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Part-Specific Override Modal -->
<div class="modal fade" id="addPartSpecificModal" tabindex="-1" aria-labelledby="addPartSpecificModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPartSpecificModalLabel">Add Part-Specific Override</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="partSpecificForm">
                    <div class="form-group mb-3">
                        <label for="partSelect">Select Part</label>
                        <select class="form-select" id="partSelect" name="part_id" required>
                            <option value="">-- Select a part --</option>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="partOrderingCost">Ordering Cost ($)</label>
                        <input type="number" class="form-control" id="partOrderingCost" name="ordering_cost" step="0.01" min="0">
                        <small class="form-text text-muted">Leave blank to use global value.</small>
                    </div>
                    <div class="form-group mb-3">
                        <label for="partHoldingCostPercent">Holding Cost Percentage (%)</label>
                        <input type="number" class="form-control" id="partHoldingCostPercent" name="holding_cost_percent" step="0.01" min="0" max="100">
                        <small class="form-text text-muted">Leave blank to use global value.</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePartSpecificBtn">Save Override</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize authentication UI
        AssetKPIAuth.initAuth(updateUI);
        
        // Load global parameters
        loadGlobalParameters();
        
        // Load part-specific parameters
        loadPartSpecificParameters();
        
        // Load EOQ calculation summary
        loadEOQSummary();
        
        // Add event listeners
        document.getElementById('editGlobalParamsBtn').addEventListener('click', showEditGlobalParamsModal);
        document.getElementById('saveGlobalParamsBtn').addEventListener('click', saveGlobalParameters);
        document.getElementById('addPartSpecificBtn').addEventListener('click', showAddPartSpecificModal);
        document.getElementById('savePartSpecificBtn').addEventListener('click', savePartSpecificParameters);
        document.getElementById('recalculateBtn').addEventListener('click', recalculateAllEOQs);
        
        // Function to load global parameters
    function loadGlobalParameters() {
        AssetKPIAuth.authenticatedFetch("/api/inventory/config")
            .then(response => {
                if (!response.ok) {
                    throw new Error("Failed to fetch inventory configuration");
                }
                return response.json();
            })
            .then(data => {
                const container = document.getElementById("globalParametersContainer");

                // Check if data is an array
                if (!Array.isArray(data)) {
                    console.error("Expected array but got:", typeof data, data);
                    container.innerHTML = "<div class=\"alert alert-danger\">Error: Received invalid data format from server</div>";
                    return;
                }

                // Filter for EOQ-related parameters
                const eoqParams = data.filter(param =>
                    param.parameter_name === "ordering_cost" ||
                    param.parameter_name === "holding_cost_percent"
                );

                if (eoqParams.length === 0) {
                    container.innerHTML = "<p class=\"text-center\">No EOQ parameters found. Please add them in the Inventory Configuration page.</p>";
                    return;
                }

                let html = "<div class=\"list-group\">";

                eoqParams.forEach(param => {
                    let displayName = "";
                    let displayValue = "";

                    if (param.parameter_name === "ordering_cost") {
                        displayName = "Ordering Cost";
                        displayValue = `$${parseFloat(param.parameter_value).toFixed(2)}`;
                    } else if (param.parameter_name === "holding_cost_percent") {
                        displayName = "Annual Holding Cost";
                        displayValue = `${parseFloat(param.parameter_value) * 100}%`;
                    }

                    html += `
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">${displayName}</h5>
                                <span class="parameter-value">${displayValue}</span>
                            </div>
                            <p class="parameter-description mb-1">${param.parameter_description || "No description available."}</p>
                            <small class="parameter-updated">Last updated: ${new Date(param.updated_at).toLocaleString()}</small>
                        </div>
                    `;
                });

                html += "</div>";
                container.innerHTML = html;
            })
            .catch(error => {
                console.error("Error loading global parameters:", error);
                document.getElementById("globalParametersContainer").innerHTML =
                    `<div class="alert alert-danger">Error loading parameters: ${error.message}</div>`;
            });
    }

    function loadPartSpecificParameters() {
            AssetKPIAuth.authenticatedFetch('/api/inventory/parts')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch parts');
                    }
                    return response.json();
                })
                .then(data => {
                    const container = document.getElementById('partSpecificContainer');
                    
                    // Filter for parts with custom EOQ parameters
                    const partsWithCustomParams = data.filter(part => 
                        part.ordering_cost !== null || 
                        part.holding_cost_percent !== null
                    );
                    
                    if (partsWithCustomParams.length === 0) {
                        container.innerHTML = '<p class="text-center">No part-specific overrides found.</p>';
                        return;
                    }
                    
                    let html = '<div class="list-group">';
                    
                    partsWithCustomParams.forEach(part => {
                        html += `
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">${part.partname}</h5>
                                    <small>${part.partnumber}</small>
                                </div>
                                <p class="mb-1">
                                    ${part.ordering_cost !== null ? `<strong>Ordering Cost:</strong> $${parseFloat(part.ordering_cost).toFixed(2)}<br>` : ''}
                                    ${part.holding_cost_percent !== null ? `<strong>Holding Cost:</strong> ${parseFloat(part.holding_cost_percent) * 100}%` : ''}
                                </p>
                                <div class="d-flex justify-content-end mt-2 admin-only">
                                    <button class="btn btn-sm btn-outline-primary edit-override" data-part-id="${part.partid}">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger ms-2 delete-override" data-part-id="${part.partid}">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                    container.innerHTML = html;
                    
                    // Add event listeners for edit and delete buttons
                    document.querySelectorAll('.edit-override').forEach(button => {
                        button.addEventListener('click', function() {
                            const partId = this.getAttribute('data-part-id');
                            editPartSpecificParameters(partId);
                        });
                    });
                    
                    document.querySelectorAll('.delete-override').forEach(button => {
                        button.addEventListener('click', function() {
                            const partId = this.getAttribute('data-part-id');
                            deletePartSpecificParameters(partId);
                        });
                    });
                })
                .catch(error => {
                    console.error('Error loading part-specific parameters:', error);
                    document.getElementById('partSpecificContainer').innerHTML = 
                        `<div class="alert alert-danger">Error loading parameters: ${error.message}</div>`;
                });
        }
        
        // Function to load EOQ calculation summary
        function loadEOQSummary() {
            AssetKPIAuth.authenticatedFetch('/api/inventory/eoq-summary')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch EOQ summary');
                    }
                    return response.json();
                })
                .then(data => {
                    document.getElementById('totalParts').textContent = data.total_parts;
                    document.getElementById('partsWithEOQ').textContent = data.parts_with_eoq;
                })
                .catch(error => {
                    console.error('Error loading EOQ summary:', error);
                    document.getElementById('totalParts').textContent = 'Error';
                    document.getElementById('partsWithEOQ').textContent = 'Error';
                });
        }
        
        // Function to show edit global parameters modal
        function showEditGlobalParamsModal() {
            AssetKPIAuth.authenticatedFetch('/api/inventory/config')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch inventory configuration');
                    }
                    return response.json();
                })
                .then(data => {
                    // Find ordering cost and holding cost parameters
                    const orderingCostParam = data.find(param => param.parameter_name === 'ordering_cost');
                    const holdingCostParam = data.find(param => param.parameter_name === 'holding_cost_percent');
                    
                    // Set form values
                    if (orderingCostParam) {
                        document.getElementById('orderingCost').value = parseFloat(orderingCostParam.parameter_value);
                    }
                    
                    if (holdingCostParam) {
                        document.getElementById('holdingCostPercent').value = parseFloat(holdingCostParam.parameter_value) * 100;
                    }
                    
                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('editGlobalParamsModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading parameters for editing:', error);
                    alert(`Error loading parameters: ${error.message}`);
                });
        }
        
        // Function to save global parameters
        function saveGlobalParameters() {
            const orderingCost = document.getElementById('orderingCost').value;
            const holdingCostPercent = document.getElementById('holdingCostPercent').value / 100; // Convert to decimal
            
            // Validate inputs
            if (!orderingCost || !holdingCostPercent) {
                alert('Please fill in all fields');
                return;
            }
            
            // Get parameter IDs
            AssetKPIAuth.authenticatedFetch('/api/inventory/config')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch inventory configuration');
                    }
                    return response.json();
                })
                .then(data => {
                    // Find ordering cost and holding cost parameters
                    const orderingCostParam = data.find(param => param.parameter_name === 'ordering_cost');
                    const holdingCostParam = data.find(param => param.parameter_name === 'holding_cost_percent');
                    
                    // Update parameters
                    const updatePromises = [];
                    
                    if (orderingCostParam) {
                        updatePromises.push(
                            AssetKPIAuth.authenticatedFetch(`/api/inventory/config/${orderingCostParam.id}`, {
                                method: 'PUT',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    parameter_value: orderingCost.toString()
                                })
                            })
                        );
                    }
                    
                    if (holdingCostParam) {
                        updatePromises.push(
                            AssetKPIAuth.authenticatedFetch(`/api/inventory/config/${holdingCostParam.id}`, {
                                method: 'PUT',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    parameter_value: holdingCostPercent.toString()
                                })
                            })
                        );
                    }
                    
                    return Promise.all(updatePromises);
                })
                .then(() => {
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editGlobalParamsModal'));
                    modal.hide();
                    
                    // Reload parameters
                    loadGlobalParameters();
                    
                    // Show success message
                    alert('Global parameters updated successfully');
                })
                .catch(error => {
                    console.error('Error saving global parameters:', error);
                    alert(`Error saving parameters: ${error.message}`);
                });
        }
        
        // Function to show add part-specific modal
        function showAddPartSpecificModal() {
            // Load parts for dropdown
            AssetKPIAuth.authenticatedFetch('/api/inventory/parts')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch parts');
                    }
                    return response.json();
                })
                .then(data => {
                    const partSelect = document.getElementById('partSelect');
                    
                    // Clear existing options
                    partSelect.innerHTML = '<option value="">-- Select a part --</option>';
                    
                    // Add parts to dropdown
                    data.forEach(part => {
                        const option = document.createElement('option');
                        option.value = part.partid;
                        option.textContent = `${part.partname} (${part.partnumber})`;
                        partSelect.appendChild(option);
                    });
                    
                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('addPartSpecificModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading parts:', error);
                    alert(`Error loading parts: ${error.message}`);
                });
        }
        
        // Function to save part-specific parameters
        function savePartSpecificParameters() {
            const partId = document.getElementById('partSelect').value;
            const orderingCost = document.getElementById('partOrderingCost').value;
            const holdingCostPercent = document.getElementById('partHoldingCostPercent').value;
            
            // Validate inputs
            if (!partId) {
                alert('Please select a part');
                return;
            }
            
            // Prepare data
            const data = {};
            
            if (orderingCost) {
                data.ordering_cost = parseFloat(orderingCost);
            }
            
            if (holdingCostPercent) {
                data.holding_cost_percent = parseFloat(holdingCostPercent) / 100; // Convert to decimal
            }
            
            // Update part
            AssetKPIAuth.authenticatedFetch(`/api/inventory/parts/${partId}/eoq-params`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to update part parameters');
                }
                return response.json();
            })
            .then(() => {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addPartSpecificModal'));
                modal.hide();
                
                // Reload part-specific parameters
                loadPartSpecificParameters();
                
                // Show success message
                alert('Part-specific parameters updated successfully');
            })
            .catch(error => {
                console.error('Error saving part-specific parameters:', error);
                alert(`Error saving parameters: ${error.message}`);
            });
        }
        
        // Function to edit part-specific parameters
        function editPartSpecificParameters(partId) {
            // Load part data
            AssetKPIAuth.authenticatedFetch(`/api/inventory/parts/${partId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch part');
                    }
                    return response.json();
                })
                .then(part => {
                    // Set form values
                    document.getElementById('partSelect').value = part.partid;
                    
                    if (part.ordering_cost !== null) {
                        document.getElementById('partOrderingCost').value = parseFloat(part.ordering_cost);
                    } else {
                        document.getElementById('partOrderingCost').value = '';
                    }
                    
                    if (part.holding_cost_percent !== null) {
                        document.getElementById('partHoldingCostPercent').value = parseFloat(part.holding_cost_percent) * 100;
                    } else {
                        document.getElementById('partHoldingCostPercent').value = '';
                    }
                    
                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('addPartSpecificModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading part for editing:', error);
                    alert(`Error loading part: ${error.message}`);
                });
        }
        
        // Function to delete part-specific parameters
        function deletePartSpecificParameters(partId) {
            if (!confirm('Are you sure you want to delete these part-specific parameters?')) {
                return;
            }
            
            // Delete parameters
            AssetKPIAuth.authenticatedFetch(`/api/inventory/parts/${partId}/eoq-params`, {
                method: 'DELETE'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to delete part parameters');
                }
                return response.json();
            })
            .then(() => {
                // Reload part-specific parameters
                loadPartSpecificParameters();
                
                // Show success message
                alert('Part-specific parameters deleted successfully');
            })
            .catch(error => {
                console.error('Error deleting part-specific parameters:', error);
                alert(`Error deleting parameters: ${error.message}`);
            });
        }
        
        // Function to recalculate all EOQs
        function recalculateAllEOQs() {
            if (!confirm('Are you sure you want to recalculate EOQ for all parts? This may take a while.')) {
                return;
            }
            
            // Disable button
            const button = document.getElementById('recalculateBtn');
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Recalculating...';
            
            // Call API to recalculate
            AssetKPIAuth.authenticatedFetch('/api/inventory/run-optimization', {
                method: 'GET'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to recalculate EOQs');
                }
                return response.json();
            })
            .then(() => {
                // Reload EOQ summary
                loadEOQSummary();
                
                // Show success message
                alert('EOQs recalculated successfully');
            })
            .catch(error => {
                console.error('Error recalculating EOQs:', error);
                alert(`Error recalculating EOQs: ${error.message}`);
            })
            .finally(() => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }
        
        // Function to update UI based on authentication state
        function updateUI(user) {
            if (user) {
                // User is signed in, load data
                loadGlobalParameters();
                loadPartSpecificParameters();
                loadEOQSummary();
            } else {
                // User is signed out, show message
                document.getElementById('globalParametersContainer').innerHTML = 
                    '<p class="text-center">Please sign in to view EOQ configuration</p>';
                document.getElementById('partSpecificContainer').innerHTML = 
                    '<p class="text-center">Please sign in to view part-specific parameters</p>';
            }
        }
    });
</script>
{% endblock %}
