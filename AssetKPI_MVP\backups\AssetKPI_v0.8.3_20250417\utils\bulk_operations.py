from typing import List, Dict, Any, Type, Optional, Union
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel
from fastapi import HTTPException, status

def bulk_create(
    db: Session,
    model_class: Type,
    items: List[Dict[str, Any]],
    return_instances: bool = False
) -> Union[List[Dict[str, Any]], List[Any]]:
    """
    Bulk create multiple items in the database.
    
    Args:
        db: SQLAlchemy database session
        model_class: SQLAlchemy model class
        items: List of dictionaries with item data
        return_instances: Whether to return the created model instances
        
    Returns:
        List of created items (as dictionaries or model instances)
        
    Raises:
        HTTPException: If there's an error during the operation
    """
    try:
        # Create model instances
        instances = [model_class(**item) for item in items]
        
        # Add all instances to the session
        db.add_all(instances)
        db.commit()
        
        # Refresh instances to get generated IDs
        for instance in instances:
            db.refresh(instance)
        
        if return_instances:
            return instances
        
        # Convert instances to dictionaries
        result = []
        for instance in instances:
            item_dict = {}
            for column in instance.__table__.columns:
                item_dict[column.name] = getattr(instance, column.name)
            result.append(item_dict)
        
        return result
    
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error during bulk create: {str(e)}"
        )


def bulk_update(
    db: Session,
    model_class: Type,
    items: List[Dict[str, Any]],
    id_field: str = "id",
    return_instances: bool = False
) -> Union[List[Dict[str, Any]], List[Any]]:
    """
    Bulk update multiple items in the database.
    
    Args:
        db: SQLAlchemy database session
        model_class: SQLAlchemy model class
        items: List of dictionaries with item data (must include ID field)
        id_field: Name of the ID field
        return_instances: Whether to return the updated model instances
        
    Returns:
        List of updated items (as dictionaries or model instances)
        
    Raises:
        HTTPException: If there's an error during the operation
    """
    try:
        # Get all IDs
        ids = [item[id_field] for item in items if id_field in item]
        
        # Fetch existing instances
        instances = db.query(model_class).filter(
            getattr(model_class, id_field).in_(ids)
        ).all()
        
        # Create a mapping of ID to instance
        instance_map = {getattr(instance, id_field): instance for instance in instances}
        
        # Update instances
        updated_instances = []
        for item in items:
            if id_field not in item:
                continue
            
            instance_id = item[id_field]
            if instance_id in instance_map:
                instance = instance_map[instance_id]
                
                # Update attributes
                for key, value in item.items():
                    if key != id_field and hasattr(instance, key):
                        setattr(instance, key, value)
                
                updated_instances.append(instance)
        
        # Commit changes
        db.commit()
        
        # Refresh instances
        for instance in updated_instances:
            db.refresh(instance)
        
        if return_instances:
            return updated_instances
        
        # Convert instances to dictionaries
        result = []
        for instance in updated_instances:
            item_dict = {}
            for column in instance.__table__.columns:
                item_dict[column.name] = getattr(instance, column.name)
            result.append(item_dict)
        
        return result
    
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error during bulk update: {str(e)}"
        )


def bulk_delete(
    db: Session,
    model_class: Type,
    ids: List[Any],
    id_field: str = "id"
) -> Dict[str, Any]:
    """
    Bulk delete multiple items from the database.
    
    Args:
        db: SQLAlchemy database session
        model_class: SQLAlchemy model class
        ids: List of IDs to delete
        id_field: Name of the ID field
        
    Returns:
        Dictionary with deletion results
        
    Raises:
        HTTPException: If there's an error during the operation
    """
    try:
        # Delete instances
        result = db.query(model_class).filter(
            getattr(model_class, id_field).in_(ids)
        ).delete(synchronize_session=False)
        
        # Commit changes
        db.commit()
        
        return {
            "deleted_count": result,
            "ids": ids
        }
    
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error during bulk delete: {str(e)}"
        )
