import re

# Read the main.py file
with open('main.py', 'r') as file:
    content = file.read()

# Find the verify_token function
verify_token_pattern = r'@app\.post\("/api/verify-token", tags=\["Authentication"\]\)\nasync def verify_token\(request: Request\):.*?finally:.*?db\.close\(\)(.*?)except Exception as e:'
verify_token_match = re.search(verify_token_pattern, content, re.DOTALL)

if verify_token_match:
    # Get the entire function
    verify_token_function = verify_token_match.group(0)
    
    # Create a new function with detailed logging
    new_function = '''@app.post("/api/verify-token", tags=["Authentication"])
async def verify_token(request: Request):
    """
    Verifies a Firebase ID token and returns user information.
    """
    print("=== Token Verification Request ===")
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        print(f"Authorization header: {auth_header[:20]}..." if auth_header else "No Authorization header")
        
        if not auth_header or not auth_header.startswith('Bearer '):
            print("No valid authorization header provided")
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        print(f"Token extracted: {token[:20]}...")

        # Verify the token with Firebase Admin SDK
        try:
            print("Attempting to verify token with Firebase Admin SDK...")
            decoded_token = auth.verify_id_token(token)
            uid = decoded_token['uid']
            print(f"Successfully verified token for UID: {uid}")
            print(f"Decoded token claims: {decoded_token}")
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            print(f"Token that failed verification: {token[:30]}...")
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )

        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            print(f"Querying database for user with UID: {uid}")
            query = text("SELECT * FROM users WHERE uid = :uid")
            print(f"SQL Query: {query}")
            print(f"Parameters: {{'uid': {uid}}}")
            
            result = db.execute(query, {"uid": uid}).fetchone()

            if result:
                print(f"Found user in database: {result}")
                # Return user information
                user_info = {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
                print(f"Returning user info: {user_info}")
                return user_info
            else:
                print(f"User with UID {uid} not found in database")
                # Let's check what users are in the database
                all_users = db.execute(text("SELECT uid, email FROM users")).fetchall()
                print(f"All users in database: {all_users}")
                
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:'''
    
    # Replace the function in the content
    content = content.replace(verify_token_function, new_function)
    
    # Write the updated content back to the file
    with open('main.py', 'w') as file:
        file.write(content)
    
    print("Added detailed logging to token verification endpoint in main.py")
else:
    print("Could not find the verify_token function in main.py")
