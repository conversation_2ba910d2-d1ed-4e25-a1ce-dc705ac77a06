/**
 * Tutorials JavaScript
 * 
 * This file contains JavaScript functionality for the tutorial system
 * and guided tours across the AssetKPI application.
 */

/**
 * Tutorial system main class
 */
class TutorialSystem {
  constructor() {
    this.currentStep = 0;
    this.totalSteps = 0;
    this.tutorialData = null;
    this.isActive = false;
    this.overlay = null;
    this.tooltip = null;
  }

  /**
   * Initialize the tutorial system
   */
  init() {
    this.createOverlay();
    this.createTooltip();
    this.setupEventListeners();
    this.loadTutorialData();
  }

  /**
   * Create overlay element
   */
  createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.className = 'tutorial-overlay';
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      z-index: 9998;
      display: none;
    `;
    document.body.appendChild(this.overlay);
  }

  /**
   * Create tooltip element
   */
  createTooltip() {
    this.tooltip = document.createElement('div');
    this.tooltip.className = 'tutorial-tooltip';
    this.tooltip.style.cssText = `
      position: fixed;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      padding: 20px;
      max-width: 300px;
      z-index: 9999;
      display: none;
    `;
    
    this.tooltip.innerHTML = `
      <div class="tutorial-content">
        <h4 class="tutorial-title"></h4>
        <p class="tutorial-description"></p>
        <div class="tutorial-progress">
          <span class="current-step">1</span> of <span class="total-steps">1</span>
        </div>
        <div class="tutorial-actions">
          <button class="btn-tutorial-prev">Previous</button>
          <button class="btn-tutorial-next">Next</button>
          <button class="btn-tutorial-skip">Skip Tour</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(this.tooltip);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Tutorial navigation buttons
    this.tooltip.querySelector('.btn-tutorial-prev').addEventListener('click', () => {
      this.previousStep();
    });

    this.tooltip.querySelector('.btn-tutorial-next').addEventListener('click', () => {
      this.nextStep();
    });

    this.tooltip.querySelector('.btn-tutorial-skip').addEventListener('click', () => {
      this.endTutorial();
    });

    // Overlay click to end tutorial
    this.overlay.addEventListener('click', () => {
      this.endTutorial();
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (this.isActive) {
        switch (e.key) {
          case 'ArrowRight':
          case 'Space':
            e.preventDefault();
            this.nextStep();
            break;
          case 'ArrowLeft':
            e.preventDefault();
            this.previousStep();
            break;
          case 'Escape':
            e.preventDefault();
            this.endTutorial();
            break;
        }
      }
    });

    // Window resize
    window.addEventListener('resize', () => {
      if (this.isActive) {
        this.positionTooltip();
      }
    });
  }

  /**
   * Load tutorial data from the server or local storage
   */
  async loadTutorialData() {
    try {
      // Try to load from server first
      const response = await fetch('/api/tutorials/data');
      if (response.ok) {
        this.tutorialData = await response.json();
      } else {
        // Fallback to default tutorial data
        this.tutorialData = this.getDefaultTutorialData();
      }
    } catch (error) {
      console.warn('Could not load tutorial data from server, using defaults');
      this.tutorialData = this.getDefaultTutorialData();
    }
  }

  /**
   * Get default tutorial data
   */
  getDefaultTutorialData() {
    return {
      dashboard: [
        {
          target: '.navbar-brand',
          title: 'Welcome to AssetKPI',
          description: 'This is your asset management dashboard. Let\'s take a quick tour!'
        },
        {
          target: '.nav-link[href="/dashboard"]',
          title: 'Dashboard',
          description: 'View key performance indicators and system overview here.'
        },
        {
          target: '.nav-link[href="/inventory"]',
          title: 'Inventory Management',
          description: 'Manage spare parts, track stock levels, and optimize inventory.'
        },
        {
          target: '.nav-link[href="/workorders"]',
          title: 'Work Orders',
          description: 'Create, track, and manage maintenance work orders.'
        },
        {
          target: '.nav-link[href="/assets"]',
          title: 'Asset Management',
          description: 'View and manage your physical assets and equipment.'
        }
      ]
    };
  }

  /**
   * Start a tutorial
   */
  startTutorial(tutorialName = 'dashboard') {
    if (!this.tutorialData || !this.tutorialData[tutorialName]) {
      console.error('Tutorial data not found for:', tutorialName);
      return;
    }

    this.currentStep = 0;
    this.totalSteps = this.tutorialData[tutorialName].length;
    this.isActive = true;

    // Show overlay
    this.overlay.style.display = 'block';
    
    // Start first step
    this.showStep(this.tutorialData[tutorialName][this.currentStep]);
    
    // Track tutorial start
    this.trackEvent('tutorial_started', { tutorial: tutorialName });
  }

  /**
   * Show a tutorial step
   */
  showStep(step) {
    const target = document.querySelector(step.target);
    
    if (!target) {
      console.warn('Tutorial target not found:', step.target);
      this.nextStep();
      return;
    }

    // Highlight target element
    this.highlightElement(target);
    
    // Update tooltip content
    this.tooltip.querySelector('.tutorial-title').textContent = step.title;
    this.tooltip.querySelector('.tutorial-description').textContent = step.description;
    this.tooltip.querySelector('.current-step').textContent = this.currentStep + 1;
    this.tooltip.querySelector('.total-steps').textContent = this.totalSteps;
    
    // Update button states
    const prevBtn = this.tooltip.querySelector('.btn-tutorial-prev');
    const nextBtn = this.tooltip.querySelector('.btn-tutorial-next');
    
    prevBtn.style.display = this.currentStep === 0 ? 'none' : 'inline-block';
    nextBtn.textContent = this.currentStep === this.totalSteps - 1 ? 'Finish' : 'Next';
    
    // Position and show tooltip
    this.positionTooltip(target);
    this.tooltip.style.display = 'block';
    
    // Scroll target into view
    target.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  /**
   * Highlight target element
   */
  highlightElement(element) {
    // Remove previous highlights
    document.querySelectorAll('.tutorial-highlight').forEach(el => {
      el.classList.remove('tutorial-highlight');
    });
    
    // Add highlight to current element
    element.classList.add('tutorial-highlight');
    
    // Add CSS for highlight if not already present
    if (!document.querySelector('#tutorial-highlight-styles')) {
      const style = document.createElement('style');
      style.id = 'tutorial-highlight-styles';
      style.textContent = `
        .tutorial-highlight {
          position: relative;
          z-index: 9997;
          box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.8) !important;
          border-radius: 4px !important;
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * Position tooltip relative to target
   */
  positionTooltip(target = null) {
    if (!target) return;
    
    const rect = target.getBoundingClientRect();
    const tooltipRect = this.tooltip.getBoundingClientRect();
    
    let top = rect.bottom + 10;
    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    
    // Adjust if tooltip goes off screen
    if (left < 10) left = 10;
    if (left + tooltipRect.width > window.innerWidth - 10) {
      left = window.innerWidth - tooltipRect.width - 10;
    }
    
    if (top + tooltipRect.height > window.innerHeight - 10) {
      top = rect.top - tooltipRect.height - 10;
    }
    
    this.tooltip.style.top = `${top}px`;
    this.tooltip.style.left = `${left}px`;
  }

  /**
   * Go to next step
   */
  nextStep() {
    if (this.currentStep < this.totalSteps - 1) {
      this.currentStep++;
      const tutorialName = Object.keys(this.tutorialData)[0]; // Assuming single tutorial for now
      this.showStep(this.tutorialData[tutorialName][this.currentStep]);
    } else {
      this.endTutorial();
    }
  }

  /**
   * Go to previous step
   */
  previousStep() {
    if (this.currentStep > 0) {
      this.currentStep--;
      const tutorialName = Object.keys(this.tutorialData)[0]; // Assuming single tutorial for now
      this.showStep(this.tutorialData[tutorialName][this.currentStep]);
    }
  }

  /**
   * End tutorial
   */
  endTutorial() {
    this.isActive = false;
    
    // Hide overlay and tooltip
    this.overlay.style.display = 'none';
    this.tooltip.style.display = 'none';
    
    // Remove highlights
    document.querySelectorAll('.tutorial-highlight').forEach(el => {
      el.classList.remove('tutorial-highlight');
    });
    
    // Track tutorial completion
    this.trackEvent('tutorial_completed', { 
      completed_steps: this.currentStep + 1,
      total_steps: this.totalSteps
    });
    
    // Mark tutorial as completed in localStorage
    localStorage.setItem('tutorial_completed', 'true');
  }

  /**
   * Track tutorial events
   */
  trackEvent(eventName, data = {}) {
    // Send to analytics if available
    if (window.gtag) {
      window.gtag('event', eventName, data);
    }
    
    // Log for debugging
    console.log('Tutorial event:', eventName, data);
  }

  /**
   * Check if user should see tutorial
   */
  shouldShowTutorial() {
    return !localStorage.getItem('tutorial_completed');
  }
}

/**
 * Tutorial helper functions
 */
const TutorialHelpers = {
  /**
   * Show tutorial trigger button
   */
  showTutorialTrigger() {
    const trigger = document.createElement('button');
    trigger.className = 'btn btn-info tutorial-trigger';
    trigger.innerHTML = '? Take Tour';
    trigger.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 1000;
      border-radius: 50px;
      padding: 10px 15px;
    `;
    
    trigger.addEventListener('click', () => {
      window.tutorialSystem.startTutorial();
    });
    
    document.body.appendChild(trigger);
  },

  /**
   * Auto-start tutorial for new users
   */
  autoStartTutorial() {
    if (window.tutorialSystem && window.tutorialSystem.shouldShowTutorial()) {
      // Delay to ensure page is fully loaded
      setTimeout(() => {
        window.tutorialSystem.startTutorial();
      }, 2000);
    }
  }
};

/**
 * Initialize tutorial system when DOM is ready
 */
document.addEventListener('DOMContentLoaded', function() {
  // Create global tutorial system instance
  window.tutorialSystem = new TutorialSystem();
  window.tutorialSystem.init();
  
  // Show tutorial trigger button
  TutorialHelpers.showTutorialTrigger();
  
  // Auto-start tutorial for new users (optional)
  // TutorialHelpers.autoStartTutorial();
});

// Export for use in other modules
window.TutorialSystem = TutorialSystem;
window.TutorialHelpers = TutorialHelpers;
