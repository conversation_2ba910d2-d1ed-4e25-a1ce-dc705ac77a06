"""
AssetKPI SDK main module.

This module provides the main SDK class that combines all API clients.
"""

from typing import Dict, Optional

from .client import Asset<PERSON><PERSON><PERSON>lient
from .assets import Assets<PERSON>lient
from .inventory import InventoryClient
from .kpi import KPIClient
from .workorders import WorkOrdersClient
from .users import UsersClient
from .analytics import AnalyticsClient
from .recommendations import RecommendationsClient


class AssetKPISDK:
    """
    Main SDK class for the AssetKPI API.
    
    This class provides access to all API clients.
    """
    
    def __init__(
        self,
        base_url: str,
        firebase_id_token: Optional[str] = None,
        api_key: Optional[str] = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_backoff_factor: float = 0.5,
        retry_status_codes: Optional[list] = None,
    ):
        """
        Initialize the AssetKPI SDK.
        
        Args:
            base_url: The base URL for the API (e.g., "http://localhost:8000/api")
            firebase_id_token: Firebase ID token for authentication
            api_key: API key for authentication
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_backoff_factor: Backoff factor for retries
            retry_status_codes: HTTP status codes to retry
        """
        self.client = AssetKPIClient(
            base_url=base_url,
            firebase_id_token=firebase_id_token,
            api_key=api_key,
            timeout=timeout,
            max_retries=max_retries,
            retry_backoff_factor=retry_backoff_factor,
            retry_status_codes=retry_status_codes,
        )
        
        # Initialize API clients
        self.assets = AssetsClient(self.client)
        self.inventory = InventoryClient(self.client)
        self.kpi = KPIClient(self.client)
        self.workorders = WorkOrdersClient(self.client)
        self.users = UsersClient(self.client)
        self.analytics = AnalyticsClient(self.client)
        self.recommendations = RecommendationsClient(self.client)
    
    def get_current_user(self) -> Dict:
        """
        Get the current authenticated user.
        
        Returns:
            User information
        """
        return self.client.get_current_user()
