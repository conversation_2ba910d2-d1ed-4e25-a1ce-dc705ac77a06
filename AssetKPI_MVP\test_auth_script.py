"""
Test script for test user authentication.
"""

import os
import sys
import requests
import json
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# Get database URL from environment or use default
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Add the parent directory to sys.path to allow imports from main
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the test_user_auth module
from test_user_auth import handle_test_user_token, is_test_user_token, TEST_TOKEN_MAP
from main import User

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def test_is_test_user_token():
    """Test the is_test_user_token function."""
    print("\n--- Testing is_test_user_token ---")
    
    # Test valid tokens
    for token in TEST_TOKEN_MAP.keys():
        assert is_test_user_token(token), f"Expected {token} to be a valid test token"
        print(f"✓ {token} is a valid test token")
    
    # Test invalid token
    assert not is_test_user_token("invalid-token"), "Expected 'invalid-token' to be an invalid test token"
    print("✓ 'invalid-token' is an invalid test token")

def test_handle_test_user_token():
    """Test the handle_test_user_token function."""
    print("\n--- Testing handle_test_user_token ---")
    
    # Test valid tokens
    for token, (user_id, role) in TEST_TOKEN_MAP.items():
        try:
            user = handle_test_user_token(token, db)
            assert user is not None, f"Expected user for token {token} to be found"
            assert user.user_id == user_id, f"Expected user_id to be {user_id}, got {user.user_id}"
            print(f"✓ {token} -> {user.email} ({user.role})")
        except Exception as e:
            print(f"✗ {token} -> {str(e)}")
    
    # Test invalid token
    try:
        handle_test_user_token("invalid-token", db)
        print("✗ 'invalid-token' did not raise an exception")
    except Exception as e:
        print(f"✓ 'invalid-token' raised an exception: {str(e)}")

def test_api_endpoint():
    """Test the API endpoint."""
    print("\n--- Testing API endpoint ---")
    
    base_url = "http://127.0.0.1:8000/api/test-user-auth"
    
    # Test valid tokens
    for token in TEST_TOKEN_MAP.keys():
        response = requests.get(f"{base_url}?token={token}")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ {token} -> {data.get('user')} ({data.get('role')})")
        else:
            print(f"✗ {token} -> {response.status_code}: {response.text}")
    
    # Test invalid token
    response = requests.get(f"{base_url}?token=invalid-token")
    if response.status_code == 200:
        data = response.json()
        if "error" in data:
            print(f"✓ 'invalid-token' -> {data.get('error')}")
        else:
            print(f"✗ 'invalid-token' -> {data}")
    else:
        print(f"✗ 'invalid-token' -> {response.status_code}: {response.text}")

def main():
    """Run all tests."""
    print("=== Testing Test User Authentication ===")
    
    try:
        test_is_test_user_token()
        test_handle_test_user_token()
        test_api_endpoint()
        
        print("\n=== All tests completed ===")
    except Exception as e:
        print(f"\n=== Tests failed: {str(e)} ===")
    finally:
        db.close()

if __name__ == "__main__":
    main()
