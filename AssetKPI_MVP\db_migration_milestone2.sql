-- AssetKPI Database Migration: Milestone 2 - Asset Specifications and Warranties

-- 2.1 Asset Specifications
CREATE TABLE IF NOT EXISTS asset_specifications (
    spec_id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    spec_name VARCHAR(100) NOT NULL,
    spec_value VARCHAR(255),
    spec_unit VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 2.2 Asset Warranty
CREATE TABLE IF NOT EXISTS asset_warranties (
    warranty_id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    warranty_type VARCHAR(50),
    provider VARCHAR(100),
    start_date DATE,
    end_date DATE,
    coverage_details TEXT,
    contact_info TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_asset_specs_asset_id ON asset_specifications(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_warranties_asset_id ON asset_warranties(asset_id);

-- End of Milestone 2 migration script
