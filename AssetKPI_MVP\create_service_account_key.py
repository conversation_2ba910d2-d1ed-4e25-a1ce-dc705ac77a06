import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_service_account_key():
    """
    Create a service account key file with user input.
    This is a helper script for when you can't directly download the key from Firebase.
    """
    print("Firebase Service Account Key Generator")
    print("=====================================")
    print("This script will help you create a service account key file for Firebase.")
    print("You'll need to enter the details from your Firebase project.")
    print()
    
    # Get project details
    project_id = input("Enter your Firebase project ID: ")
    private_key_id = input("Enter your private key ID: ")
    
    # For private key, handle the multi-line input
    print("\nEnter your private key (paste the entire key including BEGIN/END lines):")
    print("When finished, enter a line with just 'END'")
    private_key_lines = []
    while True:
        line = input()
        if line == "END":
            break
        private_key_lines.append(line)
    private_key = "\n".join(private_key_lines)
    
    client_email = input("\nEnter the client email: ")
    client_id = input("Enter the client ID: ")
    auth_uri = input("Enter the auth URI [https://accounts.google.com/o/oauth2/auth]: ") or "https://accounts.google.com/o/oauth2/auth"
    token_uri = input("Enter the token URI [https://oauth2.googleapis.com/token]: ") or "https://oauth2.googleapis.com/token"
    auth_provider_x509_cert_url = input("Enter the auth provider x509 cert URL [https://www.googleapis.com/oauth2/v1/certs]: ") or "https://www.googleapis.com/oauth2/v1/certs"
    client_x509_cert_url = input("Enter the client x509 cert URL: ")
    
    # Create the service account key JSON
    service_account_key = {
        "type": "service_account",
        "project_id": project_id,
        "private_key_id": private_key_id,
        "private_key": private_key,
        "client_email": client_email,
        "client_id": client_id,
        "auth_uri": auth_uri,
        "token_uri": token_uri,
        "auth_provider_x509_cert_url": auth_provider_x509_cert_url,
        "client_x509_cert_url": client_x509_cert_url,
        "universe_domain": "googleapis.com"
    }
    
    # Save to file
    output_path = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
    with open(output_path, "w") as f:
        json.dump(service_account_key, f, indent=2)
    
    print(f"\nService account key saved to {output_path}")
    print("Make sure to keep this file secure and add it to your .gitignore!")

if __name__ == "__main__":
    create_service_account_key()
