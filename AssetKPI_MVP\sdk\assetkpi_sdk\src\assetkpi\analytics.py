"""
Analytics API client module.

This module provides methods for interacting with the analytics-related endpoints
of the AssetKPI API.
"""

from typing import Dict, List, Optional, Union, Any

from .client import AssetKPIClient


class AnalyticsClient:
    """Client for analytics-related API endpoints."""
    
    def __init__(self, client: AssetKPIClient):
        """
        Initialize the analytics client.
        
        Args:
            client: The AssetKPI API client
        """
        self.client = client
    
    def get_user_activity(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        user_id: Optional[str] = None,
        event_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """
        Get user activity data for analytics.
        
        Args:
            start_date: Start date for filtering (YYYY-MM-DD)
            end_date: End date for filtering (YYYY-MM-DD)
            user_id: Filter by user ID
            event_type: Filter by event type
            limit: Maximum number of items to return
            offset: Number of items to skip
            
        Returns:
            Paginated response with user activity data
        """
        params = {
            "limit": limit,
            "offset": offset,
        }
        
        if start_date:
            params["start_date"] = start_date
        
        if end_date:
            params["end_date"] = end_date
        
        if user_id:
            params["user_id"] = user_id
        
        if event_type:
            params["event_type"] = event_type
        
        return self.client.get("/usage/activity", params=params)
    
    def get_feature_usage(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        feature: Optional[str] = None,
        aggregation: str = "daily",
    ) -> Dict[str, Any]:
        """
        Get feature usage data for analytics.
        
        Args:
            start_date: Start date for filtering (YYYY-MM-DD)
            end_date: End date for filtering (YYYY-MM-DD)
            feature: Filter by feature name
            aggregation: Time aggregation level
            
        Returns:
            Feature usage data
        """
        params = {
            "aggregation": aggregation,
        }
        
        if start_date:
            params["start_date"] = start_date
        
        if end_date:
            params["end_date"] = end_date
        
        if feature:
            params["feature"] = feature
        
        return self.client.get("/usage/feature-usage", params=params)
    
    def get_user_sessions(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        user_id: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """
        Get user session data for analytics.
        
        Args:
            start_date: Start date for filtering (YYYY-MM-DD)
            end_date: End date for filtering (YYYY-MM-DD)
            user_id: Filter by user ID
            limit: Maximum number of items to return
            offset: Number of items to skip
            
        Returns:
            Paginated response with user session data
        """
        params = {
            "limit": limit,
            "offset": offset,
        }
        
        if start_date:
            params["start_date"] = start_date
        
        if end_date:
            params["end_date"] = end_date
        
        if user_id:
            params["user_id"] = user_id
        
        return self.client.get("/usage/user-sessions", params=params)
