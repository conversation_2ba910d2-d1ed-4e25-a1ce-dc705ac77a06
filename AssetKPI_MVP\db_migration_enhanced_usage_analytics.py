"""
Database migration script for enhanced usage analytics.

This script adds new columns to the user_activity_logs table and creates
new tables for scheduled reports and user notification preferences.
"""
import os
import sys
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Float, ForeignKey, ARRAY, text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.schema import CreateTable
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
engine = create_engine(DATABASE_URL)
Base = declarative_base()

# Define models
class UserActivityLog(Base):
    __tablename__ = "user_activity_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(255), nullable=True)
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    event_type = Column(String(50), nullable=False)
    details = Column(JSONB, nullable=True)
    session_id = Column(String(255), nullable=True)
    
    # Enhanced tracking fields
    duration_seconds = Column(Float, nullable=True)
    component_id = Column(String(255), nullable=True)
    previous_page = Column(String(255), nullable=True)
    browser_info = Column(JSONB, nullable=True)
    interaction_depth = Column(Integer, nullable=True)
    conversion_event = Column(Boolean, default=False)
    user_role = Column(String(50), nullable=True)

class ScheduledReport(Base):
    __tablename__ = "scheduled_reports"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    report_type = Column(String(50), nullable=False)
    period = Column(String(20), nullable=False)
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    format = Column(String(20), nullable=False)
    file_path = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    sent_to = Column(ARRAY(String), nullable=True)
    status = Column(String(20), default="pending", nullable=False)

class UserNotificationPreferences(Base):
    __tablename__ = "user_notification_preferences"
    
    user_id = Column(String(255), primary_key=True)
    receive_usage_reports = Column(Boolean, default=False)
    receive_inventory_reports = Column(Boolean, default=False)
    receive_kpi_reports = Column(Boolean, default=False)
    report_frequency = Column(String(20), default="weekly")
    email_format = Column(String(20), default="html")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

def run_migration():
    """Run the database migration."""
    try:
        # Connect to the database
        conn = engine.connect()
        
        # Check if user_activity_logs table exists
        result = conn.execute(text(
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_activity_logs')"
        ))
        table_exists = result.scalar()
        
        if table_exists:
            print("user_activity_logs table exists, adding new columns...")
            
            # Check which columns need to be added
            columns_to_add = [
                ("duration_seconds", "FLOAT"),
                ("component_id", "VARCHAR(255)"),
                ("previous_page", "VARCHAR(255)"),
                ("browser_info", "JSONB"),
                ("interaction_depth", "INTEGER"),
                ("conversion_event", "BOOLEAN DEFAULT FALSE"),
                ("user_role", "VARCHAR(50)")
            ]
            
            for column_name, column_type in columns_to_add:
                # Check if column exists
                result = conn.execute(text(
                    f"SELECT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'user_activity_logs' AND column_name = '{column_name}')"
                ))
                column_exists = result.scalar()
                
                if not column_exists:
                    print(f"Adding column {column_name} to user_activity_logs table...")
                    conn.execute(text(f"ALTER TABLE user_activity_logs ADD COLUMN {column_name} {column_type}"))
                else:
                    print(f"Column {column_name} already exists in user_activity_logs table.")
            
            # Add indexes for new columns
            indexes_to_add = [
                ("idx_user_activity_component_id", "component_id"),
                ("idx_user_activity_conversion_event", "conversion_event"),
                ("idx_user_activity_user_role", "user_role")
            ]
            
            for index_name, column_name in indexes_to_add:
                # Check if index exists
                result = conn.execute(text(
                    f"SELECT EXISTS (SELECT FROM pg_indexes WHERE indexname = '{index_name}')"
                ))
                index_exists = result.scalar()
                
                if not index_exists:
                    print(f"Adding index {index_name} to user_activity_logs table...")
                    conn.execute(text(f"CREATE INDEX {index_name} ON user_activity_logs ({column_name})"))
                else:
                    print(f"Index {index_name} already exists.")
        else:
            print("user_activity_logs table does not exist, creating it...")
            UserActivityLog.__table__.create(engine)
            print("user_activity_logs table created.")
        
        # Check if scheduled_reports table exists
        result = conn.execute(text(
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'scheduled_reports')"
        ))
        table_exists = result.scalar()
        
        if not table_exists:
            print("Creating scheduled_reports table...")
            ScheduledReport.__table__.create(engine)
            print("scheduled_reports table created.")
        else:
            print("scheduled_reports table already exists.")
        
        # Check if user_notification_preferences table exists
        result = conn.execute(text(
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_notification_preferences')"
        ))
        table_exists = result.scalar()
        
        if not table_exists:
            print("Creating user_notification_preferences table...")
            UserNotificationPreferences.__table__.create(engine)
            print("user_notification_preferences table created.")
        else:
            print("user_notification_preferences table already exists.")
        
        # Create default notification preferences for admin users
        print("Creating default notification preferences for admin users...")
        conn.execute(text("""
            INSERT INTO user_notification_preferences (user_id, receive_usage_reports, receive_inventory_reports, receive_kpi_reports)
            SELECT user_id, TRUE, TRUE, TRUE
            FROM users
            WHERE role = 'ADMIN'
            AND NOT EXISTS (
                SELECT 1 FROM user_notification_preferences WHERE user_notification_preferences.user_id = users.user_id
            )
        """))
        
        print("Migration completed successfully.")
        conn.close()
        return True
    
    except Exception as e:
        print(f"Error during migration: {e}")
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
