{% extends "layout.html" %}

{% block title %}Asset Maintenance | AssetKPI{% endblock %}

{% block styles %}
<style>
    .asset-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
    .status-badge {
        font-size: 1rem;
        padding: 5px 10px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    .status-maintenance {
        background-color: #ffc107;
    }
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #dee2e6;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -25px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #0d6efd;
    }
    .timeline-item.completed::before {
        background-color: #28a745;
    }
    .timeline-item.in-progress::before {
        background-color: #ffc107;
    }
    .timeline-item.cancelled::before {
        background-color: #dc3545;
    }
    .timeline-date {
        font-size: 0.85rem;
        color: #6c757d;
    }
    .work-order-card {
        transition: transform 0.3s;
    }
    .work-order-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/assets">Assets</a></li>
                <li class="breadcrumb-item"><a href="/assets/{{ asset.assetid }}">{{ asset.assetname }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Maintenance History</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Asset Header -->
<div class="asset-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>{{ asset.assetname }} - Maintenance History</h1>
            <p class="text-muted">{{ asset.assettype }} | {{ asset.manufacturer }} {{ asset.model }}</p>
            <div class="d-flex align-items-center mt-2">
                <span class="badge status-{{ asset.status|lower if asset.status else 'inactive' }} status-badge me-2">{{ asset.status }}</span>
                <span class="text-muted">Serial: {{ asset.serialnumber }}</span>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <i class="fas fa-plus"></i> Create Work Order
            </button>
        </div>
    </div>
</div>

<!-- Asset Navigation -->
<ul class="nav nav-pills mb-4">
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}">Overview</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/kpi">KPIs</a>
    </li>
    <li class="nav-item">
        <a class="nav-link active" href="/assets/{{ asset.assetid }}/maintenance">Maintenance</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/specifications">Specifications</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/documents">Documents</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/meters">Meters</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/pm">PM Schedules</a>
    </li>
</ul>

<!-- Maintenance History -->
<div class="row">
    <div class="col-md-8">
        <!-- Work Orders Table -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Work Order History</h5>
                <div>
                    <button class="btn btn-sm btn-outline-secondary" id="toggleView">
                        <i class="fas fa-th-list"></i> Toggle View
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Table View (Default) -->
                <div id="tableView">
                    {% if work_orders %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for wo in work_orders %}
                                        <tr>
                                            <td>{{ wo.workorderid }}</td>
                                            <td>{{ wo.workordertype }}</td>
                                            <td>{{ wo.description[:30] }}{% if wo.description|length > 30 %}...{% endif %}</td>
                                            <td>
                                                <span class="badge {% if wo.status == 'Completed' %}bg-success{% elif wo.status == 'In Progress' %}bg-primary{% elif wo.status == 'On Hold' %}bg-warning{% elif wo.status == 'Cancelled' %}bg-danger{% else %}bg-secondary{% endif %}">
                                                    {{ wo.status }}
                                                </span>
                                            </td>
                                            <td>{{ wo.startdate.strftime('%Y-%m-%d') if wo.startdate else 'N/A' }}</td>
                                            <td>{{ wo.enddate.strftime('%Y-%m-%d') if wo.enddate else 'N/A' }}</td>
                                            <td>
                                                <a href="/workorders/{{ wo.workorderid }}" class="btn btn-sm btn-primary">View</a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-center">No work orders found for this asset.</p>
                    {% endif %}
                </div>

                <!-- Timeline View (Hidden by Default) -->
                <div id="timelineView" class="d-none">
                    {% if work_orders %}
                        <div class="timeline">
                            {% for wo in work_orders %}
                                <div class="timeline-item {% if wo.status == 'Completed' %}completed{% elif wo.status == 'In Progress' %}in-progress{% elif wo.status == 'Cancelled' %}cancelled{% endif %}">
                                    <div class="timeline-date">{{ wo.startdate.strftime('%Y-%m-%d') if wo.startdate else 'N/A' }}</div>
                                    <div class="card work-order-card mb-2">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">WO #{{ wo.workorderid }} - {{ wo.workordertype }}</h6>
                                            <span class="badge {% if wo.status == 'Completed' %}bg-success{% elif wo.status == 'In Progress' %}bg-primary{% elif wo.status == 'On Hold' %}bg-warning{% elif wo.status == 'Cancelled' %}bg-danger{% else %}bg-secondary{% endif %}">
                                                {{ wo.status }}
                                            </span>
                                        </div>
                                        <div class="card-body">
                                            <p>{{ wo.description }}</p>
                                            <div class="d-flex justify-content-between">
                                                <small class="text-muted">Start: {{ wo.startdate.strftime('%Y-%m-%d') if wo.startdate else 'N/A' }}</small>
                                                <small class="text-muted">End: {{ wo.enddate.strftime('%Y-%m-%d') if wo.enddate else 'N/A' }}</small>
                                            </div>
                                        </div>
                                        <div class="card-footer text-end">
                                            <a href="/workorders/{{ wo.workorderid }}" class="btn btn-sm btn-primary">View Details</a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-center">No work orders found for this asset.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Maintenance Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Maintenance Summary</h5>
            </div>
            <div class="card-body">
                {% if work_orders %}
                    {% set completed = work_orders|selectattr('status', 'equalto', 'Completed')|list|length %}
                    {% set in_progress = work_orders|selectattr('status', 'equalto', 'In Progress')|list|length %}
                    {% set on_hold = work_orders|selectattr('status', 'equalto', 'On Hold')|list|length %}
                    {% set cancelled = work_orders|selectattr('status', 'equalto', 'Cancelled')|list|length %}
                    {% set total = work_orders|length %}

                    <div class="mb-3">
                        <h6>Work Order Status</h6>
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ (completed / total * 100)|round }}%;" aria-valuenow="{{ completed }}" aria-valuemin="0" aria-valuemax="{{ total }}">
                                {{ completed }} Completed
                            </div>
                            <div class="progress-bar bg-primary" role="progressbar" style="width: {{ (in_progress / total * 100)|round }}%;" aria-valuenow="{{ in_progress }}" aria-valuemin="0" aria-valuemax="{{ total }}">
                                {{ in_progress }} In Progress
                            </div>
                            <div class="progress-bar bg-warning" role="progressbar" style="width: {{ (on_hold / total * 100)|round }}%;" aria-valuenow="{{ on_hold }}" aria-valuemin="0" aria-valuemax="{{ total }}">
                                {{ on_hold }} On Hold
                            </div>
                            <div class="progress-bar bg-danger" role="progressbar" style="width: {{ (cancelled / total * 100)|round }}%;" aria-valuenow="{{ cancelled }}" aria-valuemin="0" aria-valuemax="{{ total }}">
                                {{ cancelled }} Cancelled
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Work Order Types</h6>
                        <ul class="list-group">
                            {% set types = {} %}
                            {% for wo in work_orders %}
                                {% if wo.workordertype in types %}
                                    {% set _ = types.update({wo.workordertype: types[wo.workordertype] + 1}) %}
                                {% else %}
                                    {% set _ = types.update({wo.workordertype: 1}) %}
                                {% endif %}
                            {% endfor %}

                            {% for type, count in types.items() %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ type }}
                                    <span class="badge bg-primary rounded-pill">{{ count }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>

                    <div>
                        <h6>Recent Activity</h6>
                        <ul class="list-group">
                            {% for wo in work_orders[:3] %}
                                <li class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">WO #{{ wo.workorderid }}</h6>
                                        <small>{{ wo.startdate.strftime('%Y-%m-%d') if wo.startdate else 'N/A' }}</small>
                                    </div>
                                    <p class="mb-1">{{ wo.description[:50] }}{% if wo.description|length > 50 %}...{% endif %}</p>
                                    <small class="text-muted">Status: {{ wo.status }}</small>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                {% else %}
                    <p class="text-center">No maintenance data available.</p>
                {% endif %}
            </div>
        </div>

        <!-- Maintenance Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Filter Work Orders</h5>
            </div>
            <div class="card-body">
                <form id="filterForm">
                    <div class="mb-3">
                        <label for="statusFilter" class="form-label">Status</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">All Statuses</option>
                            <option value="Completed">Completed</option>
                            <option value="In Progress">In Progress</option>
                            <option value="On Hold">On Hold</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="typeFilter" class="form-label">Type</label>
                        <select class="form-select" id="typeFilter">
                            <option value="">All Types</option>
                            <option value="Preventive">Preventive</option>
                            <option value="Corrective">Corrective</option>
                            <option value="Emergency">Emergency</option>
                            <option value="Inspection">Inspection</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="dateRangeFilter" class="form-label">Date Range</label>
                        <div class="input-group">
                            <input type="date" class="form-control" id="startDateFilter">
                            <span class="input-group-text">to</span>
                            <input type="date" class="form-control" id="endDateFilter">
                        </div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" id="applyFilters">Apply Filters</button>
                        <button type="button" class="btn btn-outline-secondary" id="resetFilters">Reset</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between table and timeline view
        const toggleViewBtn = document.getElementById('toggleView');
        const tableView = document.getElementById('tableView');
        const timelineView = document.getElementById('timelineView');

        toggleViewBtn.addEventListener('click', function() {
            if (tableView.classList.contains('d-none')) {
                tableView.classList.remove('d-none');
                timelineView.classList.add('d-none');
                toggleViewBtn.innerHTML = '<i class="fas fa-th-list"></i> Toggle View';
            } else {
                tableView.classList.add('d-none');
                timelineView.classList.remove('d-none');
                toggleViewBtn.innerHTML = '<i class="fas fa-table"></i> Toggle View';
            }
        });

        // Filter functionality
        const statusFilter = document.getElementById('statusFilter');
        const typeFilter = document.getElementById('typeFilter');
        const startDateFilter = document.getElementById('startDateFilter');
        const endDateFilter = document.getElementById('endDateFilter');
        const applyFiltersBtn = document.getElementById('applyFilters');
        const resetFiltersBtn = document.getElementById('resetFilters');

        applyFiltersBtn.addEventListener('click', function() {
            // Implement filter logic here
            alert('Filter functionality would be implemented here');
        });

        resetFiltersBtn.addEventListener('click', function() {
            statusFilter.value = '';
            typeFilter.value = '';
            startDateFilter.value = '';
            endDateFilter.value = '';
        });
    });
</script>
{% endblock %}
