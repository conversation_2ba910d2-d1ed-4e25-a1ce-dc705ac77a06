"""
Bulk operations examples for the AssetKPI SDK.
"""

import os
import json
from dotenv import load_dotenv
from assetkpi import AssetKPISDK, AuthenticationError, RateLimitError, APIError

# Load environment variables
load_dotenv()

# Get API key from environment variable
API_KEY = os.getenv("ASSETKPI_API_KEY")
BASE_URL = os.getenv("ASSETKPI_BASE_URL", "http://localhost:8000/api")

def bulk_create_parts_example(sdk):
    """Example of bulk creating spare parts."""
    print("=== Bulk Create Parts Example ===")
    
    # Sample parts data
    parts = [
        {
            "partname": "Bulk Test Part 1",
            "partnumber": "BTP-001",
            "manufacturer": "Test Manufacturer",
            "stockquantity": 10,
            "reorderlevel": 5,
            "unitprice": 25.99,
            "leadtimedays": 7
        },
        {
            "partname": "Bulk Test Part 2",
            "partnumber": "BTP-002",
            "manufacturer": "Test Manufacturer",
            "stockquantity": 15,
            "reorderlevel": 8,
            "unitprice": 34.99,
            "leadtimedays": 10
        },
        {
            "partname": "Bulk Test Part 3",
            "partnumber": "BTP-003",
            "manufacturer": "Test Manufacturer",
            "stockquantity": 5,
            "reorderlevel": 3,
            "unitprice": 19.99,
            "leadtimedays": 5
        }
    ]
    
    print(f"Creating {len(parts)} parts in bulk...")
    
    try:
        result = sdk.inventory.bulk_create_parts(parts)
        print(f"Bulk create result: {result.get('message', 'Unknown')}")
        print(f"Created {result.get('created_count', 0)} parts")
        
        # Get the created part IDs
        created_ids = result.get('created_ids', [])
        print(f"Created part IDs: {created_ids}")
        
        return created_ids
    except APIError as e:
        print(f"API error ({e.status_code}): {str(e)}")
        return []


def bulk_update_parts_example(sdk, part_ids):
    """Example of bulk updating spare parts."""
    print("\n=== Bulk Update Parts Example ===")
    
    if not part_ids:
        print("No part IDs provided for bulk update")
        return
    
    # Get current part data
    print("Getting current part data...")
    parts_to_update = []
    
    for part_id in part_ids:
        try:
            part = sdk.inventory.get_part(part_id)
            parts_to_update.append({
                "partid": part_id,
                "stockquantity": part.get('stockquantity', 0) + 5,  # Increase stock by 5
                "reorderlevel": part.get('reorderlevel', 0) + 2  # Increase reorder level by 2
            })
        except APIError as e:
            print(f"Error getting part {part_id}: {str(e)}")
    
    if not parts_to_update:
        print("No parts to update")
        return
    
    print(f"Updating {len(parts_to_update)} parts in bulk...")
    
    try:
        result = sdk.inventory.bulk_update_parts(parts_to_update)
        print(f"Bulk update result: {result.get('message', 'Unknown')}")
        print(f"Updated {result.get('updated_count', 0)} parts")
    except APIError as e:
        print(f"API error ({e.status_code}): {str(e)}")


def bulk_delete_parts_example(sdk, part_ids):
    """Example of bulk deleting spare parts."""
    print("\n=== Bulk Delete Parts Example ===")
    
    if not part_ids:
        print("No part IDs provided for bulk delete")
        return
    
    print(f"Deleting {len(part_ids)} parts in bulk...")
    
    try:
        result = sdk.inventory.bulk_delete_parts(part_ids)
        print(f"Bulk delete result: {result.get('message', 'Unknown')}")
        print(f"Deleted {result.get('deleted_count', 0)} parts")
    except APIError as e:
        print(f"API error ({e.status_code}): {str(e)}")


def filtering_example(sdk):
    """Example of using filtering with the SDK."""
    print("\n=== Filtering Example ===")
    
    # Define filters for parts with low stock
    filters = [
        {
            "field": "stockquantity",
            "operator": "lte",  # less than or equal
            "value": "10"
        },
        {
            "field": "reorderlevel",
            "operator": "gt",  # greater than
            "value": "0"
        }
    ]
    
    print("Getting parts with low stock...")
    
    try:
        parts = sdk.inventory.get_parts(
            limit=10,
            filters=filters
        )
        
        print(f"Found {len(parts.get('items', []))} parts with low stock:")
        for part in parts.get('items', []):
            print(f"  Part ID: {part.get('partid', 'N/A')}, Name: {part.get('partname', 'N/A')}")
            print(f"    Stock: {part.get('stockquantity', 'N/A')}, Reorder Level: {part.get('reorderlevel', 'N/A')}")
    except APIError as e:
        print(f"API error ({e.status_code}): {str(e)}")
    
    # Define filters for recent work orders
    filters = [
        {
            "field": "startdate",
            "operator": "gte",  # greater than or equal
            "value": "2023-01-01"
        },
        {
            "field": "status",
            "operator": "eq",  # equal
            "value": "COMPLETED"
        }
    ]
    
    print("\nGetting recent completed work orders...")
    
    try:
        work_orders = sdk.workorders.get_workorders(
            limit=10,
            filters=filters
        )
        
        print(f"Found {len(work_orders.get('items', []))} recent completed work orders:")
        for wo in work_orders.get('items', []):
            print(f"  WO ID: {wo.get('workorderid', 'N/A')}, Type: {wo.get('workordertype', 'N/A')}")
            print(f"    Start Date: {wo.get('startdate', 'N/A')}, End Date: {wo.get('enddate', 'N/A')}")
    except APIError as e:
        print(f"API error ({e.status_code}): {str(e)}")


def pagination_example(sdk):
    """Example of using pagination with the SDK."""
    print("\n=== Pagination Example ===")
    
    # Get the first page of assets
    page_size = 5
    offset = 0
    
    print(f"Getting assets (page 1, size {page_size})...")
    
    try:
        assets = sdk.assets.get_assets(
            limit=page_size,
            offset=offset
        )
        
        total = assets.get('total', 0)
        has_more = assets.get('has_more', False)
        
        print(f"Page 1: Found {len(assets.get('items', []))} assets (total: {total})")
        for asset in assets.get('items', []):
            print(f"  Asset ID: {asset.get('assetid', 'N/A')}, Name: {asset.get('assetname', 'N/A')}")
        
        # Get the second page if there are more assets
        if has_more:
            offset += page_size
            print(f"\nGetting assets (page 2, size {page_size})...")
            
            assets = sdk.assets.get_assets(
                limit=page_size,
                offset=offset
            )
            
            print(f"Page 2: Found {len(assets.get('items', []))} assets")
            for asset in assets.get('items', []):
                print(f"  Asset ID: {asset.get('assetid', 'N/A')}, Name: {asset.get('assetname', 'N/A')}")
    except APIError as e:
        print(f"API error ({e.status_code}): {str(e)}")


def main():
    """Run bulk operations examples."""
    # Initialize the SDK
    sdk = AssetKPISDK(
        base_url=BASE_URL,
        api_key=API_KEY
    )
    
    try:
        # Run bulk create example
        created_ids = bulk_create_parts_example(sdk)
        
        # Run bulk update example
        bulk_update_parts_example(sdk, created_ids)
        
        # Run filtering example
        filtering_example(sdk)
        
        # Run pagination example
        pagination_example(sdk)
        
        # Run bulk delete example (clean up)
        bulk_delete_parts_example(sdk, created_ids)
        
    except AuthenticationError as e:
        print(f"Authentication error: {str(e)}")
    except RateLimitError as e:
        print(f"Rate limit exceeded. Retry after {e.retry_after} seconds")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")


if __name__ == "__main__":
    main()
