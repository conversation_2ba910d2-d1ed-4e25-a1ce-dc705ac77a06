{% extends "layout.html" %}

{% block title %}{{ part.partname }} | Inventory | AssetKPI{% endblock %}

{% block styles %}
<style>
    .part-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
    .metric-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
        height: 100%;
    }
    .metric-card:hover {
        transform: translateY(-5px);
    }
    .metric-value {
        font-size: 24px;
        font-weight: bold;
    }
    .metric-unit {
        font-size: 14px;
        color: #6c757d;
    }
    .status-badge {
        font-size: 1rem;
        padding: 5px 10px;
    }
    .status-ok {
        background-color: #28a745;
    }
    .status-warning {
        background-color: #ffc107;
    }
    .status-critical {
        background-color: #dc3545;
    }
    .chart-container {
        height: 300px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/inventory">Inventory</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ part.partname }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Part Header -->
<div class="part-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>{{ part.partname }}</h1>
            <p class="text-muted">Part #: {{ part.partnumber }}</p>
            <div class="d-flex align-items-center mt-2">
                {% if part.stockquantity > part.reorderlevel|default(0, true) * 1.5 %}
                    <span class="badge status-ok status-badge me-2">In Stock</span>
                {% elif part.stockquantity > part.reorderlevel|default(0, true) %}
                    <span class="badge status-warning status-badge me-2">Low Stock</span>
                {% else %}
                    <span class="badge status-critical status-badge me-2">Reorder</span>
                {% endif %}
                <span class="text-muted">ABC Class: {{ part.abc_classification|default('Not Classified') }}</span>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <i class="fas fa-edit"></i> Edit Part
            </button>
        </div>
    </div>
</div>

<!-- Part Navigation -->
<ul class="nav nav-pills mb-4">
    <li class="nav-item">
        <a class="nav-link active" href="#overview" data-bs-toggle="tab">Overview</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#inventory" data-bs-toggle="tab">Inventory Analysis</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#usage" data-bs-toggle="tab">Usage History</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#assets" data-bs-toggle="tab">Related Assets</a>
    </li>
</ul>

<!-- Tab Content -->
<div class="tab-content">
    <!-- Overview Tab -->
    <div class="tab-pane fade show active" id="overview">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Current Stock</h5>
                        <p class="metric-value">{{ part.stockquantity|default(0) }}</p>
                        <p class="metric-unit">units</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Unit Price</h5>
                        <p class="metric-value">${{ part.unitprice|default(0)|round(2) }}</p>
                        <p class="metric-unit">per unit</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Value</h5>
                        <p class="metric-value">${{ (part.stockquantity|default(0) * part.unitprice|default(0))|round(2) }}</p>
                        <p class="metric-unit">inventory value</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Reorder Level</h5>
                        <p class="metric-value">{{ part.reorderlevel|default('N/A') }}</p>
                        <p class="metric-unit">minimum stock</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Part Details -->
        <div class="row mb-4">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Part Details</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Part ID:</th>
                                <td>{{ part.partid }}</td>
                            </tr>
                            <tr>
                                <th>Part Name:</th>
                                <td>{{ part.partname }}</td>
                            </tr>
                            <tr>
                                <th>Part Number:</th>
                                <td>{{ part.partnumber }}</td>
                            </tr>
                            <tr>
                                <th>Description:</th>
                                <td>{{ part.description|default('No description available') }}</td>
                            </tr>
                            <tr>
                                <th>Category:</th>
                                <td>{{ part.category|default('Uncategorized') }}</td>
                            </tr>
                            <tr>
                                <th>Location:</th>
                                <td>{{ part.location|default('Not specified') }}</td>
                            </tr>
                            <tr>
                                <th>Manufacturer:</th>
                                <td>{{ part.manufacturer|default('Not specified') }}</td>
                            </tr>
                            <tr>
                                <th>Supplier:</th>
                                <td>{{ part.supplier|default('Not specified') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Inventory Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Current Stock:</th>
                                <td>{{ part.stockquantity|default(0) }} units</td>
                            </tr>
                            <tr>
                                <th>Unit Price:</th>
                                <td>${{ part.unitprice|default(0)|round(2) }}</td>
                            </tr>
                            <tr>
                                <th>Total Value:</th>
                                <td>${{ (part.stockquantity|default(0) * part.unitprice|default(0))|round(2) }}</td>
                            </tr>
                            <tr>
                                <th>Reorder Level:</th>
                                <td>{{ part.reorderlevel|default('Not set') }}</td>
                            </tr>
                            <tr>
                                <th>ABC Classification:</th>
                                <td>{{ part.abc_classification|default('Not classified') }}</td>
                            </tr>
                            <tr>
                                <th>Lead Time:</th>
                                <td>{{ part.leadtime|default('Not specified') }} days</td>
                            </tr>
                            <tr>
                                <th>Min Order Qty:</th>
                                <td>{{ part.min_order_quantity|default('Not specified') }}</td>
                            </tr>
                            <tr>
                                <th>Last Updated:</th>
                                <td>{{ part.last_updated.strftime('%Y-%m-%d') if part.last_updated else 'Never' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Analysis Tab -->
    <div class="tab-pane fade" id="inventory">
        <div class="row mb-4">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Inventory Analysis</h5>
                    </div>
                    <div class="card-body">
                        {% if inventory_analysis %}
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 40%">Optimal Stock Level:</th>
                                    <td>{{ inventory_analysis.optimal_stock|default('Not calculated') }}</td>
                                </tr>
                                <tr>
                                    <th>Stock Difference:</th>
                                    <td>
                                        {% if inventory_analysis.stock_difference < 0 %}
                                            <span class="text-danger">{{ inventory_analysis.stock_difference }} (Overstock)</span>
                                        {% elif inventory_analysis.stock_difference > 0 %}
                                            <span class="text-warning">+{{ inventory_analysis.stock_difference }} (Understock)</span>
                                        {% else %}
                                            <span class="text-success">0 (Optimal)</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Current Cost:</th>
                                    <td>${{ inventory_analysis.current_cost|default(0)|round(2) }}</td>
                                </tr>
                                <tr>
                                    <th>Optimal Cost:</th>
                                    <td>${{ inventory_analysis.optimal_cost|default(0)|round(2) }}</td>
                                </tr>
                                <tr>
                                    <th>Potential Savings:</th>
                                    <td>${{ inventory_analysis.potential_savings|default(0)|round(2) }}</td>
                                </tr>
                                <tr>
                                    <th>Days of Supply:</th>
                                    <td>{{ inventory_analysis.days_of_supply|default('Not calculated') }} days</td>
                                </tr>
                                <tr>
                                    <th>Stockout Risk:</th>
                                    <td>
                                        {% if inventory_analysis.stockout_risk < 10 %}
                                            <span class="text-success">{{ inventory_analysis.stockout_risk }}% (Low)</span>
                                        {% elif inventory_analysis.stockout_risk < 25 %}
                                            <span class="text-warning">{{ inventory_analysis.stockout_risk }}% (Medium)</span>
                                        {% else %}
                                            <span class="text-danger">{{ inventory_analysis.stockout_risk }}% (High)</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Analysis Date:</th>
                                    <td>{{ inventory_analysis.analysis_date.strftime('%Y-%m-%d') if inventory_analysis.analysis_date else 'Not available' }}</td>
                                </tr>
                            </table>
                        {% else %}
                            <p class="text-center">No inventory analysis available for this part.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>EOQ Parameters</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">Economic Order Quantity:</th>
                                <td>{{ inventory_analysis.eoq|default('Not calculated') if inventory_analysis else 'Not calculated' }}</td>
                            </tr>
                            <tr>
                                <th>Annual Demand:</th>
                                <td>{{ inventory_analysis.annual_demand|default('Not specified') if inventory_analysis else 'Not specified' }}</td>
                            </tr>
                            <tr>
                                <th>Ordering Cost:</th>
                                <td>${{ inventory_analysis.ordering_cost|default('Not specified') if inventory_analysis else 'Not specified' }}</td>
                            </tr>
                            <tr>
                                <th>Holding Cost:</th>
                                <td>${{ inventory_analysis.holding_cost|default('Not specified') if inventory_analysis else 'Not specified' }}</td>
                            </tr>
                            <tr>
                                <th>Safety Stock:</th>
                                <td>{{ inventory_analysis.safety_stock|default('Not calculated') if inventory_analysis else 'Not calculated' }}</td>
                            </tr>
                            <tr>
                                <th>Reorder Point:</th>
                                <td>{{ inventory_analysis.reorder_point|default('Not calculated') if inventory_analysis else 'Not calculated' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Charts -->
        <div class="row mb-4">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Stock Level History</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="stockHistoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Usage Trend</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="usageTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage History Tab -->
    <div class="tab-pane fade" id="usage">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Work Orders Using This Part</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Work Order ID</th>
                                        <th>Description</th>
                                        <th>Asset</th>
                                        <th>Start Date</th>
                                        <th>Status</th>
                                        <th>Quantity Used</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if work_orders %}
                                        {% for wo in work_orders %}
                                            <tr>
                                                <td><a href="/workorders/{{ wo.workorderid }}">{{ wo.workorderid }}</a></td>
                                                <td>{{ wo.description }}</td>
                                                <td><a href="/assets/{{ wo.assetid }}">{{ wo.assetid }}</a></td>
                                                <td>{{ wo.startdate.strftime('%Y-%m-%d') if wo.startdate else 'Not started' }}</td>
                                                <td>
                                                    {% if wo.status == 'COMPLETED' %}
                                                        <span class="badge bg-success">Completed</span>
                                                    {% elif wo.status == 'IN_PROGRESS' %}
                                                        <span class="badge bg-primary">In Progress</span>
                                                    {% elif wo.status == 'PLANNED' %}
                                                        <span class="badge bg-info">Planned</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ wo.status }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>1</td> <!-- Placeholder since we don't have actual quantity data -->
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center">No work orders found using this part.</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Assets Tab -->
    <div class="tab-pane fade" id="assets">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Assets Using This Part</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Asset ID</th>
                                        <th>Asset Name</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Location</th>
                                        <th>Criticality</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if assets %}
                                        {% for asset in assets %}
                                            <tr>
                                                <td><a href="/assets/{{ asset.assetid }}">{{ asset.assetid }}</a></td>
                                                <td>{{ asset.assetname }}</td>
                                                <td>{{ asset.assettype }}</td>
                                                <td>
                                                    {% if asset.status == 'ACTIVE' %}
                                                        <span class="badge bg-success">Active</span>
                                                    {% elif asset.status == 'INACTIVE' %}
                                                        <span class="badge bg-danger">Inactive</span>
                                                    {% elif asset.status == 'MAINTENANCE' %}
                                                        <span class="badge bg-warning">Maintenance</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ asset.status }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ asset.location }}</td>
                                                <td>{{ asset.criticality }}</td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center">No assets found using this part.</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Bootstrap tabs
        const triggerTabList = [].slice.call(document.querySelectorAll('a[data-bs-toggle="tab"]'));
        triggerTabList.forEach(function(triggerEl) {
            new bootstrap.Tab(triggerEl);
        });

        // Sample data for stock history chart
        const stockHistoryCtx = document.getElementById('stockHistoryChart').getContext('2d');
        const stockHistoryChart = new Chart(stockHistoryCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [
                    {
                        label: 'Stock Level',
                        data: [{{ part.stockquantity|default(10) + 5 }}, {{ part.stockquantity|default(10) + 2 }}, {{ part.stockquantity|default(10) }}, {{ part.stockquantity|default(10) - 2 }}, {{ part.stockquantity|default(10) - 4 }}, {{ part.stockquantity|default(10) }}],
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Reorder Level',
                        data: [{{ part.reorderlevel|default(5) }}, {{ part.reorderlevel|default(5) }}, {{ part.reorderlevel|default(5) }}, {{ part.reorderlevel|default(5) }}, {{ part.reorderlevel|default(5) }}, {{ part.reorderlevel|default(5) }}],
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderDash: [5, 5],
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantity'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                }
            }
        });

        // Sample data for usage trend chart
        const usageTrendCtx = document.getElementById('usageTrendChart').getContext('2d');
        const usageTrendChart = new Chart(usageTrendCtx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [
                    {
                        label: 'Monthly Usage',
                        data: [3, 5, 2, 4, 6, 2],
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantity Used'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
