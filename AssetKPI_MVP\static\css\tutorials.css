/**
 * Tutorials Styles
 * 
 * This file contains styles for the tutorial system and guided tours
 * across the AssetKPI application.
 */

/* ===== Tutorial Container ===== */

.tutorial-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.tutorial-header {
  text-align: center;
  margin-bottom: 3rem;
}

.tutorial-title {
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 1rem;
}

.tutorial-description {
  color: #7f8c8d;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== Tutorial Grid ===== */

.tutorial-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.tutorial-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #dee2e6;
}

.tutorial-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.tutorial-image {
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
}

.tutorial-content {
  padding: 1.5rem;
}

.tutorial-card-title {
  color: #2c3e50;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.tutorial-card-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.tutorial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #95a5a6;
}

.tutorial-duration {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.tutorial-difficulty {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.difficulty-beginner {
  background: #d5f4e6;
  color: #27ae60;
}

.difficulty-intermediate {
  background: #fef9e7;
  color: #f39c12;
}

.difficulty-advanced {
  background: #fadbd8;
  color: #e74c3c;
}

.tutorial-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-tutorial {
  flex: 1;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  text-decoration: none;
  text-align: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-start {
  background: #3498db;
  color: white;
  border: 1px solid #3498db;
}

.btn-start:hover {
  background: #2980b9;
  border-color: #2980b9;
  color: white;
}

.btn-preview {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-preview:hover {
  background: #3498db;
  color: white;
}

/* ===== Tutorial Steps ===== */

.tutorial-steps {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 2rem;
  margin-bottom: 2rem;
}

.steps-header {
  margin-bottom: 2rem;
}

.steps-title {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.steps-description {
  color: #7f8c8d;
  line-height: 1.6;
}

.step-list {
  list-style: none;
  padding: 0;
  counter-reset: step-counter;
}

.step-item {
  counter-increment: step-counter;
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #ecf0f1;
}

.step-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-number::before {
  content: counter(step-counter);
}

.step-content {
  flex: 1;
}

.step-title {
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.step-code {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  overflow-x: auto;
}

/* ===== Tutorial Navigation ===== */

.tutorial-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #dee2e6;
}

.nav-button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.nav-previous {
  background: transparent;
  color: #7f8c8d;
  border: 1px solid #bdc3c7;
}

.nav-previous:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.nav-next {
  background: #3498db;
  color: white;
  border: 1px solid #3498db;
}

.nav-next:hover {
  background: #2980b9;
  border-color: #2980b9;
  color: white;
}

/* ===== Progress Indicator ===== */

.tutorial-progress {
  background: #ecf0f1;
  height: 4px;
  border-radius: 2px;
  margin-bottom: 2rem;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: #3498db;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* ===== Responsive Design ===== */

@media (max-width: 767.98px) {
  .tutorial-container {
    padding: 1rem;
  }
  
  .tutorial-title {
    font-size: 2rem;
  }
  
  .tutorial-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .tutorial-content {
    padding: 1rem;
  }
  
  .tutorial-actions {
    flex-direction: column;
  }
  
  .tutorial-steps {
    padding: 1rem;
  }
  
  .step-item {
    flex-direction: column;
    text-align: center;
  }
  
  .step-number {
    margin-right: 0;
    margin-bottom: 1rem;
  }
  
  .tutorial-navigation {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-button {
    width: 100%;
    text-align: center;
  }
}
