# Tutorials

Welcome to the AssetKPI Integration Tutorials. These tutorials provide step-by-step guidance for integrating with AssetKPI using webhooks, ERP connectors, and the Python SDK.

## Table of Contents

- [Getting Started with Webhooks](./getting-started-webhooks.md)
- [Setting Up ERP Integration](./setting-up-erp-integration.md)
- [Data Mapping Best Practices](./data-mapping-best-practices.md)
- [Troubleshooting Integration Issues](./troubleshooting-integration.md)
- [Advanced Integration Scenarios](./advanced-integration-scenarios.md)

## Getting Started with Webhooks

Learn how to set up and use webhooks to receive real-time notifications from AssetKPI.

[Read the tutorial](./getting-started-webhooks.md)

## Setting Up ERP Integration

Learn how to configure and manage ERP integration to synchronize data between AssetKPI and your ERP system.

[Read the tutorial](./setting-up-erp-integration.md)

## Data Mapping Best Practices

Learn best practices for mapping data between AssetKPI and external systems.

[Read the tutorial](./data-mapping-best-practices.md)

## Troubleshooting Integration Issues

Learn how to identify and resolve common integration issues.

[Read the tutorial](./troubleshooting-integration.md)

## Advanced Integration Scenarios

Explore advanced integration scenarios and learn how to implement complex integration solutions.

[Read the tutorial](./advanced-integration-scenarios.md)
