import requests
import json
import time

# Base URL
base_url = "http://127.0.0.1:8000"

# Test login
def test_login():
    print("Testing login page...")
    response = requests.get(f"{base_url}/login")
    print(f"Status code: {response.status_code}")
    print(f"Response length: {len(response.text)} characters")
    print("Login page loaded successfully" if response.status_code == 200 else "Failed to load login page")
    print()

# Test debug token endpoint
def test_debug_token():
    print("Testing debug token endpoint...")
    # This is just a test, so we're using a fake token
    headers = {
        "Authorization": "Bearer fake_token"
    }
    response = requests.post(f"{base_url}/api/debug-token", headers=headers)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    print()

# Test user check endpoint
def test_user_check():
    print("Testing user check endpoint...")
    # Use a known user ID from the database
    # Try with the real Firebase user first
    user_id = "uasUzj4IXFaqJC3pcEiOCL3vD3t2"
    response = requests.get(f"{base_url}/api/user-check/{user_id}")
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    print()

# Run tests
if __name__ == "__main__":
    # Wait for the server to reload
    print("Waiting for server to reload...")
    time.sleep(5)
    print("Running tests...\n")

    test_login()
    test_debug_token()
    test_user_check()
