import psycopg2
from datetime import datetime, timezone
import sys

# Database connection string
DB_CONNECTION = "postgresql://postgres:Arcanum@localhost:5432/AssetKPI"

def add_firebase_test_user(firebase_uid, email, role="ADMIN", full_name="Firebase Test User"):
    """
    Add a test user to the database with the given Firebase UID.
    """
    try:
        # Connect to the database
        conn = psycopg2.connect(DB_CONNECTION)
        cur = conn.cursor()
        
        # Check if user already exists
        cur.execute("SELECT user_id FROM users WHERE user_id = %s", (firebase_uid,))
        if cur.fetchone():
            print(f"User with UID {firebase_uid} already exists in the database.")
            conn.close()
            return
        
        # Insert the user
        created_at = datetime.now(timezone.utc)
        cur.execute(
            "INSERT INTO users (user_id, email, role, full_name, created_at, api_access_enabled) VALUES (%s, %s, %s, %s, %s, %s)",
            (firebase_uid, email, role, full_name, created_at, True)
        )
        
        # Commit the transaction
        conn.commit()
        print(f"Successfully added user with UID {firebase_uid} to the database.")
        
        # Close the connection
        conn.close()
    except Exception as e:
        print(f"Error adding user to database: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python add_firebase_test_user.py <firebase_uid> <email> [role] [full_name]")
        sys.exit(1)
    
    firebase_uid = sys.argv[1]
    email = sys.argv[2]
    role = sys.argv[3] if len(sys.argv) > 3 else "ADMIN"
    full_name = sys.argv[4] if len(sys.argv) > 4 else "Firebase Test User"
    
    add_firebase_test_user(firebase_uid, email, role, full_name)
