/**
 * Onboarding Wizard Component
 * 
 * This component guides new users through the initial setup process.
 */

class OnboardingWizard {
    /**
     * Initialize the onboarding wizard.
     * 
     * @param {Object} options - Configuration options
     * @param {string} options.containerId - ID of the container element
     * @param {boolean} options.canSkip - Whether the wizard can be skipped
     * @param {Function} options.onComplete - Callback function when wizard is completed
     */
    constructor(options = {}) {
        this.containerId = options.containerId || 'onboarding-wizard-container';
        this.canSkip = options.canSkip !== undefined ? options.canSkip : true;
        this.onComplete = options.onComplete || function() {};
        
        // Define wizard steps
        this.steps = [
            {
                id: 'welcome',
                title: 'Welcome to AssetKPI',
                template: 'welcome-template',
                validate: () => true // No validation needed for welcome screen
            },
            {
                id: 'profile',
                title: 'Profile Setup',
                template: 'profile-template',
                validate: this.validateProfile.bind(this)
            },
            {
                id: 'organization',
                title: 'Organization Setup',
                template: 'organization-template',
                validate: this.validateOrganization.bind(this)
            },
            {
                id: 'features',
                title: 'Feature Selection',
                template: 'features-template',
                validate: this.validateFeatures.bind(this)
            },
            {
                id: 'data-import',
                title: 'Data Import Options',
                template: 'data-import-template',
                validate: this.validateDataImport.bind(this)
            },
            {
                id: 'dashboard',
                title: 'Dashboard Customization',
                template: 'dashboard-template',
                validate: this.validateDashboard.bind(this)
            },
            {
                id: 'completion',
                title: 'Setup Complete',
                template: 'completion-template',
                validate: () => true // No validation needed for completion screen
            }
        ];
        
        // Initialize state
        this.currentStepIndex = 0;
        this.data = {
            profile: {},
            organization: {},
            features: [],
            dataImport: {
                importMethod: 'none'
            },
            dashboard: {
                widgets: []
            }
        };
        
        // Load saved state if available
        this.loadState();
    }
    
    /**
     * Initialize the wizard UI.
     */
    init() {
        // Get container element
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            console.error(`Container element with ID "${this.containerId}" not found.`);
            return;
        }
        
        // Create wizard elements
        this.createWizardElements();
        
        // Render current step
        this.renderStep(this.currentStepIndex);
        
        // Add event listeners
        this.addEventListeners();
    }
    
    /**
     * Create wizard UI elements.
     */
    createWizardElements() {
        // Create wizard container
        this.wizardEl = document.createElement('div');
        this.wizardEl.className = 'onboarding-wizard';
        
        // Create progress bar
        this.progressEl = document.createElement('div');
        this.progressEl.className = 'onboarding-progress';
        this.wizardEl.appendChild(this.progressEl);
        
        // Create step indicators
        this.stepIndicatorsEl = document.createElement('ul');
        this.stepIndicatorsEl.className = 'step-indicators';
        this.progressEl.appendChild(this.stepIndicatorsEl);
        
        // Create step indicators for each step
        this.steps.forEach((step, index) => {
            const indicatorEl = document.createElement('li');
            indicatorEl.className = 'step-indicator';
            indicatorEl.dataset.step = index;
            indicatorEl.innerHTML = `
                <span class="step-number">${index + 1}</span>
                <span class="step-title">${step.title}</span>
            `;
            this.stepIndicatorsEl.appendChild(indicatorEl);
        });
        
        // Create content area
        this.contentEl = document.createElement('div');
        this.contentEl.className = 'onboarding-content';
        this.wizardEl.appendChild(this.contentEl);
        
        // Create navigation area
        this.navigationEl = document.createElement('div');
        this.navigationEl.className = 'onboarding-navigation';
        this.wizardEl.appendChild(this.navigationEl);
        
        // Create navigation buttons
        this.backBtn = document.createElement('button');
        this.backBtn.className = 'btn btn-secondary back-btn';
        this.backBtn.textContent = 'Back';
        this.navigationEl.appendChild(this.backBtn);
        
        this.skipBtn = document.createElement('button');
        this.skipBtn.className = 'btn btn-link skip-btn';
        this.skipBtn.textContent = 'Skip';
        this.navigationEl.appendChild(this.skipBtn);
        
        this.nextBtn = document.createElement('button');
        this.nextBtn.className = 'btn btn-primary next-btn';
        this.nextBtn.textContent = 'Next';
        this.navigationEl.appendChild(this.nextBtn);
        
        // Add wizard to container
        this.container.appendChild(this.wizardEl);
    }
    
    /**
     * Add event listeners to wizard elements.
     */
    addEventListeners() {
        // Next button click
        this.nextBtn.addEventListener('click', () => {
            this.handleNext();
        });
        
        // Back button click
        this.backBtn.addEventListener('click', () => {
            this.handleBack();
        });
        
        // Skip button click
        this.skipBtn.addEventListener('click', () => {
            this.handleSkip();
        });
        
        // Step indicator click
        this.stepIndicatorsEl.querySelectorAll('.step-indicator').forEach(indicator => {
            indicator.addEventListener('click', (event) => {
                const stepIndex = parseInt(event.currentTarget.dataset.step, 10);
                if (stepIndex < this.currentStepIndex) {
                    this.goToStep(stepIndex);
                }
            });
        });
    }
    
    /**
     * Render a specific step.
     * 
     * @param {number} stepIndex - Index of the step to render
     */
    renderStep(stepIndex) {
        const step = this.steps[stepIndex];
        
        // Update step indicators
        this.stepIndicatorsEl.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            indicator.classList.remove('active', 'completed');
            if (index === stepIndex) {
                indicator.classList.add('active');
            } else if (index < stepIndex) {
                indicator.classList.add('completed');
            }
        });
        
        // Update content
        const templateEl = document.getElementById(step.template);
        if (!templateEl) {
            console.error(`Template element with ID "${step.template}" not found.`);
            return;
        }
        
        this.contentEl.innerHTML = templateEl.innerHTML;
        
        // Fill form fields with saved data
        this.populateFormFields();
        
        // Update navigation buttons
        this.updateNavigationButtons();
    }
    
    /**
     * Populate form fields with saved data.
     */
    populateFormFields() {
        const step = this.steps[this.currentStepIndex];
        const stepData = this.data[step.id];
        
        if (!stepData) {
            return;
        }
        
        // Find all form elements in the current step
        const formElements = this.contentEl.querySelectorAll('input, select, textarea');
        
        formElements.forEach(element => {
            const name = element.name;
            if (!name) {
                return;
            }
            
            // Set value based on element type
            if (element.type === 'checkbox' || element.type === 'radio') {
                if (Array.isArray(stepData[name])) {
                    element.checked = stepData[name].includes(element.value);
                } else {
                    element.checked = stepData[name] === element.value;
                }
            } else {
                if (stepData[name] !== undefined) {
                    element.value = stepData[name];
                }
            }
        });
    }
    
    /**
     * Update navigation buttons based on current step.
     */
    updateNavigationButtons() {
        // Back button
        if (this.currentStepIndex === 0) {
            this.backBtn.style.display = 'none';
        } else {
            this.backBtn.style.display = 'inline-block';
        }
        
        // Skip button
        if (!this.canSkip || this.currentStepIndex === this.steps.length - 1) {
            this.skipBtn.style.display = 'none';
        } else {
            this.skipBtn.style.display = 'inline-block';
        }
        
        // Next button
        if (this.currentStepIndex === this.steps.length - 1) {
            this.nextBtn.textContent = 'Finish';
        } else {
            this.nextBtn.textContent = 'Next';
        }
    }
    
    /**
     * Handle next button click.
     */
    handleNext() {
        const currentStep = this.steps[this.currentStepIndex];
        
        // Validate current step
        if (!this.validateStep()) {
            return;
        }
        
        // Save data from current step
        this.saveStepData();
        
        // Check if this is the last step
        if (this.currentStepIndex === this.steps.length - 1) {
            this.complete();
            return;
        }
        
        // Go to next step
        this.goToStep(this.currentStepIndex + 1);
    }
    
    /**
     * Handle back button click.
     */
    handleBack() {
        if (this.currentStepIndex > 0) {
            this.goToStep(this.currentStepIndex - 1);
        }
    }
    
    /**
     * Handle skip button click.
     */
    handleSkip() {
        // Save current progress
        this.saveStepData();
        
        // Go to next step
        this.goToStep(this.currentStepIndex + 1);
    }
    
    /**
     * Go to a specific step.
     * 
     * @param {number} stepIndex - Index of the step to go to
     */
    goToStep(stepIndex) {
        if (stepIndex < 0 || stepIndex >= this.steps.length) {
            return;
        }
        
        this.currentStepIndex = stepIndex;
        this.renderStep(stepIndex);
        this.saveState();
    }
    
    /**
     * Validate the current step.
     * 
     * @returns {boolean} - Whether the step is valid
     */
    validateStep() {
        const step = this.steps[this.currentStepIndex];
        return step.validate();
    }
    
    /**
     * Validate profile step.
     * 
     * @returns {boolean} - Whether the step is valid
     */
    validateProfile() {
        const fullNameEl = this.contentEl.querySelector('[name="fullName"]');
        const roleEl = this.contentEl.querySelector('[name="role"]');
        
        let isValid = true;
        let errorMessage = '';
        
        // Validate full name
        if (!fullNameEl.value.trim()) {
            isValid = false;
            errorMessage = 'Please enter your full name.';
            fullNameEl.classList.add('is-invalid');
        } else {
            fullNameEl.classList.remove('is-invalid');
        }
        
        // Validate role
        if (!roleEl.value) {
            isValid = false;
            errorMessage = errorMessage || 'Please select your role.';
            roleEl.classList.add('is-invalid');
        } else {
            roleEl.classList.remove('is-invalid');
        }
        
        // Show error message if any
        const errorEl = this.contentEl.querySelector('.error-message');
        if (errorEl) {
            errorEl.textContent = errorMessage;
            errorEl.style.display = isValid ? 'none' : 'block';
        }
        
        return isValid;
    }
    
    /**
     * Validate organization step.
     * 
     * @returns {boolean} - Whether the step is valid
     */
    validateOrganization() {
        const orgNameEl = this.contentEl.querySelector('[name="orgName"]');
        const industryEl = this.contentEl.querySelector('[name="industry"]');
        
        let isValid = true;
        let errorMessage = '';
        
        // Validate organization name
        if (!orgNameEl.value.trim()) {
            isValid = false;
            errorMessage = 'Please enter your organization name.';
            orgNameEl.classList.add('is-invalid');
        } else {
            orgNameEl.classList.remove('is-invalid');
        }
        
        // Validate industry
        if (!industryEl.value) {
            isValid = false;
            errorMessage = errorMessage || 'Please select your industry.';
            industryEl.classList.add('is-invalid');
        } else {
            industryEl.classList.remove('is-invalid');
        }
        
        // Show error message if any
        const errorEl = this.contentEl.querySelector('.error-message');
        if (errorEl) {
            errorEl.textContent = errorMessage;
            errorEl.style.display = isValid ? 'none' : 'block';
        }
        
        return isValid;
    }
    
    /**
     * Validate features step.
     * 
     * @returns {boolean} - Whether the step is valid
     */
    validateFeatures() {
        const featureEls = this.contentEl.querySelectorAll('[name="features"]:checked');
        
        let isValid = featureEls.length > 0;
        let errorMessage = '';
        
        // Validate feature selection
        if (!isValid) {
            errorMessage = 'Please select at least one feature.';
        }
        
        // Show error message if any
        const errorEl = this.contentEl.querySelector('.error-message');
        if (errorEl) {
            errorEl.textContent = errorMessage;
            errorEl.style.display = isValid ? 'none' : 'block';
        }
        
        return isValid;
    }
    
    /**
     * Validate data import step.
     * 
     * @returns {boolean} - Whether the step is valid
     */
    validateDataImport() {
        // This step is optional, so always valid
        return true;
    }
    
    /**
     * Validate dashboard step.
     * 
     * @returns {boolean} - Whether the step is valid
     */
    validateDashboard() {
        const widgetEls = this.contentEl.querySelectorAll('[name="widgets"]:checked');
        
        let isValid = widgetEls.length > 0;
        let errorMessage = '';
        
        // Validate widget selection
        if (!isValid) {
            errorMessage = 'Please select at least one widget.';
        }
        
        // Show error message if any
        const errorEl = this.contentEl.querySelector('.error-message');
        if (errorEl) {
            errorEl.textContent = errorMessage;
            errorEl.style.display = isValid ? 'none' : 'block';
        }
        
        return isValid;
    }
    
    /**
     * Save data from the current step.
     */
    saveStepData() {
        const step = this.steps[this.currentStepIndex];
        const formData = {};
        
        // Find all form elements in the current step
        const formElements = this.contentEl.querySelectorAll('input, select, textarea');
        
        formElements.forEach(element => {
            const name = element.name;
            if (!name) {
                return;
            }
            
            // Get value based on element type
            if (element.type === 'checkbox') {
                if (!formData[name]) {
                    formData[name] = [];
                }
                if (element.checked) {
                    formData[name].push(element.value);
                }
            } else if (element.type === 'radio') {
                if (element.checked) {
                    formData[name] = element.value;
                }
            } else {
                formData[name] = element.value;
            }
        });
        
        // Save data for the current step
        this.data[step.id] = formData;
        
        // Save state to localStorage
        this.saveState();
    }
    
    /**
     * Save wizard state to localStorage.
     */
    saveState() {
        const state = {
            currentStepIndex: this.currentStepIndex,
            data: this.data
        };
        
        localStorage.setItem('onboardingWizardState', JSON.stringify(state));
        
        // Also save to server
        this.saveStateToServer();
    }
    
    /**
     * Load wizard state from localStorage.
     */
    loadState() {
        const stateJson = localStorage.getItem('onboardingWizardState');
        if (!stateJson) {
            return;
        }
        
        try {
            const state = JSON.parse(stateJson);
            this.currentStepIndex = state.currentStepIndex || 0;
            this.data = state.data || {};
        } catch (error) {
            console.error('Error loading onboarding wizard state:', error);
        }
    }
    
    /**
     * Save wizard state to server.
     */
    saveStateToServer() {
        // Create payload
        const payload = {
            step: this.currentStepIndex,
            data: this.data
        };
        
        // Send to server
        fetch('/api/users/onboarding/progress', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to save onboarding progress');
            }
            return response.json();
        })
        .catch(error => {
            console.error('Error saving onboarding progress:', error);
        });
    }
    
    /**
     * Complete the wizard.
     */
    complete() {
        // Save final state
        this.saveState();
        
        // Mark as complete on server
        fetch('/api/users/onboarding/complete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to complete onboarding');
            }
            return response.json();
        })
        .then(data => {
            // Clear localStorage state
            localStorage.removeItem('onboardingWizardState');
            
            // Call onComplete callback
            this.onComplete(this.data);
        })
        .catch(error => {
            console.error('Error completing onboarding:', error);
        });
    }
}

// Export the OnboardingWizard class
export default OnboardingWizard;
