# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Fix the get_config_value function
old_function = '''def get_config_value(app: FastAPI, param_name: str, default_value: Any) -> Any:
    """
    Get a configuration value from the application state.
    If the parameter doesn't exist, return the default value.
    """
    try:
        config = app.state.config
        if param_name in config:
            return config[param_name]
        else:
            print(f"WARNING: Config parameter '{param_name}' not found in app state, using default.")
            return default_value
    except Exception as e:
        print(f"ERROR: Could not retrieve config parameter '{param_name}' from app state: {e}")
        return default_value'''

new_function = '''def get_config_value(db_or_app, param_name: str, default_value: Any) -> Any:
    """
    Get a configuration value from the database or application state.
    If the parameter doesn't exist, return the default value.
    
    Args:
        db_or_app: Either a database session or the FastAPI app
        param_name: The name of the parameter to retrieve
        default_value: The default value to return if the parameter is not found
    """
    # Check if we're getting from app state or database
    if isinstance(db_or_app, FastAPI):
        try:
            if hasattr(db_or_app.state, 'config'):
                config = db_or_app.state.config
                if param_name in config:
                    return config[param_name]
            print(f"WARNING: Config parameter '{param_name}' not found in app state, using default.")
            return default_value
        except Exception as e:
            print(f"ERROR: Could not retrieve config parameter '{param_name}' from app state: {e}")
            return default_value
    else:
        # Assume it's a database session
        try:
            # Here you would query the database for the parameter
            # For now, just return the default value
            print(f"INFO: Using default value for '{param_name}': {default_value}")
            return default_value
        except Exception as e:
            print(f"ERROR: Could not retrieve config parameter '{param_name}' from database: {e}")
            return default_value'''

content = content.replace(old_function, new_function)

# Fix the configuration loading code
old_config_loading = '''    print("INFO:     Starting up scheduler and loading configuration...")
    db = SessionLocal()
    try:
        # Load configuration values from the database
        global_config = {}
        global_config['DEFAULT_ORDERING_COST'] = get_config_value(db, 'DEFAULT_ORDERING_COST', Decimal('50.00'))
        global_config['DEFAULT_ANNUAL_HOLDING_COST_PERCENT'] = get_config_value(db, 'DEFAULT_ANNUAL_HOLDING_COST_PERCENT', Decimal('0.15'))
        global_config['SAFETY_STOCK_SERVICE_LEVEL_Z'] = get_config_value(db, 'SAFETY_STOCK_SERVICE_LEVEL_Z', Decimal('1.65'))
        global_config['SAFETY_STOCK_LEAD_TIME_VARIABILITY_FACTOR'] = get_config_value(db, 'SAFETY_STOCK_LEAD_TIME_VARIABILITY_FACTOR', Decimal('0.2'))
        global_config['SAFETY_STOCK_DEMAND_VARIABILITY_FACTOR'] = get_config_value(db, 'SAFETY_STOCK_DEMAND_VARIABILITY_FACTOR', Decimal('0.3'))

        # Store the configuration in the app state (or a global variable)
        app.state.config = global_config'''

new_config_loading = '''    print("INFO:     Starting up scheduler and loading configuration...")
    db = SessionLocal()
    try:
        # Initialize app.state.config if it doesn't exist
        if not hasattr(app, 'state'):
            app.state = SimpleNamespace()
        if not hasattr(app.state, 'config'):
            app.state.config = {}
            
        # Load configuration values from the database
        app.state.config['DEFAULT_ORDERING_COST'] = get_config_value(db, 'DEFAULT_ORDERING_COST', Decimal('50.00'))
        app.state.config['DEFAULT_ANNUAL_HOLDING_COST_PERCENT'] = get_config_value(db, 'DEFAULT_ANNUAL_HOLDING_COST_PERCENT', Decimal('0.15'))
        app.state.config['SAFETY_STOCK_SERVICE_LEVEL_Z'] = get_config_value(db, 'SAFETY_STOCK_SERVICE_LEVEL_Z', Decimal('1.65'))
        app.state.config['SAFETY_STOCK_LEAD_TIME_VARIABILITY_FACTOR'] = get_config_value(db, 'SAFETY_STOCK_LEAD_TIME_VARIABILITY_FACTOR', Decimal('0.2'))
        app.state.config['SAFETY_STOCK_DEMAND_VARIABILITY_FACTOR'] = get_config_value(db, 'SAFETY_STOCK_DEMAND_VARIABILITY_FACTOR', Decimal('0.3'))
        
        # Add other configuration parameters
        app.state.config['overstock_days_threshold'] = 180
        app.state.config['obsolete_days_threshold'] = 365
        app.state.config['overstock_safety_stock_multiplier'] = 3.0
        app.state.config['eoq_order_threshold'] = 0.8
        app.state.config['ordering_cost'] = Decimal('50.00')
        app.state.config['holding_cost_percent'] = Decimal('0.15')'''

content = content.replace(old_config_loading, new_config_loading)

# Add the SimpleNamespace import
if 'from types import SimpleNamespace' not in content:
    import_line = 'from decimal import Decimal'
    new_import_line = 'from decimal import Decimal\nfrom types import SimpleNamespace'
    content = content.replace(import_line, new_import_line)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed the configuration loading code in main.py")
