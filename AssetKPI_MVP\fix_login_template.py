# Create a temporary file with the fixed code
with open('templates/login.html', 'r') as file:
    content = file.read()

# Find the script section
script_start = '    <script>\n        // DOM elements'

# Replace it with the updated script section
updated_script = '''    <script>
        // Firebase configuration from server
        const firebaseConfig = {{ firebase_config | tojson }};
        
        // Initialize Firebase with the configuration from the server
        if (firebaseConfig.apiKey !== "YOUR_API_KEY") {
            firebase.initializeApp(firebaseConfig);
            console.log('Firebase initialized with config from server');
        } else {
            console.error('Firebase configuration is missing or invalid');
        }
        
        // DOM elements'''

content = content.replace(script_start, updated_script)

# Write the fixed content back to the file
with open('templates/login.html', 'w') as file:
    file.write(content)

print("Fixed the login.html template")
