"""
Recommendations API client module.

This module provides methods for interacting with the recommendation-related endpoints
of the AssetKPI API.
"""

from typing import Dict, List, Optional, Union, Any

from .client import AssetKPIClient


class RecommendationsClient:
    """Client for recommendation-related API endpoints."""
    
    def __init__(self, client: AssetKPIClient):
        """
        Initialize the recommendations client.
        
        Args:
            client: The AssetKPI API client
        """
        self.client = client
    
    def get_recommendations(
        self,
        status: Optional[str] = None,
        part_id: Optional[int] = None,
        recommendation_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """
        Get a list of inventory recommendations.
        
        Args:
            status: Filter by recommendation status
            part_id: Filter by part ID
            recommendation_type: Filter by recommendation type
            limit: Maximum number of items to return
            offset: Number of items to skip
            
        Returns:
            Paginated response with recommendations
        """
        params = {
            "limit": limit,
            "offset": offset,
        }
        
        if status:
            params["status"] = status
        
        if part_id:
            params["part_id"] = part_id
        
        if recommendation_type:
            params["recommendation_type"] = recommendation_type
        
        return self.client.get("/recommendations", params=params)
    
    def update_recommendation_status(
        self, recommendation_id: int, new_status: str
    ) -> Dict[str, Any]:
        """
        Update the status of an inventory recommendation.
        
        Args:
            recommendation_id: ID of the recommendation
            new_status: New status for the recommendation
            
        Returns:
            Update confirmation
        """
        return self.client.patch(
            f"/recommendations/{recommendation_id}/status",
            data={"new_status": new_status},
        )
