"""
Work Orders API client module.

This module provides methods for interacting with the work order-related endpoints
of the AssetKPI API.
"""

from typing import Dict, List, Optional, Union, Any

from .client import AssetKPIClient


class WorkOrdersClient:
    """Client for work order-related API endpoints."""
    
    def __init__(self, client: AssetKPIClient):
        """
        Initialize the work orders client.
        
        Args:
            client: The AssetKPI API client
        """
        self.client = client
    
    def get_workorders(
        self,
        limit: int = 100,
        offset: int = 0,
        sort: str = "workorderid",
        order: str = "asc",
        filters: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Get a list of work orders.
        
        Args:
            limit: Maximum number of items to return
            offset: Number of items to skip
            sort: Field to sort by
            order: Sort order (asc or desc)
            filters: List of filter conditions
            
        Returns:
            Paginated response with work orders
        """
        params = {
            "limit": limit,
            "offset": offset,
            "sort": sort,
            "order": order,
        }
        
        if filters:
            params["filters"] = filters
        
        return self.client.get("/workorders", params=params)
    
    def get_workorder(self, workorder_id: int) -> Dict[str, Any]:
        """
        Get a specific work order.
        
        Args:
            workorder_id: ID of the work order
            
        Returns:
            Work order details
        """
        return self.client.get(f"/workorders/{workorder_id}")
    
    def create_workorder(self, workorder_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new work order.
        
        Args:
            workorder_data: Work order data
            
        Returns:
            Created work order
        """
        return self.client.post("/workorders", data=workorder_data)
    
    def update_workorder(
        self, workorder_id: int, workorder_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a work order.
        
        Args:
            workorder_id: ID of the work order
            workorder_data: Updated work order data
            
        Returns:
            Updated work order
        """
        return self.client.put(f"/workorders/{workorder_id}", data=workorder_data)
    
    def delete_workorder(self, workorder_id: int) -> Dict[str, Any]:
        """
        Delete a work order.
        
        Args:
            workorder_id: ID of the work order
            
        Returns:
            Deletion confirmation
        """
        return self.client.delete(f"/workorders/{workorder_id}")
    
    def get_workorders_count(self, status: Optional[str] = None) -> Dict[str, Any]:
        """
        Get the count of work orders, optionally filtered by status.
        
        Args:
            status: Filter by work order status
            
        Returns:
            Work order count
        """
        params = {}
        if status:
            params["status"] = status
        
        return self.client.get("/workorders/count", params=params)
    
    def ingest_workorder(self, workorder_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ingest a single work order.
        
        Args:
            workorder_data: Work order data
            
        Returns:
            Ingestion confirmation
        """
        return self.client.post("/ingest/workorder", data=workorder_data)
