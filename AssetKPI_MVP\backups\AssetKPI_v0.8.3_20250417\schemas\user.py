"""
User schemas for the AssetKPI application.

This module defines the Pydantic models for user data.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """
    Base schema for user data.
    """
    email: EmailStr
    role: str
    full_name: Optional[str] = None


class UserCreate(UserBase):
    """
    Schema for creating a new user.
    """
    user_id: str
    password: Optional[str] = None


class UserUpdate(BaseModel):
    """
    Schema for updating a user.
    """
    email: Optional[EmailStr] = None
    role: Optional[str] = None
    full_name: Optional[str] = None


class UserInDB(UserBase):
    """
    Schema for user data in the database.
    """
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    onboarding_completed: bool = False
    onboarding_step: Optional[int] = None
    
    class Config:
        orm_mode = True


class UserOnboardingUpdate(BaseModel):
    """
    Schema for updating user onboarding data.
    """
    onboarding_step: int
    onboarding_data: Dict[str, Any]
    onboarding_completed: Optional[bool] = False
