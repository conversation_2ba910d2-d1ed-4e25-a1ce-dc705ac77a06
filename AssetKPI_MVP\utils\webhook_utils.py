"""
Webhook utility functions for the AssetKPI application.

This module provides utility functions for triggering webhook events.
"""

import logging
from typing import Dict, Any, Optional, List, Union
from sqlalchemy.orm import Session

from services.webhook_service import create_webhook_event
from models.webhooks import WebhookEventType

# Create a logger for this module
logger = logging.getLogger(__name__)


def trigger_webhook_event(
    db: Session,
    event_type: Union[str, WebhookEventType],
    resource_id: Optional[int] = None,
    resource_type: Optional[str] = None,
    data: Optional[Dict[str, Any]] = None,
    subscription_ids: Optional[List[int]] = None
) -> List[int]:
    """
    Trigger a webhook event.
    
    Args:
        db: Database session
        event_type: Type of the event (can be string or WebhookEventType enum)
        resource_id: Optional ID of the resource related to the event
        resource_type: Optional type of the resource related to the event
        data: Optional additional data for the event
        subscription_ids: Optional list of subscription IDs to filter by
        
    Returns:
        List of created event IDs
    """
    try:
        # Convert event_type to string if it's an enum
        if isinstance(event_type, WebhookEventType):
            event_type_str = event_type.value
        else:
            event_type_str = event_type
        
        # Prepare payload
        payload = {
            "event_type": event_type_str
        }
        
        if resource_id is not None:
            payload["resource_id"] = resource_id
        
        if resource_type is not None:
            payload["resource_type"] = resource_type
        
        if data is not None:
            payload["data"] = data
        
        # Create webhook events
        event_ids = create_webhook_event(
            db=db,
            event_type=event_type_str,
            payload=payload,
            subscription_ids=subscription_ids
        )
        
        return event_ids
    
    except Exception as e:
        logger.error(f"Error triggering webhook event: {str(e)}")
        return []


# Convenience functions for common event types

def trigger_inventory_updated(
    db: Session,
    part_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger an inventory.updated webhook event.
    
    Args:
        db: Database session
        part_id: ID of the spare part
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.INVENTORY_UPDATED,
        resource_id=part_id,
        resource_type="sparepart",
        data=data
    )


def trigger_inventory_created(
    db: Session,
    part_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger an inventory.created webhook event.
    
    Args:
        db: Database session
        part_id: ID of the spare part
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.INVENTORY_CREATED,
        resource_id=part_id,
        resource_type="sparepart",
        data=data
    )


def trigger_inventory_deleted(
    db: Session,
    part_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger an inventory.deleted webhook event.
    
    Args:
        db: Database session
        part_id: ID of the spare part
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.INVENTORY_DELETED,
        resource_id=part_id,
        resource_type="sparepart",
        data=data
    )


def trigger_inventory_threshold_reached(
    db: Session,
    part_id: int,
    threshold_type: str,
    current_value: float,
    threshold_value: float,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger an inventory.threshold_reached webhook event.
    
    Args:
        db: Database session
        part_id: ID of the spare part
        threshold_type: Type of threshold (e.g., "reorder_point", "safety_stock")
        current_value: Current value
        threshold_value: Threshold value
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    threshold_data = {
        "threshold_type": threshold_type,
        "current_value": current_value,
        "threshold_value": threshold_value
    }
    
    if data:
        threshold_data.update(data)
    
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.INVENTORY_THRESHOLD_REACHED,
        resource_id=part_id,
        resource_type="sparepart",
        data=threshold_data
    )


def trigger_workorder_created(
    db: Session,
    workorder_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger a workorder.created webhook event.
    
    Args:
        db: Database session
        workorder_id: ID of the work order
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.WORKORDER_CREATED,
        resource_id=workorder_id,
        resource_type="workorder",
        data=data
    )


def trigger_workorder_updated(
    db: Session,
    workorder_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger a workorder.updated webhook event.
    
    Args:
        db: Database session
        workorder_id: ID of the work order
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.WORKORDER_UPDATED,
        resource_id=workorder_id,
        resource_type="workorder",
        data=data
    )


def trigger_workorder_completed(
    db: Session,
    workorder_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger a workorder.completed webhook event.
    
    Args:
        db: Database session
        workorder_id: ID of the work order
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.WORKORDER_COMPLETED,
        resource_id=workorder_id,
        resource_type="workorder",
        data=data
    )


def trigger_asset_created(
    db: Session,
    asset_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger an asset.created webhook event.
    
    Args:
        db: Database session
        asset_id: ID of the asset
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.ASSET_CREATED,
        resource_id=asset_id,
        resource_type="asset",
        data=data
    )


def trigger_asset_updated(
    db: Session,
    asset_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger an asset.updated webhook event.
    
    Args:
        db: Database session
        asset_id: ID of the asset
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.ASSET_UPDATED,
        resource_id=asset_id,
        resource_type="asset",
        data=data
    )


def trigger_kpi_calculated(
    db: Session,
    kpi_name: str,
    kpi_value: float,
    asset_id: Optional[int] = None,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger a kpi.calculated webhook event.
    
    Args:
        db: Database session
        kpi_name: Name of the KPI
        kpi_value: Value of the KPI
        asset_id: Optional ID of the asset
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    kpi_data = {
        "kpi_name": kpi_name,
        "kpi_value": kpi_value
    }
    
    if asset_id is not None:
        kpi_data["asset_id"] = asset_id
    
    if data:
        kpi_data.update(data)
    
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.KPI_CALCULATED,
        resource_type="kpi",
        data=kpi_data
    )


def trigger_recommendation_created(
    db: Session,
    recommendation_id: int,
    data: Optional[Dict[str, Any]] = None
) -> List[int]:
    """
    Trigger a recommendation.created webhook event.
    
    Args:
        db: Database session
        recommendation_id: ID of the recommendation
        data: Optional additional data for the event
        
    Returns:
        List of created event IDs
    """
    return trigger_webhook_event(
        db=db,
        event_type=WebhookEventType.RECOMMENDATION_CREATED,
        resource_id=recommendation_id,
        resource_type="recommendation",
        data=data
    )
