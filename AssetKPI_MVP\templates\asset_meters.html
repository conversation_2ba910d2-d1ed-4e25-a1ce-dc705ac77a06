{% extends "layout.html" %}

{% block title %}Asset Meters | AssetKPI{% endblock %}

{% block styles %}
<style>
    .asset-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
    .status-badge {
        font-size: 1rem;
        padding: 5px 10px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    .status-maintenance {
        background-color: #ffc107;
    }
    .meter-card {
        transition: transform 0.3s;
    }
    .meter-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    .meter-value {
        font-size: 24px;
        font-weight: bold;
    }
    .meter-unit {
        font-size: 14px;
        color: #6c757d;
    }
    .chart-container {
        height: 250px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/assets">Assets</a></li>
                <li class="breadcrumb-item"><a href="/assets/{{ asset.assetid }}">{{ asset.assetname }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Meters</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Asset Header -->
<div class="asset-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>{{ asset.assetname }} - Meters</h1>
            <p class="text-muted">{{ asset.assettype }} | {{ asset.manufacturer }} {{ asset.model }}</p>
            <div class="d-flex align-items-center mt-2">
                <span class="badge status-{{ asset.status|lower if asset.status else 'inactive' }} status-badge me-2">{{ asset.status }}</span>
                <span class="text-muted">Serial: {{ asset.serialnumber }}</span>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <i class="fas fa-plus"></i> Add Meter
            </button>
            <button class="btn btn-success auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <i class="fas fa-tachometer-alt"></i> Record Reading
            </button>
        </div>
    </div>
</div>

<!-- Asset Navigation -->
<ul class="nav nav-pills mb-4">
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}">Overview</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/kpi">KPIs</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/maintenance">Maintenance</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/specifications">Specifications</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/documents">Documents</a>
    </li>
    <li class="nav-item">
        <a class="nav-link active" href="/assets/{{ asset.assetid }}/meters">Meters</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/pm">PM Schedules</a>
    </li>
</ul>

<!-- Meters Overview -->
<div class="row mb-4">
    {% if meters %}
        {% for meter in meters %}
            <div class="col-md-4 mb-4">
                <div class="card meter-card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">{{ meter.meter_name }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="meter-value">{{ meter.current_reading }}</div>
                            <div class="meter-unit">{{ meter.unit_of_measure }}</div>
                        </div>
                        <p class="text-muted text-center">
                            Last updated: {{ meter.last_reading_date.strftime('%Y-%m-%d %H:%M') if meter.last_reading_date else 'Never' }}
                        </p>
                        <div class="chart-container">
                            <canvas id="meterChart{{ meter.meter_id }}"></canvas>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-between">
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#meterHistoryModal{{ meter.meter_id }}">
                            View History
                        </button>
                        <button class="btn btn-success btn-sm auth-required-content" data-role="ENGINEER,MANAGER,ADMIN" data-bs-toggle="modal" data-bs-target="#recordReadingModal" data-meter-id="{{ meter.meter_id }}" data-meter-name="{{ meter.meter_name }}">
                            Record Reading
                        </button>
                    </div>
                </div>
            </div>

            <!-- Meter History Modal -->
            <div class="modal fade" id="meterHistoryModal{{ meter.meter_id }}" tabindex="-1" aria-labelledby="meterHistoryModalLabel{{ meter.meter_id }}" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="meterHistoryModalLabel{{ meter.meter_id }}">{{ meter.meter_name }} - Reading History</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            {% if meter_readings[meter.meter_id] %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Reading Date</th>
                                                <th>Value</th>
                                                <th>Entered By</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for reading in meter_readings[meter.meter_id] %}
                                                <tr>
                                                    <td>{{ reading.reading_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                                    <td>{{ reading.reading_value }} {{ meter.unit_of_measure }}</td>
                                                    <td>{{ reading.entered_by }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <p class="text-center">No reading history available for this meter.</p>
                            {% endif %}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                No meters found for this asset. Please add meters to track asset performance.
            </div>
        </div>
    {% endif %}
</div>

<!-- Record Reading Modal -->
<div class="modal fade" id="recordReadingModal" tabindex="-1" aria-labelledby="recordReadingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recordReadingModalLabel">Record Meter Reading</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="recordReadingForm">
                    <input type="hidden" id="meterIdInput">
                    <div class="mb-3">
                        <label for="meterNameDisplay" class="form-label">Meter</label>
                        <input type="text" class="form-control" id="meterNameDisplay" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="readingValue" class="form-label">Reading Value</label>
                        <input type="number" class="form-control" id="readingValue" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="readingDate" class="form-label">Reading Date</label>
                        <input type="datetime-local" class="form-control" id="readingDate" required>
                    </div>
                    <div class="mb-3">
                        <label for="enteredBy" class="form-label">Entered By</label>
                        <input type="text" class="form-control" id="enteredBy" required>
                    </div>
                    <div class="mb-3">
                        <label for="readingNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="readingNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveReadingBtn">Save Reading</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Meter Modal -->
<div class="modal fade" id="addMeterModal" tabindex="-1" aria-labelledby="addMeterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMeterModalLabel">Add New Meter</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addMeterForm">
                    <div class="mb-3">
                        <label for="meterName" class="form-label">Meter Name</label>
                        <input type="text" class="form-control" id="meterName" required>
                    </div>
                    <div class="mb-3">
                        <label for="meterType" class="form-label">Meter Type</label>
                        <select class="form-select" id="meterType" required>
                            <option value="">Select Type</option>
                            <option value="Runtime">Runtime</option>
                            <option value="Cycles">Cycles</option>
                            <option value="Distance">Distance</option>
                            <option value="Temperature">Temperature</option>
                            <option value="Pressure">Pressure</option>
                            <option value="Flow">Flow</option>
                            <option value="Electrical">Electrical</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="unitOfMeasure" class="form-label">Unit of Measure</label>
                        <input type="text" class="form-control" id="unitOfMeasure" required>
                    </div>
                    <div class="mb-3">
                        <label for="initialReading" class="form-label">Initial Reading</label>
                        <input type="number" class="form-control" id="initialReading" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="meterDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="meterDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveMeterBtn">Save Meter</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize meter charts
        {% if meters %}
            {% for meter in meters %}
                {% if meter_readings[meter.meter_id] %}
                    const ctx{{ meter.meter_id }} = document.getElementById('meterChart{{ meter.meter_id }}').getContext('2d');

                    // Extract data for chart
                    const labels{{ meter.meter_id }} = [
                        {% for reading in meter_readings[meter.meter_id][:10]|reverse %}
                            '{{ reading.reading_date.strftime('%Y-%m-%d') }}',
                        {% endfor %}
                    ];

                    const data{{ meter.meter_id }} = [
                        {% for reading in meter_readings[meter.meter_id][:10]|reverse %}
                            {{ reading.reading_value }},
                        {% endfor %}
                    ];

                    new Chart(ctx{{ meter.meter_id }}, {
                        type: 'line',
                        data: {
                            labels: labels{{ meter.meter_id }},
                            datasets: [{
                                label: '{{ meter.meter_name }}',
                                data: data{{ meter.meter_id }},
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    title: {
                                        display: true,
                                        text: '{{ meter.unit_of_measure }}'
                                    }
                                }
                            }
                        }
                    });
                {% endif %}
            {% endfor %}
        {% endif %}

        // Record Reading Modal
        const recordReadingModal = document.getElementById('recordReadingModal');
        if (recordReadingModal) {
            recordReadingModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const meterId = button.getAttribute('data-meter-id');
                const meterName = button.getAttribute('data-meter-name');

                document.getElementById('meterIdInput').value = meterId;
                document.getElementById('meterNameDisplay').value = meterName;

                // Set current date and time as default
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');

                document.getElementById('readingDate').value = `${year}-${month}-${day}T${hours}:${minutes}`;
            });
        }

        // Save Reading Button
        const saveReadingBtn = document.getElementById('saveReadingBtn');
        if (saveReadingBtn) {
            saveReadingBtn.addEventListener('click', function() {
                // Implement save reading functionality here
                alert('Save reading functionality would be implemented here');
                const modal = bootstrap.Modal.getInstance(recordReadingModal);
                modal.hide();
            });
        }

        // Add Meter Modal
        const addMeterBtn = document.querySelector('.btn-primary[data-role="ENGINEER,MANAGER,ADMIN"]');
        const addMeterModal = new bootstrap.Modal(document.getElementById('addMeterModal'));

        if (addMeterBtn && addMeterBtn.innerHTML.includes('Add Meter')) {
            addMeterBtn.addEventListener('click', function() {
                addMeterModal.show();
            });
        }

        // Save Meter Button
        const saveMeterBtn = document.getElementById('saveMeterBtn');
        if (saveMeterBtn) {
            saveMeterBtn.addEventListener('click', function() {
                // Implement save meter functionality here
                alert('Save meter functionality would be implemented here');
                addMeterModal.hide();
            });
        }
    });
</script>
{% endblock %}
