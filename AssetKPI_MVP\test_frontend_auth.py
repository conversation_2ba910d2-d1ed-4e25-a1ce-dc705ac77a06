"""
Test script for frontend authentication.
"""

import os
import sys
import requests
import json
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# Get database URL from environment or use default
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Add the parent directory to sys.path to allow imports from main
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the test_user_auth module
from test_user_auth import TEST_TOKEN_MAP

def test_login_page():
    """Test that the login page loads correctly."""
    print("\n--- Testing login page ---")
    
    response = requests.get("http://127.0.0.1:8000/login")
    if response.status_code == 200:
        print(f"✓ Login page loaded successfully")
        
        # Check if our new JavaScript files are included
        if "test_auth.js" in response.text and "combined_auth.js" in response.text:
            print(f"✓ Test authentication JavaScript files are included")
        else:
            print(f"✗ Test authentication JavaScript files are not included")
            
        # Check if the test authentication tab is present
        if 'id="test-auth"' in response.text and 'id="test-token"' in response.text:
            print(f"✓ Test authentication UI elements are present")
        else:
            print(f"✗ Test authentication UI elements are not present")
    else:
        print(f"✗ Login page failed to load: {response.status_code}")

def test_test_auth_endpoint():
    """Test the test user authentication endpoint."""
    print("\n--- Testing test user authentication endpoint ---")
    
    # Test each token
    for token, (user_id, role) in TEST_TOKEN_MAP.items():
        response = requests.get(f"http://127.0.0.1:8000/api/test-user-auth?token={token}")
        if response.status_code == 200:
            data = response.json()
            if "error" in data:
                print(f"✗ {token} -> {data['error']}")
            else:
                print(f"✓ {token} -> {data.get('user')} ({data.get('role')})")
        else:
            print(f"✗ {token} -> {response.status_code}: {response.text}")
    
    # Test invalid token
    response = requests.get("http://127.0.0.1:8000/api/test-user-auth?token=invalid-token")
    if response.status_code != 200:
        print(f"✓ Invalid token returns error: {response.status_code}")
    else:
        data = response.json()
        if "error" in data:
            print(f"✓ Invalid token returns error: {data['error']}")
        else:
            print(f"✗ Invalid token did not return an error")

def test_api_with_test_token():
    """Test API access with test token."""
    print("\n--- Testing API access with test token ---")
    
    # Test each token
    for token, (user_id, role) in TEST_TOKEN_MAP.items():
        response = requests.get(
            "http://127.0.0.1:8000/api/kpi/history/MTTR_Calculated",
            headers={"Authorization": f"Bearer {token}"}
        )
        if response.status_code == 200:
            print(f"✓ {token} -> API access successful")
        else:
            print(f"✗ {token} -> API access failed: {response.status_code}")
    
    # Test invalid token
    response = requests.get(
        "http://127.0.0.1:8000/api/kpi/history/MTTR_Calculated",
        headers={"Authorization": "Bearer invalid-token"}
    )
    if response.status_code != 200:
        print(f"✓ Invalid token API access returns error: {response.status_code}")
    else:
        print(f"✗ Invalid token API access did not return an error")

def main():
    """Run all tests."""
    print("=== Testing Frontend Authentication ===")
    
    try:
        test_login_page()
        test_test_auth_endpoint()
        test_api_with_test_token()
        
        print("\n=== All tests completed ===")
    except Exception as e:
        print(f"\n=== Tests failed: {str(e)} ===")

if __name__ == "__main__":
    main()
