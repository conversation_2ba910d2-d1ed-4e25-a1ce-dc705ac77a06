/**
 * Help System JavaScript
 * 
 * This file contains JavaScript functionality for the help system,
 * documentation, and support features across the AssetKPI application.
 */

/**
 * Help system main class
 */
class HelpSystem {
  constructor() {
    this.searchIndex = [];
    this.currentCategory = null;
    this.searchTimeout = null;
  }

  /**
   * Initialize the help system
   */
  init() {
    this.setupSearch();
    this.setupFAQ();
    this.setupNavigation();
    this.setupContactForm();
    this.loadHelpData();
  }

  /**
   * Setup search functionality
   */
  setupSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchResults = document.querySelector('.search-results');

    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
          this.performSearch(e.target.value);
        }, 300);
      });

      // Handle search on Enter key
      searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          this.performSearch(e.target.value);
        }
      });
    }
  }

  /**
   * Setup FAQ functionality
   */
  setupFAQ() {
    const faqQuestions = document.querySelectorAll('.faq-question');

    faqQuestions.forEach(question => {
      question.addEventListener('click', () => {
        const answer = question.nextElementSibling;
        const isActive = question.classList.contains('active');

        // Close all other FAQ items
        faqQuestions.forEach(q => {
          q.classList.remove('active');
          const a = q.nextElementSibling;
          if (a) a.classList.remove('show');
        });

        // Toggle current item
        if (!isActive) {
          question.classList.add('active');
          if (answer) answer.classList.add('show');
        }
      });
    });
  }

  /**
   * Setup navigation functionality
   */
  setupNavigation() {
    const categoryLinks = document.querySelectorAll('.help-category');

    categoryLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const category = link.dataset.category;
        this.loadCategory(category);
      });
    });

    // Breadcrumb navigation
    const breadcrumbLinks = document.querySelectorAll('.breadcrumb a');
    breadcrumbLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const target = link.getAttribute('href');
        this.navigateTo(target);
      });
    });
  }

  /**
   * Setup contact form
   */
  setupContactForm() {
    const contactForm = document.querySelector('.contact-form');

    if (contactForm) {
      contactForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.submitContactForm(contactForm);
      });
    }

    // Setup contact buttons
    const contactButtons = document.querySelectorAll('.btn-contact');
    contactButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const action = button.dataset.action;
        this.handleContactAction(action);
      });
    });
  }

  /**
   * Load help data from server
   */
  async loadHelpData() {
    try {
      const response = await fetch('/api/help/data');
      if (response.ok) {
        const data = await response.json();
        this.searchIndex = data.searchIndex || [];
        this.helpData = data;
      } else {
        this.loadDefaultHelpData();
      }
    } catch (error) {
      console.warn('Could not load help data from server, using defaults');
      this.loadDefaultHelpData();
    }
  }

  /**
   * Load default help data
   */
  loadDefaultHelpData() {
    this.searchIndex = [
      {
        title: 'Getting Started',
        content: 'Learn how to set up and use AssetKPI for the first time.',
        category: 'basics',
        url: '/help/getting-started'
      },
      {
        title: 'Managing Assets',
        content: 'How to add, edit, and organize your assets in the system.',
        category: 'assets',
        url: '/help/managing-assets'
      },
      {
        title: 'Inventory Management',
        content: 'Track spare parts, manage stock levels, and optimize inventory.',
        category: 'inventory',
        url: '/help/inventory-management'
      },
      {
        title: 'Work Orders',
        content: 'Create, assign, and track maintenance work orders.',
        category: 'maintenance',
        url: '/help/work-orders'
      },
      {
        title: 'KPI Dashboard',
        content: 'Understanding and customizing your KPI dashboard.',
        category: 'analytics',
        url: '/help/kpi-dashboard'
      }
    ];
  }

  /**
   * Perform search
   */
  performSearch(query) {
    if (!query || query.length < 2) {
      this.hideSearchResults();
      return;
    }

    const results = this.searchIndex.filter(item => {
      const searchText = `${item.title} ${item.content}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    });

    this.displaySearchResults(results, query);
  }

  /**
   * Display search results
   */
  displaySearchResults(results, query) {
    let resultsContainer = document.querySelector('.search-results');
    
    if (!resultsContainer) {
      resultsContainer = document.createElement('div');
      resultsContainer.className = 'search-results';
      resultsContainer.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
      `;
      
      const searchContainer = document.querySelector('.help-search');
      if (searchContainer) {
        searchContainer.style.position = 'relative';
        searchContainer.appendChild(resultsContainer);
      }
    }

    if (results.length === 0) {
      resultsContainer.innerHTML = `
        <div class="search-result-item" style="padding: 1rem; text-align: center; color: #7f8c8d;">
          No results found for "${query}"
        </div>
      `;
    } else {
      resultsContainer.innerHTML = results.map(result => `
        <div class="search-result-item" style="padding: 1rem; border-bottom: 1px solid #ecf0f1; cursor: pointer;" data-url="${result.url}">
          <h6 style="margin: 0 0 0.5rem; color: #2c3e50;">${this.highlightQuery(result.title, query)}</h6>
          <p style="margin: 0; font-size: 0.9rem; color: #7f8c8d;">${this.highlightQuery(result.content, query)}</p>
          <small style="color: #95a5a6;">${result.category}</small>
        </div>
      `).join('');
    }

    resultsContainer.style.display = 'block';

    // Add click handlers to results
    resultsContainer.querySelectorAll('.search-result-item').forEach(item => {
      item.addEventListener('click', () => {
        const url = item.dataset.url;
        if (url) {
          this.navigateTo(url);
          this.hideSearchResults();
        }
      });
    });
  }

  /**
   * Highlight search query in text
   */
  highlightQuery(text, query) {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  /**
   * Hide search results
   */
  hideSearchResults() {
    const resultsContainer = document.querySelector('.search-results');
    if (resultsContainer) {
      resultsContainer.style.display = 'none';
    }
  }

  /**
   * Load help category
   */
  async loadCategory(category) {
    try {
      const response = await fetch(`/api/help/category/${category}`);
      if (response.ok) {
        const data = await response.json();
        this.displayCategory(data);
      } else {
        console.error('Failed to load category:', category);
      }
    } catch (error) {
      console.error('Error loading category:', error);
    }
  }

  /**
   * Display help category
   */
  displayCategory(data) {
    const contentArea = document.querySelector('.help-content');
    if (contentArea) {
      contentArea.innerHTML = `
        <div class="help-category-content">
          <h2>${data.title}</h2>
          <p>${data.description}</p>
          <div class="help-articles">
            ${data.articles.map(article => `
              <div class="help-article-card">
                <h4><a href="${article.url}">${article.title}</a></h4>
                <p>${article.summary}</p>
                <small>Last updated: ${article.lastUpdated}</small>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    }
  }

  /**
   * Navigate to help page
   */
  navigateTo(url) {
    if (url.startsWith('/help/')) {
      // Internal help navigation
      window.location.href = url;
    } else {
      // External link
      window.open(url, '_blank');
    }
  }

  /**
   * Submit contact form
   */
  async submitContactForm(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    try {
      const response = await fetch('/api/help/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        this.showMessage('Your message has been sent successfully!', 'success');
        form.reset();
      } else {
        this.showMessage('Failed to send message. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      this.showMessage('An error occurred. Please try again later.', 'error');
    }
  }

  /**
   * Handle contact actions
   */
  handleContactAction(action) {
    switch (action) {
      case 'email':
        window.location.href = 'mailto:<EMAIL>';
        break;
      case 'chat':
        this.openChatWidget();
        break;
      case 'phone':
        window.location.href = 'tel:+1234567890';
        break;
      case 'ticket':
        window.open('/support/ticket', '_blank');
        break;
    }
  }

  /**
   * Open chat widget
   */
  openChatWidget() {
    // Placeholder for chat widget integration
    console.log('Opening chat widget...');
    this.showMessage('Chat support is coming soon!', 'info');
  }

  /**
   * Show message to user
   */
  showMessage(message, type = 'info') {
    const messageEl = document.createElement('div');
    messageEl.className = `alert alert-${type}`;
    messageEl.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      padding: 1rem;
      border-radius: 0.375rem;
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    `;
    messageEl.textContent = message;

    document.body.appendChild(messageEl);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (messageEl.parentNode) {
        messageEl.parentNode.removeChild(messageEl);
      }
    }, 5000);
  }
}

/**
 * Help keyboard shortcuts
 */
const HelpKeyboardShortcuts = {
  init() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + ? to open help
      if ((e.ctrlKey || e.metaKey) && e.key === '?') {
        e.preventDefault();
        this.openHelp();
      }
      
      // F1 to open help
      if (e.key === 'F1') {
        e.preventDefault();
        this.openHelp();
      }
    });
  },

  openHelp() {
    window.location.href = '/help';
  }
};

/**
 * Initialize help system when DOM is ready
 */
document.addEventListener('DOMContentLoaded', function() {
  // Create global help system instance
  window.helpSystem = new HelpSystem();
  window.helpSystem.init();
  
  // Initialize keyboard shortcuts
  HelpKeyboardShortcuts.init();
  
  // Hide search results when clicking outside
  document.addEventListener('click', (e) => {
    const searchContainer = document.querySelector('.help-search');
    if (searchContainer && !searchContainer.contains(e.target)) {
      window.helpSystem.hideSearchResults();
    }
  });
});

// Export for use in other modules
window.HelpSystem = HelpSystem;
window.HelpKeyboardShortcuts = HelpKeyboardShortcuts;
