# AssetKPI - Intelligent KPI & Inventory Optimization System Blueprint

This blueprint provides a comprehensive guide to recreate the AssetKPI application from scratch, including architecture, technology stack, database schema, and implementation details.

## 1. Project Overview

AssetKPI is an Intelligent KPI & Inventory Optimization System designed to help maintenance and inventory managers track key performance indicators, optimize inventory levels, and make data-driven decisions. The system calculates maintenance KPIs like MTTR and MTBF, provides inventory optimization recommendations, and visualizes trends through interactive charts.

## 2. Technology Stack

### Backend
- **Framework**: FastAPI
- **Database**: PostgreSQL
- **ORM**: SQLAlchemy
- **Authentication**: Firebase Authentication
- **Scheduler**: APScheduler
- **Environment Variables**: python-dotenv

### Frontend
- **Templating**: Jinja2
- **UI Framework**: Bootstrap 5
- **Charts**: Chart.js
- **JavaScript**: Vanilla JS with modular components

### Development Tools
- **Version Control**: Git
- **Package Management**: pip
- **Testing**: pytest
- **API Testing**: Postman

## 3. Project Structure

```
AssetKPI/
├── static/                  # Static files (CSS, JS, images)
│   ├── css/                 # CSS files
│   ├── js/                  # JavaScript files
│   └── images/              # Image files
├── templates/               # Jinja2 templates
├── middleware/              # Custom middleware
├── models/                  # Data models
├── routes/                  # API routes
├── utils/                   # Utility functions
├── jobs/                    # Scheduled jobs
├── permissions/             # Permission handling
├── sdk/                     # SDK for API integration
├── docs/                    # Documentation
├── tests/                   # Tests
├── db_models.py             # Database models
├── main.py                  # Main application file
├── .env                     # Environment variables
├── requirements.txt         # Dependencies
└── README.md                # Project documentation
```

## 4. Database Schema

The database schema includes the following main tables:

### Core Tables
- **users**: User accounts and authentication
- **assets**: Equipment and assets being monitored
- **workorders**: Maintenance work orders
- **spareparts**: Inventory items and spare parts
- **kpireports**: KPI reports and calculations

### Supporting Tables
- **calculated_kpi_history**: Historical KPI values
- **inventory_recommendations**: Inventory optimization recommendations
- **eoq_calculations**: Economic Order Quantity calculations
- **safety_stock_calculations**: Safety stock calculations
- **user_activity_logs**: User activity tracking

## 5. Authentication System

The application uses a dual authentication system:

### Firebase Authentication
- Secure user management with Firebase Auth
- JWT token validation
- Role-based access control

### Test Authentication
- Simplified authentication for development and testing
- Predefined test tokens for different user roles
- Not for production use

## 6. Key Features Implementation

### 6.1 KPI Calculation

- **MTTR (Mean Time To Repair)**: Calculate average repair time from work orders
- **MTBF (Mean Time Between Failures)**: Calculate average time between failures
- **Failure Rate**: Calculate failures per time period

### 6.2 Inventory Optimization

- **EOQ (Economic Order Quantity)**: Calculate optimal order quantities
- **Safety Stock**: Calculate buffer inventory levels
- **ABC Classification**: Categorize inventory by value and usage
- **Reorder Points**: Calculate when to reorder inventory

### 6.3 Visualization

- Interactive charts for KPI trends
- Inventory level visualization
- Work order status dashboards
- Comparative analysis views

### 6.4 API and Integration

- RESTful API for all operations
- SDK for programmatic access
- Webhook support for event notifications
- Bulk import/export capabilities

## 7. Implementation Steps

### Step 1: Project Setup
1. Create project directory structure
2. Set up virtual environment
3. Install dependencies
4. Configure environment variables

### Step 2: Database Setup
1. Create PostgreSQL database
2. Define database models
3. Create migration scripts
4. Initialize database with sample data

### Step 3: Authentication Implementation
1. Set up Firebase project
2. Configure Firebase Authentication
3. Implement authentication middleware
4. Create test authentication system

### Step 4: Core API Implementation
1. Implement asset management endpoints
2. Implement work order management endpoints
3. Implement inventory management endpoints
4. Implement KPI calculation endpoints

### Step 5: Frontend Implementation
1. Create base templates
2. Implement dashboard views
3. Create interactive charts
4. Implement user interface components

### Step 6: Advanced Features
1. Implement scheduled jobs
2. Add usage analytics
3. Create inventory optimization algorithms
4. Implement webhook system

### Step 7: Testing and Documentation
1. Write unit tests
2. Create API documentation
3. Write user guides
4. Prepare deployment documentation

## 8. Detailed Implementation Guide

### 8.1 Database Models Implementation

Create a `db_models.py` file with the following key models:

```python
import enum
from datetime import datetime
from sqlalchemy import (
    create_engine, Column, Integer, String, Numeric, Date, Boolean,
    MetaData, Table, text, Text, func, DateTime, asc, desc, ForeignKey,
    Enum as SQLAlchemyEnum, and_
)
from sqlalchemy.orm import declarative_base, relationship, sessionmaker
from sqlalchemy.sql import functions as sql_functions
from decimal import Decimal

# Define Base
Base = declarative_base()

# --- Enums ---
class UserRole(str, enum.Enum):
    VIEWER = "VIEWER"
    ENGINEER = "ENGINEER"
    MANAGER = "MANAGER"
    ADMIN = "ADMIN"

# --- User Model ---
class User(Base):
    __tablename__ = 'users'
    user_id = Column(String(255), primary_key=True, index=True)
    email = Column(String(255), nullable=False, unique=True, index=True)
    role = Column(SQLAlchemyEnum(UserRole), nullable=False, default=UserRole.VIEWER, index=True)
    full_name = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=sql_functions.now())
    last_login = Column(DateTime(timezone=True))
    api_access_enabled = Column(Boolean, default=True)
    api_key_id = Column(String(255), unique=True, index=True, nullable=True)

# --- Asset Model ---
class Asset(Base):
    __tablename__ = 'assets'
    assetid = Column(Integer, primary_key=True, index=True)
    assetname = Column(String(100))
    assettype = Column(String(50))
    serialnumber = Column(String(50))
    location = Column(String(100))
    status = Column(String(50))
    criticality = Column(String(50))
    purchasedate = Column(Date)
    lastservicedate = Column(Date)
    lifecyclestage = Column(String(50))
    manufacturer = Column(String(100))
    model = Column(String(100))
    createdat = Column(DateTime, default=datetime.now)
    location_id = Column(Integer, ForeignKey('asset_locations.location_id'))
    system_id = Column(Integer, ForeignKey('asset_systems.system_id'))

# --- Spare Part Model ---
class Sparepart(Base):
    __tablename__ = 'spareparts'
    partid = Column(Integer, primary_key=True, index=True)
    partname = Column(String(100))
    partnumber = Column(String(50))
    manufacturer = Column(String(100))
    stockquantity = Column(Integer)
    monthlydemand = Column(Integer)
    reorderlevel = Column(Integer)
    safetystock = Column(Integer)
    eoq = Column(Numeric(10, 2))
    leadtimedays = Column(Integer)
    unitprice = Column(Numeric(10, 2))
    stockoutrisk = Column(Numeric(5, 2))
    lastrestocked = Column(Date)
    avg_monthly_consumption = Column(Numeric(10, 2), default=0.0)
    abc_classification = Column(String(1), index=True)
    inventory_metrics_last_updated = Column(DateTime(timezone=True))
    calculated_safety_stock = Column(Numeric(10, 2))
    annual_demand = Column(Numeric(12, 2))
    ordering_cost = Column(Numeric(10, 2))
    holding_cost_percent = Column(Numeric(5, 2))
    lead_time_variability = Column(Numeric(5, 2))
    demand_variability = Column(Numeric(5, 2))
    service_level = Column(Numeric(5, 2))
    reorder_point = Column(Numeric(10, 2))

# --- Work Order Model ---
class WorkOrder(Base):
    __tablename__ = 'workorders'
    workorderid = Column(Integer, primary_key=True, index=True)
    assetid = Column(Integer, ForeignKey('assets.assetid'))
    workordertype = Column(String(50))
    description = Column(String(255))
    status = Column(String(50))
    priority = Column(String(50))
    assignedto = Column(String(100))
    createddate = Column(Date)
    startdate = Column(DateTime)
    enddate = Column(DateTime)
    downtimeminutes = Column(Integer)
    completiondetails = Column(Text)
    failurecode = Column(String(50))
    repaircode = Column(String(50))
    causecode = Column(String(50))

# --- KPI History Model ---
class CalculatedKpiHistory(Base):
    __tablename__ = 'calculated_kpi_history'
    id = Column(Integer, primary_key=True, index=True)
    calculation_timestamp = Column(DateTime, server_default=sql_functions.now())
    kpi_name = Column(String(100), nullable=False)
    kpi_value = Column(Numeric(15, 5))
    kpi_unit = Column(String(50))
    asset_id = Column(Integer, nullable=True)
    calculation_source = Column(String(50))
```

### 8.2 Main Application Setup

Create a `main.py` file with the following structure:

```python
import os
import csv
import io
import math
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

# Firebase imports
import firebase_admin
from firebase_admin import credentials, auth

# FastAPI imports
from fastapi import (
    FastAPI, Request, Depends, File, UploadFile, Form,
    HTTPException, Path, Security, Query, status
)
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware

# SQLAlchemy imports
from sqlalchemy import create_engine, desc, asc, func, and_, or_, not_
from sqlalchemy.orm import sessionmaker, Session

# APScheduler
from apscheduler.schedulers.asyncio import AsyncIOScheduler

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:password@localhost:5432/AssetKPI")

# API Key setup
API_KEY_NAME = "X-API-KEY"
API_KEY = os.getenv("SECRET_API_KEY", "your-default-api-key")

# Initialize Firebase Admin SDK
try:
    SERVICE_ACCOUNT_KEY_PATH = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
    print(f"INFO:     Looking for Firebase service account key at: {SERVICE_ACCOUNT_KEY_PATH}")

    if os.path.exists(SERVICE_ACCOUNT_KEY_PATH):
        print(f"INFO:     Found service account key at: {os.path.abspath(SERVICE_ACCOUNT_KEY_PATH)}")
        cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)
    else:
        print(f"ERROR:    Service account key not found at: {SERVICE_ACCOUNT_KEY_PATH}")
        cred = None

    if cred:
        try:
            firebase_admin.initialize_app(cred)
            print("INFO:     Firebase Admin SDK initialized successfully.")
        except ValueError as e:
            print(f"INFO:     Firebase Admin SDK already initialized: {e}")
    else:
        print("ERROR:    Could not initialize Firebase Admin SDK. Credentials not loaded.")
except Exception as e:
    print(f"ERROR:    Failed to initialize Firebase Admin SDK: {e}")

# SQLAlchemy setup
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Jinja2 Templates setup
templates = Jinja2Templates(directory="templates")

# Scheduler setup
scheduler = AsyncIOScheduler()

# Import database models
from db_models import (
    Base, UserRole, User, Asset, Sparepart, WorkOrder,
    CalculatedKpiHistory
)

# Database dependency
def get_db():
    """Database session dependency"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# API Key dependency
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

def get_api_key(api_key_header: str = Security(api_key_header)):
    if api_key_header == API_KEY:
        return api_key_header
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid API Key",
    )

# Application lifespan
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Start scheduler
    scheduler.start()
    print("INFO:     Starting up scheduler...")

    # Register scheduled jobs
    scheduler.add_job(run_kpi_calculations_job, 'interval', minutes=1, id='kpi_job')
    scheduler.add_job(run_inventory_optimization_job, 'interval', minutes=1, id='inventory_job')

    yield

    # Shutdown: Stop scheduler
    scheduler.shutdown()
    print("INFO:     Shutting down scheduler...")

# FastAPI app
app = FastAPI(
    title="AssetKPI - Intelligent KPI & Inventory Optimization System",
    description="AssetKPI provides a comprehensive API for asset management, inventory optimization, and KPI calculation.",
    version="1.0.0",
    lifespan=lifespan,
)

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Scheduled job functions
def run_kpi_calculations_job():
    print(f"--- Running Scheduled KPI Calculation Job at {datetime.now()} ---")
    db = SessionLocal()
    try:
        # KPI calculation logic here
        pass
    finally:
        db.close()

def run_inventory_optimization_job():
    print(f"--- Running Scheduled Inventory Optimization Job at {datetime.now()} ---")
    db = SessionLocal()
    try:
        # Inventory optimization logic here
        pass
    finally:
        db.close()

# Root endpoint
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# Dashboard endpoint
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    return templates.TemplateResponse("dashboard.html", {"request": request})

# Login endpoint
@app.get("/login", response_class=HTMLResponse)
async def login(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# API endpoints will be added here

# Run the application
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
```

### 8.3 Authentication Implementation

Create an `auth.py` file for Firebase authentication:

```python
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
import firebase_admin
from firebase_admin import auth

# Import User model and database dependency
from db_models import User, UserRole
from main import get_db

# OAuth2 scheme for Bearer token
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Test user authentication functions
def is_test_user_token(token: str) -> bool:
    """Check if the token is a test user token."""
    test_tokens = ["test-admin-token", "test-manager-token", "test-engineer-token", "test-viewer-token", "johan-token"]
    return token in test_tokens

def handle_test_user_token(token: str, db: Session):
    """Handle test user token authentication."""
    if token == "test-admin-token":
        user = db.query(User).filter(User.email == "<EMAIL>").first()
    elif token == "test-manager-token":
        user = db.query(User).filter(User.email == "<EMAIL>").first()
    elif token == "test-engineer-token":
        user = db.query(User).filter(User.email == "<EMAIL>").first()
    elif token == "test-viewer-token":
        user = db.query(User).filter(User.email == "<EMAIL>").first()
    elif token == "johan-token":
        user = db.query(User).filter(User.email == "<EMAIL>").first()
    else:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid test token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Test user not found in database",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """Get the current user from the token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Check if this is a test user token
    if is_test_user_token(token):
        print(f"DEBUG: Using test user token: {token}")
        try:
            # Handle test user token
            user = handle_test_user_token(token, db)
            print(f"DEBUG: Test user authenticated: {user.email}")
            return user
        except Exception as e:
            print(f"ERROR: Test user authentication failed: {e}")
            raise credentials_exception

    # Not a test user token, proceed with Firebase authentication
    try:
        # Verify the ID token
        decoded_token = auth.verify_id_token(token, check_revoked=True)
        firebase_uid = decoded_token.get("uid")
        if firebase_uid is None:
            print("ERROR: UID not found in decoded token")
            raise credentials_exception
    except Exception as e:
        print(f"ERROR: Firebase token verification failed: {e}")
        raise credentials_exception

    # Token is valid, now get user from our database
    user = db.query(User).filter(User.user_id == firebase_uid).first()
    if user is None:
        # User authenticated with Firebase but not in our database
        print(f"ERROR: User with UID {firebase_uid} authenticated but not found in local DB.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User not registered in this application",
        )

    print(f"INFO: User {user.email} (Role: {user.role}) authenticated successfully.")
    return user

# Role-based access control dependencies
def require_admin(current_user: User = Depends(get_current_user)):
    """Require admin role."""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Admin role required.",
        )
    return current_user

def require_manager_plus(current_user: User = Depends(get_current_user)):
    """Require manager or admin role."""
    if current_user.role not in [UserRole.MANAGER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Manager or Admin role required.",
        )
    return current_user

def require_engineer_plus(current_user: User = Depends(get_current_user)):
    """Require engineer, manager, or admin role."""
    if current_user.role not in [UserRole.ENGINEER, UserRole.MANAGER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Engineer, Manager, or Admin role required.",
        )
    return current_user

def require_viewer_plus(current_user: User = Depends(get_current_user)):
    """Require any authenticated user."""
    return current_user
```

### 8.4 API Endpoints Implementation

Add the following API endpoints to your `main.py` file:

```python
# --- API Endpoints ---

# Authentication endpoints
@app.get("/api/auth-test", tags=["Authentication"])
async def auth_test(current_user: User = Depends(get_current_user)):
    """
    Simple endpoint to test if authentication is working.
    Returns user info if authenticated.
    """
    return {
        "authenticated": True,
        "user_id": current_user.user_id,
        "email": current_user.email,
        "role": current_user.role,
        "message": "Authentication successful!"
    }

@app.get("/api/user/profile", tags=["Users"])
async def get_user_profile(current_user: User = Depends(get_current_user)):
    """
    Get the current user's profile information.
    """
    return {
        "user_id": current_user.user_id,
        "email": current_user.email,
        "role": current_user.role,
        "full_name": current_user.full_name,
        "created_at": current_user.created_at
    }

# KPI endpoints
@app.get("/api/kpi/latest", tags=["KPIs"])
async def get_latest_kpis(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """
    Get the latest KPI values.
    """
    try:
        # Get latest KPI values from database
        mttr = db.query(CalculatedKpiHistory).filter(CalculatedKpiHistory.kpi_name == "MTTR_Calculated").order_by(desc(CalculatedKpiHistory.calculation_timestamp)).first()
        mtbf = db.query(CalculatedKpiHistory).filter(CalculatedKpiHistory.kpi_name == "MTBF_Calculated").order_by(desc(CalculatedKpiHistory.calculation_timestamp)).first()
        failure_rate = db.query(CalculatedKpiHistory).filter(CalculatedKpiHistory.kpi_name == "FailureRate_Calculated").order_by(desc(CalculatedKpiHistory.calculation_timestamp)).first()

        # Get open work orders count
        open_work_orders = db.query(func.count(WorkOrder.workorderid)).filter(WorkOrder.status == "OPEN").scalar()

        return {
            "mttr": float(mttr.kpi_value) if mttr else 0,
            "mtbf": float(mtbf.kpi_value) if mtbf else 0,
            "failure_rate": float(failure_rate.kpi_value) if failure_rate else 0,
            "open_work_orders": open_work_orders or 0,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        print(f"Error getting latest KPIs: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting latest KPIs: {str(e)}")

@app.get("/api/kpi/history/{kpi_name}", tags=["KPIs"])
async def get_kpi_history(
    kpi_name: str,
    limit: int = Query(100, description="Maximum number of data points to return"),
    start_date: Optional[str] = Query(None, description="Start date for filtering (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date for filtering (YYYY-MM-DD)"),
    asset_id: Optional[int] = Query(None, description="Filter by asset ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get historical data for a specific KPI.
    """
    try:
        # Parse date filters if provided
        start_date_obj = None
        end_date_obj = None

        if start_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")

        if end_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            # Set to end of day
            end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)

        # Build query
        query = db.query(CalculatedKpiHistory).filter(CalculatedKpiHistory.kpi_name == kpi_name)

        if start_date_obj:
            query = query.filter(CalculatedKpiHistory.calculation_timestamp >= start_date_obj)

        if end_date_obj:
            query = query.filter(CalculatedKpiHistory.calculation_timestamp <= end_date_obj)

        if asset_id:
            query = query.filter(CalculatedKpiHistory.asset_id == asset_id)

        # Order by timestamp and limit results
        query = query.order_by(desc(CalculatedKpiHistory.calculation_timestamp)).limit(limit)

        # Execute query
        kpi_history = query.all()

        # Format results
        result = []
        for kpi in kpi_history:
            result.append({
                "timestamp": kpi.calculation_timestamp.isoformat(),
                "value": float(kpi.kpi_value),
                "unit": kpi.kpi_unit,
                "asset_id": kpi.asset_id,
                "source": kpi.calculation_source
            })

        return result
    except Exception as e:
        print(f"Error getting KPI history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting KPI history: {str(e)}")

# Asset endpoints
@app.get("/api/assets", tags=["Assets"])
async def get_assets(
    limit: int = Query(100, description="Maximum number of assets to return"),
    offset: int = Query(0, description="Number of assets to skip"),
    status: Optional[str] = Query(None, description="Filter by status"),
    asset_type: Optional[str] = Query(None, description="Filter by asset type"),
    location: Optional[str] = Query(None, description="Filter by location"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a list of assets with optional filtering.
    """
    try:
        # Build query
        query = db.query(Asset)

        # Apply filters
        if status:
            query = query.filter(Asset.status == status)

        if asset_type:
            query = query.filter(Asset.assettype == asset_type)

        if location:
            query = query.filter(Asset.location == location)

        # Get total count
        total_count = query.count()

        # Apply pagination
        query = query.offset(offset).limit(limit)

        # Execute query
        assets = query.all()

        # Format results
        result = []
        for asset in assets:
            result.append({
                "assetid": asset.assetid,
                "assetname": asset.assetname,
                "assettype": asset.assettype,
                "serialnumber": asset.serialnumber,
                "location": asset.location,
                "status": asset.status,
                "criticality": asset.criticality,
                "purchasedate": asset.purchasedate.isoformat() if asset.purchasedate else None,
                "lastservicedate": asset.lastservicedate.isoformat() if asset.lastservicedate else None,
                "lifecyclestage": asset.lifecyclestage,
                "manufacturer": asset.manufacturer,
                "model": asset.model
            })

        return {
            "items": result,
            "total": total_count,
            "limit": limit,
            "offset": offset
        }
    except Exception as e:
        print(f"Error getting assets: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting assets: {str(e)}")
```

### 8.5 KPI Calculation Implementation

Create a `kpi_calculations.py` file with the following functions:

```python
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any, Optional
from sqlalchemy import func, desc, and_, or_
from sqlalchemy.orm import Session
from db_models import WorkOrder, Asset, CalculatedKpiHistory

def calculate_mttr_from_workorders(db: Session) -> float:
    """
    Calculate Mean Time To Repair (MTTR) from work orders.

    MTTR = Total repair time / Number of repairs
    """
    try:
        # Get completed work orders with repair times
        completed_wos = db.query(WorkOrder).filter(
            WorkOrder.status == "COMPLETED",
            WorkOrder.downtimeminutes.isnot(None),
            WorkOrder.downtimeminutes > 0
        ).all()

        if not completed_wos:
            print("No completed work orders with repair times found.")
            return 0.0

        # Calculate total repair time and count
        total_repair_time = sum(wo.downtimeminutes for wo in completed_wos)
        repair_count = len(completed_wos)

        # Calculate MTTR in hours
        mttr_hours = (total_repair_time / repair_count) / 60.0

        print(f"Calculated MTTR: {mttr_hours:.2f} hours from {repair_count} repairs")
        return mttr_hours

    except Exception as e:
        print(f"Error calculating MTTR: {e}")
        return 0.0

def get_failure_and_operating_time(db: Session) -> Tuple[int, float]:
    """
    Get the number of failures and total operating time.

    Returns:
        Tuple[int, float]: (number of failures, total operating time in hours)
    """
    try:
        # Get failure work orders
        failure_wos = db.query(WorkOrder).filter(
            WorkOrder.workordertype == "CORRECTIVE",
            WorkOrder.status == "COMPLETED"
        ).all()

        num_failures = len(failure_wos)

        # Calculate operating time (simplified: 24 hours * 365 days * number of assets)
        # In a real system, this would be based on actual asset operating hours
        asset_count = db.query(func.count(Asset.assetid)).scalar()
        operating_time = 24.0 * 365.0 * asset_count

        return num_failures, operating_time

    except Exception as e:
        print(f"Error getting failure and operating time: {e}")
        return 0, 0.0

def calculate_mtbf(num_failures: int, operating_time: float) -> float:
    """
    Calculate Mean Time Between Failures (MTBF).

    MTBF = Total operating time / Number of failures

    Args:
        num_failures: Number of failures
        operating_time: Total operating time in hours

    Returns:
        float: MTBF in hours
    """
    if num_failures == 0:
        return 0.0

    mtbf = operating_time / num_failures
    print(f"Calculated MTBF: {mtbf:.2f} hours from {num_failures} failures and {operating_time:.2f} operating hours")
    return mtbf

def calculate_failure_rate(num_failures: int, operating_time: float) -> float:
    """
    Calculate Failure Rate (failures per year).

    Failure Rate = (Number of failures / Total operating time) * 8760

    Args:
        num_failures: Number of failures
        operating_time: Total operating time in hours

    Returns:
        float: Failure rate in failures per year
    """
    if operating_time == 0:
        return 0.0

    # Calculate failures per hour
    failures_per_hour = num_failures / operating_time

    # Convert to failures per year (8760 hours in a year)
    failures_per_year = failures_per_hour * 8760

    print(f"Calculated Failure Rate: {failures_per_year:.4f} failures/year")
    return failures_per_year

def save_calculated_kpi(db: Session, name: str, value: float, unit: str, source: str, asset_id: Optional[int] = None) -> None:
    """
    Save a calculated KPI to the database.

    Args:
        db: Database session
        name: KPI name
        value: KPI value
        unit: KPI unit
        source: Source of the calculation
        asset_id: Optional asset ID
    """
    try:
        # Create KPI history record
        kpi = CalculatedKpiHistory(
            kpi_name=name,
            kpi_value=value,
            kpi_unit=unit,
            asset_id=asset_id,
            calculation_source=source
        )

        # Add to database
        db.add(kpi)
        db.commit()

        print(f"Prepared KPI for save: {name} = {value} {unit} (Source: {source})")

    except Exception as e:
        db.rollback()
        print(f"Error saving KPI {name}: {e}")
```

### 8.6 Inventory Optimization Implementation

Create an `inventory_optimization.py` file with the following functions:

```python
import math
from typing import List, Dict, Any, Optional
from sqlalchemy import func, desc, and_, or_
from sqlalchemy.orm import Session
from db_models import Sparepart, CalculatedKpiHistory

def calculate_eoq(annual_demand: float, ordering_cost: float, holding_cost_percent: float, unit_price: float) -> float:
    """
    Calculate Economic Order Quantity (EOQ).

    EOQ = sqrt((2 * Annual Demand * Ordering Cost) / (Holding Cost Percent * Unit Price))

    Args:
        annual_demand: Annual demand in units
        ordering_cost: Cost per order in currency units
        holding_cost_percent: Annual holding cost as a percentage of unit price (e.g., 0.25 for 25%)
        unit_price: Unit price in currency units

    Returns:
        float: Economic Order Quantity
    """
    if annual_demand <= 0 or ordering_cost <= 0 or holding_cost_percent <= 0 or unit_price <= 0:
        return 0.0

    holding_cost = holding_cost_percent * unit_price
    eoq = math.sqrt((2 * annual_demand * ordering_cost) / holding_cost)

    return round(eoq, 2)

def calculate_safety_stock(
    lead_time: float,
    avg_daily_demand: float,
    lead_time_variability: float,
    demand_variability: float,
    service_level_z: float
) -> float:
    """
    Calculate Safety Stock.

    Safety Stock = Z * sqrt((L * σ_d^2) + (D^2 * σ_L^2))

    Where:
    - Z = Service level factor (Z-score)
    - L = Average lead time
    - σ_d = Standard deviation of daily demand
    - D = Average daily demand
    - σ_L = Standard deviation of lead time

    Args:
        lead_time: Average lead time in days
        avg_daily_demand: Average daily demand in units
        lead_time_variability: Standard deviation of lead time
        demand_variability: Standard deviation of daily demand
        service_level_z: Service level Z-score (e.g., 1.645 for 95% service level)

    Returns:
        float: Safety stock in units
    """
    if lead_time <= 0 or avg_daily_demand < 0:
        return 0.0

    # Calculate safety stock
    safety_stock = service_level_z * math.sqrt(
        (lead_time * demand_variability**2) +
        (avg_daily_demand**2 * lead_time_variability**2)
    )

    return round(safety_stock, 2)

def calculate_reorder_point(avg_daily_demand: float, lead_time: float, safety_stock: float) -> float:
    """
    Calculate Reorder Point.

    Reorder Point = (Average Daily Demand * Lead Time) + Safety Stock

    Args:
        avg_daily_demand: Average daily demand in units
        lead_time: Lead time in days
        safety_stock: Safety stock in units

    Returns:
        float: Reorder point in units
    """
    if avg_daily_demand < 0 or lead_time < 0 or safety_stock < 0:
        return 0.0

    reorder_point = (avg_daily_demand * lead_time) + safety_stock

    return round(reorder_point, 2)

def calculate_abc_classification(db: Session) -> Dict[str, int]:
    """
    Calculate ABC classification for all spare parts.

    A: Top 20% of items by value
    B: Next 30% of items by value
    C: Remaining 50% of items by value

    Returns:
        Dict[str, int]: Count of items in each classification
    """
    try:
        # Get all spare parts with price and demand
        parts = db.query(Sparepart).filter(
            Sparepart.unitprice.isnot(None),
            Sparepart.unitprice > 0,
            Sparepart.avg_monthly_consumption.isnot(None)
        ).all()

        if not parts:
            print("No spare parts with price and demand found.")
            return {"A": 0, "B": 0, "C": 0}

        # Calculate annual value for each part
        part_values = []
        for part in parts:
            annual_value = float(part.unitprice) * float(part.avg_monthly_consumption) * 12
            part_values.append((part, annual_value))

        # Sort by value in descending order
        part_values.sort(key=lambda x: x[1], reverse=True)

        # Calculate total value
        total_value = sum(value for _, value in part_values)

        # Determine thresholds
        a_threshold = 0.8 * total_value  # Top 20% by value
        b_threshold = 0.5 * total_value  # Next 30% by value

        # Classify parts
        a_count = 0
        b_count = 0
        c_count = 0

        cumulative_value = 0
        for part, value in part_values:
            cumulative_value += value

            if cumulative_value <= a_threshold:
                part.abc_classification = "A"
                a_count += 1
            elif cumulative_value <= b_threshold:
                part.abc_classification = "B"
                b_count += 1
            else:
                part.abc_classification = "C"
                c_count += 1

        # Save to database
        db.commit()

        print(f"ABC Classification: A={a_count}, B={b_count}, C={c_count}")
        return {"A": a_count, "B": b_count, "C": c_count}

    except Exception as e:
        db.rollback()
        print(f"Error calculating ABC classification: {e}")
        return {"A": 0, "B": 0, "C": 0}
```

### 8.7 Scheduled Jobs Implementation

Create a `scheduled_jobs.py` file with the following functions:

```python
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from db_models import CalculatedKpiHistory
from kpi_calculations import (
    calculate_mttr_from_workorders,
    get_failure_and_operating_time,
    calculate_mtbf,
    calculate_failure_rate,
    save_calculated_kpi,
    calculate_workorder_data_quality
)
from inventory_optimization import (
    calculate_abc_classification,
    update_inventory_metrics
)

def run_kpi_calculations_job(db: Session) -> None:
    """
    Run KPI calculations job.

    This job calculates various KPIs and saves them to the database.
    """
    print(f"--- Running KPI Calculations Job at {datetime.now()} ---")

    try:
        # Calculate MTTR
        mttr = calculate_mttr_from_workorders(db)
        save_calculated_kpi(db, "MTTR_Calculated", mttr, "hours", "scheduled_job")

        # Calculate MTBF and Failure Rate
        num_failures, operating_time = get_failure_and_operating_time(db)

        mtbf = calculate_mtbf(num_failures, operating_time)
        save_calculated_kpi(db, "MTBF_Calculated", mtbf, "hours", "scheduled_job")

        failure_rate = calculate_failure_rate(num_failures, operating_time)
        save_calculated_kpi(db, "FailureRate_Calculated", failure_rate, "failures/year", "scheduled_job")

        # Calculate data quality metrics
        data_quality = calculate_workorder_data_quality(db)

        print(f"KPI Calculations completed successfully.")
        print(f"MTTR: {mttr:.2f} hours")
        print(f"MTBF: {mtbf:.2f} hours")
        print(f"Failure Rate: {failure_rate:.4f} failures/year")
        print(f"Data Quality: {data_quality}")

    except Exception as e:
        print(f"Error in KPI calculations job: {e}")

def run_inventory_optimization_job(db: Session) -> None:
    """
    Run inventory optimization job.

    This job calculates inventory optimization metrics and saves them to the database.
    """
    print(f"--- Running Inventory Optimization Job at {datetime.now()} ---")

    try:
        # Calculate ABC classification
        abc_results = calculate_abc_classification(db)

        # Update inventory metrics
        metrics_results = update_inventory_metrics(db)

        print(f"Inventory Optimization completed successfully.")
        print(f"ABC Classification: {abc_results}")
        print(f"Inventory Metrics: {metrics_results}")

        # Save summary metrics
        save_calculated_kpi(db, "ABC_Class_A_Count", abc_results["A"], "count", "scheduled_job")
        save_calculated_kpi(db, "ABC_Class_B_Count", abc_results["B"], "count", "scheduled_job")
        save_calculated_kpi(db, "ABC_Class_C_Count", abc_results["C"], "count", "scheduled_job")

        save_calculated_kpi(db, "EOQ_Updated_Count", metrics_results["eoq_count"], "count", "scheduled_job")
        save_calculated_kpi(db, "SafetyStock_Updated_Count", metrics_results["safety_stock_count"], "count", "scheduled_job")
        save_calculated_kpi(db, "ReorderPoint_Updated_Count", metrics_results["reorder_point_count"], "count", "scheduled_job")

    except Exception as e:
        print(f"Error in inventory optimization job: {e}")

def run_data_cleanup_job(db: Session) -> None:
    """
    Run data cleanup job.

    This job cleans up old data from the database.
    """
    print(f"--- Running Data Cleanup Job at {datetime.now()} ---")

    try:
        # Delete KPI history older than 1 year
        one_year_ago = datetime.now() - timedelta(days=365)

        deleted_count = db.query(CalculatedKpiHistory).filter(
            CalculatedKpiHistory.calculation_timestamp < one_year_ago
        ).delete()

        db.commit()

        print(f"Data Cleanup completed successfully.")
        print(f"Deleted {deleted_count} old KPI history records.")

    except Exception as e:
        db.rollback()
        print(f"Error in data cleanup job: {e}")
```

### 8.8 Environment Configuration

Create a `.env` file with the following configuration:

```
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/AssetKPI

# API Configuration
SECRET_API_KEY=c5e52be8-9b1c-4fcd-8457-741c91ef5c85

# Firebase Configuration
FIREBASE_SERVICE_ACCOUNT_KEY=firebase-service-account.json
```

### 8.9 Project Requirements

Create a `requirements.txt` file with the following dependencies:

```
fastapi==0.95.1
uvicorn==0.22.0
sqlalchemy==2.0.12
psycopg2-binary==2.9.6
python-dotenv==1.0.0
jinja2==3.1.2
firebase-admin==6.1.0
apscheduler==3.10.1
pandas==2.0.1
python-multipart==0.0.6
```

## 9. Deployment Guide

### 9.1 Local Development Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/AssetKPI.git
   cd AssetKPI
   ```

2. **Create a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up the database**:
   - Create a PostgreSQL database named `AssetKPI`
   - Update the `.env` file with your database credentials

5. **Initialize the database**:
   ```bash
   python init_db.py
   ```

6. **Set up Firebase**:
   - Create a Firebase project at https://console.firebase.google.com/
   - Enable Authentication with Email/Password
   - Generate a service account key and save it as `firebase-service-account.json`

7. **Run the application**:
   ```bash
   python -m uvicorn main:app --reload
   ```

### 9.2 Production Deployment

#### 9.2.1 Docker Deployment

1. **Create a Dockerfile**:
   ```dockerfile
   FROM python:3.10-slim

   WORKDIR /app

   COPY requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt

   COPY . .

   CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
   ```

2. **Create a docker-compose.yml file**:
   ```yaml
   version: '3'

   services:
     web:
       build: .
       ports:
         - "8000:8000"
       depends_on:
         - db
       environment:
         - DATABASE_URL=**************************************/AssetKPI
         - SECRET_API_KEY=${SECRET_API_KEY}
         - FIREBASE_SERVICE_ACCOUNT_KEY=firebase-service-account.json
       volumes:
         - ./firebase-service-account.json:/app/firebase-service-account.json

     db:
       image: postgres:14
       environment:
         - POSTGRES_USER=postgres
         - POSTGRES_PASSWORD=password
         - POSTGRES_DB=AssetKPI
       volumes:
         - postgres_data:/var/lib/postgresql/data

   volumes:
     postgres_data:
   ```

3. **Build and run with Docker Compose**:
   ```bash
   docker-compose up -d
   ```

#### 9.2.2 Cloud Deployment (AWS)

1. **Set up an RDS PostgreSQL instance**
2. **Deploy the application to EC2 or ECS**
3. **Set up a load balancer**
4. **Configure security groups and networking**
5. **Set up monitoring and logging**

## 10. Testing Guide

### 10.1 Unit Testing

Create a `tests` directory with the following structure:

```
tests/
├── __init__.py
├── test_api.py
├── test_auth.py
├── test_kpi_calculations.py
├── test_inventory_optimization.py
└── conftest.py
```

Example test file for KPI calculations:

```python
import pytest
from sqlalchemy.orm import Session
from kpi_calculations import (
    calculate_mttr_from_workorders,
    calculate_mtbf,
    calculate_failure_rate
)
from db_models import WorkOrder, Asset

def test_calculate_mttr_from_workorders(db_session):
    # Create test work orders
    wo1 = WorkOrder(
        workorderid=1,
        assetid=1,
        workordertype="CORRECTIVE",
        status="COMPLETED",
        downtimeminutes=120
    )
    wo2 = WorkOrder(
        workorderid=2,
        assetid=1,
        workordertype="CORRECTIVE",
        status="COMPLETED",
        downtimeminutes=60
    )

    db_session.add_all([wo1, wo2])
    db_session.commit()

    # Calculate MTTR
    mttr = calculate_mttr_from_workorders(db_session)

    # Assert
    assert mttr == 1.5  # (120 + 60) / 2 / 60 = 1.5 hours

def test_calculate_mtbf():
    # Test data
    num_failures = 10
    operating_time = 8760  # 1 year in hours

    # Calculate MTBF
    mtbf = calculate_mtbf(num_failures, operating_time)

    # Assert
    assert mtbf == 876.0  # 8760 / 10 = 876 hours

def test_calculate_failure_rate():
    # Test data
    num_failures = 10
    operating_time = 8760  # 1 year in hours

    # Calculate failure rate
    failure_rate = calculate_failure_rate(num_failures, operating_time)

    # Assert
    assert failure_rate == 10.0  # (10 / 8760) * 8760 = 10 failures/year
```

### 10.2 Integration Testing

Example integration test for API endpoints:

```python
from fastapi.testclient import TestClient
from main import app
import pytest

client = TestClient(app)

def test_get_latest_kpis(auth_headers):
    response = client.get("/api/kpi/latest", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert "mttr" in data
    assert "mtbf" in data
    assert "failure_rate" in data
    assert "open_work_orders" in data
    assert "timestamp" in data

def test_get_assets(auth_headers):
    response = client.get("/api/assets", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "total" in data
    assert "limit" in data
    assert "offset" in data
```

## 11. Conclusion

This blueprint provides a comprehensive guide to recreate the AssetKPI application from scratch. By following the steps outlined in this document, you can build a fully functional KPI and inventory optimization system with the following features:

- Asset management and tracking
- Work order management
- Inventory optimization with EOQ and safety stock calculations
- KPI calculation and visualization
- User authentication and authorization
- Scheduled jobs for automated calculations
- RESTful API for integration with other systems

The application is designed to be scalable, maintainable, and extensible, allowing for future enhancements and customizations to meet specific business requirements.