# KPI API Endpoints

This document provides detailed information about the KPI-related API endpoints in the AssetKPI system.

## KPI Retrieval

### Get Latest KPIs

Retrieves the latest KPI values.

**Endpoint:** `GET /api/kpis/latest`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Response:**

```json
{
  "mttr": 4.5,
  "mtbf": 720.0,
  "failure_rate": 12.2,
  "data_quality_score": 85.0,
  "report_date": "2023-04-15T00:00:00Z"
}
```

### Get KPI History

Retrieves historical values for a specific KPI.

**Endpoint:** `GET /api/kpis/history/{kpi_name}`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| kpi_name | string | Yes | Name of the KPI (e.g., `MTTR_Calculated`, `MTBF_Calculated`) |

**Query Parameters:**

| Parameter | Type | Required | Description | Default |
|-----------|------|----------|-------------|---------|
| start_date | string | No | Start date for history (format: YYYY-MM-DD) | 30 days ago |
| end_date | string | No | End date for history (format: YYYY-MM-DD) | today |
| limit | integer | No | Maximum number of results to return | 100 |

**Response:**

```json
[
  {
    "id": 1,
    "kpi_name": "MTTR_Calculated",
    "kpi_value": 4.5,
    "kpi_unit": "hours",
    "calculation_date": "2023-04-15T00:00:00Z",
    "calculation_source": "scheduled_job"
  },
  {
    "id": 2,
    "kpi_name": "MTTR_Calculated",
    "kpi_value": 4.8,
    "kpi_unit": "hours",
    "calculation_date": "2023-04-08T00:00:00Z",
    "calculation_source": "scheduled_job"
  },
  ...
]
```

### Get KPI Report

Retrieves a comprehensive KPI report for a specific date range.

**Endpoint:** `GET /api/kpis/report`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Query Parameters:**

| Parameter | Type | Required | Description | Default |
|-----------|------|----------|-------------|---------|
| start_date | string | No | Start date for report (format: YYYY-MM-DD) | 30 days ago |
| end_date | string | No | End date for report (format: YYYY-MM-DD) | today |
| asset_id | integer | No | Filter by asset ID | All assets |

**Response:**

```json
{
  "summary": {
    "mttr": {
      "current": 4.5,
      "previous": 4.8,
      "change_percent": -6.25,
      "trend": "improving"
    },
    "mtbf": {
      "current": 720.0,
      "previous": 680.0,
      "change_percent": 5.88,
      "trend": "improving"
    },
    "failure_rate": {
      "current": 12.2,
      "previous": 12.8,
      "change_percent": -4.69,
      "trend": "improving"
    },
    "data_quality_score": {
      "current": 85.0,
      "previous": 82.0,
      "change_percent": 3.66,
      "trend": "improving"
    }
  },
  "trends": {
    "mttr": [
      {"date": "2023-04-15", "value": 4.5},
      {"date": "2023-04-08", "value": 4.8},
      {"date": "2023-04-01", "value": 5.2},
      ...
    ],
    "mtbf": [
      {"date": "2023-04-15", "value": 720.0},
      {"date": "2023-04-08", "value": 680.0},
      {"date": "2023-04-01", "value": 650.0},
      ...
    ],
    "failure_rate": [
      {"date": "2023-04-15", "value": 12.2},
      {"date": "2023-04-08", "value": 12.8},
      {"date": "2023-04-01", "value": 13.5},
      ...
    ]
  },
  "assets": [
    {
      "asset_id": 1,
      "asset_name": "Pump Station 1",
      "mttr": 3.8,
      "mtbf": 840.0,
      "failure_rate": 10.5
    },
    {
      "asset_id": 2,
      "asset_name": "Conveyor Belt A",
      "mttr": 5.2,
      "mtbf": 620.0,
      "failure_rate": 14.1
    },
    ...
  ],
  "data_quality": {
    "total_work_orders": 120,
    "missing_repair_time": 5,
    "missing_dates": 3,
    "invalid_intervals": 2,
    "score": 85.0
  }
}
```

## KPI Calculation

### Run KPI Calculations

Manually triggers the KPI calculation job.

**Endpoint:** `GET /api/kpis/run-calculations`

**Authentication Required:** Yes

**Permissions Required:** `MANAGER` or `ADMIN`

**Response:**

```json
{
  "status": "success",
  "message": "KPI calculation job completed successfully",
  "results": {
    "mttr": 4.5,
    "mtbf": 720.0,
    "failure_rate": 12.2,
    "data_quality_score": 85.0
  }
}
```

### Save Custom KPI

Saves a custom KPI value.

**Endpoint:** `POST /api/kpis/custom`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher

**Request Body:**

```json
{
  "kpi_name": "Custom_Efficiency",
  "kpi_value": 92.5,
  "kpi_unit": "percent",
  "asset_id": 1,
  "notes": "Manually calculated efficiency based on production data"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Custom KPI saved successfully",
  "kpi_id": 156
}
```

## Data Quality

### Get Data Quality Metrics

Retrieves data quality metrics for work order data.

**Endpoint:** `GET /api/kpis/data-quality`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Response:**

```json
{
  "total_work_orders": 120,
  "missing_repair_time": 5,
  "missing_start_date": 2,
  "missing_end_date": 3,
  "corrective_work_orders": 85,
  "invalid_corrective_intervals": 2,
  "score": 85.0,
  "recommendations": [
    "Add missing repair time data for 5 work orders",
    "Fix invalid time intervals for 2 corrective work orders"
  ]
}
```
