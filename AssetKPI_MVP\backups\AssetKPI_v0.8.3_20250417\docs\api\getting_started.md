# Getting Started with the AssetKPI API

This guide will help you get started with using the AssetKPI API for your applications.

## Prerequisites

Before you begin, make sure you have:

1. Access to an AssetKPI instance (running locally or on a server)
2. Authentication credentials (Firebase account or API key)
3. Basic knowledge of REST APIs and your preferred programming language

## Authentication

The AssetKPI API supports two authentication methods:

### Firebase Authentication

For browser-based applications and user-specific operations, use a Firebase ID token:

```
Authorization: Bearer <firebase_id_token>
```

To get a Firebase ID token, you need to authenticate with Firebase:

```javascript
// JavaScript example
import { getAuth, signInWithEmailAndPassword } from "firebase/auth";

const auth = getAuth();
signInWithEmailAndPassword(auth, "<EMAIL>", "password")
  .then((userCredential) => {
    // Get the ID token
    return userCredential.user.getIdToken();
  })
  .then((token) => {
    console.log("Firebase ID token:", token);
    // Use this token in your API requests
  })
  .catch((error) => {
    console.error("Authentication error:", error);
  });
```

### API Key Authentication

For server-to-server communication and automated processes, use an API key:

```
X-API-Key: <api_key>
```

To get an API key, you need to create one in the AssetKPI system:

1. Log in to the AssetKPI web interface
2. Navigate to the Admin section
3. Go to API Keys
4. Click "Create New API Key"
5. Give your key a name and select the appropriate permissions
6. Copy the generated API key (it will only be shown once)

## Making Your First API Request

Let's start by making a simple request to get the latest KPIs:

### Using cURL

```bash
# Using Firebase ID token
curl -X GET "http://localhost:8000/api/kpis/latest" \
  -H "Authorization: Bearer <firebase_id_token>"

# Using API key
curl -X GET "http://localhost:8000/api/kpis/latest" \
  -H "X-API-Key: <api_key>"
```

### Using Python

```python
import requests

# Configuration
API_BASE_URL = "http://localhost:8000/api"
API_KEY = "your-api-key"

# Make the request
response = requests.get(
    f"{API_BASE_URL}/kpis/latest",
    headers={"X-API-Key": API_KEY}
)

# Check if the request was successful
if response.status_code == 200:
    kpis = response.json()
    print(f"MTTR: {kpis['mttr']} hours")
    print(f"MTBF: {kpis['mtbf']} hours")
    print(f"Failure Rate: {kpis['failure_rate']} failures/year")
else:
    print(f"Error: {response.status_code}")
    print(response.text)
```

### Using JavaScript

```javascript
// Configuration
const API_BASE_URL = "http://localhost:8000/api";
const API_KEY = "your-api-key";

// Make the request
fetch(`${API_BASE_URL}/kpis/latest`, {
  headers: {
    "X-API-Key": API_KEY
  }
})
.then(response => {
  if (!response.ok) {
    throw new Error(`HTTP error! Status: ${response.status}`);
  }
  return response.json();
})
.then(kpis => {
  console.log(`MTTR: ${kpis.mttr} hours`);
  console.log(`MTBF: ${kpis.mtbf} hours`);
  console.log(`Failure Rate: ${kpis.failure_rate} failures/year`);
})
.catch(error => {
  console.error("Error fetching KPIs:", error);
});
```

## Creating a Simple Client

Let's create a simple client class that you can use for all your API requests:

### Python Client

```python
import requests

class AssetKPIClient:
    def __init__(self, base_url, firebase_id_token=None, api_key=None):
        self.base_url = base_url
        self.firebase_id_token = firebase_id_token
        self.api_key = api_key

    def get_headers(self):
        headers = {'Content-Type': 'application/json'}
        if self.firebase_id_token:
            headers['Authorization'] = f'Bearer {self.firebase_id_token}'
        elif self.api_key:
            headers['X-API-Key'] = self.api_key
        return headers

    def get(self, endpoint, params=None):
        url = f"{self.base_url}{endpoint}"
        response = requests.get(url, headers=self.get_headers(), params=params)
        response.raise_for_status()  # Raise an exception for 4XX/5XX responses
        return response.json()

    def post(self, endpoint, data):
        url = f"{self.base_url}{endpoint}"
        response = requests.post(url, headers=self.get_headers(), json=data)
        response.raise_for_status()
        return response.json()

    def put(self, endpoint, data):
        url = f"{self.base_url}{endpoint}"
        response = requests.put(url, headers=self.get_headers(), json=data)
        response.raise_for_status()
        return response.json()

    def delete(self, endpoint):
        url = f"{self.base_url}{endpoint}"
        response = requests.delete(url, headers=self.get_headers())
        response.raise_for_status()
        return response.json()

# Example usage
client = AssetKPIClient("http://localhost:8000/api", api_key="your-api-key")

# Get latest KPIs
kpis = client.get("/kpis/latest")
print(f"MTTR: {kpis['mttr']} hours")

# Get all spare parts
parts = client.get("/inventory/parts")
print(f"Found {len(parts)} spare parts")

# Create a work order
new_work_order = {
    "assetId": 5,
    "workOrderType": "Preventive",
    "description": "Quarterly maintenance check",
    "status": "OPEN",
    "assignedTo": "John Smith",
    "downtimeMinutes": 60,
    "startDate": "2023-04-16T09:00:00Z"
}
result = client.post("/ingest/workorder", new_work_order)
print(f"Created work order with ID: {result['workorder_id']}")
```

### JavaScript Client

```javascript
class AssetKPIClient {
  constructor(baseUrl, firebaseIdToken = null, apiKey = null) {
    this.baseUrl = baseUrl;
    this.firebaseIdToken = firebaseIdToken;
    this.apiKey = apiKey;
  }

  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.firebaseIdToken) {
      headers['Authorization'] = `Bearer ${this.firebaseIdToken}`;
    } else if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey;
    }

    return headers;
  }

  async get(endpoint, params = {}) {
    const url = new URL(`${this.baseUrl}${endpoint}`);

    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async post(endpoint, data) {
    const url = `${this.baseUrl}${endpoint}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async put(endpoint, data) {
    const url = `${this.baseUrl}${endpoint}`;

    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async delete(endpoint) {
    const url = `${this.baseUrl}${endpoint}`;

    const response = await fetch(url, {
      method: 'DELETE',
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }
}

// Example usage
const client = new AssetKPIClient("http://localhost:8000/api", null, "your-api-key");

// Get latest KPIs
client.get("/kpis/latest")
  .then(kpis => {
    console.log(`MTTR: ${kpis.mttr} hours`);
  })
  .catch(error => {
    console.error("Error:", error);
  });

// Get all spare parts
client.get("/inventory/parts")
  .then(parts => {
    console.log(`Found ${parts.length} spare parts`);
  })
  .catch(error => {
    console.error("Error:", error);
  });

// Create a work order
const newWorkOrder = {
  assetId: 5,
  workOrderType: "Preventive",
  description: "Quarterly maintenance check",
  status: "OPEN",
  assignedTo: "John Smith",
  downtimeMinutes: 60,
  startDate: "2023-04-16T09:00:00Z"
};

client.post("/ingest/workorder", newWorkOrder)
  .then(result => {
    console.log(`Created work order with ID: ${result.workorder_id}`);
  })
  .catch(error => {
    console.error("Error:", error);
  });
```

## Common API Operations

Here are some common operations you might want to perform with the AssetKPI API:

### Inventory Management

#### Get all spare parts

```python
parts = client.get("/inventory/parts")
```

#### Get a specific part

```python
part = client.get(f"/inventory/parts/{part_id}")
```

#### Update stock quantity

```python
client.put(f"/inventory/parts/{part_id}", {"stockquantity": 20})
```

#### Get inventory analysis

```python
analysis = client.get("/inventory/analysis")
```

### Work Order Management

#### Get all work orders

```python
work_orders = client.get("/workorders")
```

#### Get open work orders

```python
open_work_orders = client.get("/workorders", {"status": "OPEN"})
```

#### Create a work order

```python
new_work_order = {
    "assetId": 5,
    "workOrderType": "Corrective",
    "description": "Replace motor coupling",
    "status": "OPEN",
    "assignedTo": "Jane Doe",
    "downtimeMinutes": 180,
    "startDate": "2023-04-16T09:00:00Z"
}
result = client.post("/ingest/workorder", new_work_order)
work_order_id = result["workorder_id"]
```

#### Close a work order

```python
client.put(f"/workorders/{work_order_id}", {
    "status": "CLOSED",
    "endDate": "2023-04-16T14:30:00Z",
    "repairTimeMinutes": 150,
    "maintenanceCost": 520.00
})
```

### KPI Management

#### Get latest KPIs

```python
kpis = client.get("/kpis/latest")
```

#### Get KPI history

```python
mttr_history = client.get("/kpis/history/MTTR_Calculated", {
    "start_date": "2023-01-01",
    "end_date": "2023-04-15"
})
```

#### Run KPI calculations

```python
client.get("/kpis/run-calculations")
```

## Error Handling

When working with the API, it's important to handle errors properly:

```python
try:
    result = client.get("/some/endpoint")
    # Process the result
except requests.exceptions.HTTPError as e:
    if e.response.status_code == 401:
        print("Authentication error. Check your credentials.")
    elif e.response.status_code == 403:
        print("Permission denied. You don't have access to this resource.")
    elif e.response.status_code == 404:
        print("Resource not found.")
    elif e.response.status_code == 429:
        print("Rate limit exceeded. Try again later.")
    else:
        print(f"API error: {e}")
except requests.exceptions.ConnectionError:
    print("Connection error. Check if the API server is running.")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Rate Limiting

The API has rate limits to prevent abuse and ensure system stability:

- 100 requests per minute for Firebase token authentication
- 200 requests per minute for API key authentication

If you exceed these limits, you'll receive a 429 Too Many Requests response with a Retry-After header:

```
Status: 429 Too Many Requests
Retry-After: 30
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1620000000
```

To avoid hitting rate limits:

1. Cache responses when appropriate
2. Batch operations when possible
3. Implement exponential backoff for retries
4. Monitor your usage with the rate limit headers
5. Use API keys for higher limits

For more detailed information, see the [Rate Limiting Guide](rate_limiting.md).

## Next Steps

Now that you're familiar with the basics, you can:

1. Explore the [API Documentation](index.md) for detailed information about all endpoints
2. Check out the [Use Cases](examples/use_cases.md) for examples of common scenarios
3. Try the [Postman Collection](examples/postman_guide.md) for interactive API testing
4. Implement more advanced features in your application

## Support

If you encounter any issues or have questions about the API, please contact the AssetKPI support team.
