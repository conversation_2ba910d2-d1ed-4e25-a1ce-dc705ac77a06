import requests
import json
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base URL for the API
BASE_URL = "http://localhost:8000"

def test_with_token(endpoint, token=None, expected_status=200):
    """
    Test an endpoint with or without a token
    """
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    print(f"\nTesting {endpoint}")
    print(f"Headers: {headers}")
    print(f"Expected status: {expected_status}")
    
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
        
        # Print the response
        print(f"Actual status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text[:100]}...")
        else:
            print(f"Response: {response.text}")
        
        if response.status_code == expected_status:
            print("✅ Test PASSED")
        else:
            print("❌ Test FAILED")
            
    except Exception as e:
        print(f"Error: {e}")
        print("❌ Test FAILED")

def main():
    # Try to read the token from file
    token = None
    try:
        with open("firebase_id_token.txt", "r") as f:
            token = f.read().strip()
            print(f"Read token from file: {token[:20]}...{token[-20:]}")
    except FileNotFoundError:
        print("Token file not found. Some tests will fail.")
    except Exception as e:
        print(f"Error reading token file: {e}")
    
    # Test scenarios
    
    # 1. Public endpoint (should work without token)
    test_with_token("/", token=None, expected_status=200)
    
    # 2. Protected endpoint without token (should fail with 401)
    test_with_token("/api/kpi/history/MTTR_Calculated", token=None, expected_status=401)
    
    # 3. Protected endpoint with valid token (should work)
    if token:
        test_with_token("/api/kpi/history/MTTR_Calculated", token=token, expected_status=200)
    
    # 4. Admin-only endpoint with valid token (should work if user is admin)
    if token:
        test_with_token("/api/users", token=token, expected_status=200)
    
    # 5. Protected endpoint with invalid token (should fail with 401)
    test_with_token("/api/kpi/history/MTTR_Calculated", token="invalid.token.here", expected_status=401)
    
    # 6. Protected endpoint with expired token (would need a real expired token)
    # For now, we'll skip this test
    
    print("\nTest summary:")
    print("1. Public endpoint without token")
    print("2. Protected endpoint without token")
    print("3. Protected endpoint with valid token")
    print("4. Admin-only endpoint with valid token")
    print("5. Protected endpoint with invalid token")

if __name__ == "__main__":
    main()
