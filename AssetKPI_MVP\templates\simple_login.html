<!DOCTYPE html>
<html>
<head>
    <title>Simple Login</title>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        #status, #result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Simple Login</h1>

    <div id="status">Initializing Firebase...</div>

    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>

    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="TestTest">
    </div>

    <button id="login-button">Sign In</button>

    <div id="result"></div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
            authDomain: "ikios-596779.firebaseapp.com",
            projectId: "ikios-596779",
            storageBucket: "ikios-596779.appspot.com",
            messagingSenderId: "1045444071940",
            appId: "1:1045444071940:web:c5e52be89b1c4fcd8457741"
        };

        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            document.getElementById('status').textContent = 'Firebase initialized successfully';
            document.getElementById('status').className = 'success';
        } catch (error) {
            document.getElementById('status').textContent = 'Firebase initialization error: ' + error.message;
            document.getElementById('status').className = 'error';
        }

        // Handle login
        document.getElementById('login-button').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');

            try {
                resultDiv.textContent = 'Signing in...';
                resultDiv.className = '';

                console.log('Attempting to sign in with:', email);
                const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);

                console.log('Sign in successful:', userCredential);
                resultDiv.textContent = 'Signed in successfully as ' + userCredential.user.email;
                resultDiv.className = 'success';

                // Get the ID token
                const token = await userCredential.user.getIdToken();
                console.log('ID token:', token);

                // Log the token for debugging
                console.log('Token first 20 chars:', token.substring(0, 20) + '...');

                // Check the user directly first
                try {
                    const userCheckResponse = await fetch('/api/user-check/' + userCredential.user.uid, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (userCheckResponse.ok) {
                        const userData = await userCheckResponse.json();
                        console.log('User check response:', userData);
                        resultDiv.textContent += '\nUser found in database. UID: ' + userData.uid;
                    } else {
                        const userError = await userCheckResponse.text();
                        console.error('User check failed:', userError);
                        resultDiv.textContent += '\nUser check failed: ' + userCheckResponse.statusText;
                    }
                } catch (error) {
                    console.error('Error checking user:', error);
                    resultDiv.textContent += '\nError checking user: ' + error.message;
                }

                // Try the debug endpoint next
                try {
                    const debugResponse = await fetch('/api/debug-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + token
                        },
                        body: JSON.stringify({ email: userCredential.user.email })
                    });

                    if (debugResponse.ok) {
                        const debugData = await debugResponse.json();
                        console.log('Debug token response:', debugData);
                        resultDiv.textContent += '\nDebug token verification successful. UID: ' + debugData.uid;
                    } else {
                        const debugError = await debugResponse.text();
                        console.error('Debug token verification failed:', debugError);
                        resultDiv.textContent += '\nDebug token verification failed: ' + debugResponse.statusText;

                        // Try to parse the error
                        try {
                            const errorObj = JSON.parse(debugError);
                            console.error('Debug token error details:', errorObj);
                            resultDiv.textContent += '\nError details: ' + errorObj.error;
                        } catch (e) {
                            console.error('Could not parse error:', debugError);
                            resultDiv.textContent += '\nRaw error: ' + debugError;
                        }
                    }
                } catch (error) {
                    console.error('Error calling debug endpoint:', error);
                    resultDiv.textContent += '\nError calling debug endpoint: ' + error.message;
                }

                // Now try the regular endpoint
                try {
                    const response = await fetch('/api/verify-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + token
                        },
                        body: JSON.stringify({ email: userCredential.user.email })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        console.log('Token verification successful:', data);
                        resultDiv.textContent += '\nToken verification successful. Role: ' + data.role;

                        // Redirect to dashboard after successful login
                        // window.location.href = '/dashboard';
                    } else {
                        const error = await response.text();
                        console.error('Token verification failed:', error);
                        resultDiv.textContent += '\nToken verification failed: ' + response.statusText;

                        // Try to parse the error
                        try {
                            const errorObj = JSON.parse(error);
                            console.error('Token verification error details:', errorObj);
                            resultDiv.textContent += '\nError details: ' + errorObj.error;
                        } catch (e) {
                            console.error('Could not parse error:', error);
                            resultDiv.textContent += '\nRaw error: ' + error;
                        }
                    }
                } catch (error) {
                    console.error('Error calling verify-token endpoint:', error);
                    resultDiv.textContent += '\nError calling verify-token endpoint: ' + error.message;
                }
            } catch (error) {
                console.error('Sign in error:', error);
                resultDiv.textContent = 'Error signing in: ' + error.message;
                resultDiv.className = 'error';
            }
        });
    </script>
</body>
</html>
