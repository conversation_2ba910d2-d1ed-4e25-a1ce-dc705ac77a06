# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Replace the problematic import
old_import = """# Local application imports
from models import (
    Base, UserRole, UserPermission, User, KpiReport, Sparepart, WorkOrder,
    WorkOrderParts, KPI, CalculatedKpiHistory, InventoryRecommendation,
    EOQCalculation, SafetyStockCalculation, InventoryAnalysis, InventoryConfig,
    AssetLocation, AssetSystem, AssetCategory, Asset, AssetSpecification,
    AssetWarranty, WorkOrderPlan, WorkOrderTask, LaborResource, WorkOrderLabor,
    PMSchedule, PMJobPlan, PMJobTask, AssetMeter, MeterReading, Storeroom,
    StorageLocation, Vendor, PurchaseOrder, POItem, InventoryTransaction,
    ResourceSkill, ToolsEquipment, WorkOrderTool, TechnicalDocument,
    AssetDocument, SafetyProcedure, WorkOrderProcedure
)"""

new_import = """# Local application imports
from models import *"""

content = content.replace(old_import, new_import)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed the imports in main.py")
