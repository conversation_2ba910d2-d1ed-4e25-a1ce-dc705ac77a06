import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def execute_migration():
    """Execute the SQL migration script for Milestone 6."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Read the SQL migration script
        with open('db_migration_milestone6.sql', 'r') as f:
            sql_script = f.read()
        
        # Split the script into individual statements
        statements = sql_script.split(';')
        
        # Execute each statement
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement + ';')
                    print(f"Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"Error executing statement: {statement[:50]}...")
                    print(f"Error: {e}")
        
        # Commit the changes
        conn.commit()
        print("Milestone 6 migration completed successfully!")
        
        # Return the connection and cursor for data population
        return conn, cursor
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return None, None

def populate_sample_data(conn, cursor):
    """Populate the database with sample data for Milestone 6."""
    try:
        print("\nPopulating sample data for Milestone 6...")
        
        # Get vendors
        cursor.execute("SELECT vendor_id, vendor_name FROM vendors")
        vendors = cursor.fetchall()
        
        if not vendors:
            print("No vendors found. Please run Milestone 5 first.")
            return
        
        # Get spare parts with vendors
        cursor.execute("""
            SELECT s.partid, s.partname, s.unitprice, s.preferred_vendor_id, s.storage_location_id
            FROM spareparts s
            WHERE s.preferred_vendor_id IS NOT NULL
        """)
        parts = cursor.fetchall()
        
        if not parts:
            print("No spare parts with vendors found. Please run Milestone 5 first.")
            return
        
        # Get storage locations
        cursor.execute("SELECT location_id, location_name FROM storage_locations")
        locations = cursor.fetchall()
        
        if not locations:
            print("No storage locations found. Please run Milestone 5 first.")
            return
        
        # Sample PO statuses
        po_statuses = ['Draft', 'Submitted', 'Approved', 'Ordered', 'Partially Received', 'Received', 'Closed', 'Cancelled']
        
        # Sample users who create POs
        po_creators = ['John Smith', 'Jane Doe', 'Bob Johnson', 'Alice Brown', 'System']
        
        # Sample shipping addresses
        shipping_addresses = [
            "Main Facility, 123 Manufacturing Way, Industrial Park, IP 12345",
            "Warehouse A, 456 Storage Drive, Warehouse District, WD 23456",
            "Production Plant, 789 Assembly Road, Factory Zone, FZ 34567",
            "Maintenance Building, 101 Repair Street, Service Area, SA 45678",
            "Distribution Center, 202 Logistics Lane, Shipping Zone, SZ 56789"
        ]
        
        # Create purchase orders
        print("Creating purchase orders...")
        
        # Track how many we've created
        pos_created = 0
        
        # Create POs for the past 2 years
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=365*2)
        
        # Group parts by vendor
        vendor_parts = {}
        for part_id, part_name, unit_price, vendor_id, storage_location_id in parts:
            if vendor_id not in vendor_parts:
                vendor_parts[vendor_id] = []
            vendor_parts[vendor_id].append((part_id, part_name, unit_price, storage_location_id))
        
        # Create 30-50 POs
        num_pos = random.randint(30, 50)
        
        for i in range(num_pos):
            # Select a random vendor
            vendor_id, vendor_name = random.choice(vendors)
            
            # Generate a PO number
            po_number = f"PO-{datetime.now().year}-{1000 + i}"
            
            # Generate random dates
            days_ago = random.randint(0, 365*2)
            order_date = end_date - timedelta(days=days_ago)
            expected_delivery_date = order_date + timedelta(days=random.randint(7, 45))
            
            # Determine status based on dates
            if order_date > end_date - timedelta(days=7):
                # Recent POs are more likely to be in early stages
                status = random.choices(po_statuses[:4], weights=[0.3, 0.3, 0.2, 0.2], k=1)[0]
            elif order_date > end_date - timedelta(days=30):
                # POs from last month are likely in middle stages
                status = random.choices(po_statuses[2:6], weights=[0.2, 0.3, 0.3, 0.2], k=1)[0]
            else:
                # Older POs are likely completed
                status = random.choices(po_statuses[4:], weights=[0.1, 0.6, 0.2, 0.1], k=1)[0]
            
            # Select a random creator
            created_by = random.choice(po_creators)
            
            # Select a random shipping address
            shipping_address = random.choice(shipping_addresses)
            
            # Insert the PO
            cursor.execute(
                """
                INSERT INTO purchase_orders
                (po_number, vendor_id, order_date, expected_delivery_date, status, 
                 shipping_address, created_by, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING po_id
                """,
                (po_number, vendor_id, order_date, expected_delivery_date, status, 
                 shipping_address, created_by, 
                 order_date + timedelta(hours=random.randint(0, 8)), 
                 order_date + timedelta(hours=random.randint(8, 24)))
            )
            
            po_id = cursor.fetchone()[0]
            
            # Add PO items
            # Get parts for this vendor
            vendor_part_list = vendor_parts.get(vendor_id, [])
            
            if not vendor_part_list:
                # If no parts for this vendor, use random parts
                vendor_part_list = random.sample(parts, min(random.randint(1, 5), len(parts)))
                vendor_part_list = [(p[0], p[1], p[2], p[4]) for p in vendor_part_list]
            
            # Determine how many different parts to order (1-5)
            num_parts = min(random.randint(1, 5), len(vendor_part_list))
            
            # Select random parts
            selected_parts = random.sample(vendor_part_list, num_parts)
            
            total_amount = 0
            
            for part_id, part_name, unit_price, storage_location_id in selected_parts:
                # Determine quantity to order
                quantity = random.randint(1, 20)
                
                # Calculate line total
                line_total = quantity * unit_price
                total_amount += line_total
                
                # Determine received quantity based on status
                received_quantity = 0
                if status in ['Partially Received', 'Received', 'Closed']:
                    if status == 'Partially Received':
                        received_quantity = random.randint(1, quantity - 1)
                    else:
                        received_quantity = quantity
                
                # Insert PO item
                cursor.execute(
                    """
                    INSERT INTO po_items
                    (po_id, part_id, quantity, unit_price, line_total, received_quantity)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING item_id
                    """,
                    (po_id, part_id, quantity, unit_price, line_total, received_quantity)
                )
                
                # Create inventory transactions for received items
                if received_quantity > 0:
                    # Receipt transaction
                    transaction_date = order_date + timedelta(days=random.randint(1, 30))
                    
                    cursor.execute(
                        """
                        INSERT INTO inventory_transactions
                        (part_id, transaction_type, quantity, transaction_date, reference_type, 
                         reference_id, location_to, unit_cost, total_cost, created_by)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (part_id, 'Receipt', received_quantity, transaction_date, 'PO', 
                         po_id, storage_location_id, unit_price, unit_price * received_quantity, created_by)
                    )
            
            # Update PO with total amount
            cursor.execute(
                """
                UPDATE purchase_orders
                SET total_amount = %s
                WHERE po_id = %s
                """,
                (total_amount, po_id)
            )
            
            pos_created += 1
            
            if pos_created % 10 == 0:
                print(f"  - Created {pos_created} purchase orders so far...")
        
        print(f"  - Created {pos_created} purchase orders with items")
        
        # Create additional inventory transactions
        print("Creating additional inventory transactions...")
        
        # Get work orders with parts
        cursor.execute("""
            SELECT DISTINCT w.workorderid, w.startdate, w.enddate, w.assignedto
            FROM workorders w
            JOIN workorderparts wp ON w.workorderid = wp.workorderid
            WHERE w.status IN ('Completed', 'Closed')
        """)
        work_orders = cursor.fetchall()
        
        # Get work order parts
        cursor.execute("""
            SELECT wp.workorderid, wp.partid, wp.quantityused, s.unitprice, s.storage_location_id
            FROM workorderparts wp
            JOIN spareparts s ON wp.partid = s.partid
            WHERE s.storage_location_id IS NOT NULL
        """)
        work_order_parts = cursor.fetchall()
        
        # Group parts by work order
        wo_parts = {}
        for wo_id, part_id, qty_used, unit_price, location_id in work_order_parts:
            if wo_id not in wo_parts:
                wo_parts[wo_id] = []
            wo_parts[wo_id].append((part_id, qty_used, unit_price, location_id))
        
        # Create issue transactions for work orders
        transactions_created = 0
        
        for wo_id, start_date, end_date, assigned_to in work_orders:
            # Get parts for this work order
            part_list = wo_parts.get(wo_id, [])
            
            if not part_list:
                continue
            
            for part_id, qty_used, unit_price, location_id in part_list:
                # Issue transaction
                if start_date:
                    transaction_date = start_date + timedelta(hours=random.randint(0, 24))
                else:
                    transaction_date = datetime.now() - timedelta(days=random.randint(1, 365))
                
                cursor.execute(
                    """
                    INSERT INTO inventory_transactions
                    (part_id, transaction_type, quantity, transaction_date, reference_type, 
                     reference_id, location_from, unit_cost, total_cost, created_by)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    (part_id, 'Issue', qty_used, transaction_date, 'WorkOrder', 
                     wo_id, location_id, unit_price, unit_price * qty_used, assigned_to)
                )
                
                transactions_created += 1
        
        # Create some random adjustment transactions
        num_adjustments = random.randint(20, 50)
        
        for _ in range(num_adjustments):
            # Pick a random part
            part_id, part_name, unit_price, vendor_id, location_id = random.choice(parts)
            
            # Determine adjustment type
            adj_type = random.choice(['Adjustment', 'Return', 'Transfer'])
            
            # Determine quantity
            quantity = random.randint(1, 10)
            
            # Determine date
            transaction_date = datetime.now() - timedelta(days=random.randint(1, 365))
            
            # Determine locations
            location_from = None
            location_to = None
            
            if adj_type == 'Transfer':
                location_from = location_id
                # Pick a different location for destination
                other_locations = [loc for loc in locations if loc[0] != location_id]
                if other_locations:
                    location_to = random.choice(other_locations)[0]
            elif adj_type == 'Adjustment':
                if random.random() < 0.5:  # 50% chance of positive adjustment
                    location_to = location_id
                else:  # 50% chance of negative adjustment
                    location_from = location_id
            else:  # Return
                location_to = location_id
            
            # Determine creator
            created_by = random.choice(po_creators)
            
            cursor.execute(
                """
                INSERT INTO inventory_transactions
                (part_id, transaction_type, quantity, transaction_date, reference_type, 
                 reference_id, location_from, location_to, unit_cost, total_cost, created_by)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (part_id, adj_type, quantity, transaction_date, 'Adjustment', 
                 None, location_from, location_to, unit_price, unit_price * quantity, created_by)
            )
            
            transactions_created += 1
        
        print(f"  - Created {transactions_created} inventory transactions")
        
        # Commit the changes
        conn.commit()
        print("Sample data population completed successfully!")
        
    except Exception as e:
        print(f"Error populating sample data: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()

def main():
    """Main function to execute migration and populate data."""
    conn, cursor = execute_migration()
    
    if conn and cursor:
        populate_sample_data(conn, cursor)
        cursor.close()
        conn.close()
    
    print("Milestone 6 completed!")

if __name__ == "__main__":
    main()
