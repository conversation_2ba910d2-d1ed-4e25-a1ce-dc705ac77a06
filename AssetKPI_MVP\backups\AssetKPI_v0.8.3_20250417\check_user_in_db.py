import os
import sys
import json
import firebase_admin
from firebase_admin import credentials, auth
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Import the User model and UserRole enum
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from main import User, UserRole

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if already initialized
        try:
            app = firebase_admin.get_app()
            print(f"Firebase Admin SDK already initialized with app: {app.name}")
            return True
        except ValueError:
            pass
        
        # Initialize with service account
        try:
            SERVICE_ACCOUNT_KEY_PATH = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
            cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)
            firebase_admin.initialize_app(cred)
            print("Firebase Admin SDK initialized successfully")
            return True
        except Exception as e:
            print(f"Error initializing Firebase Admin SDK: {e}")
            return False
    except Exception as e:
        print(f"Error in initialize_firebase: {e}")
        return False

def get_firebase_user_from_token(token):
    """Get Firebase user from token"""
    try:
        # Verify the token
        decoded_token = auth.verify_id_token(token)
        firebase_uid = decoded_token.get("uid")
        email = decoded_token.get("email")
        
        print(f"Firebase UID from token: {firebase_uid}")
        print(f"Email from token: {email}")
        
        return firebase_uid, email
    except Exception as e:
        print(f"Error getting Firebase user from token: {e}")
        return None, None

def check_user_in_db(firebase_uid):
    """Check if user exists in database"""
    try:
        # Query the database
        user = db.query(User).filter(User.user_id == firebase_uid).first()
        
        if user:
            print(f"User found in database:")
            print(f"  User ID: {user.user_id}")
            print(f"  Email: {user.email}")
            print(f"  Role: {user.role}")
            print(f"  Full Name: {user.full_name}")
            print(f"  Created At: {user.created_at}")
            print(f"  Last Login: {user.last_login}")
            return user
        else:
            print(f"User with Firebase UID {firebase_uid} not found in database")
            return None
    except Exception as e:
        print(f"Error checking user in database: {e}")
        return None

def create_user_in_db(firebase_uid, email, role=UserRole.VIEWER, full_name=None):
    """Create a user in the database"""
    try:
        # Create new user
        user = User(
            user_id=firebase_uid,
            email=email,
            role=role,
            full_name=full_name,
            created_at=datetime.now(),
            last_login=None
        )
        
        # Add to database
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print(f"Created user in database:")
        print(f"  User ID: {user.user_id}")
        print(f"  Email: {user.email}")
        print(f"  Role: {user.role}")
        
        return user
    except Exception as e:
        db.rollback()
        print(f"Error creating user in database: {e}")
        return None

def list_all_users_in_db():
    """List all users in the database"""
    try:
        users = db.query(User).all()
        
        print(f"Found {len(users)} users in database:")
        for user in users:
            print(f"  User ID: {user.user_id}")
            print(f"  Email: {user.email}")
            print(f"  Role: {user.role}")
            print()
        
        return users
    except Exception as e:
        print(f"Error listing users in database: {e}")
        return []

def main():
    # Initialize Firebase Admin SDK
    if not initialize_firebase():
        print("Failed to initialize Firebase Admin SDK. Exiting.")
        sys.exit(1)
    
    # Read token from file
    try:
        with open("firebase_id_token.txt", "r") as f:
            token = f.read().strip()
            print(f"Read token from file: {token[:20]}...{token[-20:]}")
    except Exception as e:
        print(f"ERROR: Failed to read token from file: {e}")
        sys.exit(1)
    
    # Get Firebase user from token
    firebase_uid, email = get_firebase_user_from_token(token)
    if not firebase_uid:
        print("Failed to get Firebase user from token. Exiting.")
        sys.exit(1)
    
    # Check if user exists in database
    user = check_user_in_db(firebase_uid)
    
    # If user doesn't exist, ask if we should create it
    if not user:
        print("\nUser not found in database. Would you like to create it? (y/n)")
        choice = input().lower()
        if choice == 'y':
            print("Creating user in database...")
            role_choice = input("Enter role (VIEWER, ENGINEER, MANAGER, ADMIN) [ADMIN]: ").upper() or "ADMIN"
            role = getattr(UserRole, role_choice, UserRole.ADMIN)
            full_name = input("Enter full name [Johan Borgulf]: ") or "Johan Borgulf"
            
            user = create_user_in_db(firebase_uid, email, role, full_name)
            if user:
                print("User created successfully!")
            else:
                print("Failed to create user.")
    
    # List all users in database
    print("\nListing all users in database:")
    list_all_users_in_db()

if __name__ == "__main__":
    main()
