
<!DOCTYPE html>
<html>
<head>
    <title>Firebase Debug</title>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
</head>
<body>
    <h1>Firebase Debug</h1>
    <div id="status">Checking Firebase configuration...</div>
    <pre id="config"></pre>

    <h2>Test Authentication</h2>
    <form id="login-form">
        <div>
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" value="TestTest">
        </div>
        <button type="button" id="login-button">Sign In</button>
    </form>

    <div id="result"></div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
            authDomain: "ikios-596779.firebaseapp.com",
            projectId: "ikios-596779",
            storageBucket: "ikios-596779.appspot.com",
            messagingSenderId: "1045444071940",
            appId: "1:1045444071940:web:c5e52be89b1c4fcd8457741"
        };

        // Display the Firebase configuration
        document.getElementById('config').textContent = JSON.stringify(firebaseConfig, null, 2);

        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            document.getElementById('status').textContent = 'Firebase initialized successfully';
            document.getElementById('status').style.color = 'green';
        } catch (error) {
            document.getElementById('status').textContent = 'Firebase initialization error: ' + error.message;
            document.getElementById('status').style.color = 'red';
        }

        // Handle login
        document.getElementById('login-button').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');

            try {
                resultDiv.textContent = 'Signing in...';
                const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
                resultDiv.textContent = 'Signed in successfully as ' + userCredential.user.email;
                resultDiv.style.color = 'green';

                // Get the ID token
                const token = await userCredential.user.getIdToken();
                const tokenPreview = token.substring(0, 10) + '...' + token.substring(token.length - 10);
                resultDiv.textContent += '
Token: ' + tokenPreview;
            } catch (error) {
                resultDiv.textContent = 'Error signing in: ' + error.message;
                resultDiv.style.color = 'red';
            }
        });
    </script>
</body>
</html>
