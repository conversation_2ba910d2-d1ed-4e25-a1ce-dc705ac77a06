import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def execute_migration():
    """Execute the SQL migration script for Milestone 1."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Read the SQL migration script
        with open('db_migration_milestone1.sql', 'r') as f:
            sql_script = f.read()
        
        # Split the script into individual statements
        statements = sql_script.split(';')
        
        # Execute each statement
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement + ';')
                    print(f"Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"Error executing statement: {statement[:50]}...")
                    print(f"Error: {e}")
        
        # Commit the changes
        conn.commit()
        print("Milestone 1 migration completed successfully!")
        
        # Return the connection and cursor for data population
        return conn, cursor
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return None, None

def populate_sample_data(conn, cursor):
    """Populate the database with sample data for Milestone 1."""
    try:
        print("\nPopulating sample data for Milestone 1...")
        
        # Sample data for asset_locations
        locations = [
            ('Main Plant', 'Building', None, 'Main manufacturing facility'),
            ('Warehouse', 'Building', None, 'Main warehouse for storage'),
            ('Production Floor', 'Area', 1, 'Production area in main plant'),
            ('Assembly Line A', 'Line', 3, 'Assembly line A in production floor'),
            ('Assembly Line B', 'Line', 3, 'Assembly line B in production floor'),
            ('Quality Control', 'Area', 1, 'QC area in main plant'),
            ('Maintenance Shop', 'Area', 1, 'Maintenance area in main plant'),
            ('Raw Materials Storage', 'Area', 2, 'Raw materials section in warehouse'),
            ('Finished Goods Storage', 'Area', 2, 'Finished goods section in warehouse'),
            ('Shipping/Receiving', 'Area', 2, 'Shipping and receiving area in warehouse')
        ]
        
        print("Inserting asset locations...")
        for location in locations:
            cursor.execute(
                """
                INSERT INTO asset_locations (location_name, location_type, parent_location_id, description)
                VALUES (%s, %s, %s, %s)
                RETURNING location_id
                """,
                location
            )
            print(f"  - Added location: {location[0]}")
        
        # Sample data for asset_systems
        systems = [
            ('Production System', 'Main production system', None),
            ('HVAC System', 'Heating, ventilation, and air conditioning', None),
            ('Electrical System', 'Main electrical distribution', None),
            ('Compressed Air System', 'Plant-wide compressed air', None),
            ('Water Treatment System', 'Process water treatment', None),
            ('Assembly System', 'Assembly line system', 1),
            ('Packaging System', 'Product packaging system', 1),
            ('Cooling Subsystem', 'HVAC cooling components', 2),
            ('Heating Subsystem', 'HVAC heating components', 2),
            ('Primary Distribution', 'Primary electrical distribution', 3)
        ]
        
        print("Inserting asset systems...")
        for system in systems:
            cursor.execute(
                """
                INSERT INTO asset_systems (system_name, description, parent_system_id)
                VALUES (%s, %s, %s)
                RETURNING system_id
                """,
                system
            )
            print(f"  - Added system: {system[0]}")
        
        # Sample data for asset_categories
        categories = [
            ('Machinery', 'Production machinery and equipment', None),
            ('Utilities', 'Utility equipment and infrastructure', None),
            ('Transportation', 'Material handling and transportation', None),
            ('IT Equipment', 'Information technology equipment', None),
            ('Buildings', 'Building structures and components', None),
            ('Production Machinery', 'Machines used in production', 1),
            ('Assembly Equipment', 'Equipment used for assembly', 1),
            ('Electrical Equipment', 'Electrical distribution equipment', 2),
            ('HVAC Equipment', 'Heating and cooling equipment', 2),
            ('Vehicles', 'Powered vehicles for material handling', 3)
        ]
        
        print("Inserting asset categories...")
        for category in categories:
            cursor.execute(
                """
                INSERT INTO asset_categories (category_name, description, parent_category_id)
                VALUES (%s, %s, %s)
                RETURNING category_id
                """,
                category
            )
            print(f"  - Added category: {category[0]}")
        
        # Update existing assets with new hierarchy and classification
        print("Updating existing assets with hierarchy and classification...")
        
        # First, get all existing assets
        cursor.execute("SELECT assetid FROM assets")
        asset_ids = [row[0] for row in cursor.fetchall()]
        
        if asset_ids:
            # Get all location IDs
            cursor.execute("SELECT location_id FROM asset_locations")
            location_ids = [row[0] for row in cursor.fetchall()]
            
            # Get all system IDs
            cursor.execute("SELECT system_id FROM asset_systems")
            system_ids = [row[0] for row in cursor.fetchall()]
            
            # Get all category IDs
            cursor.execute("SELECT category_id FROM asset_categories")
            category_ids = [row[0] for row in cursor.fetchall()]
            
            # Update each asset with random hierarchy and classification
            for asset_id in asset_ids:
                location_id = random.choice(location_ids) if location_ids else None
                system_id = random.choice(system_ids) if system_ids else None
                category_id = random.choice(category_ids) if category_ids else None
                
                # Some assets might have parent assets
                parent_asset_id = random.choice(asset_ids) if asset_ids and random.random() < 0.3 else None
                # Avoid self-reference
                if parent_asset_id == asset_id:
                    parent_asset_id = None
                
                cursor.execute(
                    """
                    UPDATE assets
                    SET location_id = %s, system_id = %s, category_id = %s, parent_asset_id = %s
                    WHERE assetid = %s
                    """,
                    (location_id, system_id, category_id, parent_asset_id, asset_id)
                )
                print(f"  - Updated asset ID {asset_id} with hierarchy and classification")
        else:
            print("  - No existing assets found to update")
        
        # Commit the changes
        conn.commit()
        print("Sample data population completed successfully!")
        
    except Exception as e:
        print(f"Error populating sample data: {e}")
        conn.rollback()

def main():
    """Main function to execute migration and populate data."""
    conn, cursor = execute_migration()
    
    if conn and cursor:
        populate_sample_data(conn, cursor)
        cursor.close()
        conn.close()
    
    print("Milestone 1 completed!")

if __name__ == "__main__":
    main()
