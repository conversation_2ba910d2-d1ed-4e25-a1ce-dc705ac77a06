"""
Middleware for tracking user activity in the AssetKPI application.

This middleware automatically logs page views and other user activities
for analytics purposes.
"""
import json
import uuid
import re
from datetime import datetime
from typing import Callable, Dict, Any, Optional
from fastapi import Request, Response
from fastapi.routing import APIRoute
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from sqlalchemy.orm import Session

def parse_user_agent(user_agent_string: str) -> Dict[str, Any]:
    """
    Parse user agent string to extract browser and device information.

    Args:
        user_agent_string: The user agent string from the request headers

    Returns:
        Dictionary with browser and device information
    """
    # Default values
    browser_info = {
        "browser": "Unknown",
        "browser_version": "Unknown",
        "os": "Unknown",
        "os_version": "Unknown",
        "device_type": "Unknown",
        "is_mobile": False,
        "is_tablet": False,
        "is_desktop": True
    }

    if not user_agent_string:
        return browser_info

    # Extract browser information
    # Chrome
    chrome_match = re.search(r'Chrome\/([\d.]+)', user_agent_string)
    if chrome_match:
        browser_info["browser"] = "Chrome"
        browser_info["browser_version"] = chrome_match.group(1)

    # Firefox
    firefox_match = re.search(r'Firefox\/([\d.]+)', user_agent_string)
    if firefox_match:
        browser_info["browser"] = "Firefox"
        browser_info["browser_version"] = firefox_match.group(1)

    # Safari
    safari_match = re.search(r'Safari\/([\d.]+)', user_agent_string)
    if safari_match and 'Chrome' not in user_agent_string:
        browser_info["browser"] = "Safari"
        browser_info["browser_version"] = safari_match.group(1)

    # Edge
    edge_match = re.search(r'Edg(?:e)?\/([\d.]+)', user_agent_string)
    if edge_match:
        browser_info["browser"] = "Edge"
        browser_info["browser_version"] = edge_match.group(1)

    # Extract OS information
    # Windows
    windows_match = re.search(r'Windows NT ([\d.]+)', user_agent_string)
    if windows_match:
        browser_info["os"] = "Windows"
        version_map = {
            "10.0": "10",
            "6.3": "8.1",
            "6.2": "8",
            "6.1": "7",
            "6.0": "Vista",
            "5.2": "XP",
            "5.1": "XP"
        }
        browser_info["os_version"] = version_map.get(windows_match.group(1), windows_match.group(1))

    # macOS
    mac_match = re.search(r'Mac OS X ([\d_.]+)', user_agent_string)
    if mac_match:
        browser_info["os"] = "macOS"
        browser_info["os_version"] = mac_match.group(1).replace("_", ".")

    # Linux
    if "Linux" in user_agent_string and "Android" not in user_agent_string:
        browser_info["os"] = "Linux"

    # Android
    android_match = re.search(r'Android ([\d.]+)', user_agent_string)
    if android_match:
        browser_info["os"] = "Android"
        browser_info["os_version"] = android_match.group(1)

    # iOS
    ios_match = re.search(r'iPhone OS ([\d_]+)', user_agent_string)
    if ios_match:
        browser_info["os"] = "iOS"
        browser_info["os_version"] = ios_match.group(1).replace("_", ".")

    # Determine device type
    if any(device in user_agent_string for device in ["Mobile", "Android", "iPhone"]):
        browser_info["device_type"] = "Mobile"
        browser_info["is_mobile"] = True
        browser_info["is_desktop"] = False
    elif any(device in user_agent_string for device in ["iPad", "Tablet"]):
        browser_info["device_type"] = "Tablet"
        browser_info["is_tablet"] = True
        browser_info["is_desktop"] = False
    else:
        browser_info["device_type"] = "Desktop"

    return browser_info

# Import models
from models.usage_analytics import UserActivityLog

class UsageTrackingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for tracking user activity in the application.

    This middleware automatically logs page views and API calls.
    """
    def __init__(self, app: ASGIApp, db_func: Callable[[], Session]):
        super().__init__(app)
        self.get_db = db_func

    async def dispatch(self, request: Request, call_next):
        """
        Process the request and log user activity.

        Args:
            request: The FastAPI request
            call_next: The next middleware or route handler

        Returns:
            The response
        """
        # Generate a session ID if not present
        session_id = request.cookies.get("session_id", str(uuid.uuid4()))

        # Get start time
        start_time = datetime.now()

        # Process the request
        response = await call_next(request)

        # Set session ID cookie if not present
        if "session_id" not in request.cookies:
            response.set_cookie(
                key="session_id",
                value=session_id,
                httponly=True,
                max_age=86400 * 30,  # 30 days
                samesite="lax"
            )

        # Skip tracking for static files and docs
        path = request.url.path
        if path.startswith("/static/") or path.startswith("/docs") or path.startswith("/redoc"):
            return response

        # Get user from request state (set by authentication middleware)
        user_id = getattr(request.state, "user", None)
        if user_id:
            user_id = getattr(user_id, "user_id", None)

        # Determine event type
        event_type = "API_CALL" if path.startswith("/api/") else "PAGE_VIEW"

        # Collect details with enhanced tracking
        details = {
            "path": str(request.url.path),
            "method": request.method,
            "query_params": dict(request.query_params),
            "referrer": request.headers.get("referer", ""),
            "user_agent": request.headers.get("user-agent", ""),
            "ip_address": request.client.host if request.client else None,
            "response_status": response.status_code,
            "response_time_ms": (datetime.now() - start_time).total_seconds() * 1000,
            "content_type": response.headers.get("content-type", ""),
            "content_length": response.headers.get("content-length", ""),
            "accept_language": request.headers.get("accept-language", ""),
            "viewport_size": request.headers.get("viewport-size", ""),
            "screen_resolution": request.headers.get("screen-resolution", ""),
            "device_memory": request.headers.get("device-memory", ""),
            "connection_type": request.headers.get("connection-type", "")
        }

        # Extract user role if available
        user_role = None
        if hasattr(request.state, "user") and request.state.user:
            user_role = getattr(request.state.user, "role", None)

        # Parse browser info from user agent
        browser_info = parse_user_agent(details.get("user_agent", ""))

        # Determine previous page from referrer
        previous_page = None
        referrer = details.get("referrer", "")
        base_url = f"{request.url.scheme}://{request.url.netloc}"
        if referrer and referrer.startswith(base_url):
            previous_page = referrer.replace(base_url, "")

        # Calculate response time in seconds
        duration_seconds = details.get("response_time_ms", 0) / 1000

        # Log the activity with enhanced tracking
        try:
            # Get a new database session directly from SessionLocal
            from main import SessionLocal
            db = SessionLocal()
            activity_log = UserActivityLog(
                user_id=user_id,
                event_type=event_type,
                details=details,
                session_id=session_id,
                # Enhanced tracking fields
                duration_seconds=duration_seconds,
                previous_page=previous_page,
                browser_info=browser_info,
                user_role=user_role
            )
            db.add(activity_log)
            db.commit()
        except Exception as e:
            print(f"Error logging user activity: {e}")
            # Don't let tracking errors affect the response
            if 'db' in locals():
                db.rollback()
        finally:
            if 'db' in locals():
                db.close()

        return response

def track_feature_usage(db, user_id: Optional[str], feature_name: str, action: str,
                       session_id: Optional[str] = None, additional_details: Optional[Dict[str, Any]] = None,
                       component_id: Optional[str] = None, duration_seconds: Optional[float] = None,
                       previous_page: Optional[str] = None, user_role: Optional[str] = None,
                       browser_info: Optional[Dict[str, Any]] = None, conversion_event: bool = False,
                       interaction_depth: Optional[int] = None):
    """
    Utility function to manually track feature usage with enhanced tracking.

    Args:
        db: Database session or session factory
        user_id: User ID (can be None for anonymous users)
        feature_name: Name of the feature being used
        action: Action being performed (e.g., "run", "export", "filter")
        session_id: Session ID (optional)
        additional_details: Additional details to log (optional)
        component_id: ID of the UI component interacted with (optional)
        duration_seconds: Duration of the interaction in seconds (optional)
        previous_page: Previous page in the user journey (optional)
        user_role: Role of the user at the time of the activity (optional)
        browser_info: Browser and device information (optional)
        conversion_event: Whether this activity represents a conversion event (optional)
        interaction_depth: Depth of interaction (e.g., clicks, scrolls) (optional)
    """
    # Check if db is a generator (from get_db dependency)
    if hasattr(db, '__next__'):
        try:
            db_session = next(db)
        except StopIteration:
            # If we can't get a session from the generator, create a new one
            from main import SessionLocal
            db_session = SessionLocal()
    else:
        # Use the provided session directly
        db_session = db

    try:
        details = {
            "feature": feature_name,
            "action": action,
            "timestamp": datetime.now().isoformat()
        }

        if additional_details:
            details.update(additional_details)

        activity_log = UserActivityLog(
            user_id=user_id,
            event_type="FEATURE_USE",
            details=details,
            session_id=session_id,
            # Enhanced tracking fields
            component_id=component_id,
            duration_seconds=duration_seconds,
            previous_page=previous_page,
            browser_info=browser_info,
            conversion_event=conversion_event,
            interaction_depth=interaction_depth,
            user_role=user_role
        )
        db_session.add(activity_log)
        db_session.commit()
        return True
    except Exception as e:
        print(f"Error tracking feature usage: {e}")
        db_session.rollback()
        return False
    finally:
        # Only close the session if we created it
        if hasattr(db, '__next__'):
            db_session.close()
