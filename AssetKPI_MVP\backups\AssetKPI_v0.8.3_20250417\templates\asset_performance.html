{% extends "layout.html" %}

{% block title %}Asset Performance Visualization{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2">Asset Performance Visualization</h1>
            <p class="text-muted">Analyze asset performance metrics and trends</p>
        </div>
    </div>

    <!-- Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Performance Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="metricSelect" class="form-label">Metric</label>
                            <select class="form-select" id="metricSelect">
                                <option value="oee" selected>OEE</option>
                                <option value="mttr">MTTR</option>
                                <option value="mtbf">MTBF</option>
                                <option value="availability">Availability</option>
                                <option value="performance">Performance</option>
                                <option value="quality">Quality</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="assetSelect" class="form-label">Asset</label>
                            <select class="form-select" id="assetSelect">
                                <option value="all" selected>All Assets</option>
                                <!-- Will be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="dateRangeSelect" class="form-label">Date Range</label>
                            <select class="form-select" id="dateRangeSelect">
                                <option value="7" selected>Last 7 Days</option>
                                <option value="30">Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="365">Last Year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="refreshBtn" class="form-label">&nbsp;</label>
                            <button id="refreshBtn" class="btn btn-primary d-block">Refresh Data</button>
                        </div>
                    </div>
                    <div class="row" id="customDateRange" style="display: none;">
                        <div class="col-md-3 mb-3">
                            <label for="startDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="endDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Performance Trends</h5>
                </div>
                <div class="card-body">
                    <div id="loadingIndicator" style="display: none;" class="text-center my-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading performance data...</p>
                    </div>
                    <div id="errorMessage" style="display: none;" class="alert alert-danger my-3"></div>
                    <div class="chart-container" style="position: relative; height:400px;">
                        <canvas id="mainChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Charts -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Asset Comparison</h5>
                </div>
                <div class="card-body">
                    <div id="comparisonLoadingIndicator" style="display: none;" class="text-center my-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="comparisonChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Performance Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="distributionLoadingIndicator" style="display: none;" class="text-center my-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="distributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Global chart instances
        let mainChart = null;
        let comparisonChart = null;
        let distributionChart = null;
        
        // Initialize date inputs with default values
        const today = new Date();
        const lastWeek = new Date(today);
        lastWeek.setDate(today.getDate() - 7);
        
        document.getElementById('startDate').valueAsDate = lastWeek;
        document.getElementById('endDate').valueAsDate = today;
        
        // Event listeners
        document.getElementById('dateRangeSelect').addEventListener('change', function() {
            const customDateRange = document.getElementById('customDateRange');
            if (this.value === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
            }
        });
        
        document.getElementById('refreshBtn').addEventListener('click', fetchData);
        
        // Load assets for dropdown
        loadAssets();
        
        // Initial data load
        fetchData();
        
        // Function to load assets for dropdown
        function loadAssets() {
            const assetSelect = document.getElementById('assetSelect');
            
            // Show loading state
            assetSelect.disabled = true;
            
            fetch('/api/assets')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Clear existing options except the first one
                    while (assetSelect.options.length > 1) {
                        assetSelect.remove(1);
                    }
                    
                    // Add assets to dropdown
                    data.forEach(asset => {
                        const option = document.createElement('option');
                        option.value = asset.assetid;
                        option.textContent = asset.assetname;
                        assetSelect.appendChild(option);
                    });
                    
                    // Enable select
                    assetSelect.disabled = false;
                })
                .catch(error => {
                    console.error('Error loading assets:', error);
                    // Enable select even on error
                    assetSelect.disabled = false;
                });
        }
        
        // Function to fetch performance data
        function fetchData() {
            // Get selected values
            const metric = document.getElementById('metricSelect').value;
            const assetId = document.getElementById('assetSelect').value;
            const dateRange = document.getElementById('dateRangeSelect').value;
            
            // Calculate date range
            let startDate, endDate;
            
            if (dateRange === 'custom') {
                startDate = document.getElementById('startDate').value;
                endDate = document.getElementById('endDate').value;
            } else {
                endDate = new Date().toISOString().split('T')[0]; // Today
                startDate = new Date();
                startDate.setDate(startDate.getDate() - parseInt(dateRange));
                startDate = startDate.toISOString().split('T')[0];
            }
            
            // Show loading indicators
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('comparisonLoadingIndicator').style.display = 'block';
            document.getElementById('distributionLoadingIndicator').style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
            
            // Build query parameters
            const params = new URLSearchParams({
                chart_type: metric,
                asset_group: assetId,
                date_from: startDate,
                date_to: endDate,
                aggregation: 'daily'
            });
            
            // Fetch data from API
            fetch(`/api/assets/performance?${params.toString()}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading indicators
                    document.getElementById('loadingIndicator').style.display = 'none';
                    document.getElementById('comparisonLoadingIndicator').style.display = 'none';
                    document.getElementById('distributionLoadingIndicator').style.display = 'none';
                    
                    // Update charts with new data
                    updateMainChart(data.time_series, metric);
                    updateComparisonChart(data.comparison, metric);
                    updateDistributionChart(data.distribution, metric);
                })
                .catch(error => {
                    console.error('Error fetching performance data:', error);
                    
                    // Hide loading indicators
                    document.getElementById('loadingIndicator').style.display = 'none';
                    document.getElementById('comparisonLoadingIndicator').style.display = 'none';
                    document.getElementById('distributionLoadingIndicator').style.display = 'none';
                    
                    // Show error message
                    const errorMessage = document.getElementById('errorMessage');
                    errorMessage.textContent = `Error loading data: ${error.message}`;
                    errorMessage.style.display = 'block';
                    
                    // Use sample data as fallback
                    useSampleData(metric);
                });
        }
        
        // Function to update main chart
        function updateMainChart(data, metric) {
            const ctx = document.getElementById('mainChart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (mainChart) {
                mainChart.destroy();
            }
            
            // Get metric label and color
            const metricInfo = getMetricInfo(metric);
            
            // Create new chart
            mainChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels || [],
                    datasets: [{
                        label: metricInfo.label,
                        data: data.values || [],
                        borderColor: metricInfo.color,
                        backgroundColor: metricInfo.backgroundColor,
                        borderWidth: 2,
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: metricInfo.label
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `${metricInfo.label} Trend`
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    }
                }
            });
        }
        
        // Function to update comparison chart
        function updateComparisonChart(data, metric) {
            const ctx = document.getElementById('comparisonChart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (comparisonChart) {
                comparisonChart.destroy();
            }
            
            // Get metric info
            const metricInfo = getMetricInfo(metric);
            
            // Create new chart
            comparisonChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.labels || [],
                    datasets: [{
                        label: metricInfo.label,
                        data: data.values || [],
                        backgroundColor: metricInfo.backgroundColor,
                        borderColor: metricInfo.color,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: metricInfo.label
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Asset Comparison'
                        }
                    }
                }
            });
        }
        
        // Function to update distribution chart
        function updateDistributionChart(data, metric) {
            const ctx = document.getElementById('distributionChart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (distributionChart) {
                distributionChart.destroy();
            }
            
            // Get metric info
            const metricInfo = getMetricInfo(metric);
            
            // Create new chart
            distributionChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: data.labels || [],
                    datasets: [{
                        data: data.values || [],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 206, 86, 0.6)',
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(153, 102, 255, 0.6)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${metricInfo.label} Distribution`
                        },
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        }
        
        // Function to get metric information
        function getMetricInfo(metric) {
            const metricMap = {
                'oee': {
                    label: 'OEE (%)',
                    color: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)'
                },
                'mttr': {
                    label: 'MTTR (hours)',
                    color: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)'
                },
                'mtbf': {
                    label: 'MTBF (hours)',
                    color: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)'
                },
                'availability': {
                    label: 'Availability (%)',
                    color: 'rgb(255, 159, 64)',
                    backgroundColor: 'rgba(255, 159, 64, 0.2)'
                },
                'performance': {
                    label: 'Performance (%)',
                    color: 'rgb(153, 102, 255)',
                    backgroundColor: 'rgba(153, 102, 255, 0.2)'
                },
                'quality': {
                    label: 'Quality (%)',
                    color: 'rgb(255, 205, 86)',
                    backgroundColor: 'rgba(255, 205, 86, 0.2)'
                }
            };
            
            return metricMap[metric] || metricMap['oee'];
        }
        
        // Function to use sample data as fallback
        function useSampleData(metric) {
            // Sample time series data
            const timeLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            let mainChartData;
            
            // Sample comparison data
            const assetNames = ['Asset 1', 'Asset 2', 'Asset 3', 'Asset 4', 'Asset 5'];
            let comparisonChartData;
            
            // Sample distribution data
            const distributionLabels = ['Excellent', 'Good', 'Average', 'Poor', 'Critical'];
            let distributionData;
            
            // Set different sample data based on metric
            switch(metric) {
                case 'mttr':
                    mainChartData = [4.2, 3.8, 4.5, 3.5, 3.2, 2.8, 3.0, 2.5, 2.2, 2.8, 3.0, 2.7];
                    comparisonChartData = [2.5, 3.8, 1.5, 4.2, 3.0];
                    distributionData = [10, 20, 40, 20, 10];
                    break;
                case 'mtbf':
                    mainChartData = [120, 135, 110, 145, 160, 175, 165, 180, 190, 175, 165, 170];
                    comparisonChartData = [180, 120, 200, 110, 150];
                    distributionData = [20, 30, 25, 15, 10];
                    break;
                case 'availability':
                    mainChartData = [88, 92, 90, 94, 96, 95, 93, 97, 98, 96, 94, 95];
                    comparisonChartData = [95, 90, 98, 85, 92];
                    distributionData = [25, 35, 20, 15, 5];
                    break;
                case 'performance':
                    mainChartData = [82, 85, 80, 83, 87, 90, 88, 92, 94, 90, 88, 91];
                    comparisonChartData = [90, 82, 94, 78, 85];
                    distributionData = [20, 30, 25, 15, 10];
                    break;
                case 'quality':
                    mainChartData = [95, 96, 94, 97, 98, 99, 97, 98, 99, 98, 97, 98];
                    comparisonChartData = [98, 95, 99, 94, 97];
                    distributionData = [30, 40, 20, 7, 3];
                    break;
                default: // OEE
                    mainChartData = [65, 70, 75, 72, 78, 82, 80, 85, 88, 84, 82, 85];
                    comparisonChartData = [85, 75, 90, 65, 80];
                    distributionData = [15, 25, 35, 15, 10];
            }
            
            // Update charts with sample data
            updateMainChart({labels: timeLabels, values: mainChartData}, metric);
            updateComparisonChart({labels: assetNames, values: comparisonChartData}, metric);
            updateDistributionChart({labels: distributionLabels, values: distributionData}, metric);
        }
    });
</script>
{% endblock %}
