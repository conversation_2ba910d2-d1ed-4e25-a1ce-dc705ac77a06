from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

class BulkCreateRequest(BaseModel):
    """
    Request model for bulk create operations.
    """
    items: List[Dict[str, Any]] = Field(..., description="List of items to create")


class BulkUpdateRequest(BaseModel):
    """
    Request model for bulk update operations.
    """
    items: List[Dict[str, Any]] = Field(..., description="List of items to update")


class BulkDeleteRequest(BaseModel):
    """
    Request model for bulk delete operations.
    """
    ids: List[Any] = Field(..., description="List of IDs to delete")


class BulkOperationResponse(BaseModel):
    """
    Response model for bulk operations.
    """
    status: str = Field(..., description="Operation status")
    message: str = Field(..., description="Operation message")
    success_count: int = Field(..., description="Number of successful operations")
    error_count: int = Field(0, description="Number of failed operations")
    errors: Optional[List[Dict[str, Any]]] = Field(None, description="List of errors")
