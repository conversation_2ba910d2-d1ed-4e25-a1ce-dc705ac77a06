{% extends "layout.html" %}

{% block title %}AssetKPI - Admin Panel{% endblock %}

{% block styles %}
<style>
    .admin-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .admin-card:hover {
        transform: translateY(-5px);
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .metric-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .role-admin {
        color: #dc3545;
        font-weight: bold;
    }
    .role-manager {
        color: #fd7e14;
        font-weight: bold;
    }
    .role-engineer {
        color: #0d6efd;
        font-weight: bold;
    }
    .role-viewer {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>Admin Panel</h1>
        <p class="text-muted">System administration and user management</p>
    </div>
</div>

<!-- System Metrics -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card admin-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">{{ system_metrics.total_users }}</div>
                <div class="metric-label">Total Users</div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card admin-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">{{ system_metrics.total_work_orders }}</div>
                <div class="metric-label">Total Work Orders</div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card admin-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">{{ system_metrics.total_spare_parts }}</div>
                <div class="metric-label">Total Spare Parts</div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card admin-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">{{ system_metrics.total_recommendations }}</div>
                <div class="metric-label">Total Recommendations</div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card admin-card bg-light">
            <div class="card-body text-center">
                <div class="metric-value">{{ system_metrics.active_recommendations }}</div>
                <div class="metric-label">Active Recommendations</div>
            </div>
        </div>
    </div>
</div>

<!-- User Management -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>User Management</h5>
                <button class="btn btn-primary mt-2" id="addUserBtn">Add New User</button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="usersTable">
                        <thead>
                            <tr>
                                <th>User ID</th>
                                <th>Email</th>
                                <th>Full Name</th>
                                <th>Role</th>
                                <th>Created At</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if users %}
                                {% for user in users %}
                                    <tr>
                                        <td>{{ user.user_id }}</td>
                                        <td>{{ user.email }}</td>
                                        <td>{{ user.full_name }}</td>
                                        <td class="role-{{ user.role|lower }}">{{ user.role }}</td>
                                        <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else 'N/A' }}</td>
                                        <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else 'Never' }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary edit-user-btn" data-user-id="{{ user.user_id }}">Edit</button>
                                            <button class="btn btn-sm btn-outline-danger delete-user-btn" data-user-id="{{ user.user_id }}">Delete</button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">No users available</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Modal -->
<div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalLabel">Add/Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" id="userId" name="userId">
                    <div class="mb-3">
                        <label for="userEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="userEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="userFullName" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="userFullName" name="fullName" required>
                    </div>
                    <div class="mb-3">
                        <label for="userRole" class="form-label">Role</label>
                        <select class="form-select" id="userRole" name="role" required>
                            <option value="VIEWER">Viewer</option>
                            <option value="ENGINEER">Engineer</option>
                            <option value="MANAGER">Manager</option>
                            <option value="ADMIN">Admin</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmationModalLabel">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="confirmationMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmActionBtn">Confirm</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Initialize Bootstrap modals
    let userModal;
    let confirmationModal;
    
    document.addEventListener('DOMContentLoaded', function() {
        userModal = new bootstrap.Modal(document.getElementById('userModal'));
        confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
        
        // Add User button
        const addUserBtn = document.getElementById('addUserBtn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', function() {
                // Clear form
                document.getElementById('userId').value = '';
                document.getElementById('userEmail').value = '';
                document.getElementById('userFullName').value = '';
                document.getElementById('userRole').value = 'VIEWER';
                
                // Update modal title
                document.getElementById('userModalLabel').textContent = 'Add New User';
                
                // Show modal
                userModal.show();
            });
        }
        
        // Edit User buttons
        const editUserBtns = document.querySelectorAll('.edit-user-btn');
        editUserBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const userId = this.dataset.userId;
                
                // Fetch user data
                AssetKPIAuth.authenticatedFetch(`/api/users/${userId}`)
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        }
                        throw new Error('Failed to fetch user data');
                    })
                    .then(user => {
                        // Fill form with user data
                        document.getElementById('userId').value = user.user_id;
                        document.getElementById('userEmail').value = user.email;
                        document.getElementById('userFullName').value = user.full_name;
                        document.getElementById('userRole').value = user.role;
                        
                        // Update modal title
                        document.getElementById('userModalLabel').textContent = 'Edit User';
                        
                        // Show modal
                        userModal.show();
                    })
                    .catch(error => {
                        console.error('Error fetching user data:', error);
                        alert('Error fetching user data: ' + error.message);
                    });
            });
        });
        
        // Delete User buttons
        const deleteUserBtns = document.querySelectorAll('.delete-user-btn');
        deleteUserBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const userId = this.dataset.userId;
                
                // Update confirmation message
                document.getElementById('confirmationMessage').textContent = `Are you sure you want to delete this user? This action cannot be undone.`;
                
                // Set up confirmation button
                const confirmBtn = document.getElementById('confirmActionBtn');
                confirmBtn.onclick = function() {
                    // Delete user
                    AssetKPIAuth.authenticatedFetch(`/api/users/${userId}`, {
                        method: 'DELETE'
                    })
                        .then(response => {
                            if (response.ok) {
                                return response.json();
                            }
                            throw new Error('Failed to delete user');
                        })
                        .then(result => {
                            // Hide modal
                            confirmationModal.hide();
                            
                            // Reload page
                            window.location.reload();
                        })
                        .catch(error => {
                            console.error('Error deleting user:', error);
                            alert('Error deleting user: ' + error.message);
                        });
                };
                
                // Show confirmation modal
                confirmationModal.show();
            });
        });
        
        // Save User button
        const saveUserBtn = document.getElementById('saveUserBtn');
        if (saveUserBtn) {
            saveUserBtn.addEventListener('click', function() {
                // Get form data
                const userId = document.getElementById('userId').value;
                const email = document.getElementById('userEmail').value;
                const fullName = document.getElementById('userFullName').value;
                const role = document.getElementById('userRole').value;
                
                // Validate form
                if (!email || !fullName || !role) {
                    alert('Please fill in all required fields');
                    return;
                }
                
                // Prepare data
                const userData = {
                    email: email,
                    full_name: fullName,
                    role: role
                };
                
                // Determine if this is an add or edit operation
                const method = userId ? 'PUT' : 'POST';
                const url = userId ? `/api/users/${userId}` : '/api/users';
                
                // Save user
                AssetKPIAuth.authenticatedFetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                })
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        }
                        throw new Error('Failed to save user');
                    })
                    .then(result => {
                        // Hide modal
                        userModal.hide();
                        
                        // Reload page
                        window.location.reload();
                    })
                    .catch(error => {
                        console.error('Error saving user:', error);
                        alert('Error saving user: ' + error.message);
                    });
            });
        }
    });
</script>
{% endblock %}
