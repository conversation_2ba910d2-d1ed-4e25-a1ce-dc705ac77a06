        // Function to handle logout
        async function handleLogout() {
            try {
                console.log('Logging out...');
                await AssetKPIAuth.signOut();
                
                // Clear token cookie
                document.cookie = 'firebaseIdToken=; path=/; max-age=0; SameSite=Strict';
                
                // Redirect to logout endpoint which will handle server-side cleanup
                window.location.href = '/logout';
            } catch (error) {
                console.error('Logout error:', error);
                alert('Error logging out. Please try again.');
            }
        }
