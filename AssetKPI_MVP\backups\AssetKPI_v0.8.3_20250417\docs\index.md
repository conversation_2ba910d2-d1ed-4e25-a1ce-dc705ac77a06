# AssetKPI Integration Documentation

Welcome to the AssetKPI Integration Documentation. This documentation provides comprehensive information about integrating with AssetKPI using webhooks, ERP connectors, and the Python SDK.

## Table of Contents

- [Introduction](#introduction)
- [Webhooks](./integration/webhooks/index.md)
- [ERP Integration](./integration/erp/index.md)
- [Python SDK](./integration/sdk/index.md)
- [Tutorials](./integration/tutorials/index.md)
- [Examples](./integration/examples/index.md)
- [API Reference](./integration/reference/index.md)

## Introduction

AssetKPI provides several integration options to connect with external systems:

### Webhooks

Webhooks allow external systems to receive real-time notifications when specific events occur in AssetKPI. This enables you to build automated workflows and keep external systems in sync with AssetKPI data.

[Learn more about Webhooks](./integration/webhooks/index.md)

### ERP Integration

AssetKPI can integrate with various ERP systems to synchronize data such as assets, inventory, and work orders. This enables you to maintain consistent data across your enterprise systems.

[Learn more about ERP Integration](./integration/erp/index.md)

### Python SDK

The AssetKPI Python SDK provides a convenient way to interact with the AssetKPI API programmatically. It includes comprehensive client classes for all API endpoints, with advanced error handling and retry logic.

[Learn more about the Python SDK](./integration/sdk/index.md)

## Getting Started

To get started with AssetKPI integration, choose the integration method that best suits your needs:

- For real-time notifications, use [Webhooks](./integration/webhooks/index.md)
- For synchronizing data with ERP systems, use [ERP Integration](./integration/erp/index.md)
- For programmatic access to AssetKPI, use the [Python SDK](./integration/sdk/index.md)

## Need Help?

If you need assistance with AssetKPI integration, please refer to our [Tutorials](./integration/tutorials/index.md) and [Examples](./integration/examples/index.md), or contact our support team.
