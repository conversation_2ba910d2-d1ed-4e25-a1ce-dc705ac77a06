{% extends "layout.html" %}

{% block title %}AssetKPI - Inventory Configuration{% endblock %}

{% block styles %}
<style>
    .config-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .config-card:hover {
        transform: translateY(-5px);
    }
    .table-responsive {
        overflow-x: auto;
    }
    .parameter-description {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .parameter-type {
        font-size: 0.8rem;
        padding: 2px 6px;
        border-radius: 4px;
        background-color: #e9ecef;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>Inventory Configuration</h1>
        <p class="text-muted">Manage inventory optimization parameters</p>
    </div>
</div>

<!-- Configuration Parameters Table -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>Configuration Parameters</h5>
                <button class="btn btn-primary" id="addParameterBtn">Add Parameter</button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="configTable">
                        <thead>
                            <tr>
                                <th>Parameter Name</th>
                                <th>Value</th>
                                <th>Description</th>
                                <th>Type</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="configTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                            <tr>
                                <td colspan="6" class="text-center">Loading configuration parameters...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div id="configMessage" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Parameter Modal -->
<div class="modal fade" id="parameterModal" tabindex="-1" aria-labelledby="parameterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="parameterModalLabel">Add/Edit Parameter</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="parameterForm">
                    <input type="hidden" id="parameterId" name="parameterId">
                    <div class="mb-3">
                        <label for="parameterName" class="form-label">Parameter Name</label>
                        <input type="text" class="form-control" id="parameterName" name="parameterName" required minlength="3" maxlength="100">
                        <div class="form-text">Use lowercase with underscores (e.g., ordering_cost)</div>
                    </div>
                    <div class="mb-3">
                        <label for="parameterValue" class="form-label">Parameter Value</label>
                        <input type="text" class="form-control" id="parameterValue" name="parameterValue" required>
                    </div>
                    <div class="mb-3">
                        <label for="parameterDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="parameterDescription" name="parameterDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="parameterType" class="form-label">Parameter Type</label>
                        <select class="form-select" id="parameterType" name="parameterType" required>
                            <option value="decimal">Decimal</option>
                            <option value="integer">Integer</option>
                            <option value="string">String</option>
                            <option value="boolean">Boolean</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveParameterBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmationModalLabel">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="confirmationMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmActionBtn">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Parameter Info Modal -->
<div class="modal fade" id="parameterInfoModal" tabindex="-1" aria-labelledby="parameterInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="parameterInfoModalLabel">Inventory Configuration Parameters</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>Economic Order Quantity (EOQ) Parameters</h6>
                <ul>
                    <li><strong>ordering_cost</strong>: The fixed cost of placing an order, regardless of the quantity ordered.</li>
                    <li><strong>holding_cost_percent</strong>: The annual cost of holding inventory, expressed as a percentage of the item's value.</li>
                </ul>
                
                <h6>Safety Stock Parameters</h6>
                <ul>
                    <li><strong>service_level_z</strong>: The Z-score corresponding to the desired service level (e.g., 1.65 for 95% service level).</li>
                    <li><strong>demand_variability_factor</strong>: Factor representing demand variability (standard deviation / average demand).</li>
                    <li><strong>lead_time_variability_factor</strong>: Factor representing lead time variability (standard deviation / average lead time).</li>
                </ul>
                
                <h6>Inventory Optimization Parameters</h6>
                <ul>
                    <li><strong>overstock_days_threshold</strong>: Number of days since last restock to consider for overstock alerts.</li>
                    <li><strong>obsolete_days_threshold</strong>: Number of days since last restock to consider for obsolete alerts.</li>
                    <li><strong>overstock_safety_stock_multiplier</strong>: Multiplier for safety stock to determine overstock level.</li>
                </ul>
                
                <h6>Common Z-scores for Service Levels</h6>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Service Level</th>
                            <th>Z-score</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>90%</td><td>1.28</td></tr>
                        <tr><td>95%</td><td>1.65</td></tr>
                        <tr><td>97%</td><td>1.88</td></tr>
                        <tr><td>98%</td><td>2.05</td></tr>
                        <tr><td>99%</td><td>2.33</td></tr>
                        <tr><td>99.9%</td><td>3.09</td></tr>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Initialize Bootstrap modals
    let parameterModal;
    let confirmationModal;
    let parameterInfoModal;
    
    // Function to load configuration parameters
    async function loadConfigParameters() {
        try {
            const response = await AssetKPIAuth.authenticatedFetch('/api/inventory/config');
            if (!response.ok) {
                throw new Error(`Failed to fetch configuration parameters: ${response.status}`);
            }
            
            const parameters = await response.json();
            const tableBody = document.getElementById('configTableBody');
            
            if (parameters.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center">No configuration parameters found</td></tr>';
                return;
            }
            
            // Sort parameters by name
            parameters.sort((a, b) => a.parameter_name.localeCompare(b.parameter_name));
            
            // Generate table rows
            tableBody.innerHTML = parameters.map(param => `
                <tr>
                    <td>${param.parameter_name}</td>
                    <td>${param.parameter_value}</td>
                    <td>
                        <div class="parameter-description">${param.parameter_description || 'No description'}</div>
                    </td>
                    <td><span class="parameter-type">${param.parameter_type}</span></td>
                    <td>${new Date(param.updated_at).toLocaleString()}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary edit-parameter-btn" data-param-id="${param.id}">Edit</button>
                        <button class="btn btn-sm btn-outline-danger delete-parameter-btn" data-param-id="${param.id}" data-param-name="${param.parameter_name}">Delete</button>
                    </td>
                </tr>
            `).join('');
            
            // Add event listeners to edit and delete buttons
            setupParameterButtons();
        } catch (error) {
            console.error('Error loading configuration parameters:', error);
            document.getElementById('configMessage').innerHTML = `
                <div class="alert alert-danger">
                    Error loading configuration parameters: ${error.message}
                </div>
            `;
        }
    }
    
    // Function to setup parameter buttons
    function setupParameterButtons() {
        // Edit buttons
        document.querySelectorAll('.edit-parameter-btn').forEach(btn => {
            btn.addEventListener('click', async function() {
                const paramId = this.dataset.paramId;
                
                try {
                    const response = await AssetKPIAuth.authenticatedFetch(`/api/inventory/config/${paramId}`);
                    if (!response.ok) {
                        throw new Error(`Failed to fetch parameter: ${response.status}`);
                    }
                    
                    const param = await response.json();
                    
                    // Fill form with parameter data
                    document.getElementById('parameterId').value = param.id;
                    document.getElementById('parameterName').value = param.parameter_name;
                    document.getElementById('parameterName').disabled = true; // Can't change parameter name
                    document.getElementById('parameterValue').value = param.parameter_value;
                    document.getElementById('parameterDescription').value = param.parameter_description || '';
                    document.getElementById('parameterType').value = param.parameter_type;
                    document.getElementById('parameterType').disabled = true; // Can't change parameter type
                    
                    // Update modal title
                    document.getElementById('parameterModalLabel').textContent = 'Edit Parameter';
                    
                    // Show modal
                    parameterModal.show();
                } catch (error) {
                    console.error('Error fetching parameter:', error);
                    document.getElementById('configMessage').innerHTML = `
                        <div class="alert alert-danger">
                            Error fetching parameter: ${error.message}
                        </div>
                    `;
                }
            });
        });
        
        // Delete buttons
        document.querySelectorAll('.delete-parameter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const paramId = this.dataset.paramId;
                const paramName = this.dataset.paramName;
                
                // Update confirmation message
                document.getElementById('confirmationMessage').textContent = `Are you sure you want to delete the parameter "${paramName}"? This action cannot be undone.`;
                
                // Set up confirmation button
                const confirmBtn = document.getElementById('confirmActionBtn');
                confirmBtn.onclick = async function() {
                    try {
                        const response = await AssetKPIAuth.authenticatedFetch(`/api/inventory/config/${paramId}`, {
                            method: 'DELETE'
                        });
                        
                        if (!response.ok) {
                            throw new Error(`Failed to delete parameter: ${response.status}`);
                        }
                        
                        // Hide modal
                        confirmationModal.hide();
                        
                        // Show success message
                        document.getElementById('configMessage').innerHTML = `
                            <div class="alert alert-success">
                                Parameter "${paramName}" deleted successfully
                            </div>
                        `;
                        
                        // Reload parameters
                        loadConfigParameters();
                    } catch (error) {
                        console.error('Error deleting parameter:', error);
                        document.getElementById('configMessage').innerHTML = `
                            <div class="alert alert-danger">
                                Error deleting parameter: ${error.message}
                            </div>
                        `;
                        confirmationModal.hide();
                    }
                };
                
                // Show confirmation modal
                confirmationModal.show();
            });
        });
    }
    
    // Function to save parameter
    async function saveParameter() {
        const paramId = document.getElementById('parameterId').value;
        const paramName = document.getElementById('parameterName').value;
        const paramValue = document.getElementById('parameterValue').value;
        const paramDesc = document.getElementById('parameterDescription').value;
        const paramType = document.getElementById('parameterType').value;
        
        // Validate form
        if (!paramName || !paramValue || !paramType) {
            document.getElementById('configMessage').innerHTML = `
                <div class="alert alert-danger">
                    Please fill in all required fields
                </div>
            `;
            return;
        }
        
        try {
            let response;
            
            if (paramId) {
                // Update existing parameter
                response = await AssetKPIAuth.authenticatedFetch(`/api/inventory/config/${paramId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        parameter_value: paramValue,
                        parameter_description: paramDesc
                    })
                });
            } else {
                // Create new parameter
                response = await AssetKPIAuth.authenticatedFetch('/api/inventory/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        parameter_name: paramName,
                        parameter_value: paramValue,
                        parameter_description: paramDesc,
                        parameter_type: paramType
                    })
                });
            }
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to save parameter: ${response.status}`);
            }
            
            // Hide modal
            parameterModal.hide();
            
            // Show success message
            document.getElementById('configMessage').innerHTML = `
                <div class="alert alert-success">
                    Parameter ${paramId ? 'updated' : 'created'} successfully
                </div>
            `;
            
            // Reload parameters
            loadConfigParameters();
        } catch (error) {
            console.error('Error saving parameter:', error);
            document.getElementById('configMessage').innerHTML = `
                <div class="alert alert-danger">
                    Error saving parameter: ${error.message}
                </div>
            `;
        }
    }
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Bootstrap modals
        parameterModal = new bootstrap.Modal(document.getElementById('parameterModal'));
        confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
        parameterInfoModal = new bootstrap.Modal(document.getElementById('parameterInfoModal'));
        
        // Add Parameter button
        const addParameterBtn = document.getElementById('addParameterBtn');
        if (addParameterBtn) {
            addParameterBtn.addEventListener('click', function() {
                // Clear form
                document.getElementById('parameterId').value = '';
                document.getElementById('parameterName').value = '';
                document.getElementById('parameterName').disabled = false;
                document.getElementById('parameterValue').value = '';
                document.getElementById('parameterDescription').value = '';
                document.getElementById('parameterType').value = 'decimal';
                document.getElementById('parameterType').disabled = false;
                
                // Update modal title
                document.getElementById('parameterModalLabel').textContent = 'Add Parameter';
                
                // Show modal
                parameterModal.show();
            });
        }
        
        // Save Parameter button
        const saveParameterBtn = document.getElementById('saveParameterBtn');
        if (saveParameterBtn) {
            saveParameterBtn.addEventListener('click', saveParameter);
        }
        
        // Add info button to card header
        const cardHeader = document.querySelector('.card-header');
        if (cardHeader) {
            const infoBtn = document.createElement('button');
            infoBtn.className = 'btn btn-sm btn-outline-info ms-2';
            infoBtn.innerHTML = '<i class="bi bi-info-circle"></i> Parameter Info';
            infoBtn.addEventListener('click', function() {
                parameterInfoModal.show();
            });
            
            // Insert before the Add Parameter button
            cardHeader.insertBefore(infoBtn, addParameterBtn);
        }
        
        // Initialize authentication UI
        AssetKPIAuth.initAuth(function(user) {
            if (user) {
                // Load configuration parameters
                loadConfigParameters();
            } else {
                // Redirect to login page
                window.location.href = '/login?redirect=/inventory-config';
            }
        });
    });
</script>
{% endblock %}
