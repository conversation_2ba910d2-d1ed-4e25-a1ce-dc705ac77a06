# Getting Started with Webhooks

This tutorial will guide you through the process of setting up and using webhooks in AssetKPI.

## Table of Contents

- [Introduction](#introduction)
- [Prerequisites](#prerequisites)
- [Step 1: Create a Webhook Endpoint](#step-1-create-a-webhook-endpoint)
- [Step 2: Register a Webhook in AssetKPI](#step-2-register-a-webhook-in-assetkpi)
- [Step 3: Test the Webhook](#step-3-test-the-webhook)
- [Step 4: Handle Webhook Events](#step-4-handle-webhook-events)
- [Next Steps](#next-steps)
- [Related Topics](#related-topics)

## Introduction

Webhooks allow you to receive real-time notifications when specific events occur in AssetKPI. This tutorial will show you how to set up a webhook endpoint, register it in AssetKPI, and handle webhook events.

## Prerequisites

Before you begin, make sure you have:

- An AssetKPI account with MANAGER or ADMIN role
- A server that can receive HTTP requests (for the webhook endpoint)
- Basic knowledge of web development

## Step 1: Create a Webhook Endpoint

First, you need to create an endpoint that can receive webhook events from AssetKPI. This endpoint should be a URL that is accessible from the internet.

Here's a simple example of a webhook endpoint using Python and Flask:

```python
from flask import Flask, request, jsonify
import hmac
import hashlib
import json

app = Flask(__name__)

@app.route('/webhook', methods=['POST'])
def webhook():
    # Get the request data
    data = request.json
    
    # Log the webhook event
    print(f"Received webhook event: {data['event']}")
    print(f"Event data: {json.dumps(data, indent=2)}")
    
    # Process the webhook event (implement your business logic here)
    # ...
    
    # Return a success response
    return jsonify({"status": "success"}), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

Save this code to a file named `webhook_server.py` and run it:

```bash
python webhook_server.py
```

This will start a web server on port 5000 that listens for webhook events.

## Step 2: Register a Webhook in AssetKPI

Now that you have a webhook endpoint, you need to register it in AssetKPI:

1. Log in to AssetKPI with a MANAGER or ADMIN account
2. Navigate to the Webhooks page (Admin > Webhooks)
3. Click the "Create Webhook" button
4. Fill in the webhook details:
   - **Name**: A descriptive name for the webhook (e.g., "Inventory Updates")
   - **URL**: The URL of your webhook endpoint (e.g., `https://your-server.com/webhook`)
   - **Event Types**: Select the event types you want to receive (e.g., `inventory.updated`, `inventory.created`)
   - **Authentication**: Configure authentication if needed
   - **Retry Settings**: Configure retry settings if needed
5. Click "Create Webhook" to save the webhook

## Step 3: Test the Webhook

After registering the webhook, you can test it to make sure it's working correctly:

1. On the Webhooks page, find your webhook in the list
2. Click the "Test" button (paper plane icon)
3. Confirm that you want to send a test webhook
4. Check your webhook endpoint logs to verify that the test event was received

If the test is successful, you should see a log message in your webhook server console showing the test event.

## Step 4: Handle Webhook Events

Now that your webhook is set up and tested, you can implement the business logic to handle different types of webhook events.

Here's an example of how to handle inventory-related events:

```python
@app.route('/webhook', methods=['POST'])
def webhook():
    # Get the request data
    data = request.json
    
    # Log the webhook event
    print(f"Received webhook event: {data['event']}")
    
    # Handle different event types
    if data['event'] == 'inventory.updated':
        handle_inventory_updated(data['data'])
    elif data['event'] == 'inventory.created':
        handle_inventory_created(data['data'])
    elif data['event'] == 'inventory.threshold_reached':
        handle_inventory_threshold_reached(data['data'])
    else:
        print(f"Unhandled event type: {data['event']}")
    
    # Return a success response
    return jsonify({"status": "success"}), 200

def handle_inventory_updated(data):
    part_id = data['resource_id']
    part_data = data['data']
    print(f"Inventory updated for part {part_id}: {part_data}")
    # Implement your business logic here
    # For example, update your local database or notify relevant personnel

def handle_inventory_created(data):
    part_id = data['resource_id']
    part_data = data['data']
    print(f"New inventory part created: {part_id}: {part_data}")
    # Implement your business logic here
    # For example, add the part to your local database

def handle_inventory_threshold_reached(data):
    part_id = data['resource_id']
    threshold_data = data['data']
    print(f"Inventory threshold reached for part {part_id}: {threshold_data}")
    # Implement your business logic here
    # For example, create a purchase order or send an alert
```

## Next Steps

Now that you have set up a basic webhook, you can:

- Implement more sophisticated event handling
- Add authentication to secure your webhook
- Set up webhooks for other event types
- Integrate webhook events with your business processes

## Related Topics

- [Webhook Concepts](../webhooks/concepts.md)
- [Event Types](../webhooks/event-types.md)
- [Payload Format](../webhooks/payload-format.md)
- [Authentication](../webhooks/authentication.md)
- [Examples](../webhooks/examples.md)
- [API Reference](../reference/webhooks.md)
