version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: assetkpi_postgres
    environment:
      POSTGRES_DB: AssetKPI
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: Arcanum
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./SQL_files:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - assetkpi_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d AssetKPI"]
      interval: 10s
      timeout: 5s
      retries: 5

  # AssetKPI FastAPI Application
  assetkpi_app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: assetkpi_app
    environment:
      - DATABASE_URL=*******************************************/AssetKPI
      - SECRET_API_KEY=c5e52be8-9b1c-4fcd-8457-741c91ef5c85
      - FIREBASE_SERVICE_ACCOUNT_KEY=firebase-service-account.json
    volumes:
      - ./firebase-service-account.json:/app/firebase-service-account.json:ro
      - ./static:/app/static
      - ./templates:/app/templates
    ports:
      - "8000:8000"
    networks:
      - assetkpi_network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching (optional, for future use)
  redis:
    image: redis:7-alpine
    container_name: assetkpi_redis
    ports:
      - "6379:6379"
    networks:
      - assetkpi_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local

networks:
  assetkpi_network:
    driver: bridge
