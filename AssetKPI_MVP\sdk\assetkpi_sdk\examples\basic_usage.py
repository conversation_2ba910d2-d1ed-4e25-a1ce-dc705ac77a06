"""
Basic usage examples for the AssetKPI SDK.
"""

import os
from dotenv import load_dotenv
from assetkpi import AssetKPISDK, AuthenticationError, RateLimitError, APIError

# Load environment variables
load_dotenv()

# Get API key from environment variable
API_KEY = os.getenv("ASSETKPI_API_KEY")
BASE_URL = os.getenv("ASSETKPI_BASE_URL", "http://localhost:8000/api")

def main():
    """Run basic SDK examples."""
    # Initialize the SDK
    sdk = AssetKPISDK(
        base_url=BASE_URL,
        api_key=API_KEY
    )
    
    try:
        # Get current user
        print("Getting current user...")
        user = sdk.users.get_current_user()
        print(f"Current User: {user.get('full_name', 'Unknown')} ({user.get('email', 'Unknown')})")
        print(f"Role: {user.get('role', 'Unknown')}")
        print()
        
        # Get inventory summary
        print("Getting inventory summary...")
        inventory_summary = sdk.inventory.get_inventory_summary()
        print(f"Total Parts: {inventory_summary.get('total_parts', 0)}")
        print(f"Total Value: ${inventory_summary.get('total_value', 0):.2f}")
        print(f"Parts Below Reorder Level: {inventory_summary.get('below_reorder', 0)}")
        print()
        
        # Get latest KPIs
        print("Getting latest KPIs...")
        kpis = sdk.kpi.get_latest_kpis()
        print(f"MTTR: {kpis.get('mttr', 'N/A')} hours")
        print(f"MTBF: {kpis.get('mtbf', 'N/A')} hours")
        print(f"Failure Rate: {kpis.get('failure_rate', 'N/A')} failures/year")
        print()
        
        # Get spare parts
        print("Getting spare parts...")
        parts = sdk.inventory.get_parts(limit=5)
        print(f"Found {len(parts.get('items', []))} parts:")
        for part in parts.get('items', []):
            print(f"  Part ID: {part.get('partid', 'N/A')}, Name: {part.get('partname', 'N/A')}")
            print(f"    Stock: {part.get('stockquantity', 'N/A')}, Reorder Level: {part.get('reorderlevel', 'N/A')}")
        print()
        
        # Get assets
        print("Getting assets...")
        assets = sdk.assets.get_assets(limit=5)
        print(f"Found {len(assets.get('items', []))} assets:")
        for asset in assets.get('items', []):
            print(f"  Asset ID: {asset.get('assetid', 'N/A')}, Name: {asset.get('assetname', 'N/A')}")
            print(f"    Type: {asset.get('assettype', 'N/A')}, Status: {asset.get('status', 'N/A')}")
        print()
        
        # Get work orders
        print("Getting work orders...")
        work_orders = sdk.workorders.get_workorders(limit=5)
        print(f"Found {len(work_orders.get('items', []))} work orders:")
        for wo in work_orders.get('items', []):
            print(f"  WO ID: {wo.get('workorderid', 'N/A')}, Type: {wo.get('workordertype', 'N/A')}")
            print(f"    Status: {wo.get('status', 'N/A')}, Asset ID: {wo.get('assetid', 'N/A')}")
        print()
        
        # Get work order count
        print("Getting work order count...")
        count = sdk.workorders.get_workorders_count()
        print(f"Total work orders: {count.get('count', 0)}")
        
        # Get open work order count
        open_count = sdk.workorders.get_workorders_count(status="OPEN")
        print(f"Open work orders: {open_count.get('count', 0)}")
        print()
        
    except AuthenticationError as e:
        print(f"Authentication error: {str(e)}")
    except RateLimitError as e:
        print(f"Rate limit exceeded. Retry after {e.retry_after} seconds")
    except APIError as e:
        print(f"API error ({e.status_code}): {str(e)}")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")


if __name__ == "__main__":
    main()
