# Contextual Help System Design

This document outlines the design for the AssetKPI contextual help system, which will provide users with relevant assistance based on their current context in the application.

## Goals

- Provide context-sensitive help to users
- Reduce the learning curve for new users
- Decrease support requests for common tasks
- Improve overall user experience
- Support different levels of help detail

## Architecture

The contextual help system consists of the following components:

1. **Help Engine**: Core class that manages help content and context
2. **Help UI Components**: Visual elements for displaying help content
3. **Help Content**: JSON-based help content definitions
4. **Help Triggers**: Elements that activate help content

### Component Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                     Help System                              │
│                                                             │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────┐│
│  │ Help          │    │ Help          │    │ Help          ││
│  │ Engine        │◄───┤ Content       │    │ Triggers      ││
│  │               │    │               │    │               ││
│  └───────┬───────┘    └───────────────┘    └───────┬───────┘│
│          │                                         │        │
│          ▼                                         │        │
│  ┌───────────────┐                                 │        │
│  │ Help          │                                 │        │
│  │ UI Components │◄────────────────────────────────┘        │
│  │               │                                          │
│  └───────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
```

## Help Engine

The Help Engine is responsible for:

- Loading help content
- Managing help context
- Providing relevant help based on context
- Searching help content
- Tracking help usage

### Context Management

The Help Engine maintains the following context information:

- Current page/route
- Current UI element
- User role
- User preferences for help

## Help UI Components

The system includes the following UI components:

### Help Icon

- Small icon that indicates help is available
- Appears next to UI elements that have associated help
- Can be clicked or hovered to show help content

### Help Tooltip/Popover

- Displays brief help content
- Shows when hovering over a help icon
- Contains links to more detailed help

### Help Panel

- Sidebar panel that displays detailed help
- Can be toggled open/closed
- Shows context-sensitive help based on current page/element
- Includes search functionality

### Help Modal

- Full-screen modal for comprehensive help
- Contains detailed instructions, images, and videos
- Organized by topics and categories

## Help Content Structure

Help content is defined using a JSON structure:

```json
{
  "id": "asset-management",
  "title": "Asset Management",
  "description": "Managing assets in AssetKPI",
  "topics": [
    {
      "id": "asset-list",
      "title": "Asset List",
      "route": "/assets",
      "content": "The Asset List page shows all your assets and their current status.",
      "elements": [
        {
          "selector": ".add-asset-btn",
          "title": "Add Asset",
          "content": "Click this button to add a new asset to your inventory.",
          "type": "tooltip"
        },
        {
          "selector": ".asset-filter",
          "title": "Filter Assets",
          "content": "Use these filters to find specific assets based on status, type, or location.",
          "type": "tooltip"
        }
      ]
    }
  ]
}
```

### Content Properties

- **id**: Unique identifier for the help content
- **title**: Title of the help content
- **description**: Brief description of the help content
- **topics**: Array of help topics
  - **id**: Unique identifier for the topic
  - **title**: Title of the topic
  - **route**: Associated route/page
  - **content**: Main content for the topic
  - **elements**: Array of UI elements with help content
    - **selector**: CSS selector for the element
    - **title**: Title of the help content
    - **content**: Help content for the element
    - **type**: Type of help display (tooltip, panel, modal)

## Help Triggers

Help can be triggered in several ways:

### Hover Triggers

- Hovering over a help icon shows a tooltip with brief help
- Hovering over a UI element with help shows a small indicator

### Click Triggers

- Clicking a help icon opens more detailed help
- Clicking a "Help" button in the UI opens the help panel
- Clicking "Learn more" in a tooltip opens detailed help

### Keyboard Triggers

- Pressing F1 opens context-sensitive help
- Pressing Shift+? opens the help search

## Context Awareness

The help system determines context based on:

### Page Context

- Current route/URL
- Page title
- Page section

### Element Context

- Element ID or class
- Element type
- Element state (enabled, disabled, selected)

### User Context

- User role
- User experience level
- Previous help usage

## Help Content Management

Help content is managed through:

### Static Content

- JSON files for basic help content
- Loaded at application startup

### Dynamic Content

- API endpoints for retrieving help content
- Can be updated without application changes

### Content Versioning

- Help content is versioned to match application versions
- Users see help relevant to their application version

## User Experience

### Help Discoverability

- Help icons are visible but unobtrusive
- Help button is always accessible in the navigation
- First-time users receive a help system introduction

### Help Presentation

- Brief help is shown in tooltips
- Detailed help is shown in the help panel
- Comprehensive help is shown in modals
- Help includes text, images, and videos as appropriate

### Help Personalization

- Users can set their preferred help level (basic, detailed, advanced)
- Help system remembers previously viewed topics
- Users can bookmark help topics for quick access

## Technical Implementation

### Frontend

- JavaScript class for the Help Engine
- React components for UI elements
- Local storage for persisting preferences
- CSS for styling and animations

### Content Storage

- JSON files for static content
- Database for dynamic content
- Search index for quick content retrieval

## Success Metrics

- Reduction in support requests
- Increased feature adoption
- Time spent using help features
- User feedback ratings
- Help search success rate

## Future Enhancements

- AI-powered help recommendations
- User-contributed help content
- Help content analytics
- Interactive help walkthroughs
- Multi-language support
