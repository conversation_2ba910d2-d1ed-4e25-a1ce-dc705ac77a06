"""
Command-line interface for the AssetKPI SDK.
"""

import argparse
import json
import os
import sys
from typing import Dict, Any, Optional

from dotenv import load_dotenv

from .sdk import AssetKPISDK
from .exceptions import AssetKPIError


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="AssetKPI SDK CLI")
    
    # Authentication options
    auth_group = parser.add_argument_group("Authentication")
    auth_group.add_argument("--base-url", help="API base URL")
    auth_group.add_argument("--api-key", help="API key for authentication")
    auth_group.add_argument("--firebase-token", help="Firebase ID token for authentication")
    
    # Command options
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Inventory commands
    inventory_parser = subparsers.add_parser("inventory", help="Inventory commands")
    inventory_subparsers = inventory_parser.add_subparsers(dest="subcommand")
    
    # inventory summary
    inventory_summary_parser = inventory_subparsers.add_parser("summary", help="Get inventory summary")
    
    # inventory parts
    inventory_parts_parser = inventory_subparsers.add_parser("parts", help="Get spare parts")
    inventory_parts_parser.add_argument("--limit", type=int, default=10, help="Maximum number of items to return")
    inventory_parts_parser.add_argument("--offset", type=int, default=0, help="Number of items to skip")
    
    # inventory part
    inventory_part_parser = inventory_subparsers.add_parser("part", help="Get a specific spare part")
    inventory_part_parser.add_argument("part_id", type=int, help="ID of the spare part")
    
    # KPI commands
    kpi_parser = subparsers.add_parser("kpi", help="KPI commands")
    kpi_subparsers = kpi_parser.add_subparsers(dest="subcommand")
    
    # kpi latest
    kpi_latest_parser = kpi_subparsers.add_parser("latest", help="Get latest KPIs")
    
    # kpi history
    kpi_history_parser = kpi_subparsers.add_parser("history", help="Get KPI history")
    kpi_history_parser.add_argument("kpi_name", help="Name of the KPI")
    kpi_history_parser.add_argument("--start-date", help="Start date (YYYY-MM-DD)")
    kpi_history_parser.add_argument("--end-date", help="End date (YYYY-MM-DD)")
    kpi_history_parser.add_argument("--limit", type=int, default=100, help="Maximum number of data points")
    
    # Assets commands
    assets_parser = subparsers.add_parser("assets", help="Assets commands")
    assets_subparsers = assets_parser.add_subparsers(dest="subcommand")
    
    # assets list
    assets_list_parser = assets_subparsers.add_parser("list", help="Get assets")
    assets_list_parser.add_argument("--limit", type=int, default=10, help="Maximum number of items to return")
    assets_list_parser.add_argument("--offset", type=int, default=0, help="Number of items to skip")
    
    # assets get
    assets_get_parser = assets_subparsers.add_parser("get", help="Get a specific asset")
    assets_get_parser.add_argument("asset_id", type=int, help="ID of the asset")
    
    # Work Orders commands
    workorders_parser = subparsers.add_parser("workorders", help="Work Orders commands")
    workorders_subparsers = workorders_parser.add_subparsers(dest="subcommand")
    
    # workorders list
    workorders_list_parser = workorders_subparsers.add_parser("list", help="Get work orders")
    workorders_list_parser.add_argument("--limit", type=int, default=10, help="Maximum number of items to return")
    workorders_list_parser.add_argument("--offset", type=int, default=0, help="Number of items to skip")
    
    # workorders get
    workorders_get_parser = workorders_subparsers.add_parser("get", help="Get a specific work order")
    workorders_get_parser.add_argument("workorder_id", type=int, help="ID of the work order")
    
    # workorders count
    workorders_count_parser = workorders_subparsers.add_parser("count", help="Get work order count")
    workorders_count_parser.add_argument("--status", help="Filter by status")
    
    # Users commands
    users_parser = subparsers.add_parser("users", help="Users commands")
    users_subparsers = users_parser.add_subparsers(dest="subcommand")
    
    # users me
    users_me_parser = users_subparsers.add_parser("me", help="Get current user")
    
    # users list
    users_list_parser = users_subparsers.add_parser("list", help="Get users")
    users_list_parser.add_argument("--limit", type=int, default=10, help="Maximum number of items to return")
    users_list_parser.add_argument("--offset", type=int, default=0, help="Number of items to skip")
    
    # users get
    users_get_parser = users_subparsers.add_parser("get", help="Get a specific user")
    users_get_parser.add_argument("user_id", help="ID of the user")
    
    # Format options
    parser.add_argument("--format", choices=["json", "pretty"], default="pretty", help="Output format")
    
    return parser.parse_args()


def get_sdk(args) -> AssetKPISDK:
    """Create an SDK instance from command-line arguments or environment variables."""
    # Load environment variables
    load_dotenv()
    
    # Get base URL
    base_url = args.base_url or os.environ.get("ASSETKPI_BASE_URL")
    if not base_url:
        print("Error: Base URL is required. Provide it with --base-url or set ASSETKPI_BASE_URL environment variable.")
        sys.exit(1)
    
    # Get authentication
    api_key = args.api_key or os.environ.get("ASSETKPI_API_KEY")
    firebase_token = args.firebase_token or os.environ.get("ASSETKPI_FIREBASE_TOKEN")
    
    if not api_key and not firebase_token:
        print("Warning: No authentication provided. API calls may fail.")
    
    # Create SDK instance
    return AssetKPISDK(
        base_url=base_url,
        api_key=api_key,
        firebase_id_token=firebase_token,
    )


def format_output(data: Any, format_type: str) -> str:
    """Format output data."""
    if format_type == "json":
        return json.dumps(data)
    else:  # pretty
        return json.dumps(data, indent=2)


def main():
    """Main entry point for the CLI."""
    args = parse_args()
    
    if not args.command:
        print("Error: Command is required.")
        sys.exit(1)
    
    try:
        sdk = get_sdk(args)
        
        # Execute command
        if args.command == "inventory":
            if args.subcommand == "summary":
                result = sdk.inventory.get_inventory_summary()
            elif args.subcommand == "parts":
                result = sdk.inventory.get_parts(limit=args.limit, offset=args.offset)
            elif args.subcommand == "part":
                result = sdk.inventory.get_part(args.part_id)
            else:
                print(f"Error: Unknown inventory subcommand: {args.subcommand}")
                sys.exit(1)
        
        elif args.command == "kpi":
            if args.subcommand == "latest":
                result = sdk.kpi.get_latest_kpis()
            elif args.subcommand == "history":
                result = sdk.kpi.get_kpi_history(
                    args.kpi_name,
                    start_date=args.start_date,
                    end_date=args.end_date,
                    limit=args.limit,
                )
            else:
                print(f"Error: Unknown KPI subcommand: {args.subcommand}")
                sys.exit(1)
        
        elif args.command == "assets":
            if args.subcommand == "list":
                result = sdk.assets.get_assets(limit=args.limit, offset=args.offset)
            elif args.subcommand == "get":
                result = sdk.assets.get_asset(args.asset_id)
            else:
                print(f"Error: Unknown assets subcommand: {args.subcommand}")
                sys.exit(1)
        
        elif args.command == "workorders":
            if args.subcommand == "list":
                result = sdk.workorders.get_workorders(limit=args.limit, offset=args.offset)
            elif args.subcommand == "get":
                result = sdk.workorders.get_workorder(args.workorder_id)
            elif args.subcommand == "count":
                result = sdk.workorders.get_workorders_count(status=args.status)
            else:
                print(f"Error: Unknown workorders subcommand: {args.subcommand}")
                sys.exit(1)
        
        elif args.command == "users":
            if args.subcommand == "me":
                result = sdk.users.get_current_user()
            elif args.subcommand == "list":
                result = sdk.users.get_users(limit=args.limit, offset=args.offset)
            elif args.subcommand == "get":
                result = sdk.users.get_user(args.user_id)
            else:
                print(f"Error: Unknown users subcommand: {args.subcommand}")
                sys.exit(1)
        
        else:
            print(f"Error: Unknown command: {args.command}")
            sys.exit(1)
        
        # Print result
        print(format_output(result, args.format))
    
    except AssetKPIError as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
