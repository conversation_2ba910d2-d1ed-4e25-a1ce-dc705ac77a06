import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def enrich_assets_data():
    """Add more comprehensive asset data to the database."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # First, get existing data to avoid duplicates
        cursor.execute("SELECT assetid, assetname FROM assets")
        existing_assets = {row[1]: row[0] for row in cursor.fetchall()}
        
        # Get locations, systems, and categories for reference
        cursor.execute("SELECT location_id, location_name FROM asset_locations")
        locations = cursor.fetchall()
        
        cursor.execute("SELECT system_id, system_name FROM asset_systems")
        systems = cursor.fetchall()
        
        cursor.execute("SELECT category_id, category_name FROM asset_categories")
        categories = cursor.fetchall()
        
        # Sample asset data
        new_assets = [
            # Production Equipment
            ('Injection Molding Machine 1', 'Production', 'IMM-2023-001', 'Production Floor', 'Active', 'Critical', '2023-01-15', '2023-06-15', 'Operational', 'Haitian', 'MA5300II'),
            ('Injection Molding Machine 2', 'Production', 'IMM-2023-002', 'Production Floor', 'Active', 'Critical', '2023-01-15', '2023-06-15', 'Operational', 'Haitian', 'MA5300II'),
            ('CNC Milling Machine 1', 'Production', 'CNC-2022-001', 'Machining Area', 'Active', 'Critical', '2022-08-10', '2023-05-20', 'Operational', 'Haas', 'VF-2'),
            ('CNC Milling Machine 2', 'Production', 'CNC-2022-002', 'Machining Area', 'Active', 'Critical', '2022-08-10', '2023-05-20', 'Operational', 'Haas', 'VF-2'),
            ('CNC Lathe 1', 'Production', 'CNCL-2022-001', 'Machining Area', 'Active', 'Critical', '2022-09-05', '2023-04-15', 'Operational', 'Haas', 'ST-20'),
            ('CNC Lathe 2', 'Production', 'CNCL-2022-002', 'Machining Area', 'Active', 'Critical', '2022-09-05', '2023-04-15', 'Operational', 'Haas', 'ST-20'),
            ('Assembly Line A', 'Production', 'AL-2021-001', 'Assembly Area', 'Active', 'Critical', '2021-06-10', '2023-03-10', 'Operational', 'In-house', 'Custom'),
            ('Assembly Line B', 'Production', 'AL-2021-002', 'Assembly Area', 'Active', 'Critical', '2021-06-10', '2023-03-10', 'Operational', 'In-house', 'Custom'),
            ('Packaging Machine 1', 'Production', 'PKG-2022-001', 'Packaging Area', 'Active', 'High', '2022-11-15', '2023-05-25', 'Operational', 'Bosch', 'CPS-1500'),
            ('Packaging Machine 2', 'Production', 'PKG-2022-002', 'Packaging Area', 'Active', 'High', '2022-11-15', '2023-05-25', 'Operational', 'Bosch', 'CPS-1500'),
            
            # HVAC Equipment
            ('Air Handling Unit 1', 'HVAC', 'AHU-2020-001', 'Mechanical Room', 'Active', 'High', '2020-03-20', '2023-02-15', 'Operational', 'Carrier', 'AHU-500'),
            ('Air Handling Unit 2', 'HVAC', 'AHU-2020-002', 'Mechanical Room', 'Active', 'High', '2020-03-20', '2023-02-15', 'Operational', 'Carrier', 'AHU-500'),
            ('Chiller 1', 'HVAC', 'CH-2020-001', 'Mechanical Room', 'Active', 'High', '2020-03-20', '2023-02-15', 'Operational', 'Trane', 'RTAC-250'),
            ('Chiller 2', 'HVAC', 'CH-2020-002', 'Mechanical Room', 'Active', 'High', '2020-03-20', '2023-02-15', 'Operational', 'Trane', 'RTAC-250'),
            ('Cooling Tower 1', 'HVAC', 'CT-2020-001', 'Roof', 'Active', 'High', '2020-03-20', '2023-02-15', 'Operational', 'BAC', 'CT-500'),
            ('Cooling Tower 2', 'HVAC', 'CT-2020-002', 'Roof', 'Active', 'High', '2020-03-20', '2023-02-15', 'Operational', 'BAC', 'CT-500'),
            
            # Electrical Equipment
            ('Main Transformer', 'Electrical', 'XFMR-2019-001', 'Electrical Room', 'Active', 'Critical', '2019-01-10', '2023-01-05', 'Operational', 'ABB', 'T-1000'),
            ('Backup Generator', 'Electrical', 'GEN-2019-001', 'Generator Room', 'Active', 'Critical', '2019-01-15', '2023-01-10', 'Operational', 'Caterpillar', 'C15'),
            ('Main Distribution Panel', 'Electrical', 'MDP-2019-001', 'Electrical Room', 'Active', 'Critical', '2019-01-20', '2023-01-15', 'Operational', 'Square D', 'MDP-1000'),
            ('UPS System', 'Electrical', 'UPS-2019-001', 'Server Room', 'Active', 'Critical', '2019-02-01', '2023-01-20', 'Operational', 'APC', 'Symmetra PX'),
            
            # Compressed Air System
            ('Air Compressor 1', 'Utility', 'AC-2021-001', 'Compressor Room', 'Active', 'High', '2021-05-10', '2023-03-05', 'Operational', 'Atlas Copco', 'GA55'),
            ('Air Compressor 2', 'Utility', 'AC-2021-002', 'Compressor Room', 'Active', 'High', '2021-05-10', '2023-03-05', 'Operational', 'Atlas Copco', 'GA55'),
            ('Air Dryer', 'Utility', 'AD-2021-001', 'Compressor Room', 'Active', 'High', '2021-05-15', '2023-03-10', 'Operational', 'Atlas Copco', 'FD-300'),
            ('Air Receiver Tank', 'Utility', 'ART-2021-001', 'Compressor Room', 'Active', 'High', '2021-05-20', '2023-03-15', 'Operational', 'Manchester', 'AR-1000'),
            
            # Material Handling
            ('Forklift 1', 'Material Handling', 'FL-2022-001', 'Warehouse', 'Active', 'Medium', '2022-02-10', '2023-01-25', 'Operational', 'Toyota', '8FGU25'),
            ('Forklift 2', 'Material Handling', 'FL-2022-002', 'Warehouse', 'Active', 'Medium', '2022-02-10', '2023-01-25', 'Operational', 'Toyota', '8FGU25'),
            ('Pallet Jack 1', 'Material Handling', 'PJ-2022-001', 'Warehouse', 'Active', 'Low', '2022-02-15', '2023-01-30', 'Operational', 'Crown', 'PTH50'),
            ('Pallet Jack 2', 'Material Handling', 'PJ-2022-002', 'Warehouse', 'Active', 'Low', '2022-02-15', '2023-01-30', 'Operational', 'Crown', 'PTH50'),
            ('Overhead Crane', 'Material Handling', 'OC-2021-001', 'Production Floor', 'Active', 'High', '2021-07-10', '2023-04-05', 'Operational', 'Konecranes', 'CXT'),
            
            # Water Treatment
            ('Water Softener', 'Utility', 'WS-2021-001', 'Utility Room', 'Active', 'Medium', '2021-08-15', '2023-04-20', 'Operational', 'Culligan', 'HE-1.5'),
            ('Reverse Osmosis System', 'Utility', 'RO-2021-001', 'Utility Room', 'Active', 'Medium', '2021-08-20', '2023-04-25', 'Operational', 'Culligan', 'G2-R200'),
            ('Deionization System', 'Utility', 'DI-2021-001', 'Utility Room', 'Active', 'Medium', '2021-08-25', '2023-04-30', 'Operational', 'Culligan', 'MB-300'),
            
            # Quality Control
            ('Coordinate Measuring Machine', 'Quality', 'CMM-2022-001', 'QC Lab', 'Active', 'High', '2022-03-10', '2023-02-05', 'Operational', 'Zeiss', 'CONTURA'),
            ('Optical Comparator', 'Quality', 'OC-2022-001', 'QC Lab', 'Active', 'Medium', '2022-03-15', '2023-02-10', 'Operational', 'Starrett', 'HB400'),
            ('Surface Roughness Tester', 'Quality', 'SRT-2022-001', 'QC Lab', 'Active', 'Medium', '2022-03-20', '2023-02-15', 'Operational', 'Mitutoyo', 'SJ-410'),
            ('Hardness Tester', 'Quality', 'HT-2022-001', 'QC Lab', 'Active', 'Medium', '2022-03-25', '2023-02-20', 'Operational', 'Wilson', 'B2000'),
            
            # IT Equipment
            ('Server Rack 1', 'IT', 'SR-2021-001', 'Server Room', 'Active', 'High', '2021-09-10', '2023-05-05', 'Operational', 'Dell', 'PowerEdge'),
            ('Server Rack 2', 'IT', 'SR-2021-002', 'Server Room', 'Active', 'High', '2021-09-10', '2023-05-05', 'Operational', 'Dell', 'PowerEdge'),
            ('Network Switch 1', 'IT', 'NS-2021-001', 'Server Room', 'Active', 'High', '2021-09-15', '2023-05-10', 'Operational', 'Cisco', 'Catalyst 9300'),
            ('Network Switch 2', 'IT', 'NS-2021-002', 'Server Room', 'Active', 'High', '2021-09-15', '2023-05-10', 'Operational', 'Cisco', 'Catalyst 9300'),
            ('Firewall', 'IT', 'FW-2021-001', 'Server Room', 'Active', 'Critical', '2021-09-20', '2023-05-15', 'Operational', 'Palo Alto', 'PA-3260')
        ]
        
        print(f"Adding {len(new_assets)} new assets...")
        
        # Insert new assets
        assets_added = 0
        for asset in new_assets:
            asset_name = asset[0]
            
            # Skip if asset already exists
            if asset_name in existing_assets:
                print(f"  - Skipping existing asset: {asset_name}")
                continue
            
            # Insert the new asset
            cursor.execute(
                """
                INSERT INTO assets 
                (assetname, assettype, serialnumber, location, status, criticality, 
                 purchasedate, lastservicedate, lifecyclestage, manufacturer, model, createdat)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING assetid
                """,
                (*asset, datetime.now())
            )
            asset_id = cursor.fetchone()[0]
            assets_added += 1
            
            # Assign random location, system, and category
            location_id = random.choice(locations)[0] if locations else None
            system_id = random.choice(systems)[0] if systems else None
            category_id = random.choice(categories)[0] if categories else None
            
            # Update the asset with hierarchy and classification
            cursor.execute(
                """
                UPDATE assets
                SET location_id = %s, system_id = %s, category_id = %s
                WHERE assetid = %s
                """,
                (location_id, system_id, category_id, asset_id)
            )
            
            # Add specifications for this asset
            add_asset_specifications(cursor, asset_id, asset[1])
            
            # Add warranty for this asset (80% chance)
            if random.random() < 0.8:
                add_asset_warranty(cursor, asset_id, asset[9])
            
            print(f"  - Added asset: {asset_name} (ID: {asset_id})")
        
        # Commit the changes
        conn.commit()
        print(f"Successfully added {assets_added} new assets!")
        
    except Exception as e:
        print(f"Error enriching assets data: {e}")
        import traceback
        traceback.print_exc()
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def add_asset_specifications(cursor, asset_id, asset_type):
    """Add specifications for an asset based on its type."""
    specs = []
    
    # Common specifications for all assets
    specs.append(('Manufacturer', 'Text', ''))
    specs.append(('Model', 'Text', ''))
    specs.append(('Serial Number', 'Text', ''))
    
    # Type-specific specifications
    if 'Production' in asset_type:
        if 'Injection' in asset_type:
            specs.append(('Clamping Force', str(random.randint(50, 500)), 'tons'))
            specs.append(('Shot Size', str(random.randint(50, 500)), 'g'))
            specs.append(('Screw Diameter', str(random.randint(25, 100)), 'mm'))
        elif 'CNC' in asset_type:
            specs.append(('Axis', str(random.randint(3, 5)), ''))
            specs.append(('Table Size', f"{random.randint(500, 2000)}x{random.randint(500, 2000)}", 'mm'))
            specs.append(('Spindle Speed', str(random.randint(8000, 15000)), 'RPM'))
        elif 'Assembly' in asset_type:
            specs.append(('Length', str(random.randint(10, 50)), 'm'))
            specs.append(('Capacity', str(random.randint(100, 1000)), 'units/hour'))
            specs.append(('Stations', str(random.randint(5, 20)), ''))
        elif 'Packaging' in asset_type:
            specs.append(('Speed', str(random.randint(20, 100)), 'units/min'))
            specs.append(('Package Size', f"{random.randint(100, 500)}x{random.randint(100, 500)}x{random.randint(100, 500)}", 'mm'))
            specs.append(('Power Consumption', str(random.randint(5, 20)), 'kW'))
    
    elif 'HVAC' in asset_type:
        if 'Air Handling' in asset_type:
            specs.append(('Airflow', str(random.randint(5000, 50000)), 'CFM'))
            specs.append(('Static Pressure', str(random.uniform(1.0, 5.0)), 'inWC'))
            specs.append(('Motor Power', str(random.randint(5, 50)), 'HP'))
        elif 'Chiller' in asset_type:
            specs.append(('Cooling Capacity', str(random.randint(50, 500)), 'tons'))
            specs.append(('Refrigerant Type', 'R-' + str(random.choice([134, 410, 407])) + random.choice(['A', 'C', '']), ''))
            specs.append(('Power Consumption', str(random.randint(50, 500)), 'kW'))
        elif 'Cooling Tower' in asset_type:
            specs.append(('Cooling Capacity', str(random.randint(50, 500)), 'tons'))
            specs.append(('Airflow', str(random.randint(10000, 100000)), 'CFM'))
            specs.append(('Water Flow', str(random.randint(100, 1000)), 'GPM'))
    
    elif 'Electrical' in asset_type:
        if 'Transformer' in asset_type:
            specs.append(('Capacity', str(random.randint(500, 5000)), 'kVA'))
            specs.append(('Primary Voltage', str(random.choice([4160, 13800, 34500])), 'V'))
            specs.append(('Secondary Voltage', str(random.choice([208, 480, 600])), 'V'))
        elif 'Generator' in asset_type:
            specs.append(('Power Output', str(random.randint(100, 2000)), 'kW'))
            specs.append(('Voltage', str(random.choice([208, 480, 600])), 'V'))
            specs.append(('Fuel Type', random.choice(['Diesel', 'Natural Gas', 'Propane']), ''))
        elif 'UPS' in asset_type:
            specs.append(('Capacity', str(random.randint(10, 200)), 'kVA'))
            specs.append(('Runtime', str(random.randint(10, 60)), 'minutes'))
            specs.append(('Battery Type', random.choice(['Lead Acid', 'Lithium Ion']), ''))
    
    elif 'Utility' in asset_type:
        if 'Compressor' in asset_type:
            specs.append(('Capacity', str(random.randint(50, 500)), 'CFM'))
            specs.append(('Pressure', str(random.randint(100, 150)), 'PSI'))
            specs.append(('Motor Power', str(random.randint(10, 100)), 'HP'))
        elif 'Water' in asset_type or 'Softener' in asset_type or 'Osmosis' in asset_type:
            specs.append(('Flow Rate', str(random.randint(10, 100)), 'GPM'))
            specs.append(('Capacity', str(random.randint(1000, 10000)), 'gallons/day'))
            specs.append(('Operating Pressure', str(random.randint(30, 80)), 'PSI'))
    
    elif 'Material Handling' in asset_type:
        if 'Forklift' in asset_type:
            specs.append(('Capacity', str(random.randint(3000, 10000)), 'lbs'))
            specs.append(('Lift Height', str(random.randint(10, 20)), 'ft'))
            specs.append(('Power Type', random.choice(['Electric', 'Propane', 'Diesel']), ''))
        elif 'Crane' in asset_type:
            specs.append(('Capacity', str(random.randint(1, 20)), 'tons'))
            specs.append(('Span', str(random.randint(20, 100)), 'ft'))
            specs.append(('Lift Height', str(random.randint(10, 50)), 'ft'))
    
    elif 'Quality' in asset_type:
        specs.append(('Accuracy', str(random.uniform(0.001, 0.01)), 'mm'))
        specs.append(('Measurement Range', str(random.randint(100, 1000)), 'mm'))
        specs.append(('Resolution', str(random.uniform(0.0001, 0.001)), 'mm'))
    
    elif 'IT' in asset_type:
        if 'Server' in asset_type:
            specs.append(('Processors', str(random.randint(2, 8)), ''))
            specs.append(('RAM', str(random.randint(64, 1024)), 'GB'))
            specs.append(('Storage', str(random.randint(1, 100)), 'TB'))
        elif 'Network' in asset_type or 'Switch' in asset_type:
            specs.append(('Ports', str(random.randint(24, 48)), ''))
            specs.append(('Speed', str(random.choice([1, 10, 40, 100])), 'Gbps'))
            specs.append(('Power Consumption', str(random.randint(100, 500)), 'W'))
    
    # Insert specifications
    for spec_name, spec_value, spec_unit in specs:
        cursor.execute(
            """
            INSERT INTO asset_specifications 
            (asset_id, spec_name, spec_value, spec_unit)
            VALUES (%s, %s, %s, %s)
            """,
            (asset_id, spec_name, spec_value, spec_unit)
        )

def add_asset_warranty(cursor, asset_id, manufacturer):
    """Add warranty information for an asset."""
    # Warranty types
    warranty_types = ['Standard', 'Extended', 'Limited', 'Comprehensive', 'Parts Only']
    
    # Generate random dates
    today = datetime.now().date()
    purchase_date = today - timedelta(days=random.randint(365, 365*3))
    warranty_length = random.choice([1, 2, 3, 5])
    start_date = purchase_date
    end_date = start_date + timedelta(days=365*warranty_length)
    
    warranty_type = random.choice(warranty_types)
    provider = manufacturer if random.random() < 0.7 else random.choice(['Extended Warranty Inc.', 'Industrial Coverage Co.', 'Equipment Protection Services'])
    
    coverage_details = f"{warranty_type} warranty covering "
    if warranty_type == 'Comprehensive':
        coverage_details += "parts, labor, and travel for all repairs."
    elif warranty_type == 'Parts Only':
        coverage_details += "replacement parts only. Labor and travel not included."
    else:
        coverage_details += "parts and labor for manufacturing defects. Travel may be extra."
    
    contact_info = f"Provider: {provider}, Phone: 555-{random.randint(100, 999)}-{random.randint(1000, 9999)}, Email: support@{provider.lower().replace(' ', '')}.com"
    
    cursor.execute(
        """
        INSERT INTO asset_warranties 
        (asset_id, warranty_type, provider, start_date, end_date, coverage_details, contact_info)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """,
        (asset_id, warranty_type, provider, start_date, end_date, coverage_details, contact_info)
    )

if __name__ == "__main__":
    enrich_assets_data()
