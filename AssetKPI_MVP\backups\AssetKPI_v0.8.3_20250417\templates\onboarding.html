{% extends "layout.html" %}

{% block title %}Welcome to AssetKPI{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div id="onboarding-wizard-container"></div>
</div>

<!-- Step Templates -->
<template id="welcome-template">
    <div class="welcome-step">
        <h2>Welcome to AssetKPI</h2>
        <p class="lead">Your comprehensive solution for asset management and performance optimization.</p>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">What is AssetKPI?</h5>
                        <p class="card-text">
                            AssetKPI is a powerful platform that helps you manage your assets, track performance metrics,
                            optimize maintenance, and make data-driven decisions to improve operational efficiency.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Getting Started</h5>
                        <p class="card-text">
                            This wizard will guide you through the initial setup process. It will take about 5-10 minutes
                            to complete. You can save your progress and return later if needed.
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info mt-4">
            <i class="fas fa-info-circle"></i>
            Already familiar with AssetKPI? You can skip this wizard and go directly to the dashboard.
        </div>
    </div>
</template>

<template id="profile-template">
    <div class="profile-step">
        <h2>Profile Setup</h2>
        <p class="lead">Tell us a bit about yourself so we can personalize your experience.</p>
        
        <div class="error-message alert alert-danger" style="display: none;"></div>
        
        <form class="mt-4">
            <div class="mb-3">
                <label for="fullName" class="form-label">Full Name</label>
                <input type="text" class="form-control" id="fullName" name="fullName" placeholder="Enter your full name">
            </div>
            
            <div class="mb-3">
                <label for="role" class="form-label">Role</label>
                <select class="form-select" id="role" name="role">
                    <option value="">Select your role</option>
                    <option value="manager">Manager</option>
                    <option value="engineer">Engineer</option>
                    <option value="technician">Technician</option>
                    <option value="operator">Operator</option>
                    <option value="analyst">Analyst</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="mb-3">
                <label for="department" class="form-label">Department</label>
                <input type="text" class="form-control" id="department" name="department" placeholder="Enter your department">
            </div>
            
            <div class="mb-3">
                <label class="form-label">Notification Preferences</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="emailNotifications" name="notifications" value="email" checked>
                    <label class="form-check-label" for="emailNotifications">
                        Email Notifications
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="browserNotifications" name="notifications" value="browser">
                    <label class="form-check-label" for="browserNotifications">
                        Browser Notifications
                    </label>
                </div>
            </div>
        </form>
    </div>
</template>

<template id="organization-template">
    <div class="organization-step">
        <h2>Organization Setup</h2>
        <p class="lead">Tell us about your organization to help us tailor AssetKPI to your needs.</p>
        
        <div class="error-message alert alert-danger" style="display: none;"></div>
        
        <form class="mt-4">
            <div class="mb-3">
                <label for="orgName" class="form-label">Organization Name</label>
                <input type="text" class="form-control" id="orgName" name="orgName" placeholder="Enter your organization name">
            </div>
            
            <div class="mb-3">
                <label for="industry" class="form-label">Industry</label>
                <select class="form-select" id="industry" name="industry">
                    <option value="">Select your industry</option>
                    <option value="manufacturing">Manufacturing</option>
                    <option value="energy">Energy & Utilities</option>
                    <option value="oil_gas">Oil & Gas</option>
                    <option value="transportation">Transportation</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="construction">Construction</option>
                    <option value="mining">Mining</option>
                    <option value="agriculture">Agriculture</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="assetCount" class="form-label">Approximate Number of Assets</label>
                        <select class="form-select" id="assetCount" name="assetCount">
                            <option value="">Select range</option>
                            <option value="1-10">1-10</option>
                            <option value="11-50">11-50</option>
                            <option value="51-100">51-100</option>
                            <option value="101-500">101-500</option>
                            <option value="501-1000">501-1000</option>
                            <option value="1000+">1000+</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="userCount" class="form-label">Approximate Number of Users</label>
                        <select class="form-select" id="userCount" name="userCount">
                            <option value="">Select range</option>
                            <option value="1-5">1-5</option>
                            <option value="6-20">6-20</option>
                            <option value="21-50">21-50</option>
                            <option value="51-100">51-100</option>
                            <option value="101-500">101-500</option>
                            <option value="500+">500+</option>
                        </select>
                    </div>
                </div>
            </div>
        </form>
    </div>
</template>

<template id="features-template">
    <div class="features-step">
        <h2>Feature Selection</h2>
        <p class="lead">Select the key features you want to focus on initially.</p>
        
        <div class="error-message alert alert-danger" style="display: none;"></div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="assetManagement" name="features" value="asset_management" checked>
                            <label class="form-check-label" for="assetManagement">
                                <h5>Asset Management</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Track and manage your assets, including location, status, and maintenance history.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="inventoryManagement" name="features" value="inventory_management">
                            <label class="form-check-label" for="inventoryManagement">
                                <h5>Inventory Management</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Manage spare parts inventory, track stock levels, and optimize reorder points.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="workOrders" name="features" value="work_orders">
                            <label class="form-check-label" for="workOrders">
                                <h5>Work Orders</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Create and manage work orders for maintenance, repairs, and other tasks.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="kpiTracking" name="features" value="kpi_tracking">
                            <label class="form-check-label" for="kpiTracking">
                                <h5>KPI Tracking</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Track key performance indicators such as MTTR, MTBF, and OEE.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<template id="data-import-template">
    <div class="data-import-step">
        <h2>Data Import Options</h2>
        <p class="lead">Choose how you want to import your existing data into AssetKPI.</p>
        
        <div class="mt-4">
            <div class="form-check mb-3">
                <input class="form-check-input" type="radio" name="importMethod" id="importNone" value="none" checked>
                <label class="form-check-label" for="importNone">
                    <strong>Start from scratch</strong> - I'll add data manually
                </label>
            </div>
            
            <div class="form-check mb-3">
                <input class="form-check-input" type="radio" name="importMethod" id="importCsv" value="csv">
                <label class="form-check-label" for="importCsv">
                    <strong>Import from CSV files</strong> - Upload data from spreadsheets
                </label>
            </div>
            
            <div class="form-check mb-3">
                <input class="form-check-input" type="radio" name="importMethod" id="importApi" value="api">
                <label class="form-check-label" for="importApi">
                    <strong>Import via API</strong> - Connect to your existing systems
                </label>
            </div>
            
            <div class="form-check mb-3">
                <input class="form-check-input" type="radio" name="importMethod" id="importLater" value="later">
                <label class="form-check-label" for="importLater">
                    <strong>Import later</strong> - I'll set this up later
                </label>
            </div>
        </div>
        
        <div id="csvImportOptions" class="mt-4" style="display: none;">
            <h5>CSV Import Options</h5>
            <p>You can download sample CSV templates for each data type:</p>
            <div class="row">
                <div class="col-md-3">
                    <a href="/static/templates/assets_template.csv" class="btn btn-outline-primary btn-sm mb-2">
                        <i class="fas fa-download"></i> Assets Template
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/static/templates/inventory_template.csv" class="btn btn-outline-primary btn-sm mb-2">
                        <i class="fas fa-download"></i> Inventory Template
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/static/templates/workorders_template.csv" class="btn btn-outline-primary btn-sm mb-2">
                        <i class="fas fa-download"></i> Work Orders Template
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="/static/templates/kpi_template.csv" class="btn btn-outline-primary btn-sm mb-2">
                        <i class="fas fa-download"></i> KPI Data Template
                    </a>
                </div>
            </div>
            <p class="mt-3">You'll be able to upload your CSV files after completing the onboarding process.</p>
        </div>
        
        <div id="apiImportOptions" class="mt-4" style="display: none;">
            <h5>API Import Options</h5>
            <p>You can connect AssetKPI to your existing systems using our API:</p>
            <ul>
                <li>ERP Systems (SAP, Oracle, Microsoft Dynamics)</li>
                <li>CMMS Systems (IBM Maximo, Infor EAM)</li>
                <li>IoT Platforms (AWS IoT, Azure IoT)</li>
                <li>Custom Systems (via REST API)</li>
            </ul>
            <p>You'll be guided through the API setup process after completing the onboarding.</p>
        </div>
    </div>
</template>

<template id="dashboard-template">
    <div class="dashboard-step">
        <h2>Dashboard Customization</h2>
        <p class="lead">Select the widgets you want to see on your dashboard.</p>
        
        <div class="error-message alert alert-danger" style="display: none;"></div>
        
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="assetSummary" name="widgets" value="asset_summary" checked>
                            <label class="form-check-label" for="assetSummary">
                                <h5>Asset Summary</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Overview of your assets by status, type, and location.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="kpiOverview" name="widgets" value="kpi_overview" checked>
                            <label class="form-check-label" for="kpiOverview">
                                <h5>KPI Overview</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Key performance indicators for your assets.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="recentWorkOrders" name="widgets" value="recent_work_orders">
                            <label class="form-check-label" for="recentWorkOrders">
                                <h5>Recent Work Orders</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            List of recent and upcoming work orders.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="inventoryStatus" name="widgets" value="inventory_status">
                            <label class="form-check-label" for="inventoryStatus">
                                <h5>Inventory Status</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Overview of inventory levels and reorder alerts.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="maintenanceCalendar" name="widgets" value="maintenance_calendar">
                            <label class="form-check-label" for="maintenanceCalendar">
                                <h5>Maintenance Calendar</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Calendar view of scheduled maintenance.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="assetPerformance" name="widgets" value="asset_performance">
                            <label class="form-check-label" for="assetPerformance">
                                <h5>Asset Performance</h5>
                            </label>
                        </div>
                        <p class="card-text">
                            Performance metrics for your assets.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<template id="completion-template">
    <div class="completion-step">
        <div class="text-center mb-4">
            <i class="fas fa-check-circle text-success" style="font-size: 64px;"></i>
            <h2 class="mt-3">Setup Complete!</h2>
            <p class="lead">You're all set to start using AssetKPI.</p>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6 offset-md-3">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Next Steps</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-tachometer-alt me-2"></i> Explore your personalized dashboard
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-cogs me-2"></i> Add your first asset
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-clipboard-list me-2"></i> Create a work order
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-book me-2"></i> Check out the tutorials
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <div class="form-check d-inline-block">
                <input class="form-check-input" type="checkbox" id="showTutorial" name="showTutorial" value="yes" checked>
                <label class="form-check-label" for="showTutorial">
                    Show me a quick tutorial
                </label>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block scripts %}
<script type="module">
    import OnboardingWizard from '/static/js/components/onboarding/OnboardingWizard.js';
    
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the onboarding wizard
        const wizard = new OnboardingWizard({
            containerId: 'onboarding-wizard-container',
            canSkip: true,
            onComplete: function(data) {
                // Check if user wants to see tutorial
                if (data.completion && data.completion.showTutorial === 'yes') {
                    window.location.href = '/tutorial';
                } else {
                    window.location.href = '/dashboard';
                }
            }
        });
        
        wizard.init();
        
        // Add event listeners for data import method selection
        const importMethodRadios = document.querySelectorAll('input[name="importMethod"]');
        importMethodRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                document.getElementById('csvImportOptions').style.display = this.value === 'csv' ? 'block' : 'none';
                document.getElementById('apiImportOptions').style.display = this.value === 'api' ? 'block' : 'none';
            });
        });
    });
</script>
{% endblock %}
