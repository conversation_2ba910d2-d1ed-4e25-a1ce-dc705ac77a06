{% extends "layout.html" %}

{% block title %}Usage Analytics{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', path='/css/analytics.css') }}">
<style>
    /* Additional inline styles if needed */
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title">Usage Analytics</h1>
            <p class="lead">Track and analyze user activity and feature usage across the application.</p>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form id="analytics-filter-form" class="row g-3">
                        <div class="col-md-3">
                            <label for="date-range" class="form-label">Date Range</label>
                            <select class="form-select" id="date-range">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                        <div class="col-md-3 date-picker-container" style="display: none;">
                            <label for="start-date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start-date">
                        </div>
                        <div class="col-md-3 date-picker-container" style="display: none;">
                            <label for="end-date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end-date">
                        </div>
                        <div class="col-md-3">
                            <label for="event-type" class="form-label">Event Type</label>
                            <select class="form-select" id="event-type">
                                <option value="all" selected>All Events</option>
                                <option value="PAGE_VIEW">Page Views</option>
                                <option value="FEATURE_USE">Feature Usage</option>
                                <option value="API_CALL">API Calls</option>
                                <option value="REPORT_GENERATED">Reports Generated</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="user-filter" class="form-label">User</label>
                            <select class="form-select" id="user-filter">
                                <option value="all" selected>All Users</option>
                                <!-- User options will be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">Apply Filters</button>
                            <button type="button" class="btn btn-secondary" id="reset-filters">Reset</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Total Activities</h5>
                    <h2 class="display-4" id="total-activities">0</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Unique Users</h5>
                    <h2 class="display-4" id="unique-users">0</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Anonymous Sessions</h5>
                    <h2 class="display-4" id="anonymous-sessions">0</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Avg. Activities Per User</h5>
                    <h2 class="display-4" id="avg-activities">0</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Activity Over Time</h5>
                </div>
                <div class="card-body">
                    <canvas id="activity-timeline-chart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Event Type Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="event-type-chart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Pages and Features -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Top Pages Viewed</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Page</th>
                                    <th>Views</th>
                                    <th>% of Total</th>
                                </tr>
                            </thead>
                            <tbody id="top-pages-table">
                                <!-- Will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Top Features Used</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Feature</th>
                                    <th>Uses</th>
                                    <th>% of Total</th>
                                </tr>
                            </thead>
                            <tbody id="top-features-table">
                                <!-- Will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Log -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Activity Log</h5>
                    <button class="btn btn-sm btn-outline-secondary" id="export-log">Export to CSV</button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>User</th>
                                    <th>Event Type</th>
                                    <th>Details</th>
                                    <th>Session ID</th>
                                </tr>
                            </thead>
                            <tbody id="activity-log-table">
                                <!-- Will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span id="showing-entries">Showing 0 of 0 entries</span>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" id="load-more">Load More</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment"></script>
<script>
    // Global variables
    let activityTimelineChart = null;
    let eventTypeChart = null;
    let currentOffset = 0;
    const pageSize = 20;
    let allUsers = [];

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        // Set default dates for the last 30 days
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);

        document.getElementById('start-date').valueAsDate = thirtyDaysAgo;
        document.getElementById('end-date').valueAsDate = today;

        // Show/hide date pickers based on date range selection
        document.getElementById('date-range').addEventListener('change', function() {
            const datePickers = document.querySelectorAll('.date-picker-container');
            if (this.value === 'custom') {
                datePickers.forEach(el => el.style.display = 'block');
            } else {
                datePickers.forEach(el => el.style.display = 'none');
            }
        });

        // Handle filter form submission
        document.getElementById('analytics-filter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            currentOffset = 0;
            loadAnalyticsSummary();
            loadActivityLog();
        });

        // Handle reset filters button
        document.getElementById('reset-filters').addEventListener('click', function() {
            document.getElementById('date-range').value = '30';
            document.getElementById('event-type').value = 'all';
            document.getElementById('user-filter').value = 'all';
            document.querySelectorAll('.date-picker-container').forEach(el => el.style.display = 'none');

            // Reset to default dates
            document.getElementById('start-date').valueAsDate = thirtyDaysAgo;
            document.getElementById('end-date').valueAsDate = today;

            currentOffset = 0;
            loadAnalyticsSummary();
            loadActivityLog();
        });

        // Handle load more button
        document.getElementById('load-more').addEventListener('click', function() {
            currentOffset += pageSize;
            loadActivityLog(true); // Append to existing data
        });

        // Handle export button
        document.getElementById('export-log').addEventListener('click', exportActivityLog);

        // Load users for the filter dropdown
        loadUsers();

        // Initial data load
        loadAnalyticsSummary();
        loadActivityLog();
    });

    // Load users for the filter dropdown
    function loadUsers() {
        fetch('/api/users')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch users');
                }
                return response.json();
            })
            .then(data => {
                allUsers = data;
                const userSelect = document.getElementById('user-filter');

                // Clear existing options except the first one
                while (userSelect.options.length > 1) {
                    userSelect.remove(1);
                }

                // Add user options
                data.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.user_id;
                    option.textContent = user.email || user.user_id;
                    userSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading users:', error);
            });
    }

    // Load analytics summary data
    function loadAnalyticsSummary() {
        const params = getFilterParams();

        fetch(`/api/analytics/summary?${params}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch analytics summary');
                }
                return response.json();
            })
            .then(data => {
                // Update summary cards
                document.getElementById('total-activities').textContent = data.total_activities.toLocaleString();
                document.getElementById('unique-users').textContent = data.unique_users.toLocaleString();
                document.getElementById('anonymous-sessions').textContent = data.anonymous_sessions.toLocaleString();

                const avgActivities = data.unique_users > 0 ?
                    Math.round(data.total_activities / (data.unique_users + data.anonymous_sessions)) : 0;
                document.getElementById('avg-activities').textContent = avgActivities.toLocaleString();

                // Update charts
                updateActivityTimelineChart(data);
                updateEventTypeChart(data.event_type_counts);

                // Update top pages table
                updateTopPagesTable(data.top_pages, data.total_activities);

                // Update top features table
                updateTopFeaturesTable(data.top_features, data.total_activities);
            })
            .catch(error => {
                console.error('Error loading analytics summary:', error);
            });
    }

    // Load activity log data
    function loadActivityLog(append = false) {
        const params = getFilterParams();

        fetch(`/api/analytics/activity?${params}&limit=${pageSize}&offset=${currentOffset}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch activity log');
                }
                return response.json();
            })
            .then(data => {
                updateActivityLogTable(data, append);
            })
            .catch(error => {
                console.error('Error loading activity log:', error);
            });
    }

    // Get filter parameters as URL query string
    function getFilterParams() {
        const dateRange = document.getElementById('date-range').value;
        const eventType = document.getElementById('event-type').value;
        const userId = document.getElementById('user-filter').value;

        let startDate, endDate;

        if (dateRange === 'custom') {
            startDate = document.getElementById('start-date').value;
            endDate = document.getElementById('end-date').value;
        } else {
            const today = new Date();
            endDate = today.toISOString().split('T')[0];

            const pastDate = new Date();
            pastDate.setDate(today.getDate() - parseInt(dateRange));
            startDate = pastDate.toISOString().split('T')[0];
        }

        let params = `start_date=${startDate}T00:00:00Z&end_date=${endDate}T23:59:59Z`;

        if (eventType !== 'all') {
            params += `&event_type=${eventType}`;
        }

        if (userId !== 'all') {
            params += `&user_id=${userId}`;
        }

        return params;
    }

    // Update the activity timeline chart
    function updateActivityTimelineChart(data) {
        const ctx = document.getElementById('activity-timeline-chart').getContext('2d');

        // Destroy existing chart if it exists
        if (activityTimelineChart) {
            activityTimelineChart.destroy();
        }

        // Create new chart
        activityTimelineChart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [{
                    label: 'Activity Count',
                    data: [], // Will be populated
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                            tooltipFormat: 'MMM D, YYYY'
                        },
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Activity Count'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            title: function(tooltipItems) {
                                return moment(tooltipItems[0].parsed.x).format('MMM D, YYYY');
                            }
                        }
                    }
                }
            }
        });

        // Simulate daily data for now - this would come from the API in a real implementation
        const startDate = new Date(data.period.start_date);
        const endDate = new Date(data.period.end_date);
        const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

        const timelineData = [];
        for (let i = 0; i < days; i++) {
            const date = new Date(startDate);
            date.setDate(date.getDate() + i);

            // Generate random activity count for demonstration
            // In a real implementation, this would come from the API
            const activityCount = Math.floor(Math.random() * (data.total_activities / days * 2));

            timelineData.push({
                x: date,
                y: activityCount
            });
        }

        activityTimelineChart.data.datasets[0].data = timelineData;
        activityTimelineChart.update();
    }

    // Update the event type chart
    function updateEventTypeChart(eventTypeCounts) {
        const ctx = document.getElementById('event-type-chart').getContext('2d');

        // Destroy existing chart if it exists
        if (eventTypeChart) {
            eventTypeChart.destroy();
        }

        // Prepare data
        const labels = Object.keys(eventTypeCounts);
        const data = Object.values(eventTypeCounts);

        // Define colors for each event type
        const colors = {
            'PAGE_VIEW': 'rgba(54, 162, 235, 0.8)',
            'FEATURE_USE': 'rgba(75, 192, 192, 0.8)',
            'API_CALL': 'rgba(255, 206, 86, 0.8)',
            'REPORT_GENERATED': 'rgba(153, 102, 255, 0.8)',
            'LOGIN': 'rgba(255, 159, 64, 0.8)',
            'LOGOUT': 'rgba(255, 99, 132, 0.8)',
            'SEARCH': 'rgba(199, 199, 199, 0.8)',
            'FILTER_APPLIED': 'rgba(83, 102, 255, 0.8)',
            'EXPORT': 'rgba(255, 99, 255, 0.8)',
            'ERROR': 'rgba(255, 0, 0, 0.8)'
        };

        const backgroundColor = labels.map(label => colors[label] || 'rgba(128, 128, 128, 0.8)');

        // Create new chart
        eventTypeChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Update the top pages table
    function updateTopPagesTable(topPages, totalActivities) {
        const tableBody = document.getElementById('top-pages-table');
        tableBody.innerHTML = '';

        if (!topPages || topPages.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="3" class="text-center">No data available</td>';
            tableBody.appendChild(row);
            return;
        }

        topPages.forEach(page => {
            const row = document.createElement('tr');
            const percentage = Math.round((page.count / totalActivities) * 100);

            row.innerHTML = `
                <td>${page.path}</td>
                <td>${page.count.toLocaleString()}</td>
                <td>${percentage}%</td>
            `;

            tableBody.appendChild(row);
        });
    }

    // Update the top features table
    function updateTopFeaturesTable(topFeatures, totalActivities) {
        const tableBody = document.getElementById('top-features-table');
        tableBody.innerHTML = '';

        if (!topFeatures || topFeatures.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="3" class="text-center">No data available</td>';
            tableBody.appendChild(row);
            return;
        }

        topFeatures.forEach(feature => {
            const row = document.createElement('tr');
            const percentage = Math.round((feature.count / totalActivities) * 100);

            row.innerHTML = `
                <td>${feature.feature}</td>
                <td>${feature.count.toLocaleString()}</td>
                <td>${percentage}%</td>
            `;

            tableBody.appendChild(row);
        });
    }

    // Update the activity log table
    function updateActivityLogTable(activities, append = false) {
        const tableBody = document.getElementById('activity-log-table');

        if (!append) {
            tableBody.innerHTML = '';
        }

        if (!activities || activities.length === 0) {
            if (!append) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" class="text-center">No data available</td>';
                tableBody.appendChild(row);
            }

            document.getElementById('load-more').disabled = true;
            document.getElementById('showing-entries').textContent = `Showing ${tableBody.children.length} of ${tableBody.children.length} entries`;
            return;
        }

        activities.forEach(activity => {
            const row = document.createElement('tr');

            // Format timestamp
            const timestamp = new Date(activity.timestamp);
            const formattedTimestamp = timestamp.toLocaleString();

            // Find user email if available
            let userDisplay = activity.user_id || 'Anonymous';
            const user = allUsers.find(u => u.user_id === activity.user_id);
            if (user && user.email) {
                userDisplay = user.email;
            }

            // Format details
            let detailsDisplay = '';
            if (activity.details) {
                if (activity.event_type === 'PAGE_VIEW') {
                    detailsDisplay = `Page: ${activity.details.path || 'Unknown'}`;
                    if (activity.details.referrer) {
                        detailsDisplay += ` (from: ${activity.details.referrer})`;
                    }
                } else if (activity.event_type === 'FEATURE_USE') {
                    detailsDisplay = `Feature: ${activity.details.feature || 'Unknown'}`;
                    if (activity.details.action) {
                        detailsDisplay += `, Action: ${activity.details.action}`;
                    }
                } else if (activity.event_type === 'REPORT_GENERATED') {
                    detailsDisplay = `Report: ${activity.details.report_type || 'Unknown'}`;
                    if (activity.details.format) {
                        detailsDisplay += `, Format: ${activity.details.format}`;
                    }
                } else {
                    detailsDisplay = JSON.stringify(activity.details);
                }
            }

            row.innerHTML = `
                <td>${formattedTimestamp}</td>
                <td>${userDisplay}</td>
                <td>${activity.event_type}</td>
                <td>${detailsDisplay}</td>
                <td>${activity.session_id || 'N/A'}</td>
            `;

            tableBody.appendChild(row);
        });

        // Update load more button state
        document.getElementById('load-more').disabled = activities.length < pageSize;

        // Update showing entries text
        document.getElementById('showing-entries').textContent = `Showing ${tableBody.children.length} entries`;
    }

    // Export activity log to CSV
    function exportActivityLog() {
        const params = getFilterParams();

        fetch(`/api/analytics/activity?${params}&limit=1000`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch activity log for export');
                }
                return response.json();
            })
            .then(data => {
                if (!data || data.length === 0) {
                    alert('No data to export');
                    return;
                }

                // Prepare CSV content
                let csvContent = 'data:text/csv;charset=utf-8,';

                // Add headers
                csvContent += 'Timestamp,User ID,Event Type,Details,Session ID\n';

                // Add data rows
                data.forEach(activity => {
                    const timestamp = new Date(activity.timestamp).toISOString();
                    const userId = activity.user_id || 'Anonymous';
                    const eventType = activity.event_type;
                    const details = activity.details ? JSON.stringify(activity.details).replace(/,/g, ';') : '';
                    const sessionId = activity.session_id || '';

                    csvContent += `${timestamp},${userId},${eventType},${details},${sessionId}\n`;
                });

                // Create download link
                const encodedUri = encodeURI(csvContent);
                const link = document.createElement('a');
                link.setAttribute('href', encodedUri);
                link.setAttribute('download', `activity_log_${new Date().toISOString().split('T')[0]}.csv`);
                document.body.appendChild(link);

                // Trigger download
                link.click();

                // Clean up
                document.body.removeChild(link);
            })
            .catch(error => {
                console.error('Error exporting activity log:', error);
                alert('Failed to export activity log: ' + error.message);
            });
    }
</script>
{% endblock %}
