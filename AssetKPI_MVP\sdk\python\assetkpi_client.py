import requests
import time
import json
import logging
from typing import Dict, List, Any, Optional, Union
from urllib.parse import urljoin

class AssetKPIClient:
    """
    Python client for the AssetKPI API.
    """
    def __init__(
        self,
        base_url: str,
        api_key: Optional[str] = None,
        firebase_token: Optional[str] = None,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: int = 1,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize the AssetKPI API client.
        
        Args:
            base_url: Base URL of the AssetKPI API (e.g., "http://localhost:8000/api")
            api_key: API key for authentication
            firebase_token: Firebase ID token for authentication
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds (will be exponentially increased)
            logger: Logger instance
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.firebase_token = firebase_token
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.logger = logger or logging.getLogger(__name__)
        
        # Validate authentication
        if not api_key and not firebase_token:
            self.logger.warning("No authentication provided. Some API endpoints may not be accessible.")
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests."""
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        if self.api_key:
            headers["X-API-Key"] = self.api_key
        elif self.firebase_token:
            headers["Authorization"] = f"Bearer {self.firebase_token}"
        
        return headers
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """
        Handle API response and convert to JSON.
        
        Args:
            response: Response from the API
            
        Returns:
            JSON response as a dictionary
            
        Raises:
            requests.HTTPError: If the response status code indicates an error
        """
        # Check if the response is successful
        response.raise_for_status()
        
        # Parse JSON response
        if response.content:
            return response.json()
        
        return {}
    
    def _request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        retries: int = 0
    ) -> Dict[str, Any]:
        """
        Make an API request with retry logic.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint
            params: Query parameters
            data: Request body
            retries: Current retry count
            
        Returns:
            JSON response as a dictionary
            
        Raises:
            requests.HTTPError: If the request fails after all retries
        """
        url = urljoin(self.base_url, endpoint.lstrip("/"))
        headers = self._get_headers()
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=data,
                timeout=self.timeout
            )
            
            # Handle rate limiting
            if response.status_code == 429 and retries < self.max_retries:
                retry_after = int(response.headers.get("Retry-After", self.retry_delay))
                self.logger.warning(f"Rate limit exceeded. Retrying after {retry_after} seconds.")
                time.sleep(retry_after)
                return self._request(method, endpoint, params, data, retries + 1)
            
            return self._handle_response(response)
            
        except requests.RequestException as e:
            # Retry on connection errors
            if retries < self.max_retries:
                retry_delay = self.retry_delay * (2 ** retries)  # Exponential backoff
                self.logger.warning(f"Request failed: {str(e)}. Retrying in {retry_delay} seconds.")
                time.sleep(retry_delay)
                return self._request(method, endpoint, params, data, retries + 1)
            
            self.logger.error(f"Request failed after {self.max_retries} retries: {str(e)}")
            raise
    
    def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a GET request to the API.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            
        Returns:
            JSON response as a dictionary
        """
        return self._request("GET", endpoint, params=params)
    
    def post(
        self,
        endpoint: str,
        data: Dict[str, Any],
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a POST request to the API.
        
        Args:
            endpoint: API endpoint
            data: Request body
            params: Query parameters
            
        Returns:
            JSON response as a dictionary
        """
        return self._request("POST", endpoint, params=params, data=data)
    
    def put(
        self,
        endpoint: str,
        data: Dict[str, Any],
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a PUT request to the API.
        
        Args:
            endpoint: API endpoint
            data: Request body
            params: Query parameters
            
        Returns:
            JSON response as a dictionary
        """
        return self._request("PUT", endpoint, params=params, data=data)
    
    def delete(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a DELETE request to the API.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            
        Returns:
            JSON response as a dictionary
        """
        return self._request("DELETE", endpoint, params=params)
    
    def get_paginated(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        limit: int = 100,
        offset: int = 0,
        max_items: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get all items from a paginated endpoint.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            limit: Number of items per page
            offset: Starting offset
            max_items: Maximum number of items to retrieve (None for all)
            
        Returns:
            List of items
        """
        all_items = []
        params = params or {}
        params["limit"] = limit
        params["offset"] = offset
        
        while True:
            response = self.get(endpoint, params)
            
            items = response.get("items", [])
            all_items.extend(items)
            
            # Check if we've reached the maximum number of items
            if max_items is not None and len(all_items) >= max_items:
                return all_items[:max_items]
            
            # Check if there are more items
            if not response.get("has_more", False):
                break
            
            # Update offset for next page
            params["offset"] += limit
        
        return all_items
    
    def bulk_create(
        self,
        endpoint: str,
        items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create multiple items in a single request.
        
        Args:
            endpoint: API endpoint
            items: List of items to create
            
        Returns:
            JSON response as a dictionary
        """
        bulk_endpoint = f"{endpoint}/bulk"
        return self.post(bulk_endpoint, {"items": items})
    
    def bulk_update(
        self,
        endpoint: str,
        items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Update multiple items in a single request.
        
        Args:
            endpoint: API endpoint
            items: List of items to update
            
        Returns:
            JSON response as a dictionary
        """
        bulk_endpoint = f"{endpoint}/bulk"
        return self.put(bulk_endpoint, {"items": items})
    
    def bulk_delete(
        self,
        endpoint: str,
        ids: List[Union[int, str]]
    ) -> Dict[str, Any]:
        """
        Delete multiple items in a single request.
        
        Args:
            endpoint: API endpoint
            ids: List of item IDs to delete
            
        Returns:
            JSON response as a dictionary
        """
        bulk_endpoint = f"{endpoint}/bulk"
        return self.delete(bulk_endpoint, {"ids": ids})
    
    # --- Convenience methods for common operations ---
    
    def get_assets(
        self,
        limit: int = 100,
        offset: int = 0,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
        **filters
    ) -> List[Dict[str, Any]]:
        """
        Get assets with optional filtering and sorting.
        
        Args:
            limit: Number of items per page
            offset: Starting offset
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)
            **filters: Additional filter parameters
            
        Returns:
            List of assets
        """
        params = {
            "limit": limit,
            "offset": offset
        }
        
        if sort_by:
            params["sort_by"] = sort_by
            params["sort_order"] = sort_order
        
        # Add filters
        params.update(filters)
        
        return self.get_paginated("/assets", params)
    
    def get_inventory_parts(
        self,
        limit: int = 100,
        offset: int = 0,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
        **filters
    ) -> List[Dict[str, Any]]:
        """
        Get inventory parts with optional filtering and sorting.
        
        Args:
            limit: Number of items per page
            offset: Starting offset
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)
            **filters: Additional filter parameters
            
        Returns:
            List of inventory parts
        """
        params = {
            "limit": limit,
            "offset": offset
        }
        
        if sort_by:
            params["sort_by"] = sort_by
            params["sort_order"] = sort_order
        
        # Add filters
        params.update(filters)
        
        return self.get_paginated("/inventory/parts", params)
    
    def get_work_orders(
        self,
        limit: int = 100,
        offset: int = 0,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
        **filters
    ) -> List[Dict[str, Any]]:
        """
        Get work orders with optional filtering and sorting.
        
        Args:
            limit: Number of items per page
            offset: Starting offset
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)
            **filters: Additional filter parameters
            
        Returns:
            List of work orders
        """
        params = {
            "limit": limit,
            "offset": offset
        }
        
        if sort_by:
            params["sort_by"] = sort_by
            params["sort_order"] = sort_order
        
        # Add filters
        params.update(filters)
        
        return self.get_paginated("/workorders", params)
    
    def get_kpi_history(
        self,
        kpi_name: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        asset_id: Optional[int] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get KPI history.
        
        Args:
            kpi_name: Name of the KPI
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            asset_id: Asset ID
            limit: Maximum number of data points
            
        Returns:
            List of KPI history points
        """
        params = {"limit": limit}
        
        if start_date:
            params["start_date"] = start_date
        
        if end_date:
            params["end_date"] = end_date
        
        if asset_id:
            params["asset_id"] = asset_id
        
        return self.get(f"/kpis/history/{kpi_name}", params)
    
    def create_work_order(
        self,
        asset_id: int,
        work_order_type: str,
        description: str,
        status: str = "OPEN",
        assigned_to: Optional[str] = None,
        failure_code: Optional[str] = None,
        failure_type: Optional[str] = None,
        downtime_minutes: Optional[int] = None,
        repair_time_minutes: Optional[int] = None,
        maintenance_cost: Optional[float] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a new work order.
        
        Args:
            asset_id: Asset ID
            work_order_type: Type of work order (Corrective, Preventive, etc.)
            description: Description of the work order
            status: Status of the work order (OPEN, CLOSED, etc.)
            assigned_to: Person assigned to the work order
            failure_code: Failure code
            failure_type: Failure type
            downtime_minutes: Downtime in minutes
            repair_time_minutes: Repair time in minutes
            maintenance_cost: Maintenance cost
            start_date: Start date (ISO format)
            end_date: End date (ISO format)
            
        Returns:
            Created work order
        """
        data = {
            "assetId": asset_id,
            "workOrderType": work_order_type,
            "description": description,
            "status": status
        }
        
        if assigned_to:
            data["assignedTo"] = assigned_to
        
        if failure_code:
            data["failureCode"] = failure_code
        
        if failure_type:
            data["failureType"] = failure_type
        
        if downtime_minutes is not None:
            data["downtimeMinutes"] = downtime_minutes
        
        if repair_time_minutes is not None:
            data["repairTimeMinutes"] = repair_time_minutes
        
        if maintenance_cost is not None:
            data["maintenanceCost"] = maintenance_cost
        
        if start_date:
            data["startDate"] = start_date
        
        if end_date:
            data["endDate"] = end_date
        
        return self.post("/ingest/workorder", data)
