# AssetKPI - Intelligent KPI & Inventory Optimization System

## Project Overview
AssetKPI (formerly IKIOS) is an Intelligent KPI & Inventory Optimization System designed to help maintenance and inventory managers track key performance indicators, optimize inventory levels, and make data-driven decisions. The system calculates maintenance KPIs like MTTR and MTBF, provides inventory optimization recommendations, and visualizes trends through interactive charts.

## Version History

### Past Versions
- **v0.1.0** - **v0.8.1**: See version history archive for details on previous versions.

### Current Version
- **v0.8.3**: Authentication, Error Handling & Functionality Fixes
  - Fixed logout functionality with proper server-side session cleanup
  - Fixed EOQ configuration page to handle empty data gracefully
  - Enhanced authentication token handling to prevent 401 Unauthorized errors
  - Added automatic token refresh mechanism for improved session management
  - Enhanced error handling for authentication failures with user-friendly messages
  - Fixed dashboard template rendering with proper error handling for missing data
  - Added comprehensive unit tests for authentication and dashboard functions
  - Improved user experience with better error messages and loading indicators

### Testing
The application now includes comprehensive unit tests for critical components:

- **Authentication Tests**: Tests for token handling, authentication state management, and error recovery
  - Access at: `/auth-tests`
  - Tests authentication functions in `auth.js`
  - Verifies token refresh, error handling, and authentication state management

- **Dashboard Tests**: Tests for dashboard data loading and visualization
  - Access at: `/dashboard-tests`
  - Tests chart rendering, data processing, and error handling
  - Verifies proper handling of authentication errors and empty data sets

To run the tests:
1. Start the application
2. Navigate to `/auth-tests` or `/dashboard-tests` in your browser
3. Click the "Run Tests" button to execute the tests
4. View the test results and detailed logs on the page

### Bug Fixes
- Fixed missing `except` clause in `main.py` that was causing server errors
- Fixed dashboard template rendering error with KPI objects
- Resolved 401 Unauthorized errors with improved token handling
- Fixed logout functionality that was previously not working
- Fixed EOQ configuration page to handle empty data gracefully
- Enhanced error handling throughout the application

## Roadmap to v1.0.0

### Milestone 1: Core KPI Calculation ✅
- Implement MTTR (Mean Time To Repair) calculation
- Implement MTBF (Mean Time Between Failures) calculation
- Create basic dashboard for KPI visualization

### Milestone 2: Usage Analytics ✅
- Implement user activity tracking
- Create analytics dashboard for system usage
- Add reporting functionality for usage patterns

### Milestone 3: Inventory Optimization ✅
- Implement EOQ (Economic Order Quantity) calculation
- Add safety stock calculation with variability factors
- Create inventory optimization dashboard

### Milestone 4: Enhanced Visualization ✅
- Add interactive charts for KPI trends
- Implement comparative analysis views
- Create customizable dashboards

### Milestone 5: API Ingestion Capabilities ✅
- Create API endpoints for data ingestion
- Implement data validation and processing
- Add authentication and rate limiting

### Milestone 6: Optimization Rules ✅
- Implement rule-based optimization engine
- Create rule management interface
- Add rule execution history and impact analysis

### Milestone 7: Configuration Management ✅
- Add system configuration interface
- Implement user preference management
- Create backup and restore functionality

### Milestone 8: Cloud Deployment Preparation ✅
- Optimize for cloud deployment
- Implement containerization
- Add scaling capabilities

### Milestone 9: Final Testing & Documentation
- Comprehensive system testing
- Complete user documentation
- Create deployment guides
