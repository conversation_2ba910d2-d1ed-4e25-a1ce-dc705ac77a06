import os
import sys
import firebase_admin
from firebase_admin import credentials, auth
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if already initialized
        try:
            app = firebase_admin.get_app()
            print(f"Firebase Admin SDK already initialized with app: {app.name}")
            return True
        except ValueError:
            pass
        
        # Initialize with service account
        try:
            SERVICE_ACCOUNT_KEY_PATH = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
            cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)
            firebase_admin.initialize_app(cred)
            print("Firebase Admin SDK initialized successfully")
            return True
        except Exception as e:
            print(f"Error initializing Firebase Admin SDK: {e}")
            return False
    except Exception as e:
        print(f"Error in initialize_firebase: {e}")
        return False

def create_firebase_user(email, password, display_name=None):
    """Create a new user in Firebase"""
    try:
        user = auth.create_user(
            email=email,
            password=password,
            display_name=display_name,
            email_verified=False
        )
        print(f"Created user in Firebase:")
        print(f"  UID: {user.uid}")
        print(f"  Email: {user.email}")
        return user
    except Exception as e:
        print(f"Error creating user in Firebase: {e}")
        return None

def main():
    # Initialize Firebase Admin SDK
    if not initialize_firebase():
        print("Failed to initialize Firebase Admin SDK. Exiting.")
        sys.exit(1)
    
    # Create user
    email = "<EMAIL>"
    password = "TestTest"
    display_name = "Johan Borgulf"
    
    user = create_firebase_user(email, password, display_name)
    if user:
        print("User created successfully!")
    else:
        print("Failed to create user.")

if __name__ == "__main__":
    main()
