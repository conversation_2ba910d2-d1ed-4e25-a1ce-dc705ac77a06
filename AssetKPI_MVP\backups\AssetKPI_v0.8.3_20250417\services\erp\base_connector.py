"""
Base ERP connector interface.

This module defines the base interface for ERP connectors.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
import logging

# Create a logger for this module
logger = logging.getLogger(__name__)


class BaseERPConnector(ABC):
    """
    Base interface for ERP connectors.
    
    This abstract class defines the interface that all ERP connectors must implement.
    """
    
    def __init__(self, connection_config: Dict[str, Any]):
        """
        Initialize the ERP connector.
        
        Args:
            connection_config: Configuration for connecting to the ERP system
        """
        self.connection_config = connection_config
        self.connection = None
    
    @abstractmethod
    def connect(self) -> bool:
        """
        Connect to the ERP system.
        
        Returns:
            True if connection is successful, False otherwise
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        Disconnect from the ERP system.
        
        Returns:
            True if disconnection is successful, False otherwise
        """
        pass
    
    @abstractmethod
    def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to the ERP system.
        
        Returns:
            Dictionary with test results
        """
        pass
    
    @abstractmethod
    def get_entity_schema(self, entity_type: str) -> Dict[str, Any]:
        """
        Get the schema for an entity type in the ERP system.
        
        Args:
            entity_type: Type of entity
            
        Returns:
            Dictionary with entity schema
        """
        pass
    
    @abstractmethod
    def get_entities(
        self,
        entity_type: str,
        filters: Optional[Dict[str, Any]] = None,
        fields: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get entities from the ERP system.
        
        Args:
            entity_type: Type of entity
            filters: Optional filters to apply
            fields: Optional list of fields to include
            limit: Optional limit on the number of entities to return
            offset: Optional offset for pagination
            
        Returns:
            List of entities
        """
        pass
    
    @abstractmethod
    def get_entity_by_id(
        self,
        entity_type: str,
        entity_id: Union[str, int],
        fields: Optional[List[str]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Get an entity by ID from the ERP system.
        
        Args:
            entity_type: Type of entity
            entity_id: ID of the entity
            fields: Optional list of fields to include
            
        Returns:
            Entity if found, None otherwise
        """
        pass
    
    @abstractmethod
    def create_entity(
        self,
        entity_type: str,
        entity_data: Dict[str, Any],
    ) -> Optional[Dict[str, Any]]:
        """
        Create an entity in the ERP system.
        
        Args:
            entity_type: Type of entity
            entity_data: Data for the entity
            
        Returns:
            Created entity if successful, None otherwise
        """
        pass
    
    @abstractmethod
    def update_entity(
        self,
        entity_type: str,
        entity_id: Union[str, int],
        entity_data: Dict[str, Any],
    ) -> Optional[Dict[str, Any]]:
        """
        Update an entity in the ERP system.
        
        Args:
            entity_type: Type of entity
            entity_id: ID of the entity
            entity_data: Updated data for the entity
            
        Returns:
            Updated entity if successful, None otherwise
        """
        pass
    
    @abstractmethod
    def delete_entity(
        self,
        entity_type: str,
        entity_id: Union[str, int],
    ) -> bool:
        """
        Delete an entity from the ERP system.
        
        Args:
            entity_type: Type of entity
            entity_id: ID of the entity
            
        Returns:
            True if deletion is successful, False otherwise
        """
        pass
    
    @abstractmethod
    def execute_query(
        self,
        query: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Execute a custom query on the ERP system.
        
        Args:
            query: Query to execute
            params: Optional parameters for the query
            
        Returns:
            Query results
        """
        pass
