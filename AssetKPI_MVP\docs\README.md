# AssetKPI Documentation

This directory contains documentation for the AssetKPI system.

## Integration Documentation

The integration documentation is located in the `integration` directory. It includes:

- [Integration Overview](integration/index.md)
- [Webhooks](integration/webhooks/index.md)
- [ERP Integration](integration/erp/index.md)
- [Python SDK](integration/sdk/index.md)
- [Tutorials](integration/tutorials/index.md)
- [Examples](integration/examples/index.md)
- [API Reference](integration/reference/index.md)

## API Documentation

The API documentation is located in the `api` directory. It includes:

- [API Overview](api/index.md)
- [Getting Started Guide](api/getting_started.md)
- Detailed endpoint documentation
- Code examples
- Use cases

## Using the API Documentation

To get started with the API:

1. Read the [Getting Started Guide](api/getting_started.md)
2. Explore the [API Overview](api/index.md)
3. Check out the [Use Cases](api/examples/use_cases.md) for examples of common scenarios
4. Try the [Postman Collection](api/examples/postman_guide.md) for interactive API testing

## Postman Collection

A Postman collection is provided to help you test the API:

- [AssetKPI API Collection](api/examples/AssetKPI_API.postman_collection.json)
- [AssetKPI API Environment](api/examples/AssetKPI_API_Environment.postman_environment.json)

To use the collection:

1. Import both files into Postman
2. Update the environment variables with your credentials
3. Follow the [Postman Guide](api/examples/postman_guide.md) for detailed instructions

## Code Examples

Code examples are provided in multiple languages:

- [Python](api/examples/python_client.md)
- [JavaScript](api/examples/javascript_client.md)
- [cURL](api/examples/curl_examples.md)

## Contributing to the Documentation

When contributing to the documentation, please follow these guidelines:

1. Use the provided template (`template.md`) for new documentation pages
2. Follow the established directory structure
3. Use Markdown for all documentation
4. Include a table of contents for each page
5. Use relative links to reference other documentation pages
6. Include examples where appropriate
7. Keep the documentation up-to-date with the codebase

If you find any issues or have suggestions for improving the documentation, please contact the AssetKPI development team.
