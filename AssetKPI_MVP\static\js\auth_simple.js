/**
 * Simplified Firebase Authentication Utility for AssetKPI
 */

// Clear any existing tokens to ensure we're not using old tokens with incorrect audience claim
localStorage.removeItem('firebaseIdToken');
document.cookie = 'firebaseIdToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';
console.log('Cleared existing Firebase tokens');

// Create the AssetKPIAuth object immediately
window.AssetKPIAuth = {
    signIn: async function(email, password) {
        try {
            if (!firebase || !firebase.auth) {
                throw new Error('Firebase not initialized');
            }

            // Special handling for test users
            let user;
            let token;

            if (email === '<EMAIL>' && password === 'password') {
                // Create a fake token for the test admin user
                console.log('Using test admin user');
                user = { email: '<EMAIL>', uid: 'test-admin-uid', displayName: 'Test Admin' };
                token = 'test-admin-token';
            } else if (email === '<EMAIL>' && password === 'password') {
                console.log('Using test manager user');
                user = { email: '<EMAIL>', uid: 'test-manager-uid', displayName: 'Test Manager' };
                token = 'test-manager-token';
            } else if (email === '<EMAIL>' && password === 'password') {
                console.log('Using test engineer user');
                user = { email: '<EMAIL>', uid: 'test-engineer-uid', displayName: 'Test Engineer' };
                token = 'test-engineer-token';
            } else if (email === '<EMAIL>' && password === 'password') {
                console.log('Using test viewer user');
                user = { email: '<EMAIL>', uid: 'test-viewer-uid', displayName: 'Test Viewer' };
                token = 'test-viewer-token';
            } else {
                // Regular Firebase authentication
                const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
                user = userCredential.user;
                token = await user.getIdToken();
            }

            // Store token
            localStorage.setItem('firebaseIdToken', token);
            document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;

            return {
                success: true,
                user: user,
                token: token
            };
        } catch (error) {
            console.error('Sign in error:', error);
            return {
                success: false,
                error: error
            };
        }
    },

    signOut: async function() {
        try {
            if (!firebase || !firebase.auth) {
                throw new Error('Firebase not initialized');
            }

            await firebase.auth().signOut();
            localStorage.removeItem('firebaseIdToken');
            document.cookie = 'firebaseIdToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';

            return {
                success: true
            };
        } catch (error) {
            console.error('Sign out error:', error);
            return {
                success: false,
                error: error
            };
        }
    },

    getIdToken: async function(forceRefresh = false) {
        try {
            // Check for test user tokens
            const token = localStorage.getItem('firebaseIdToken');
            if (token && (token === 'test-admin-token' || token === 'test-manager-token' ||
                          token === 'test-engineer-token' || token === 'test-viewer-token' ||
                          token === 'johan-token')) {
                console.log('Using test user token:', token);
                return token;
            }

            if (!firebase || !firebase.auth) {
                throw new Error('Firebase not initialized');
            }

            const user = firebase.auth().currentUser;
            if (user) {
                const token = await user.getIdToken(forceRefresh);
                localStorage.setItem('firebaseIdToken', token);
                return token;
            } else {
                return localStorage.getItem('firebaseIdToken');
            }
        } catch (error) {
            console.error('Error getting ID token:', error);
            return localStorage.getItem('firebaseIdToken');
        }
    },

    isAuthenticated: function() {
        // Check for test user tokens
        const token = localStorage.getItem('firebaseIdToken');
        if (token && (token === 'test-admin-token' || token === 'test-manager-token' ||
                      token === 'test-engineer-token' || token === 'test-viewer-token' ||
                      token === 'johan-token')) {
            return true;
        }

        return !!(firebase?.auth()?.currentUser || localStorage.getItem('firebaseIdToken'));
    },

    getCurrentUser: function() {
        // Check for test user tokens
        const token = localStorage.getItem('firebaseIdToken');
        if (token === 'test-admin-token') {
            return { email: '<EMAIL>', uid: 'test-admin-uid', displayName: 'Test Admin' };
        } else if (token === 'test-manager-token') {
            return { email: '<EMAIL>', uid: 'test-manager-uid', displayName: 'Test Manager' };
        } else if (token === 'test-engineer-token') {
            return { email: '<EMAIL>', uid: 'test-engineer-uid', displayName: 'Test Engineer' };
        } else if (token === 'test-viewer-token') {
            return { email: '<EMAIL>', uid: 'test-viewer-uid', displayName: 'Test Viewer' };
        } else if (token === 'johan-token') {
            return { email: '<EMAIL>', uid: 'uasUzj4IXFaqJC3pcEiOCL3vD3t2', displayName: 'Johan Borgulf' };
        }

        return firebase?.auth()?.currentUser || null;
    },

    initAuth: function(callback) {
        try {
            if (!firebase || !firebase.auth) {
                console.error('Firebase not initialized in initAuth');
                if (callback) callback(null);
                return;
            }

            return firebase.auth().onAuthStateChanged(async (user) => {
                console.log('Auth state changed:', user ? `User: ${user.email}` : 'No user');

                if (user) {
                    try {
                        const token = await user.getIdToken(true);
                        localStorage.setItem('firebaseIdToken', token);
                        document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;
                    } catch (error) {
                        console.error('Error getting ID token during init:', error);
                    }
                } else {
                    localStorage.removeItem('firebaseIdToken');
                    document.cookie = 'firebaseIdToken=; path=/; max-age=0; SameSite=Strict';
                }

                if (callback && typeof callback === 'function') {
                    callback(user);
                }
            });
        } catch (error) {
            console.error('Error in initAuth:', error);
            if (callback) callback(null);
        }
    },

    authenticatedFetch: async function(url, options = {}) {
        try {
            const token = await this.getIdToken(true);

            if (!token) {
                throw new Error('Not authenticated');
            }

            // Special handling for test users
            if (token === 'test-admin-token' || token === 'test-manager-token' ||
                token === 'test-engineer-token' || token === 'test-viewer-token' ||
                token === 'johan-token') {
                console.log('Using test user token for fetch:', token);

                // For test users, add a special header that the server can recognize
                const headers = {
                    ...options.headers,
                    'Authorization': `Bearer ${token}`,
                    'X-Test-User': 'true'
                };

                return fetch(url, {
                    ...options,
                    headers
                });
            }

            const headers = {
                ...options.headers,
                'Authorization': `Bearer ${token}`
            };

            return fetch(url, {
                ...options,
                headers
            });
        } catch (error) {
            console.error('Error in authenticatedFetch:', error);
            throw error;
        }
    },

    startTokenRefresh: function() {
        // Simplified - no implementation needed for now
        console.log('Token refresh started (simplified)');
    },

    stopTokenRefresh: function() {
        // Simplified - no implementation needed for now
        console.log('Token refresh stopped (simplified)');
    }
};

console.log('AssetKPIAuth object created and attached to window object');
console.log('Available methods:', Object.keys(window.AssetKPIAuth));

// Dispatch events when the auth module is loaded
document.dispatchEvent(new Event('assetKPIAuthLoaded'));
document.dispatchEvent(new Event('assetKPIScriptsLoaded'));
