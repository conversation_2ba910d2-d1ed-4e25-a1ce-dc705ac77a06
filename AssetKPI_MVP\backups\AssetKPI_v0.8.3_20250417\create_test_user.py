import os
import sys
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Import the User model and UserRole enum
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from main import User, UserRole, Base

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def create_test_user(user_id, email, role, full_name=None):
    """Create a test user in the database."""
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.user_id == user_id).first()
        if existing_user:
            print(f"User with ID {user_id} already exists.")
            return existing_user
        
        # Create new user
        user = User(
            user_id=user_id,
            email=email,
            role=role,
            full_name=full_name,
            created_at=datetime.now(),
            last_login=None
        )
        
        # Add to database
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print(f"Created user: {user.email} with role {user.role}")
        return user
    
    except Exception as e:
        db.rollback()
        print(f"Error creating user: {e}")
        return None

if __name__ == "__main__":
    # Create test users with different roles
    create_test_user(
        user_id="test-admin-uid",
        email="<EMAIL>",
        role=UserRole.ADMIN,
        full_name="Test Admin"
    )
    
    create_test_user(
        user_id="test-manager-uid",
        email="<EMAIL>",
        role=UserRole.MANAGER,
        full_name="Test Manager"
    )
    
    create_test_user(
        user_id="test-engineer-uid",
        email="<EMAIL>",
        role=UserRole.ENGINEER,
        full_name="Test Engineer"
    )
    
    create_test_user(
        user_id="test-viewer-uid",
        email="<EMAIL>",
        role=UserRole.VIEWER,
        full_name="Test Viewer"
    )
    
    print("Test users created successfully!")
