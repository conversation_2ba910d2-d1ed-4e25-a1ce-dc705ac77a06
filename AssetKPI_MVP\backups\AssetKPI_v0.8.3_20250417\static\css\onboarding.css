/**
 * Onboarding Wizard Styles
 */

/* Wizard Container */
.onboarding-wizard {
    max-width: 900px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Progress Bar */
.onboarding-progress {
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

/* Step Indicators */
.step-indicators {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    justify-content: space-between;
}

.step-indicator {
    position: relative;
    flex: 1;
    text-align: center;
    padding: 0 10px;
    cursor: default;
}

.step-indicator:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    width: 100%;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    margin: 0 auto 5px;
    background-color: #dee2e6;
    color: #6c757d;
    border-radius: 50%;
    font-weight: bold;
    position: relative;
    z-index: 2;
}

.step-title {
    display: none;
    font-size: 0.8rem;
    color: #6c757d;
}

/* Active Step */
.step-indicator.active .step-number {
    background-color: #007bff;
    color: #fff;
}

.step-indicator.active .step-title {
    display: block;
    color: #007bff;
    font-weight: bold;
}

/* Completed Step */
.step-indicator.completed .step-number {
    background-color: #28a745;
    color: #fff;
}

.step-indicator.completed .step-number::after {
    content: '✓';
}

.step-indicator.completed:not(:last-child)::after {
    background-color: #28a745;
}

/* Content Area */
.onboarding-content {
    padding: 2rem;
    min-height: 400px;
}

/* Navigation Area */
.onboarding-navigation {
    padding: 1rem 2rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

.back-btn {
    margin-right: auto;
}

.skip-btn {
    margin-right: 1rem;
}

.next-btn {
    margin-left: auto;
}

/* Step-specific Styles */
.welcome-step .card {
    height: 100%;
    transition: transform 0.2s;
}

.welcome-step .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.features-step .card,
.dashboard-step .card {
    transition: transform 0.2s;
    cursor: pointer;
}

.features-step .card:hover,
.dashboard-step .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.features-step .form-check,
.dashboard-step .form-check {
    padding-left: 0;
}

.features-step .form-check-input,
.dashboard-step .form-check-input {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.features-step .form-check-label,
.dashboard-step .form-check-label {
    display: block;
    width: 100%;
}

.completion-step {
    text-align: center;
}

/* Error Message */
.error-message {
    display: none;
    margin-bottom: 1rem;
}

/* Responsive Adjustments */
@media (min-width: 768px) {
    .step-title {
        display: block;
    }
}

@media (max-width: 767px) {
    .onboarding-wizard {
        margin: 1rem;
    }
    
    .onboarding-content {
        padding: 1rem;
    }
    
    .step-indicator.active .step-title {
        display: block;
    }
}
