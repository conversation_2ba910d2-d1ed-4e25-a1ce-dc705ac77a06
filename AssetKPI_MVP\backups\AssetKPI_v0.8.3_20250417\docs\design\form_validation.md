# Form Validation Framework Design

This document outlines the design for the AssetKPI form validation framework, which will provide consistent validation and error handling across the application.

## Goals

- Provide consistent validation across all forms
- Improve user experience with clear error messages
- Reduce form submission errors
- Support both client-side and server-side validation
- Make validation rules easy to maintain and extend

## Architecture

The form validation framework consists of the following components:

1. **Validation Rules**: Reusable validation rules for common field types
2. **Validation Engine**: Core logic for validating form data
3. **Error Display**: Components for displaying validation errors
4. **Server Integration**: Integration with server-side validation

### Component Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                  Form Validation Framework                   │
│                                                             │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────┐│
│  │ Validation    │    │ Validation    │    │ Error         ││
│  │ Rules         │◄───┤ Engine        │───►│ Display       ││
│  │               │    │               │    │               ││
│  └───────────────┘    └───────┬───────┘    └───────────────┘│
│                               │                             │
│                               ▼                             │
│                      ┌───────────────┐                      │
│                      │ Server        │                      │
│                      │ Integration   │                      │
│                      │               │                      │
│                      └───────────────┘                      │
└─────────────────────────────────────────────────────────────┘
```

## Validation Rules

The framework includes predefined validation rules for common field types:

### Required Fields

```javascript
{
  rule: 'required',
  message: 'This field is required',
  validate: (value) => value !== undefined && value !== null && value !== ''
}
```

### Text Length

```javascript
{
  rule: 'minLength',
  params: { min: 3 },
  message: 'Must be at least {min} characters',
  validate: (value, params) => !value || value.length >= params.min
}
```

```javascript
{
  rule: 'maxLength',
  params: { max: 100 },
  message: 'Must be no more than {max} characters',
  validate: (value, params) => !value || value.length <= params.max
}
```

### Numeric Values

```javascript
{
  rule: 'numeric',
  message: 'Must be a number',
  validate: (value) => !value || !isNaN(parseFloat(value))
}
```

```javascript
{
  rule: 'min',
  params: { min: 0 },
  message: 'Must be at least {min}',
  validate: (value, params) => !value || parseFloat(value) >= params.min
}
```

```javascript
{
  rule: 'max',
  params: { max: 100 },
  message: 'Must be no more than {max}',
  validate: (value, params) => !value || parseFloat(value) <= params.max
}
```

### Patterns

```javascript
{
  rule: 'pattern',
  params: { pattern: /^[A-Z0-9]+$/ },
  message: 'Must match the required format',
  validate: (value, params) => !value || params.pattern.test(value)
}
```

### Email

```javascript
{
  rule: 'email',
  message: 'Must be a valid email address',
  validate: (value) => !value || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
}
```

### Date

```javascript
{
  rule: 'date',
  message: 'Must be a valid date',
  validate: (value) => !value || !isNaN(Date.parse(value))
}
```

```javascript
{
  rule: 'minDate',
  params: { min: '2023-01-01' },
  message: 'Must be on or after {min}',
  validate: (value, params) => !value || new Date(value) >= new Date(params.min)
}
```

```javascript
{
  rule: 'maxDate',
  params: { max: '2023-12-31' },
  message: 'Must be on or before {max}',
  validate: (value, params) => !value || new Date(value) <= new Date(params.max)
}
```

### Custom

```javascript
{
  rule: 'custom',
  params: { validate: (value) => value === 'expected' },
  message: 'Custom validation failed',
  validate: (value, params) => params.validate(value)
}
```

## Validation Engine

The validation engine is responsible for:

- Validating form data against rules
- Collecting validation errors
- Formatting error messages

### Field Configuration

Fields are configured with validation rules:

```javascript
{
  name: 'assetName',
  label: 'Asset Name',
  rules: [
    { rule: 'required', message: 'Asset name is required' },
    { rule: 'minLength', params: { min: 3 }, message: 'Asset name must be at least 3 characters' },
    { rule: 'maxLength', params: { max: 100 }, message: 'Asset name must be no more than 100 characters' }
  ]
}
```

### Form Configuration

Forms are configured with fields:

```javascript
{
  id: 'assetForm',
  fields: [
    // Asset name field configuration
    // Asset type field configuration
    // Asset status field configuration
    // ...
  ]
}
```

### Validation Process

1. **Field Validation**: Each field is validated against its rules
2. **Form Validation**: Cross-field validations are performed
3. **Error Collection**: Errors are collected and organized by field
4. **Error Formatting**: Error messages are formatted with parameters

## Error Display

The framework includes components for displaying validation errors:

### Field Error

```html
<div class="form-group">
  <label for="assetName">Asset Name</label>
  <input type="text" id="assetName" name="assetName" class="form-control is-invalid">
  <div class="invalid-feedback">Asset name is required</div>
</div>
```

### Form Error Summary

```html
<div class="alert alert-danger">
  <h5>Please correct the following errors:</h5>
  <ul>
    <li>Asset Name: Asset name is required</li>
    <li>Asset Type: Asset type is required</li>
  </ul>
</div>
```

## Server Integration

The framework integrates with server-side validation:

### Server Validation Response

```json
{
  "success": false,
  "errors": {
    "assetName": ["Asset name is required"],
    "assetType": ["Asset type is required"]
  }
}
```

### Error Handling

1. **Form Submission**: Form data is submitted to the server
2. **Server Validation**: Server validates the data
3. **Error Response**: Server returns validation errors
4. **Error Display**: Client displays the errors

## Implementation

### Client-Side Implementation

```javascript
// Validation rules
const validationRules = {
  required: {
    validate: (value) => value !== undefined && value !== null && value !== '',
    message: 'This field is required'
  },
  // Other rules...
};

// Validate a field
function validateField(field, value) {
  const errors = [];
  
  for (const ruleConfig of field.rules) {
    const rule = validationRules[ruleConfig.rule];
    
    if (!rule.validate(value, ruleConfig.params)) {
      let message = ruleConfig.message || rule.message;
      
      // Replace parameters in message
      if (ruleConfig.params) {
        for (const [key, val] of Object.entries(ruleConfig.params)) {
          message = message.replace(`{${key}}`, val);
        }
      }
      
      errors.push(message);
    }
  }
  
  return errors;
}

// Validate a form
function validateForm(form, formData) {
  const errors = {};
  
  for (const field of form.fields) {
    const fieldErrors = validateField(field, formData[field.name]);
    
    if (fieldErrors.length > 0) {
      errors[field.name] = fieldErrors;
    }
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
}

// Display errors
function displayErrors(form, errors) {
  // Clear previous errors
  clearErrors(form);
  
  // Display form error summary
  if (Object.keys(errors).length > 0) {
    const errorSummary = createErrorSummary(form, errors);
    form.insertBefore(errorSummary, form.firstChild);
  }
  
  // Display field errors
  for (const [fieldName, fieldErrors] of Object.entries(errors)) {
    const fieldElement = form.querySelector(`[name="${fieldName}"]`);
    
    if (fieldElement) {
      fieldElement.classList.add('is-invalid');
      
      const errorElement = document.createElement('div');
      errorElement.className = 'invalid-feedback';
      errorElement.textContent = fieldErrors[0];
      
      fieldElement.parentNode.appendChild(errorElement);
    }
  }
}
```

### Server-Side Implementation

```python
def validate_asset(data):
    errors = {}
    
    # Validate asset name
    if not data.get('assetName'):
        errors['assetName'] = ['Asset name is required']
    elif len(data['assetName']) < 3:
        errors['assetName'] = ['Asset name must be at least 3 characters']
    elif len(data['assetName']) > 100:
        errors['assetName'] = ['Asset name must be no more than 100 characters']
    
    # Validate asset type
    if not data.get('assetType'):
        errors['assetType'] = ['Asset type is required']
    
    # Return validation result
    return {
        'valid': len(errors) == 0,
        'errors': errors
    }
```

## User Experience

### Validation Timing

- **On Input**: Validate as the user types (for immediate feedback)
- **On Blur**: Validate when the field loses focus (for less intrusive feedback)
- **On Submit**: Validate when the form is submitted (for final validation)

### Error Presentation

- **Field Errors**: Display errors next to the relevant fields
- **Error Summary**: Display a summary of all errors at the top of the form
- **Error Styling**: Use consistent styling for error messages
- **Error Focus**: Focus on the first field with an error

### Accessibility

- **ARIA Attributes**: Use ARIA attributes to indicate validation state
- **Error Announcements**: Announce errors to screen readers
- **Keyboard Navigation**: Ensure keyboard users can navigate to error messages

## Success Metrics

- Reduction in form submission errors
- Improved form completion rates
- Reduced time to complete forms
- Positive user feedback

## Future Enhancements

- Dynamic validation rules based on form state
- Field-specific validation components
- Validation rule editor for administrators
- Internationalization of error messages
