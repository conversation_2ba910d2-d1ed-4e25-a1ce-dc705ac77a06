import os
import sys
import json
import firebase_admin
from firebase_admin import credentials, auth
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def initialize_firebase():
    """Initialize Firebase Admin SDK with verbose logging"""
    try:
        # Check if already initialized
        try:
            app = firebase_admin.get_app()
            print(f"Firebase Admin SDK already initialized with app: {app.name}")
            return True
        except ValueError:
            pass  # Not initialized yet
        
        # Get service account key path
        service_account_key_path = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
        print(f"Using service account key path: {service_account_key_path}")
        
        # Check if file exists
        if not os.path.exists(service_account_key_path):
            print(f"ERROR: Service account key file not found at {service_account_key_path}")
            return False
        
        # Print file size and modification time
        file_size = os.path.getsize(service_account_key_path)
        file_mtime = os.path.getmtime(service_account_key_path)
        print(f"Service account key file size: {file_size} bytes")
        print(f"Service account key file last modified: {file_mtime}")
        
        # Try to read the file
        try:
            with open(service_account_key_path, 'r') as f:
                service_account_data = json.load(f)
                print(f"Service account key file loaded successfully")
                print(f"Project ID: {service_account_data.get('project_id', 'Not found')}")
                print(f"Client email: {service_account_data.get('client_email', 'Not found')}")
        except Exception as e:
            print(f"ERROR: Failed to read service account key file: {e}")
            return False
        
        # Initialize Firebase Admin SDK
        cred = credentials.Certificate(service_account_key_path)
        firebase_admin.initialize_app(cred)
        print("Firebase Admin SDK initialized successfully")
        return True
    except Exception as e:
        print(f"ERROR: Failed to initialize Firebase Admin SDK: {e}")
        return False

def verify_token(token):
    """Verify a Firebase ID token"""
    try:
        # Print token info for debugging (first and last 10 chars only for security)
        token_preview = f"{token[:10]}...{token[-10:]}" if len(token) > 20 else "[token too short]"
        print(f"Verifying token: {token_preview}")
        
        # Try to fetch Google's public keys to verify connectivity
        try:
            import requests
            response = requests.get("https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>", timeout=5)
            if response.status_code == 200:
                print("Successfully fetched Google's public keys")
            else:
                print(f"WARNING: Failed to fetch Google's public keys: {response.status_code}")
        except Exception as e:
            print(f"WARNING: Network error fetching Google's public keys: {e}")
        
        # Verify the token
        decoded_token = auth.verify_id_token(token)
        print("Token verified successfully!")
        print(f"User ID: {decoded_token.get('uid')}")
        print(f"Email: {decoded_token.get('email')}")
        print(f"Issuer: {decoded_token.get('iss')}")
        print(f"Audience: {decoded_token.get('aud')}")
        print(f"Expiration: {decoded_token.get('exp')}")
        return True
    except auth.ExpiredIdTokenError as e:
        print(f"ERROR: Token expired: {e}")
        return False
    except auth.InvalidIdTokenError as e:
        print(f"ERROR: Invalid token: {e}")
        return False
    except auth.RevokedIdTokenError as e:
        print(f"ERROR: Token revoked: {e}")
        return False
    except Exception as e:
        print(f"ERROR: Token verification failed: {e}")
        return False

def main():
    # Initialize Firebase Admin SDK
    if not initialize_firebase():
        print("Failed to initialize Firebase Admin SDK. Exiting.")
        sys.exit(1)
    
    # Read token from file
    try:
        with open("firebase_id_token.txt", "r") as f:
            token = f.read().strip()
            print(f"Read token from file: {token[:20]}...{token[-20:]}")
    except Exception as e:
        print(f"ERROR: Failed to read token from file: {e}")
        sys.exit(1)
    
    # Verify token
    if verify_token(token):
        print("Token verification successful!")
    else:
        print("Token verification failed!")

if __name__ == "__main__":
    main()
