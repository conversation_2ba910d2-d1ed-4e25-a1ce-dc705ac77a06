/**
 * Forms Styles
 * 
 * This file contains styles for forms and form components
 * across the AssetKPI application.
 */

/* ===== Base Form Styles ===== */

.form-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-title {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
}

.form-description {
  color: #7f8c8d;
  font-size: 1rem;
  line-height: 1.6;
}

/* ===== Form Groups ===== */

.form-group {
  margin-bottom: 1.5rem;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-label.required::after {
  content: " *";
  color: #e74c3c;
}

.form-help {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-top: 0.25rem;
  line-height: 1.4;
}

/* ===== Form Controls ===== */

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #bdc3c7;
  border-radius: 0.375rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #2c3e50;
  background-color: #fff;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #3498db;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-control:disabled {
  background-color: #ecf0f1;
  color: #7f8c8d;
  cursor: not-allowed;
}

.form-control.is-invalid {
  border-color: #e74c3c;
}

.form-control.is-invalid:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
}

.form-control.is-valid {
  border-color: #27ae60;
}

.form-control.is-valid:focus {
  border-color: #27ae60;
  box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
}

/* ===== Select Controls ===== */

.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #bdc3c7;
  border-radius: 0.375rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #2c3e50;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  appearance: none;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #3498db;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* ===== Textarea ===== */

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

/* ===== Checkboxes and Radios ===== */

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}

.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  margin-left: -1.5em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0, 0, 0, 0.25);
  appearance: none;
  color-adjust: exact;
}

.form-check-input[type="checkbox"] {
  border-radius: 0.25em;
}

.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-input:active {
  filter: brightness(90%);
}

.form-check-input:focus {
  border-color: #3498db;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

.form-check-input:checked {
  background-color: #3498db;
  border-color: #3498db;
}

.form-check-input:checked[type="checkbox"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}

.form-check-input:checked[type="radio"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-label {
  color: #2c3e50;
  cursor: pointer;
}

/* ===== Input Groups ===== */

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group > .form-control {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #7f8c8d;
  text-align: center;
  white-space: nowrap;
  background-color: #ecf0f1;
  border: 1px solid #bdc3c7;
}

.input-group > .form-control:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .form-control:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group > .input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* ===== Form Validation ===== */

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #e74c3c;
}

.is-invalid ~ .invalid-feedback {
  display: block;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #27ae60;
}

.is-valid ~ .valid-feedback {
  display: block;
}

/* ===== Form Buttons ===== */

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #dee2e6;
}

.btn-form {
  padding: 0.75rem 2rem;
  border-radius: 0.375rem;
  font-weight: 600;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary-form {
  background-color: #3498db;
  border-color: #3498db;
  color: #fff;
}

.btn-primary-form:hover {
  background-color: #2980b9;
  border-color: #2980b9;
  color: #fff;
}

.btn-secondary-form {
  background-color: transparent;
  border-color: #bdc3c7;
  color: #7f8c8d;
}

.btn-secondary-form:hover {
  background-color: #ecf0f1;
  border-color: #95a5a6;
  color: #2c3e50;
}

/* ===== Responsive Design ===== */

@media (max-width: 767.98px) {
  .form-container {
    padding: 1rem;
  }
  
  .form-title {
    font-size: 1.5rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-form {
    width: 100%;
    text-align: center;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .input-group > .form-control,
  .input-group > .input-group-text {
    border-radius: 0.375rem !important;
    margin-bottom: 0.5rem;
  }
  
  .input-group > .form-control:last-child,
  .input-group > .input-group-text:last-child {
    margin-bottom: 0;
  }
}
