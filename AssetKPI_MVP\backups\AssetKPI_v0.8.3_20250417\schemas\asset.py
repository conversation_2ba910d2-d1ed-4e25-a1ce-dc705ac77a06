"""
Asset schemas for the AssetKPI application.

This module defines the Pydantic models for asset data.
"""

from typing import Optional, List
from datetime import date
from decimal import Decimal
from pydantic import BaseModel, Field, field_validator, ConfigDict


class AssetBase(BaseModel):
    """
    Base schema for asset data.
    """
    assetname: str = Field(..., min_length=3, max_length=100, description="Name of the asset")
    assettype: str = Field(..., min_length=1, max_length=50, description="Type of the asset")
    status: str = Field(..., min_length=1, max_length=30, description="Status of the asset")
    location_id: int = Field(..., description="ID of the asset location")
    
    # Optional fields
    system_id: Optional[int] = Field(None, description="ID of the asset system")
    category_id: Optional[int] = Field(None, description="ID of the asset category")
    parent_asset_id: Optional[int] = Field(None, description="ID of the parent asset")
    manufacturer: Optional[str] = Field(None, max_length=100, description="Manufacturer of the asset")
    model: Optional[str] = Field(None, max_length=100, description="Model of the asset")
    serialnumber: Optional[str] = Field(None, max_length=100, description="Serial number of the asset")
    installdate: Optional[date] = Field(None, description="Installation date of the asset")
    purchasedate: Optional[date] = Field(None, description="Purchase date of the asset")
    purchasecost: Optional[Decimal] = Field(None, ge=0, description="Purchase cost of the asset")
    warrantyexpiration: Optional[date] = Field(None, description="Warranty expiration date of the asset")
    criticality: Optional[str] = Field(None, max_length=30, description="Criticality of the asset")
    description: Optional[str] = Field(None, description="Description of the asset")
    
    # Validators
    @field_validator('assetname')
    def validate_assetname(cls, v):
        """Validate asset name."""
        if not v or not v.strip():
            raise ValueError("Asset name cannot be empty")
        return v.strip()
    
    @field_validator('assettype')
    def validate_assettype(cls, v):
        """Validate asset type."""
        if not v or not v.strip():
            raise ValueError("Asset type cannot be empty")
        return v.strip()
    
    @field_validator('status')
    def validate_status(cls, v):
        """Validate asset status."""
        valid_statuses = ["Active", "Inactive", "Maintenance"]
        if v not in valid_statuses:
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")
        return v
    
    @field_validator('criticality')
    def validate_criticality(cls, v):
        """Validate asset criticality."""
        if v is None:
            return v
        
        valid_criticalities = ["High", "Medium", "Low"]
        if v not in valid_criticalities:
            raise ValueError(f"Criticality must be one of: {', '.join(valid_criticalities)}")
        return v
    
    @field_validator('purchasedate')
    def validate_purchasedate(cls, v):
        """Validate purchase date."""
        if v is None:
            return v
        
        if v > date.today():
            raise ValueError("Purchase date cannot be in the future")
        return v
    
    @field_validator('warrantyexpiration')
    def validate_warrantyexpiration(cls, v):
        """Validate warranty expiration date."""
        if v is None:
            return v
        
        if v < date.today():
            # This is just a warning, not an error
            pass
        return v
    
    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "assetname": "Pump Station 1",
                "assettype": "Equipment",
                "status": "Active",
                "location_id": 1,
                "system_id": 2,
                "category_id": 3,
                "manufacturer": "ABC Manufacturing",
                "model": "XYZ-123",
                "serialnumber": "SN12345",
                "purchasedate": "2023-01-15",
                "purchasecost": 5000.00,
                "criticality": "High",
                "description": "Main pump station for water treatment facility"
            }
        }
    )


class AssetCreate(AssetBase):
    """
    Schema for creating a new asset.
    """
    pass


class AssetUpdate(BaseModel):
    """
    Schema for updating an existing asset.
    """
    assetname: Optional[str] = Field(None, min_length=3, max_length=100, description="Name of the asset")
    assettype: Optional[str] = Field(None, min_length=1, max_length=50, description="Type of the asset")
    status: Optional[str] = Field(None, min_length=1, max_length=30, description="Status of the asset")
    location_id: Optional[int] = Field(None, description="ID of the asset location")
    system_id: Optional[int] = Field(None, description="ID of the asset system")
    category_id: Optional[int] = Field(None, description="ID of the asset category")
    parent_asset_id: Optional[int] = Field(None, description="ID of the parent asset")
    manufacturer: Optional[str] = Field(None, max_length=100, description="Manufacturer of the asset")
    model: Optional[str] = Field(None, max_length=100, description="Model of the asset")
    serialnumber: Optional[str] = Field(None, max_length=100, description="Serial number of the asset")
    installdate: Optional[date] = Field(None, description="Installation date of the asset")
    purchasedate: Optional[date] = Field(None, description="Purchase date of the asset")
    purchasecost: Optional[Decimal] = Field(None, ge=0, description="Purchase cost of the asset")
    warrantyexpiration: Optional[date] = Field(None, description="Warranty expiration date of the asset")
    criticality: Optional[str] = Field(None, max_length=30, description="Criticality of the asset")
    description: Optional[str] = Field(None, description="Description of the asset")
    
    # Validators
    @field_validator('assetname')
    def validate_assetname(cls, v):
        """Validate asset name."""
        if v is None:
            return v
        
        if not v.strip():
            raise ValueError("Asset name cannot be empty")
        return v.strip()
    
    @field_validator('assettype')
    def validate_assettype(cls, v):
        """Validate asset type."""
        if v is None:
            return v
        
        if not v.strip():
            raise ValueError("Asset type cannot be empty")
        return v.strip()
    
    @field_validator('status')
    def validate_status(cls, v):
        """Validate asset status."""
        if v is None:
            return v
        
        valid_statuses = ["Active", "Inactive", "Maintenance"]
        if v not in valid_statuses:
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")
        return v
    
    @field_validator('criticality')
    def validate_criticality(cls, v):
        """Validate asset criticality."""
        if v is None:
            return v
        
        valid_criticalities = ["High", "Medium", "Low"]
        if v not in valid_criticalities:
            raise ValueError(f"Criticality must be one of: {', '.join(valid_criticalities)}")
        return v
    
    @field_validator('purchasedate')
    def validate_purchasedate(cls, v):
        """Validate purchase date."""
        if v is None:
            return v
        
        if v > date.today():
            raise ValueError("Purchase date cannot be in the future")
        return v
    
    @field_validator('warrantyexpiration')
    def validate_warrantyexpiration(cls, v):
        """Validate warranty expiration date."""
        if v is None:
            return v
        
        if v < date.today():
            # This is just a warning, not an error
            pass
        return v
    
    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "assetname": "Pump Station 1",
                "status": "Maintenance",
                "manufacturer": "ABC Manufacturing",
                "description": "Updated description"
            }
        }
    )


class Asset(AssetBase):
    """
    Schema for asset data.
    """
    assetid: int = Field(..., description="ID of the asset")
    
    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "assetid": 1,
                "assetname": "Pump Station 1",
                "assettype": "Equipment",
                "status": "Active",
                "location_id": 1,
                "system_id": 2,
                "category_id": 3,
                "manufacturer": "ABC Manufacturing",
                "model": "XYZ-123",
                "serialnumber": "SN12345",
                "purchasedate": "2023-01-15",
                "purchasecost": 5000.00,
                "criticality": "High",
                "description": "Main pump station for water treatment facility"
            }
        }
    )


class AssetResponse(BaseModel):
    """
    Schema for asset response.
    """
    success: bool = Field(..., description="Whether the operation was successful")
    message: Optional[str] = Field(None, description="Message about the operation")
    asset: Optional[Asset] = Field(None, description="Asset data")
    errors: Optional[dict] = Field(None, description="Validation errors")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": "Asset created successfully",
                "asset": {
                    "assetid": 1,
                    "assetname": "Pump Station 1",
                    "assettype": "Equipment",
                    "status": "Active",
                    "location_id": 1
                }
            }
        }
    )


class AssetListResponse(BaseModel):
    """
    Schema for asset list response.
    """
    success: bool = Field(..., description="Whether the operation was successful")
    message: Optional[str] = Field(None, description="Message about the operation")
    assets: List[Asset] = Field(..., description="List of assets")
    total: int = Field(..., description="Total number of assets")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": "Assets retrieved successfully",
                "assets": [
                    {
                        "assetid": 1,
                        "assetname": "Pump Station 1",
                        "assettype": "Equipment",
                        "status": "Active",
                        "location_id": 1
                    },
                    {
                        "assetid": 2,
                        "assetname": "Pump Station 2",
                        "assettype": "Equipment",
                        "status": "Maintenance",
                        "location_id": 1
                    }
                ],
                "total": 2
            }
        }
    )
