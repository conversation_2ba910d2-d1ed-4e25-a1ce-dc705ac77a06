from fastapi import FastAP<PERSON>
from fastapi.openapi.utils import get_openapi

app = FastAPI(
    title="AssetKPI - Intelligent KPI & Inventory Optimization System",
    description="""AssetKPI provides a comprehensive API for asset management, inventory optimization, and KPI calculation.

## Features

* **Asset Management**: Track and manage assets, their specifications, and performance metrics
* **Inventory Optimization**: Calculate EOQ, safety stock, and generate inventory recommendations
* **KPI Calculation**: Calculate and track maintenance KPIs like MTTR, MTBF, and failure rate
* **Work Order Management**: Create and manage maintenance work orders
* **User Management**: Manage users, roles, and permissions
* **Usage Analytics**: Track and analyze user activity and feature usage

## Authentication

The API supports two authentication methods:

* **Firebase Authentication**: For browser-based applications and user-specific operations
* **API Key Authentication**: For server-to-server communication and automated processes

## Rate Limiting

API requests are subject to rate limiting to ensure system stability:

* 100 requests per minute for authenticated users (Firebase token)
* 200 requests per minute for API key authentication

## Versioning

The current version is v0.7.0. All endpoints are prefixed with `/api`.
    """,
    version="0.7.0",
    openapi_tags=[
        {
            "name": "Authentication",
            "description": "Operations related to authentication and authorization",
        },
        {
            "name": "Assets",
            "description": "Operations for managing assets and their specifications",
        },
        {
            "name": "Inventory",
            "description": "Operations for managing inventory items and optimization",
        },
        {
            "name": "KPIs",
            "description": "Operations for calculating and retrieving KPIs",
        },
        {
            "name": "Work Orders",
            "description": "Operations for managing maintenance work orders",
        },
        {
            "name": "Users",
            "description": "Operations for managing users, roles, and permissions",
        },
        {
            "name": "Analytics",
            "description": "Operations for retrieving analytics data",
        },
        {
            "name": "Dashboard",
            "description": "Operations for dashboard data retrieval",
        },
        {
            "name": "Ingestion",
            "description": "Operations for data ingestion",
        },
    ],
    contact={
        "name": "AssetKPI Support",
        "url": "https://assetkpi.example.com/support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
)

@app.get("/test", tags=["Authentication"])
async def test_endpoint():
    return {"message": "Hello World"}

# Custom OpenAPI schema generator to add more detailed examples and responses
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
        tags=app.openapi_tags,
        servers=app.servers,
        terms_of_service=app.terms_of_service,
        contact=app.contact,
        license_info=app.license_info,
    )

    # Add common responses to all endpoints
    for path in openapi_schema["paths"]:
        for method in openapi_schema["paths"][path]:
            if method.lower() in ["get", "post", "put", "delete", "patch"]:
                # Add authentication error responses
                if "responses" not in openapi_schema["paths"][path][method]:
                    openapi_schema["paths"][path][method]["responses"] = {}
                
                # Add common error responses if they don't exist
                for status_code in ["401", "403", "404", "429", "500"]:
                    if status_code not in openapi_schema["paths"][path][method]["responses"]:
                        # Define standard error responses
                        if status_code == "401":
                            response = {
                                "description": "Unauthorized - Authentication credentials were not provided or are invalid",
                                "content": {
                                    "application/json": {
                                        "example": {"detail": "Could not validate credentials"}
                                    }
                                }
                            }
                        elif status_code == "403":
                            response = {
                                "description": "Forbidden - You do not have permission to perform this action",
                                "content": {
                                    "application/json": {
                                        "example": {"detail": "Not enough permissions"}
                                    }
                                }
                            }
                        elif status_code == "404":
                            response = {
                                "description": "Not Found - The requested resource was not found",
                                "content": {
                                    "application/json": {
                                        "example": {"detail": "Resource not found"}
                                    }
                                }
                            }
                        elif status_code == "429":
                            response = {
                                "description": "Too Many Requests - Rate limit exceeded",
                                "content": {
                                    "application/json": {
                                        "example": {"detail": "Rate limit exceeded. Please try again later."}
                                    }
                                }
                            }
                        else:  # 500
                            response = {
                                "description": "Internal Server Error - An unexpected error occurred",
                                "content": {
                                    "application/json": {
                                        "example": {"detail": "An unexpected error occurred"}
                                    }
                                }
                            }
                        
                        openapi_schema["paths"][path][method]["responses"][status_code] = response
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8002)
