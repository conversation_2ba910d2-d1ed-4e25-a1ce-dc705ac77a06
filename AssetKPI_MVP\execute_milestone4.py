import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def execute_migration():
    """Execute the SQL migration script for Milestone 4."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Read the SQL migration script
        with open('db_migration_milestone4.sql', 'r') as f:
            sql_script = f.read()
        
        # Split the script into individual statements
        statements = sql_script.split(';')
        
        # Execute each statement
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement + ';')
                    print(f"Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"Error executing statement: {statement[:50]}...")
                    print(f"Error: {e}")
        
        # Commit the changes
        conn.commit()
        print("Milestone 4 migration completed successfully!")
        
        # Return the connection and cursor for data population
        return conn, cursor
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return None, None

def populate_sample_data(conn, cursor):
    """Populate the database with sample data for Milestone 4."""
    try:
        print("\nPopulating sample data for Milestone 4...")
        
        # Get all existing assets
        cursor.execute("SELECT assetid, assetname, assettype FROM assets")
        assets = cursor.fetchall()
        
        if not assets:
            print("No assets found in the database. Please add assets first.")
            return
        
        # Sample PM schedule templates
        pm_schedule_templates = [
            # (name_prefix, frequency_type, frequency_value, frequency_unit)
            ('Monthly Inspection', 'Calendar', 30, 'Days'),
            ('Quarterly Maintenance', 'Calendar', 90, 'Days'),
            ('Semi-Annual Service', 'Calendar', 180, 'Days'),
            ('Annual Overhaul', 'Calendar', 365, 'Days'),
            ('Runtime Based Maintenance', 'Meter', 500, 'Hours'),
            ('Cycle Based Inspection', 'Meter', 1000, 'Cycles'),
            ('Distance Based Service', 'Meter', 5000, 'Miles'),
            ('Production Based Maintenance', 'Meter', 10000, 'Units')
        ]
        
        # Sample meter types based on asset type
        meter_templates = {
            'Pump': [
                ('Runtime', 'Hours', 'Runtime Hours'),
                ('Flow', 'Gallons', 'Total Flow'),
                ('Pressure', 'PSI', 'Operating Pressure')
            ],
            'Motor': [
                ('Runtime', 'Hours', 'Runtime Hours'),
                ('Starts', 'Count', 'Number of Starts'),
                ('Temperature', 'Celsius', 'Operating Temperature')
            ],
            'Conveyor': [
                ('Runtime', 'Hours', 'Runtime Hours'),
                ('Distance', 'Feet', 'Total Distance'),
                ('Load', 'Tons', 'Total Load Carried')
            ],
            'HVAC': [
                ('Runtime', 'Hours', 'Runtime Hours'),
                ('Cycles', 'Count', 'Number of Cycles'),
                ('Temperature', 'Celsius', 'Operating Temperature')
            ],
            'Generator': [
                ('Runtime', 'Hours', 'Runtime Hours'),
                ('Power', 'kWh', 'Total Power Generated'),
                ('Fuel', 'Gallons', 'Fuel Consumed')
            ],
            'default': [
                ('Runtime', 'Hours', 'Runtime Hours'),
                ('Cycles', 'Count', 'Number of Cycles')
            ]
        }
        
        # Sample PM job tasks
        pm_task_templates = {
            'Inspection': [
                'Visually inspect for damage or wear',
                'Check for loose connections',
                'Inspect for leaks',
                'Check alignment',
                'Verify proper operation'
            ],
            'Lubrication': [
                'Lubricate bearings',
                'Check oil levels',
                'Change oil',
                'Grease fittings',
                'Apply lubricant to moving parts'
            ],
            'Cleaning': [
                'Clean exterior surfaces',
                'Clean filters',
                'Remove debris',
                'Clean electrical contacts',
                'Flush cooling system'
            ],
            'Testing': [
                'Test operation',
                'Perform load test',
                'Check safety systems',
                'Verify alarms',
                'Test backup systems'
            ],
            'Adjustment': [
                'Adjust tension',
                'Calibrate sensors',
                'Adjust clearances',
                'Balance rotating components',
                'Tune control parameters'
            ]
        }
        
        print("Creating PM schedules, job plans, and tasks...")
        
        # Create PM schedules and job plans for each asset
        for asset_id, asset_name, asset_type in assets:
            # Determine how many PM schedules to create for this asset (1-3)
            num_schedules = random.randint(1, 3)
            
            for i in range(num_schedules):
                # Pick a random schedule template
                template = random.choice(pm_schedule_templates)
                schedule_name = f"{template[0]} - {asset_name}"
                frequency_type = template[1]
                frequency_value = template[2]
                frequency_unit = template[3]
                
                # Calculate last completed and next due dates
                today = datetime.now()
                days_since_last = random.randint(0, frequency_value)
                last_completed_date = today - timedelta(days=days_since_last)
                next_due_date = last_completed_date + timedelta(days=frequency_value)
                
                # Create the PM schedule
                cursor.execute(
                    """
                    INSERT INTO pm_schedules
                    (asset_id, schedule_name, frequency_type, frequency_value, frequency_unit, 
                     last_completed_date, next_due_date, enabled)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING schedule_id
                    """,
                    (asset_id, schedule_name, frequency_type, frequency_value, frequency_unit, 
                     last_completed_date, next_due_date, True)
                )
                schedule_id = cursor.fetchone()[0]
                
                # Create a job plan for this schedule
                plan_name = f"{template[0]} Plan for {asset_name}"
                description = f"Standard {template[0].lower()} procedure for {asset_type if asset_type else 'equipment'}"
                estimated_duration = random.randint(30, 240)  # 30 minutes to 4 hours
                safety_instructions = "Wear appropriate PPE. Follow lockout/tagout procedures. Ensure area is clear of personnel."
                
                cursor.execute(
                    """
                    INSERT INTO pm_job_plans
                    (schedule_id, plan_name, description, estimated_duration, safety_instructions)
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING plan_id
                    """,
                    (schedule_id, plan_name, description, estimated_duration, safety_instructions)
                )
                plan_id = cursor.fetchone()[0]
                
                # Create tasks for this job plan
                # Determine which task categories to include
                task_categories = random.sample(list(pm_task_templates.keys()), 
                                              k=random.randint(2, len(pm_task_templates)))
                
                sequence = 1
                for category in task_categories:
                    # Pick 1-3 tasks from this category
                    tasks = random.sample(pm_task_templates[category], 
                                         k=random.randint(1, min(3, len(pm_task_templates[category]))))
                    
                    for task in tasks:
                        task_description = f"{category}: {task}"
                        estimated_hours = round(random.uniform(0.25, 1.5), 2)
                        required_tools = "Basic hand tools, multimeter, torque wrench"
                        required_parts = "Filters, lubricant, gaskets as needed"
                        
                        cursor.execute(
                            """
                            INSERT INTO pm_job_tasks
                            (plan_id, task_description, sequence_number, estimated_hours, required_tools, required_parts)
                            VALUES (%s, %s, %s, %s, %s, %s)
                            """,
                            (plan_id, task_description, sequence, estimated_hours, required_tools, required_parts)
                        )
                        
                        sequence += 1
                
                print(f"  - Created PM schedule '{schedule_name}' with job plan and {sequence-1} tasks")
        
        print("Creating asset meters and readings...")
        
        # Create meters for each asset
        for asset_id, asset_name, asset_type in assets:
            # Determine which meter template to use
            template_key = 'default'
            for key in meter_templates.keys():
                if asset_type and key.lower() in asset_type.lower():
                    template_key = key
                    break
            
            # Get the template
            template = meter_templates[template_key]
            
            # Determine how many meters to create for this asset (1-3)
            num_meters = random.randint(1, min(3, len(template)))
            
            # Pick random meters from the template
            selected_meters = random.sample(template, k=num_meters)
            
            for meter_type, unit, meter_name in selected_meters:
                # Create the meter
                current_reading = round(random.uniform(100, 10000), 2)
                last_reading_date = datetime.now() - timedelta(days=random.randint(1, 30))
                
                cursor.execute(
                    """
                    INSERT INTO asset_meters
                    (asset_id, meter_name, meter_type, unit_of_measure, current_reading, last_reading_date)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING meter_id
                    """,
                    (asset_id, meter_name, meter_type, unit, current_reading, last_reading_date)
                )
                meter_id = cursor.fetchone()[0]
                
                # Create historical readings for this meter
                num_readings = random.randint(5, 20)
                
                # Start with the current reading and work backwards
                reading_value = current_reading
                reading_date = last_reading_date
                
                for _ in range(num_readings):
                    # Decrease the reading as we go back in time
                    reading_value -= round(random.uniform(10, 100), 2)
                    if reading_value < 0:
                        reading_value = 0
                    
                    # Go back in time
                    reading_date -= timedelta(days=random.randint(5, 30))
                    
                    # Random person who entered the reading
                    entered_by = random.choice(['John Smith', 'Jane Doe', 'Bob Johnson', 'Alice Brown'])
                    
                    cursor.execute(
                        """
                        INSERT INTO meter_readings
                        (meter_id, reading_value, reading_date, entered_by)
                        VALUES (%s, %s, %s, %s)
                        """,
                        (meter_id, reading_value, reading_date, entered_by)
                    )
                
                print(f"  - Created meter '{meter_name}' for asset '{asset_name}' with {num_readings} historical readings")
        
        # Commit the changes
        conn.commit()
        print("Sample data population completed successfully!")
        
    except Exception as e:
        print(f"Error populating sample data: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()

def main():
    """Main function to execute migration and populate data."""
    conn, cursor = execute_migration()
    
    if conn and cursor:
        populate_sample_data(conn, cursor)
        cursor.close()
        conn.close()
    
    print("Milestone 4 completed!")

if __name__ == "__main__":
    main()
