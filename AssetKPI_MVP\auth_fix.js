// Function to update UI based on authentication state
function updateAuth<PERSON>(user) {
    if (user) {
        // User is authenticated
        authRequiredElements.forEach(el => {
            el.style.display = 'block';
        });
        authNotRequiredElements.forEach(el => {
            el.style.display = 'none';
        });

        // Update user info
        userEmail.textContent = user.email;
        userAvatar.textContent = getInitials(user.email);

        // Fetch user role from API
        AssetKPIAuth.authenticatedFetch('/api/users/me')
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error('Failed to fetch user info');
            })
            .then(data => {
                userRole.textContent = data.role || 'Unknown Role';

                // Update navigation based on role
                const userRoleValue = data.role || '';
                authRequiredElements.forEach(el => {
                    const requiredRoles = (el.dataset.role || '').split(',');
                    if (requiredRoles.length === 0 || requiredRoles.includes(userRoleValue)) {
                        el.style.display = 'block';
                    } else {
                        el.style.display = 'none';
                    }
                });
            })
            .catch(error => {
                console.error('Error fetching user info:', error);
                userRole.textContent = 'Role: Unknown';
            });
    } else {
        // User is not authenticated
        authRequiredElements.forEach(el => {
            el.style.display = 'none';
        });
        authNotRequiredElements.forEach(el => {
            el.style.display = 'block';
        });
    }
}
