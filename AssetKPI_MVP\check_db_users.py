import psycopg2
import json

# Connect to the database
conn = psycopg2.connect("postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
cur = conn.cursor()

# Get the column names of the users table
cur.execute("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'users' ORDER BY ordinal_position")
columns = cur.fetchall()
print("Columns in users table:")
for column in columns:
    print(f"{column[0]} ({column[1]})")

# Get sample data from the users table
cur.execute("SELECT * FROM users LIMIT 5")
rows = cur.fetchall()
column_names = [desc[0] for desc in cur.description]

print("\nSample data from users table:")
for row in rows:
    user_data = dict(zip(column_names, row))
    print(json.dumps(user_data, default=str, indent=2))

# Close the connection
cur.close()
conn.close()
