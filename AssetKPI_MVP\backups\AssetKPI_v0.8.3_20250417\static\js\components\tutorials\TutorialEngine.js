/**
 * Tutorial Engine
 * 
 * This class manages interactive tutorials in the AssetKPI application.
 * It handles tutorial loading, step navigation, element targeting, and user interactions.
 */

class TutorialEngine {
    /**
     * Initialize the tutorial engine.
     * 
     * @param {Object} options - Configuration options
     * @param {string} options.containerId - ID of the container element for tutorial UI
     * @param {boolean} options.autoStart - Whether to automatically start tutorials for new features
     * @param {Function} options.onComplete - Callback function when a tutorial is completed
     */
    constructor(options = {}) {
        this.containerId = options.containerId || 'tutorial-container';
        this.autoStart = options.autoStart !== undefined ? options.autoStart : true;
        this.onComplete = options.onComplete || function() {};
        
        // Initialize state
        this.tutorials = {};
        this.currentTutorial = null;
        this.currentStepIndex = 0;
        this.isActive = false;
        
        // UI elements
        this.container = null;
        this.overlay = null;
        this.tooltip = null;
        this.highlight = null;
        this.progressIndicator = null;
        
        // Event handlers
        this.eventHandlers = {};
        
        // Load user preferences and progress
        this.loadUserPreferences();
        this.loadTutorialProgress();
    }
    
    /**
     * Initialize the tutorial engine.
     */
    init() {
        // Create container if it doesn't exist
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = this.containerId;
            document.body.appendChild(this.container);
        }
        
        // Create UI elements
        this.createUIElements();
        
        // Add event listeners
        this.addEventListeners();
        
        // Check for auto-start tutorials
        if (this.autoStart) {
            this.checkForAutoStartTutorials();
        }
    }
    
    /**
     * Create UI elements for tutorials.
     */
    createUIElements() {
        // Create overlay
        this.overlay = document.createElement('div');
        this.overlay.className = 'tutorial-overlay';
        this.container.appendChild(this.overlay);
        
        // Create tooltip
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'tutorial-tooltip';
        this.tooltip.innerHTML = `
            <div class="tutorial-tooltip-header">
                <h5 class="tutorial-title"></h5>
                <button class="tutorial-close-btn">&times;</button>
            </div>
            <div class="tutorial-tooltip-body"></div>
            <div class="tutorial-tooltip-footer">
                <button class="tutorial-prev-btn">Previous</button>
                <div class="tutorial-progress"></div>
                <button class="tutorial-next-btn">Next</button>
            </div>
        `;
        this.container.appendChild(this.tooltip);
        
        // Create highlight
        this.highlight = document.createElement('div');
        this.highlight.className = 'tutorial-highlight';
        this.container.appendChild(this.highlight);
        
        // Create progress indicator
        this.progressIndicator = document.createElement('div');
        this.progressIndicator.className = 'tutorial-progress-indicator';
        this.container.appendChild(this.progressIndicator);
        
        // Hide all elements initially
        this.hideUIElements();
    }
    
    /**
     * Add event listeners to UI elements.
     */
    addEventListeners() {
        // Close button
        const closeBtn = this.tooltip.querySelector('.tutorial-close-btn');
        closeBtn.addEventListener('click', () => {
            this.endTutorial();
        });
        
        // Previous button
        const prevBtn = this.tooltip.querySelector('.tutorial-prev-btn');
        prevBtn.addEventListener('click', () => {
            this.previousStep();
        });
        
        // Next button
        const nextBtn = this.tooltip.querySelector('.tutorial-next-btn');
        nextBtn.addEventListener('click', () => {
            this.nextStep();
        });
        
        // Overlay click
        this.overlay.addEventListener('click', (event) => {
            // Allow clicks on the highlighted element
            if (event.target === this.overlay) {
                // Do nothing, prevent closing on overlay click
            }
        });
        
        // Window resize
        window.addEventListener('resize', () => {
            if (this.isActive) {
                this.positionUIElements();
            }
        });
        
        // Page navigation
        window.addEventListener('popstate', () => {
            if (this.isActive) {
                this.checkStepValidity();
            }
        });
    }
    
    /**
     * Hide all UI elements.
     */
    hideUIElements() {
        this.overlay.style.display = 'none';
        this.tooltip.style.display = 'none';
        this.highlight.style.display = 'none';
        this.progressIndicator.style.display = 'none';
    }
    
    /**
     * Show UI elements.
     */
    showUIElements() {
        this.overlay.style.display = 'block';
        this.tooltip.style.display = 'block';
        this.progressIndicator.style.display = 'block';
    }
    
    /**
     * Load a tutorial by ID.
     * 
     * @param {string} tutorialId - ID of the tutorial to load
     * @returns {Promise<boolean>} - Whether the tutorial was loaded successfully
     */
    async loadTutorial(tutorialId) {
        try {
            // Check if tutorial is already loaded
            if (this.tutorials[tutorialId]) {
                return true;
            }
            
            // Fetch tutorial data
            const response = await fetch(`/api/tutorials/${tutorialId}`);
            if (!response.ok) {
                throw new Error(`Failed to load tutorial: ${response.statusText}`);
            }
            
            const tutorialData = await response.json();
            this.tutorials[tutorialId] = tutorialData;
            
            return true;
        } catch (error) {
            console.error(`Error loading tutorial ${tutorialId}:`, error);
            return false;
        }
    }
    
    /**
     * Start a tutorial.
     * 
     * @param {string} tutorialId - ID of the tutorial to start
     * @param {number} stepIndex - Optional step index to start from
     * @returns {Promise<boolean>} - Whether the tutorial was started successfully
     */
    async startTutorial(tutorialId, stepIndex = 0) {
        // Load tutorial if not already loaded
        const loaded = await this.loadTutorial(tutorialId);
        if (!loaded) {
            return false;
        }
        
        // Set current tutorial and step
        this.currentTutorial = this.tutorials[tutorialId];
        this.currentStepIndex = stepIndex;
        this.isActive = true;
        
        // Show UI elements
        this.showUIElements();
        
        // Show current step
        this.showCurrentStep();
        
        // Update progress
        this.updateProgress();
        
        // Save tutorial state
        this.saveTutorialProgress();
        
        return true;
    }
    
    /**
     * End the current tutorial.
     */
    endTutorial() {
        // Hide UI elements
        this.hideUIElements();
        
        // Remove event handlers
        this.removeEventHandlers();
        
        // Reset state
        this.isActive = false;
        
        // Save tutorial progress
        this.saveTutorialProgress();
    }
    
    /**
     * Show the current tutorial step.
     */
    showCurrentStep() {
        if (!this.currentTutorial || !this.isActive) {
            return;
        }
        
        const step = this.currentTutorial.steps[this.currentStepIndex];
        if (!step) {
            return;
        }
        
        // Update tooltip content
        const titleEl = this.tooltip.querySelector('.tutorial-title');
        const bodyEl = this.tooltip.querySelector('.tutorial-tooltip-body');
        
        titleEl.textContent = step.title || '';
        bodyEl.innerHTML = step.content || '';
        
        // Find target element
        const targetEl = document.querySelector(step.target);
        if (!targetEl) {
            console.warn(`Target element not found: ${step.target}`);
            this.positionTooltip(null, step.position || 'center');
            return;
        }
        
        // Highlight target element
        if (step.highlight) {
            this.highlightElement(targetEl);
        } else {
            this.highlight.style.display = 'none';
        }
        
        // Position tooltip
        this.positionTooltip(targetEl, step.position || 'bottom');
        
        // Scroll to target if needed
        this.scrollToElement(targetEl);
        
        // Add event handlers for actions
        this.addStepEventHandlers(step);
        
        // Update navigation buttons
        this.updateNavigationButtons();
    }
    
    /**
     * Highlight a target element.
     * 
     * @param {HTMLElement} targetEl - Element to highlight
     */
    highlightElement(targetEl) {
        if (!targetEl) {
            this.highlight.style.display = 'none';
            return;
        }
        
        const rect = targetEl.getBoundingClientRect();
        
        // Position highlight
        this.highlight.style.top = `${rect.top + window.scrollY}px`;
        this.highlight.style.left = `${rect.left + window.scrollX}px`;
        this.highlight.style.width = `${rect.width}px`;
        this.highlight.style.height = `${rect.height}px`;
        this.highlight.style.display = 'block';
    }
    
    /**
     * Position the tooltip relative to a target element.
     * 
     * @param {HTMLElement} targetEl - Target element
     * @param {string} position - Position (top, right, bottom, left, center)
     */
    positionTooltip(targetEl, position) {
        if (!targetEl) {
            // Center in viewport if no target
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const tooltipWidth = this.tooltip.offsetWidth;
            const tooltipHeight = this.tooltip.offsetHeight;
            
            this.tooltip.style.top = `${(viewportHeight - tooltipHeight) / 2 + window.scrollY}px`;
            this.tooltip.style.left = `${(viewportWidth - tooltipWidth) / 2 + window.scrollX}px`;
            this.tooltip.classList.remove('tooltip-top', 'tooltip-right', 'tooltip-bottom', 'tooltip-left');
            this.tooltip.classList.add('tooltip-center');
            return;
        }
        
        const targetRect = targetEl.getBoundingClientRect();
        const tooltipWidth = this.tooltip.offsetWidth;
        const tooltipHeight = this.tooltip.offsetHeight;
        const margin = 10; // Margin between tooltip and target
        
        let top, left;
        
        // Remove all position classes
        this.tooltip.classList.remove('tooltip-top', 'tooltip-right', 'tooltip-bottom', 'tooltip-left', 'tooltip-center');
        
        // Position based on specified position
        switch (position) {
            case 'top':
                top = targetRect.top + window.scrollY - tooltipHeight - margin;
                left = targetRect.left + window.scrollX + (targetRect.width - tooltipWidth) / 2;
                this.tooltip.classList.add('tooltip-top');
                break;
            
            case 'right':
                top = targetRect.top + window.scrollY + (targetRect.height - tooltipHeight) / 2;
                left = targetRect.right + window.scrollX + margin;
                this.tooltip.classList.add('tooltip-right');
                break;
            
            case 'bottom':
                top = targetRect.bottom + window.scrollY + margin;
                left = targetRect.left + window.scrollX + (targetRect.width - tooltipWidth) / 2;
                this.tooltip.classList.add('tooltip-bottom');
                break;
            
            case 'left':
                top = targetRect.top + window.scrollY + (targetRect.height - tooltipHeight) / 2;
                left = targetRect.left + window.scrollX - tooltipWidth - margin;
                this.tooltip.classList.add('tooltip-left');
                break;
            
            default:
                // Default to bottom
                top = targetRect.bottom + window.scrollY + margin;
                left = targetRect.left + window.scrollX + (targetRect.width - tooltipWidth) / 2;
                this.tooltip.classList.add('tooltip-bottom');
                break;
        }
        
        // Ensure tooltip is within viewport
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        if (left < 0) {
            left = margin;
        } else if (left + tooltipWidth > viewportWidth) {
            left = viewportWidth - tooltipWidth - margin;
        }
        
        if (top < 0) {
            top = margin;
        } else if (top + tooltipHeight > viewportHeight + window.scrollY) {
            top = viewportHeight + window.scrollY - tooltipHeight - margin;
        }
        
        // Set position
        this.tooltip.style.top = `${top}px`;
        this.tooltip.style.left = `${left}px`;
    }
    
    /**
     * Scroll to an element if it's not fully visible.
     * 
     * @param {HTMLElement} targetEl - Element to scroll to
     */
    scrollToElement(targetEl) {
        if (!targetEl) {
            return;
        }
        
        const rect = targetEl.getBoundingClientRect();
        const isVisible = (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= window.innerHeight &&
            rect.right <= window.innerWidth
        );
        
        if (!isVisible) {
            targetEl.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }
    
    /**
     * Add event handlers for step actions.
     * 
     * @param {Object} step - Step data
     */
    addStepEventHandlers(step) {
        // Remove existing handlers
        this.removeEventHandlers();
        
        if (!step.action) {
            return;
        }
        
        const targetEl = document.querySelector(step.action.element);
        if (!targetEl) {
            return;
        }
        
        switch (step.action.type) {
            case 'click':
                // Add click handler
                const clickHandler = () => {
                    // If we need to wait for an element or page, don't advance automatically
                    if (!step.waitForElement && !step.waitForPage) {
                        setTimeout(() => {
                            this.nextStep();
                        }, 500);
                    }
                };
                
                targetEl.addEventListener('click', clickHandler);
                this.eventHandlers.click = {
                    element: targetEl,
                    handler: clickHandler
                };
                
                // If waiting for element
                if (step.waitForElement) {
                    const observer = new MutationObserver((mutations, obs) => {
                        const element = document.querySelector(step.waitForElement);
                        if (element) {
                            obs.disconnect();
                            setTimeout(() => {
                                this.nextStep();
                            }, 500);
                        }
                    });
                    
                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });
                    
                    this.eventHandlers.observer = observer;
                }
                
                // If waiting for page
                if (step.waitForPage) {
                    const pageChangeHandler = () => {
                        if (window.location.pathname === step.waitForPage) {
                            setTimeout(() => {
                                this.nextStep();
                            }, 1000);
                            window.removeEventListener('popstate', pageChangeHandler);
                        }
                    };
                    
                    window.addEventListener('popstate', pageChangeHandler);
                    this.eventHandlers.popstate = {
                        element: window,
                        handler: pageChangeHandler
                    };
                }
                break;
            
            case 'input':
                // Add input handler
                const inputHandler = (event) => {
                    if (step.action.value && event.target.value === step.action.value) {
                        setTimeout(() => {
                            this.nextStep();
                        }, 500);
                    }
                };
                
                targetEl.addEventListener('input', inputHandler);
                this.eventHandlers.input = {
                    element: targetEl,
                    handler: inputHandler
                };
                break;
            
            case 'hover':
                // Add hover handler
                const hoverHandler = () => {
                    setTimeout(() => {
                        this.nextStep();
                    }, 500);
                };
                
                targetEl.addEventListener('mouseenter', hoverHandler);
                this.eventHandlers.hover = {
                    element: targetEl,
                    handler: hoverHandler
                };
                break;
        }
    }
    
    /**
     * Remove event handlers.
     */
    removeEventHandlers() {
        // Remove click handler
        if (this.eventHandlers.click) {
            this.eventHandlers.click.element.removeEventListener(
                'click',
                this.eventHandlers.click.handler
            );
            delete this.eventHandlers.click;
        }
        
        // Remove input handler
        if (this.eventHandlers.input) {
            this.eventHandlers.input.element.removeEventListener(
                'input',
                this.eventHandlers.input.handler
            );
            delete this.eventHandlers.input;
        }
        
        // Remove hover handler
        if (this.eventHandlers.hover) {
            this.eventHandlers.hover.element.removeEventListener(
                'mouseenter',
                this.eventHandlers.hover.handler
            );
            delete this.eventHandlers.hover;
        }
        
        // Remove popstate handler
        if (this.eventHandlers.popstate) {
            this.eventHandlers.popstate.element.removeEventListener(
                'popstate',
                this.eventHandlers.popstate.handler
            );
            delete this.eventHandlers.popstate;
        }
        
        // Disconnect observer
        if (this.eventHandlers.observer) {
            this.eventHandlers.observer.disconnect();
            delete this.eventHandlers.observer;
        }
    }
    
    /**
     * Update navigation buttons based on current step.
     */
    updateNavigationButtons() {
        const prevBtn = this.tooltip.querySelector('.tutorial-prev-btn');
        const nextBtn = this.tooltip.querySelector('.tutorial-next-btn');
        
        // Previous button
        if (this.currentStepIndex === 0) {
            prevBtn.disabled = true;
        } else {
            prevBtn.disabled = false;
        }
        
        // Next button
        if (this.currentStepIndex === this.currentTutorial.steps.length - 1) {
            nextBtn.textContent = 'Finish';
        } else {
            nextBtn.textContent = 'Next';
        }
    }
    
    /**
     * Update progress indicator.
     */
    updateProgress() {
        if (!this.currentTutorial) {
            return;
        }
        
        const progressEl = this.tooltip.querySelector('.tutorial-progress');
        progressEl.textContent = `${this.currentStepIndex + 1} / ${this.currentTutorial.steps.length}`;
    }
    
    /**
     * Go to the next step.
     */
    nextStep() {
        if (!this.currentTutorial || !this.isActive) {
            return;
        }
        
        // Check if this is the last step
        if (this.currentStepIndex === this.currentTutorial.steps.length - 1) {
            this.completeTutorial();
            return;
        }
        
        // Go to next step
        this.currentStepIndex++;
        
        // Show current step
        this.showCurrentStep();
        
        // Update progress
        this.updateProgress();
        
        // Save tutorial progress
        this.saveTutorialProgress();
    }
    
    /**
     * Go to the previous step.
     */
    previousStep() {
        if (!this.currentTutorial || !this.isActive || this.currentStepIndex === 0) {
            return;
        }
        
        // Go to previous step
        this.currentStepIndex--;
        
        // Show current step
        this.showCurrentStep();
        
        // Update progress
        this.updateProgress();
        
        // Save tutorial progress
        this.saveTutorialProgress();
    }
    
    /**
     * Complete the current tutorial.
     */
    completeTutorial() {
        if (!this.currentTutorial || !this.isActive) {
            return;
        }
        
        // Mark tutorial as completed
        const tutorialId = this.currentTutorial.id;
        const completedTutorials = this.getCompletedTutorials();
        if (!completedTutorials.includes(tutorialId)) {
            completedTutorials.push(tutorialId);
            localStorage.setItem('completedTutorials', JSON.stringify(completedTutorials));
        }
        
        // End tutorial
        this.endTutorial();
        
        // Call onComplete callback
        this.onComplete(tutorialId);
        
        // Save tutorial completion to server
        this.saveTutorialCompletion(tutorialId);
    }
    
    /**
     * Check if a tutorial has been completed.
     * 
     * @param {string} tutorialId - ID of the tutorial
     * @returns {boolean} - Whether the tutorial has been completed
     */
    isTutorialCompleted(tutorialId) {
        const completedTutorials = this.getCompletedTutorials();
        return completedTutorials.includes(tutorialId);
    }
    
    /**
     * Get completed tutorials.
     * 
     * @returns {string[]} - Array of completed tutorial IDs
     */
    getCompletedTutorials() {
        const completedTutorialsJson = localStorage.getItem('completedTutorials');
        return completedTutorialsJson ? JSON.parse(completedTutorialsJson) : [];
    }
    
    /**
     * Check for tutorials that should auto-start.
     */
    checkForAutoStartTutorials() {
        // Check if user is new
        const isNewUser = localStorage.getItem('isNewUser') === 'true';
        
        // Check current page
        const currentPath = window.location.pathname;
        
        // Auto-start tutorial based on page
        if (currentPath === '/assets' && !this.isTutorialCompleted('asset-management')) {
            this.startTutorial('asset-management');
        } else if (currentPath === '/inventory' && !this.isTutorialCompleted('inventory-management')) {
            this.startTutorial('inventory-management');
        }
    }
    
    /**
     * Check if the current step is still valid.
     * For example, if the user navigated to a different page.
     */
    checkStepValidity() {
        if (!this.currentTutorial || !this.isActive) {
            return;
        }
        
        const step = this.currentTutorial.steps[this.currentStepIndex];
        if (!step) {
            return;
        }
        
        // Check if target element exists
        const targetEl = document.querySelector(step.target);
        if (!targetEl) {
            // Target element not found, end tutorial
            this.endTutorial();
        }
    }
    
    /**
     * Load user preferences.
     */
    loadUserPreferences() {
        const preferencesJson = localStorage.getItem('tutorialPreferences');
        if (preferencesJson) {
            const preferences = JSON.parse(preferencesJson);
            this.autoStart = preferences.autoStart !== undefined ? preferences.autoStart : this.autoStart;
        }
    }
    
    /**
     * Save user preferences.
     */
    saveUserPreferences() {
        const preferences = {
            autoStart: this.autoStart
        };
        
        localStorage.setItem('tutorialPreferences', JSON.stringify(preferences));
    }
    
    /**
     * Load tutorial progress.
     */
    loadTutorialProgress() {
        const progressJson = localStorage.getItem('tutorialProgress');
        if (progressJson) {
            const progress = JSON.parse(progressJson);
            
            if (progress.tutorialId && progress.stepIndex !== undefined) {
                // Load tutorial and resume
                this.loadTutorial(progress.tutorialId).then(loaded => {
                    if (loaded && this.autoStart) {
                        this.startTutorial(progress.tutorialId, progress.stepIndex);
                    }
                });
            }
        }
    }
    
    /**
     * Save tutorial progress.
     */
    saveTutorialProgress() {
        if (!this.currentTutorial) {
            localStorage.removeItem('tutorialProgress');
            return;
        }
        
        const progress = {
            tutorialId: this.currentTutorial.id,
            stepIndex: this.currentStepIndex
        };
        
        localStorage.setItem('tutorialProgress', JSON.stringify(progress));
    }
    
    /**
     * Save tutorial completion to server.
     * 
     * @param {string} tutorialId - ID of the completed tutorial
     */
    saveTutorialCompletion(tutorialId) {
        fetch('/api/tutorials/completion', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                tutorialId: tutorialId,
                completedAt: new Date().toISOString()
            })
        }).catch(error => {
            console.error('Error saving tutorial completion:', error);
        });
    }
    
    /**
     * Position all UI elements.
     */
    positionUIElements() {
        if (!this.currentTutorial || !this.isActive) {
            return;
        }
        
        const step = this.currentTutorial.steps[this.currentStepIndex];
        if (!step) {
            return;
        }
        
        // Find target element
        const targetEl = document.querySelector(step.target);
        
        // Highlight target element
        if (step.highlight && targetEl) {
            this.highlightElement(targetEl);
        }
        
        // Position tooltip
        this.positionTooltip(targetEl, step.position || 'bottom');
    }
}

// Export the TutorialEngine class
export default TutorialEngine;
