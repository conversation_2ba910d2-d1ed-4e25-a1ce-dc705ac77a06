import psycopg2
import os
from dotenv import load_dotenv
from decimal import Decimal

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
db_params = {
    'dbname': 'AssetKPI',
    'user': 'postgres',
    'password': 'Arcanum',
    'host': 'localhost',
    'port': '5432'
}

# Try to get connection parameters from environment variables
database_url = os.getenv('DATABASE_URL')
if database_url:
    # Parse the DATABASE_URL
    try:
        # Format: postgresql://username:password@host:port/dbname
        parts = database_url.split('://', 1)[1].split('@')
        user_pass = parts[0].split(':')
        host_port_db = parts[1].split('/')
        host_port = host_port_db[0].split(':')
        
        db_params = {
            'dbname': host_port_db[1],
            'user': user_pass[0],
            'password': user_pass[1],
            'host': host_port[0],
            'port': host_port[1] if len(host_port) > 1 else '5432'
        }
        print(f"Using database connection parameters from DATABASE_URL")
    except Exception as e:
        print(f"Error parsing DATABASE_URL: {e}")
        print(f"Using default database connection parameters")

# SQL to create the inventory_config table
create_inventory_config_table_sql = """
CREATE TABLE IF NOT EXISTS inventory_config (
    id SERIAL PRIMARY KEY,
    parameter_name VARCHAR(100) UNIQUE NOT NULL,
    parameter_value VARCHAR(255) NOT NULL,
    parameter_description TEXT,
    parameter_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
"""

# Default configuration parameters
default_config_params = [
    {
        'parameter_name': 'ordering_cost',
        'parameter_value': '50.00',
        'parameter_description': 'Cost per order in currency units',
        'parameter_type': 'decimal'
    },
    {
        'parameter_name': 'holding_cost_percent',
        'parameter_value': '0.15',
        'parameter_description': 'Annual holding cost as a percentage of unit cost (e.g., 0.15 for 15%)',
        'parameter_type': 'decimal'
    },
    {
        'parameter_name': 'service_level_z',
        'parameter_value': '1.65',
        'parameter_description': 'Z-score for desired service level (e.g., 1.65 for 95% service level)',
        'parameter_type': 'decimal'
    },
    {
        'parameter_name': 'demand_variability_factor',
        'parameter_value': '0.3',
        'parameter_description': 'Factor representing demand variability (std dev / avg demand)',
        'parameter_type': 'decimal'
    },
    {
        'parameter_name': 'lead_time_variability_factor',
        'parameter_value': '0.2',
        'parameter_description': 'Factor representing lead time variability (std dev / avg lead time)',
        'parameter_type': 'decimal'
    },
    {
        'parameter_name': 'overstock_days_threshold',
        'parameter_value': '180',
        'parameter_description': 'Number of days since last restock to consider for overstock alerts',
        'parameter_type': 'integer'
    },
    {
        'parameter_name': 'obsolete_days_threshold',
        'parameter_value': '365',
        'parameter_description': 'Number of days since last restock to consider for obsolete alerts',
        'parameter_type': 'integer'
    },
    {
        'parameter_name': 'overstock_safety_stock_multiplier',
        'parameter_value': '3',
        'parameter_description': 'Multiplier for safety stock to determine overstock level',
        'parameter_type': 'decimal'
    }
]

def main():
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Create the inventory_config table if it doesn't exist
        print("Creating inventory_config table if it doesn't exist...")
        cursor.execute(create_inventory_config_table_sql)
        
        # Insert default configuration parameters
        print("Inserting default configuration parameters...")
        for param in default_config_params:
            # Check if parameter already exists
            cursor.execute(
                "SELECT id FROM inventory_config WHERE parameter_name = %s",
                (param['parameter_name'],)
            )
            if cursor.fetchone() is None:
                # Parameter doesn't exist, insert it
                cursor.execute(
                    """
                    INSERT INTO inventory_config 
                    (parameter_name, parameter_value, parameter_description, parameter_type)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (
                        param['parameter_name'],
                        param['parameter_value'],
                        param['parameter_description'],
                        param['parameter_type']
                    )
                )
                print(f"  - Added parameter: {param['parameter_name']} = {param['parameter_value']}")
            else:
                print(f"  - Parameter already exists: {param['parameter_name']}")
        
        # Commit the changes
        conn.commit()
        print("Inventory configuration initialized successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    main()
