import psycopg2

# Connect to the database
conn = psycopg2.connect("postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
cur = conn.cursor()

# Query to find all foreign keys referencing the users table
query = """
SELECT
    tc.table_schema, 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' AND ccu.table_name='users';
"""

cur.execute(query)
foreign_keys = cur.fetchall()

print("Foreign keys referencing the users table:")
for fk in foreign_keys:
    print(f"Table: {fk[2]}, Column: {fk[3]}, Constraint: {fk[1]}")

# Close the connection
cur.close()
conn.close()
