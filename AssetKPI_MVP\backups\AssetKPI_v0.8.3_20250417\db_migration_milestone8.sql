-- AssetKPI Database Migration: Milestone 8 - Documentation

-- 8.1 Technical Documentation
CREATE TABLE IF NOT EXISTS technical_documents (
    document_id SERIAL PRIMARY KEY,
    document_name VARCHAR(100) NOT NULL,
    document_type VARCHAR(50),
    description TEXT,
    file_path VARCHAR(255),
    version VARCHAR(50),
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS asset_documents (
    id SERIAL PRIMARY KEY,
    asset_id INTEGER REFERENCES assets(assetid),
    document_id INTEGER REFERENCES technical_documents(document_id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 8.2 Safety Procedures
CREATE TABLE IF NOT EXISTS safety_procedures (
    procedure_id SERIAL PRIMARY KEY,
    procedure_name VARCHAR(100) NOT NULL,
    description TEXT,
    procedure_steps TEXT,
    required_ppe TEXT,
    hazards TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS work_order_procedures (
    id SERIAL PRIMARY KEY,
    workorder_id INTEGER REFERENCES workorders(workorderid),
    procedure_id INTEGER REFERENCES safety_procedures(procedure_id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_asset_documents_asset_id ON asset_documents(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_documents_document_id ON asset_documents(document_id);
CREATE INDEX IF NOT EXISTS idx_work_order_procedures_workorder_id ON work_order_procedures(workorder_id);
CREATE INDEX IF NOT EXISTS idx_work_order_procedures_procedure_id ON work_order_procedures(procedure_id);

-- End of Milestone 8 migration script
