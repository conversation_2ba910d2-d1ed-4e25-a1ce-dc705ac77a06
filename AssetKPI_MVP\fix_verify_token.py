# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Find the verify_token endpoint
verify_token_pattern = '''@app.post("/api/verify-token", tags=["Authentication"])
async def verify_token(request: Request, token_data: dict):
    """
    Verify a Firebase ID token and return user information.
    """
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        
        # Verify the token with Firebase Admin SDK
        try:
            decoded_token = auth.verify_id_token(token, check_revoked=True)
            firebase_uid = decoded_token.get("uid")
            if firebase_uid is None:
                return JSONResponse(
                    status_code=401,
                    content={"error": "UID not found in decoded token"}
                )
            
            # Get user from database
            db = SessionLocal()
            try:
                # Query the users table for the user with this UID
                query = text("SELECT * FROM users WHERE uid = :uid")
                result = db.execute(query, {"uid": firebase_uid}).fetchone()
                
                if result:
                    # Return user information
                    return {
                        "uid": result[0],
                        "email": result[1],
                        "role": result[2],
                        "full_name": result[3]
                    }
                else:
                    return JSONResponse(
                        status_code=401,
                        content={"error": f"User with UID {firebase_uid} not found in database"}
                    )
            finally:
                db.close()
        except auth.ExpiredIdTokenError as e:
            return JSONResponse(
                status_code=401,
                content={"error": f"Token expired: {e}"}
            )
        except auth.InvalidIdTokenError as e:
            return JSONResponse(
                status_code=401,
                content={"error": f"Invalid token: {e}"}
            )
        except auth.RevokedIdTokenError as e:
            return JSONResponse(
                status_code=401,
                content={"error": f"Token revoked: {e}"}
            )
        except Exception as e:
            return JSONResponse(
                status_code=401,
                content={"error": f"Token verification failed: {e}"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {e}"}
        )'''

# Replace it with the fixed endpoint
fixed_verify_token = '''@app.post("/api/verify-token", tags=["Authentication"])
async def verify_token(request: Request, token_data: dict):
    """
    Verify a Firebase ID token and return user information.
    """
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        
        # Verify the token with Firebase Admin SDK
        try:
            decoded_token = auth.verify_id_token(token, check_revoked=True)
            firebase_uid = decoded_token.get("uid")
            if firebase_uid is None:
                return JSONResponse(
                    status_code=401,
                    content={"error": "UID not found in decoded token"}
                )
            
            # Get user from database
            db = SessionLocal()
            try:
                # Query the users table for the user with this UID
                # Use user_id column instead of uid
                query = text("SELECT * FROM users WHERE user_id = :uid")
                result = db.execute(query, {"uid": firebase_uid}).fetchone()
                
                if result:
                    # Return user information
                    return {
                        "uid": result[0],
                        "email": result[1],
                        "role": result[2],
                        "full_name": result[3]
                    }
                else:
                    return JSONResponse(
                        status_code=401,
                        content={"error": f"User with UID {firebase_uid} not found in database"}
                    )
            finally:
                db.close()
        except auth.ExpiredIdTokenError as e:
            return JSONResponse(
                status_code=401,
                content={"error": f"Token expired: {e}"}
            )
        except auth.InvalidIdTokenError as e:
            return JSONResponse(
                status_code=401,
                content={"error": f"Invalid token: {e}"}
            )
        except auth.RevokedIdTokenError as e:
            return JSONResponse(
                status_code=401,
                content={"error": f"Token revoked: {e}"}
            )
        except Exception as e:
            return JSONResponse(
                status_code=401,
                content={"error": f"Token verification failed: {e}"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {e}"}
        )'''

content = content.replace(verify_token_pattern, fixed_verify_token)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed verify_token endpoint in main.py")
