# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Add a debug endpoint after the verify_token function
debug_endpoint = '''

@app.post("/api/debug-token", tags=["Authentication"])
async def debug_token(request: Request):
    """
    Debug endpoint for token verification.
    """
    print("=== Debug Token Request ===")
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        print(f"Authorization header: {auth_header[:20]}..." if auth_header else "No Authorization header")
        
        if not auth_header or not auth_header.startswith('Bearer '):
            print("No valid authorization header provided")
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        print(f"Token extracted: {token[:20]}...")

        # Verify the token with Firebase Admin SDK
        try:
            print("Attempting to verify token with Firebase Admin SDK...")
            decoded_token = auth.verify_id_token(token)
            uid = decoded_token['uid']
            print(f"Successfully verified token for UID: {uid}")
            print(f"Decoded token claims: {decoded_token}")
            
            # Return the decoded token
            return {
                "success": True,
                "uid": uid,
                "decoded_token": decoded_token
            }
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            print(f"Token that failed verification: {token[:30]}...")
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )
    except Exception as e:
        print(f"Error debugging token: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )
'''

# Find a good place to add the debug endpoint (after the verify_token function)
verify_token_end = '''    except Exception as e:
        print(f"Error verifying token: {e}")
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

content = content.replace(verify_token_end, verify_token_end + debug_endpoint)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Added debug endpoint to main.py")
