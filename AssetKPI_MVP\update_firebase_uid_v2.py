import psycopg2
import json

# Connect to the database
conn = psycopg2.connect("postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
conn.autocommit = True  # Set autocommit to True to avoid transaction issues
cur = conn.cursor()

# Get the current test admin user
cur.execute("SELECT * FROM users WHERE email = 'joh<PERSON><PERSON><PERSON>@gmail.com'")
user = cur.fetchone()

if user:
    user_id = user[0]
    email = user[1]
    role = user[2]
    full_name = user[3]
    print(f"Found user: {email} with ID {user_id}")
    
    # Print the user's permissions
    cur.execute("SELECT * FROM user_permissions WHERE user_id = %s", (user_id,))
    permissions = cur.fetchall()
    print(f"User has {len(permissions)} permissions")
    
    # Check if the user has the correct ID
    if user_id != "firebase-test-admin-uid":
        print(f"User ID is {user_id}, not firebase-test-admin-uid")
        
        # Check if there's already a user with the ID we want to use
        cur.execute("SELECT * FROM users WHERE user_id = 'firebase-test-admin-uid'")
        existing_user = cur.fetchone()
        if existing_user:
            print(f"There's already a user with ID firebase-test-admin-uid: {existing_user[1]}")
        else:
            print("No user with ID firebase-test-admin-uid exists")
            
            # Update the user's ID
            try:
                # First update the user_permissions table
                cur.execute("UPDATE user_permissions SET user_id = 'firebase-test-admin-uid' WHERE user_id = %s", (user_id,))
                permissions_updated = cur.rowcount
                print(f"Updated {permissions_updated} rows in user_permissions table")
                
                # Then update the users table
                cur.execute("UPDATE users SET user_id = 'firebase-test-admin-uid' WHERE user_id = %s", (user_id,))
                users_updated = cur.rowcount
                print(f"Updated {users_updated} rows in users table")
                
                print("Updates completed successfully")
            except Exception as e:
                print(f"Error: {e}")
else:
    print("No user found <NAME_EMAIL>")

# Close the connection
cur.close()
conn.close()
print("Connection closed")
