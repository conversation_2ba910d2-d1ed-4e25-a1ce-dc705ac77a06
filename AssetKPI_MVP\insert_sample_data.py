import psycopg2
from decimal import Decimal
from datetime import datetime, timedelta

# Connect to the database
conn = psycopg2.connect('postgresql://postgres:Arcanum@localhost:5432/AssetKPI')
cur = conn.cursor()

# Get all part IDs
cur.execute('SELECT partid FROM spareparts;')
part_ids = [row[0] for row in cur.fetchall()]

print(f"Found {len(part_ids)} parts")

# Insert sample EOQ calculations
for part_id in part_ids:
    # Get part details
    cur.execute('SELECT unitprice, stockquantity FROM spareparts WHERE partid = %s;', (part_id,))
    result = cur.fetchone()
    if not result:
        continue
    
    unit_price, stock_quantity = result
    
    # Skip if unit price is None
    if unit_price is None:
        continue
    
    # Use sample values
    annual_demand = 100
    ordering_cost = 50
    holding_cost_percent = 0.2
    
    # Calculate EOQ
    annual_holding_cost_per_unit = float(unit_price) * holding_cost_percent
    eoq_value = ((2 * annual_demand * ordering_cost) / annual_holding_cost_per_unit) ** 0.5
    eoq_value = round(eoq_value)
    
    # Calculate additional metrics
    optimal_order_frequency = annual_demand / eoq_value if eoq_value > 0 else 0
    annual_ordering_cost = ordering_cost * optimal_order_frequency
    annual_holding_cost = annual_holding_cost_per_unit * (eoq_value / 2)
    total_annual_cost = annual_ordering_cost + annual_holding_cost
    
    # Insert EOQ calculation
    cur.execute('''
        INSERT INTO eoq_calculations (
            part_id, annual_demand, ordering_cost, holding_cost, eoq_value,
            annual_ordering_cost, annual_holding_cost, total_annual_cost,
            optimal_order_frequency, calculated_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ''', (
        part_id, annual_demand, ordering_cost, annual_holding_cost_per_unit, eoq_value,
        annual_ordering_cost, annual_holding_cost, total_annual_cost,
        int(optimal_order_frequency), datetime.now()
    ))
    
    # Update part with EOQ value
    cur.execute('UPDATE spareparts SET eoq = %s WHERE partid = %s;', (eoq_value, part_id))
    
    print(f"Inserted EOQ calculation for part {part_id}")

# Insert sample safety stock calculations
for part_id in part_ids:
    # Get part details
    cur.execute('SELECT leadtimedays FROM spareparts WHERE partid = %s;', (part_id,))
    result = cur.fetchone()
    if not result:
        continue
    
    lead_time_days = result[0]
    
    # Skip if lead time is None
    if lead_time_days is None:
        lead_time_days = 7  # Default value
    
    # Use sample values
    avg_daily_demand = 1
    demand_variability = 0.3
    lead_time_variability = 0.2
    service_level = 0.95
    service_level_z = 1.96  # Z-score for 95% service level
    
    # Calculate safety stock
    std_dev_demand = avg_daily_demand * demand_variability
    std_dev_lead_time = lead_time_days * lead_time_variability
    
    variance_demand_lead = (lead_time_days * (std_dev_demand**2))
    variance_lead_demand = ((avg_daily_demand**2) * (std_dev_lead_time**2))
    
    combined_std_dev = (variance_demand_lead + variance_lead_demand) ** 0.5
    safety_stock_value = service_level_z * combined_std_dev
    safety_stock_value = round(safety_stock_value)
    
    # Calculate reorder point
    reorder_point = (avg_daily_demand * lead_time_days) + safety_stock_value
    
    # Insert safety stock calculation
    cur.execute('''
        INSERT INTO safety_stock_calculations (
            part_id, avg_daily_demand, lead_time_days, demand_variability,
            lead_time_variability, service_level, service_level_z,
            safety_stock_value, reorder_point, calculated_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ''', (
        part_id, avg_daily_demand, lead_time_days, demand_variability,
        lead_time_variability, service_level, service_level_z,
        safety_stock_value, reorder_point, datetime.now()
    ))
    
    # Update part with safety stock value
    cur.execute('UPDATE spareparts SET calculated_safety_stock = %s, reorder_point = %s WHERE partid = %s;', 
                (safety_stock_value, reorder_point, part_id))
    
    print(f"Inserted safety stock calculation for part {part_id}")

# Insert sample inventory analysis
for part_id in part_ids:
    # Get part details
    cur.execute('''
        SELECT s.stockquantity, s.unitprice, s.eoq, s.calculated_safety_stock, s.lastrestocked
        FROM spareparts s
        WHERE s.partid = %s;
    ''', (part_id,))
    result = cur.fetchone()
    if not result:
        continue
    
    stock_quantity, unit_price, eoq, safety_stock, last_restocked = result
    
    # Skip if required values are None
    if stock_quantity is None or unit_price is None or eoq is None or safety_stock is None:
        continue
    
    # Calculate optimal stock level
    optimal_stock = int((float(eoq) / 2) + float(safety_stock))
    
    # Calculate stock difference
    stock_difference = stock_quantity - optimal_stock
    
    # Calculate costs
    current_cost = stock_quantity * float(unit_price)
    optimal_cost = optimal_stock * float(unit_price)
    potential_savings = current_cost - optimal_cost if current_cost > optimal_cost else 0
    
    # Calculate days of supply
    avg_daily_demand = 1  # Sample value
    days_of_supply = int(stock_quantity / avg_daily_demand) if avg_daily_demand > 0 else 0
    
    # Calculate stockout risk
    stockout_risk = 0.0
    if stock_quantity < optimal_stock:
        deficit = optimal_stock - stock_quantity
        stockout_risk = min(100.0, (deficit / optimal_stock) * 100)
    
    # Insert inventory analysis
    cur.execute('''
        INSERT INTO inventory_analysis (
            part_id, current_stock, optimal_stock, stock_difference,
            current_cost, optimal_cost, potential_savings,
            days_of_supply, stockout_risk, last_usage_date, analysis_date
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ''', (
        part_id, stock_quantity, optimal_stock, stock_difference,
        current_cost, optimal_cost, potential_savings,
        days_of_supply, stockout_risk, datetime.now() - timedelta(days=30), datetime.now()
    ))
    
    # Update part with days of supply
    cur.execute('UPDATE spareparts SET days_of_supply = %s WHERE partid = %s;', (days_of_supply, part_id))
    
    print(f"Inserted inventory analysis for part {part_id}")

# Commit changes
conn.commit()
print("All sample data inserted successfully")

# Close connection
cur.close()
conn.close()
