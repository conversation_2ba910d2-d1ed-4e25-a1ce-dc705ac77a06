import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Import the User model
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from main import User

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def update_user_id(old_user_id, new_user_id):
    """Update a user's ID in the database"""
    try:
        # Find the user with the old ID
        user = db.query(User).filter(User.user_id == old_user_id).first()
        
        if not user:
            print(f"User with ID {old_user_id} not found")
            return False
        
        print(f"Found user:")
        print(f"  User ID: {user.user_id}")
        print(f"  Email: {user.email}")
        print(f"  Role: {user.role}")
        
        # Update the user ID
        user.user_id = new_user_id
        db.commit()
        
        print(f"Updated user ID to {new_user_id}")
        return True
    except Exception as e:
        db.rollback()
        print(f"Error updating user ID: {e}")
        return False

def main():
    # Old and new user IDs
    old_user_id = "firebase-test-admin-uid"
    new_user_id = "uasUzj4IXFaqJC3pcEiOCL3vD3t2"
    
    # Update the user ID
    success = update_user_id(old_user_id, new_user_id)
    
    if success:
        print("User ID updated successfully")
    else:
        print("Failed to update user ID")
    
    # List all users
    users = db.query(User).all()
    print(f"\nUsers in database:")
    for user in users:
        print(f"  User ID: {user.user_id}")
        print(f"  Email: {user.email}")
        print(f"  Role: {user.role}")
        print()

if __name__ == "__main__":
    main()
