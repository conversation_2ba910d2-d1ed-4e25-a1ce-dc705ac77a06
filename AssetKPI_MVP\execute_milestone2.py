import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def execute_migration():
    """Execute the SQL migration script for Milestone 2."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Read the SQL migration script
        with open('db_migration_milestone2.sql', 'r') as f:
            sql_script = f.read()
        
        # Split the script into individual statements
        statements = sql_script.split(';')
        
        # Execute each statement
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement + ';')
                    print(f"Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"Error executing statement: {statement[:50]}...")
                    print(f"Error: {e}")
        
        # Commit the changes
        conn.commit()
        print("Milestone 2 migration completed successfully!")
        
        # Return the connection and cursor for data population
        return conn, cursor
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return None, None

def populate_sample_data(conn, cursor):
    """Populate the database with sample data for Milestone 2."""
    try:
        print("\nPopulating sample data for Milestone 2...")
        
        # Get all existing assets
        cursor.execute("SELECT assetid, assettype FROM assets")
        assets = cursor.fetchall()
        
        if not assets:
            print("No assets found in the database. Please add assets first.")
            return
        
        # Sample specifications based on asset type
        spec_templates = {
            'Pump': [
                ('Flow Rate', '100-500', 'GPM'),
                ('Pressure Rating', '50-200', 'PSI'),
                ('Motor Power', '5-50', 'HP'),
                ('RPM', '1000-3600', 'RPM'),
                ('Impeller Size', '6-12', 'inches')
            ],
            'Motor': [
                ('Power Rating', '1-100', 'HP'),
                ('Voltage', '120-480', 'V'),
                ('Current Rating', '10-100', 'A'),
                ('RPM', '1000-3600', 'RPM'),
                ('Efficiency', '85-95', '%')
            ],
            'Conveyor': [
                ('Length', '10-100', 'feet'),
                ('Width', '2-6', 'feet'),
                ('Speed', '10-100', 'feet/min'),
                ('Load Capacity', '100-5000', 'lbs'),
                ('Belt Material', 'Rubber/PVC/Metal', '')
            ],
            'HVAC': [
                ('Cooling Capacity', '5000-50000', 'BTU'),
                ('Heating Capacity', '5000-50000', 'BTU'),
                ('Airflow', '500-5000', 'CFM'),
                ('Refrigerant Type', 'R-410A/R-134a', ''),
                ('Energy Efficiency Ratio', '10-20', 'EER')
            ],
            'Generator': [
                ('Power Output', '5-500', 'kW'),
                ('Voltage', '120-480', 'V'),
                ('Frequency', '50-60', 'Hz'),
                ('Fuel Type', 'Diesel/Natural Gas', ''),
                ('Run Time', '8-72', 'hours')
            ],
            'default': [
                ('Weight', '10-5000', 'lbs'),
                ('Dimensions', 'varies', 'LxWxH'),
                ('Material', 'Steel/Aluminum/Plastic', ''),
                ('Power Requirement', '120-480', 'V'),
                ('Operating Temperature', '0-100', '°C')
            ]
        }
        
        # Sample warranty providers
        warranty_providers = [
            'Manufacturer Warranty',
            'Extended Service Plan',
            'Third-Party Warranty',
            'Maintenance Contract',
            'Parts & Labor Warranty'
        ]
        
        # Sample warranty types
        warranty_types = [
            'Standard',
            'Extended',
            'Limited',
            'Comprehensive',
            'Parts Only'
        ]
        
        print("Inserting asset specifications...")
        for asset_id, asset_type in assets:
            # Determine which template to use based on asset type
            template_key = 'default'
            for key in spec_templates.keys():
                if asset_type and key.lower() in asset_type.lower():
                    template_key = key
                    break
            
            # Get the template
            template = spec_templates[template_key]
            
            # Add specifications for this asset
            for spec_name, value_range, unit in template:
                # Generate a random value based on the range
                if '-' in value_range:
                    try:
                        min_val, max_val = map(float, value_range.split('-'))
                        value = str(round(random.uniform(min_val, max_val), 2))
                    except ValueError:
                        value = value_range
                else:
                    value = value_range
                
                cursor.execute(
                    """
                    INSERT INTO asset_specifications 
                    (asset_id, spec_name, spec_value, spec_unit)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (asset_id, spec_name, value, unit)
                )
            
            print(f"  - Added specifications for asset ID {asset_id}")
            
            # Add warranty for this asset (50% chance)
            if random.random() < 0.5:
                provider = random.choice(warranty_providers)
                warranty_type = random.choice(warranty_types)
                
                # Generate random dates
                today = datetime.now().date()
                start_date = today - timedelta(days=random.randint(0, 365*2))
                end_date = start_date + timedelta(days=random.randint(365, 365*5))
                
                coverage_details = f"{warranty_type} warranty covering parts and labor for manufacturing defects."
                contact_info = f"Provider: {provider}, Phone: 555-{random.randint(100, 999)}-{random.randint(1000, 9999)}"
                
                cursor.execute(
                    """
                    INSERT INTO asset_warranties 
                    (asset_id, warranty_type, provider, start_date, end_date, coverage_details, contact_info)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """,
                    (asset_id, warranty_type, provider, start_date, end_date, coverage_details, contact_info)
                )
                
                print(f"  - Added warranty for asset ID {asset_id}")
        
        # Commit the changes
        conn.commit()
        print("Sample data population completed successfully!")
        
    except Exception as e:
        print(f"Error populating sample data: {e}")
        conn.rollback()

def main():
    """Main function to execute migration and populate data."""
    conn, cursor = execute_migration()
    
    if conn and cursor:
        populate_sample_data(conn, cursor)
        cursor.close()
        conn.close()
    
    print("Milestone 2 completed!")

if __name__ == "__main__":
    main()
