/**
 * Onboarding Styles
 * 
 * This file contains styles for the onboarding process and user guidance
 * across the AssetKPI application.
 */

/* ===== Onboarding Container ===== */

.onboarding-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.onboarding-header {
  text-align: center;
  margin-bottom: 2rem;
}

.onboarding-title {
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 1rem;
}

.onboarding-subtitle {
  color: #7f8c8d;
  font-size: 1.2rem;
  margin-bottom: 0;
}

/* ===== Progress Indicator ===== */

.onboarding-progress {
  margin-bottom: 3rem;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 1rem;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #ecf0f1;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.step-number.active {
  background: #3498db;
  color: white;
}

.step-number.completed {
  background: #27ae60;
  color: white;
}

.step-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  text-align: center;
  max-width: 100px;
}

.step-label.active {
  color: #2c3e50;
  font-weight: 600;
}

/* Progress line */
.progress-steps::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 40px;
  right: 40px;
  height: 2px;
  background: #ecf0f1;
  z-index: 1;
}

.progress-line {
  position: absolute;
  top: 20px;
  left: 40px;
  height: 2px;
  background: #3498db;
  z-index: 1;
  transition: width 0.3s ease;
}

/* ===== Onboarding Cards ===== */

.onboarding-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid #dee2e6;
}

.onboarding-card.active {
  border-color: #3498db;
  box-shadow: 0 0.5rem 1rem rgba(52, 152, 219, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.card-title {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.card-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* ===== Action Buttons ===== */

.onboarding-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.btn-onboarding {
  padding: 0.75rem 2rem;
  border-radius: 0.375rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-primary-onboarding {
  background: #3498db;
  color: white;
  border: 2px solid #3498db;
}

.btn-primary-onboarding:hover {
  background: #2980b9;
  border-color: #2980b9;
  color: white;
}

.btn-secondary-onboarding {
  background: transparent;
  color: #7f8c8d;
  border: 2px solid #bdc3c7;
}

.btn-secondary-onboarding:hover {
  background: #ecf0f1;
  color: #2c3e50;
  border-color: #95a5a6;
}

/* ===== Checklist ===== */

.onboarding-checklist {
  list-style: none;
  padding: 0;
}

.checklist-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #ecf0f1;
}

.checklist-item:last-child {
  border-bottom: none;
}

.checklist-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ecf0f1;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 0.8rem;
}

.checklist-icon.completed {
  background: #27ae60;
  color: white;
}

.checklist-text {
  flex: 1;
  color: #2c3e50;
}

.checklist-text.completed {
  text-decoration: line-through;
  color: #7f8c8d;
}

/* ===== Welcome Message ===== */

.welcome-message {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 3rem 2rem;
  border-radius: 0.5rem;
  text-align: center;
  margin-bottom: 2rem;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 1rem;
}

.welcome-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

/* ===== Responsive Design ===== */

@media (max-width: 767.98px) {
  .onboarding-container {
    padding: 1rem;
  }
  
  .onboarding-title {
    font-size: 2rem;
  }
  
  .progress-steps {
    flex-direction: column;
    gap: 1rem;
  }
  
  .progress-steps::before,
  .progress-line {
    display: none;
  }
  
  .onboarding-actions {
    flex-direction: column;
    gap: 1rem;
  }
  
  .btn-onboarding {
    width: 100%;
    text-align: center;
  }
  
  .onboarding-card {
    padding: 1.5rem;
  }
  
  .welcome-message {
    padding: 2rem 1rem;
  }
  
  .welcome-title {
    font-size: 1.5rem;
  }
}
