-- AssetKPI Database Migration: Milestone 7 - Resource Management

-- 7.1 Resource Skills
CREATE TABLE IF NOT EXISTS resource_skills (
    skill_id SERIAL PRIMARY KEY,
    resource_id INTEGER REFERENCES labor_resources(resource_id),
    skill_name VARCHAR(100) NOT NULL,
    proficiency_level VARCHAR(50),
    certification VARCHAR(100),
    expiration_date DATE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 7.2 Tools and Equipment
CREATE TABLE IF NOT EXISTS tools_equipment (
    tool_id SERIAL PRIMARY KEY,
    tool_name VARCHAR(100) NOT NULL,
    tool_type VARCHAR(50),
    serial_number VARCHAR(50),
    status VARCHAR(30),
    location VARCHAR(100),
    calibration_due_date DATE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS work_order_tools (
    id SERIAL PRIMARY KEY,
    workorder_id INTEGER REFERENCES workorders(workorderid),
    tool_id INTEGER REFERENCES tools_equipment(tool_id),
    checkout_date TIMESTAMP,
    return_date TIMESTAMP,
    checked_out_by VA<PERSON>HA<PERSON>(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_resource_skills_resource_id ON resource_skills(resource_id);
CREATE INDEX IF NOT EXISTS idx_work_order_tools_workorder_id ON work_order_tools(workorder_id);
CREATE INDEX IF NOT EXISTS idx_work_order_tools_tool_id ON work_order_tools(tool_id);

-- End of Milestone 7 migration script
