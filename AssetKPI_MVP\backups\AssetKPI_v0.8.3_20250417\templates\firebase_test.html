<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f5f5f5;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        input {
            padding: 8px;
            margin: 5px 0;
            display: inline-block;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            width: 100%;
        }
        .form-group {
            margin-bottom: 15px;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            overflow-x: auto;
        }
        .hidden {
            display: none;
        }
        .error {
            color: red;
            margin-top: 10px;
        }
        .success {
            color: green;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Firebase Authentication Test</h1>

    <div class="container">
        <h2>Firebase Configuration</h2>
        <pre id="configInfo">Loading...</pre>
    </div>

    <div class="container">
        <h2>Sign In</h2>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="TestTest">
        </div>
        <button id="signInButton">Sign In</button>
        <button id="signOutButton">Sign Out</button>
        <div id="authError" class="error hidden"></div>
        <div id="authSuccess" class="success hidden"></div>
    </div>

    <div class="container">
        <h2>Authentication Status</h2>
        <p id="authStatus">Not authenticated</p>
        <div id="userInfo" class="hidden">
            <h3>User Info</h3>
            <pre id="userInfoJson"></pre>
        </div>
    </div>

    <div class="container">
        <h2>Create User</h2>
        <div class="form-group">
            <label for="newEmail">Email:</label>
            <input type="email" id="newEmail" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="newPassword">Password:</label>
            <input type="password" id="newPassword" value="TestTest">
        </div>
        <button id="createUserButton">Create User</button>
        <div id="createError" class="error hidden"></div>
        <div id="createSuccess" class="success hidden"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
            authDomain: "ikios-59679.firebaseapp.com",
            projectId: "ikios-59679",
            storageBucket: "ikios-59679.appspot.com",
            messagingSenderId: "1045286122604",
            appId: "1:1045286122604:web:c9e9c9b9b9b9b9b9b9b9b9"
        };

        // Display configuration (without API key)
        document.getElementById('configInfo').textContent = JSON.stringify({
            authDomain: firebaseConfig.authDomain,
            projectId: firebaseConfig.projectId,
            storageBucket: firebaseConfig.storageBucket,
            messagingSenderId: firebaseConfig.messagingSenderId,
            appId: firebaseConfig.appId
        }, null, 2);

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();

        // DOM elements
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const signInButton = document.getElementById('signInButton');
        const signOutButton = document.getElementById('signOutButton');
        const authStatus = document.getElementById('authStatus');
        const userInfo = document.getElementById('userInfo');
        const userInfoJson = document.getElementById('userInfoJson');
        const authError = document.getElementById('authError');
        const authSuccess = document.getElementById('authSuccess');

        const newEmailInput = document.getElementById('newEmail');
        const newPasswordInput = document.getElementById('newPassword');
        const createUserButton = document.getElementById('createUserButton');
        const createError = document.getElementById('createError');
        const createSuccess = document.getElementById('createSuccess');

        // Sign in with email and password
        signInButton.addEventListener('click', async () => {
            const email = emailInput.value;
            const password = passwordInput.value;

            // Clear previous messages
            authError.textContent = '';
            authError.classList.add('hidden');
            authSuccess.textContent = '';
            authSuccess.classList.add('hidden');

            try {
                console.log(`Attempting to sign in with email: ${email}`);
                const userCredential = await auth.signInWithEmailAndPassword(email, password);
                const user = userCredential.user;

                console.log('Sign in successful:', user);
                authSuccess.textContent = `Signed in successfully as ${user.email}`;
                authSuccess.classList.remove('hidden');

                authStatus.textContent = `Authenticated as ${user.email}`;
                userInfo.classList.remove('hidden');
                userInfoJson.textContent = JSON.stringify(user.toJSON(), null, 2);
            } catch (error) {
                console.error('Sign in error:', error);
                authError.textContent = `Error: ${error.message}`;
                authError.classList.remove('hidden');
            }
        });

        // Sign out
        signOutButton.addEventListener('click', async () => {
            try {
                await auth.signOut();
                authStatus.textContent = 'Signed out';
                userInfo.classList.add('hidden');

                authSuccess.textContent = 'Signed out successfully';
                authSuccess.classList.remove('hidden');
                authError.classList.add('hidden');
            } catch (error) {
                console.error('Sign out error:', error);
                authError.textContent = `Error: ${error.message}`;
                authError.classList.remove('hidden');
            }
        });

        // Create user
        createUserButton.addEventListener('click', async () => {
            const email = newEmailInput.value;
            const password = newPasswordInput.value;

            // Clear previous messages
            createError.textContent = '';
            createError.classList.add('hidden');
            createSuccess.textContent = '';
            createSuccess.classList.add('hidden');

            try {
                console.log(`Attempting to create user with email: ${email}`);

                // Create user with email and password
                const userCredential = await auth.createUserWithEmailAndPassword(email, password);
                const user = userCredential.user;

                console.log('User created successfully:', user);
                createSuccess.textContent = `User created successfully: ${user.email}`;
                createSuccess.classList.remove('hidden');

                // Update auth status
                authStatus.textContent = `Authenticated as ${user.email}`;
                userInfo.classList.remove('hidden');
                userInfoJson.textContent = JSON.stringify(user.toJSON(), null, 2);
            } catch (error) {
                console.error('Create user error:', error);
                createError.textContent = `Error: ${error.message}`;
                createError.classList.remove('hidden');
            }
        });

        // Listen for auth state changes
        auth.onAuthStateChanged((user) => {
            if (user) {
                authStatus.textContent = `Authenticated as ${user.email}`;
                userInfo.classList.remove('hidden');
                userInfoJson.textContent = JSON.stringify(user.toJSON(), null, 2);
            } else {
                authStatus.textContent = 'Not authenticated';
                userInfo.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
