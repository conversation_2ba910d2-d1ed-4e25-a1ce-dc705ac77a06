import requests
import sys
import json

# Base URL
base_url = "http://127.0.0.1:8000"

def test_debug_token(token):
    """
    Test the debug token endpoint with a real Firebase token.
    """
    print("Testing debug token endpoint with real token...")
    headers = {
        "Authorization": f"Bearer {token}"
    }
    response = requests.post(f"{base_url}/api/debug-token", headers=headers)
    print(f"Status code: {response.status_code}")
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except:
        print(f"Response: {response.text}")
    print()

def test_user_check(user_id):
    """
    Test the user check endpoint with a real user ID.
    """
    print(f"Testing user check endpoint for user {user_id}...")
    response = requests.get(f"{base_url}/api/user-check/{user_id}")
    print(f"Status code: {response.status_code}")
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except:
        print(f"Response: {response.text}")
    print()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_firebase_token.py <token> [user_id]")
        sys.exit(1)
    
    token = sys.argv[1]
    test_debug_token(token)
    
    if len(sys.argv) > 2:
        user_id = sys.argv[2]
        test_user_check(user_id)
