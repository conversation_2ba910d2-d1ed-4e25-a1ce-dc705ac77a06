{% extends "layout.html" %}

{% block title %}KPI Analytics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2">KPI Analytics</h1>
            <p class="text-muted">Advanced analysis of key performance indicators</p>
        </div>
    </div>

    <!-- Main Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">KPI Trend Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:400px;">
                        <canvas id="mainChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Asset Comparison</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:400px;">
                        <canvas id="assetChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Include Chart.js directly in this template -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

<script>
    // Wait for the DOM to be fully loaded
    window.onload = function() {
        console.log('Window loaded, creating charts');

        // Create the main chart
        setTimeout(function() {
            createMainChart();
            createAssetChart();
        }, 500);

        // Function to create the main chart
        function createMainChart() {
            console.log('Creating main chart');
            const canvas = document.getElementById('mainChart');
            if (!canvas) {
                console.error('Main chart canvas not found');
                return;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Could not get 2D context for main chart');
                return;
            }

            // Sample data
            const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const data = [82.5, 84.3, 79.8, 85.2, 87.1, 86.5, 88.2, 85.7, 83.9, 86.3, 88.5, 89.2];

            try {
                // Create chart
                const mainChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'OEE (%)',
                            data: data,
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderWidth: 2,
                            tension: 0.1,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                title: {
                                    display: true,
                                    text: 'OEE (%)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Month'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Overall Equipment Effectiveness (OEE) Trend'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            }
                        }
                    }
                });

                console.log('Main chart created successfully');
            } catch (error) {
                console.error('Error creating main chart:', error);
            }
        }

        // Function to create the asset comparison chart
        function createAssetChart() {
            console.log('Creating asset chart');
            const canvas = document.getElementById('assetChart');
            if (!canvas) {
                console.error('Asset chart canvas not found');
                return;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Could not get 2D context for asset chart');
                return;
            }

            // Sample data
            const labels = ['CNC Machine', 'HVAC System', 'Injection Molding 1', 'Injection Molding 2', 'CNC Milling 1'];
            const data = [82.5, 88.3, 78.9, 86.2, 90.1];

            try {
                // Create chart
                const assetChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'OEE (%)',
                            data: data,
                            backgroundColor: 'rgba(153, 102, 255, 0.6)',
                            borderColor: 'rgba(153, 102, 255, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'OEE (%)'
                                }
                            },
                            x: {
                                ticks: {
                                    maxRotation: 45,
                                    minRotation: 45
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'OEE by Asset'
                            }
                        }
                    }
                });

                console.log('Asset chart created successfully');
            } catch (error) {
                console.error('Error creating asset chart:', error);
            }
        }
    };
</script>
{% endblock %}
