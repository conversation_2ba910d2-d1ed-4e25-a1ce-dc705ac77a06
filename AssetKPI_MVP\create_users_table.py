import psycopg2

# Database connection parameters
db_params = {
    'dbname': 'Asset<PERSON><PERSON>',
    'user': 'postgres',
    'password': '<PERSON>anu<PERSON>',
    'host': 'localhost',
    'port': '5432'
}

# SQL to create the users table
create_users_table_sql = """
CREATE TABLE IF NOT EXISTS users (
    user_id VARCHAR(255) PRIMARY KEY, -- Firebase UID (usually a string)
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'VIEWER', -- e.g., VIEWER, ENGINEER, MANAGER, ADMIN
    full_name VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_login TIMESTAMPTZ
);
CREATE INDEX IF NOT EXISTS idx_users_email ON users (email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users (role);
"""

try:
    # Connect to the database
    conn = psycopg2.connect(**db_params)
    conn.autocommit = True
    
    # Create a cursor
    cursor = conn.cursor()
    
    # Execute the SQL
    cursor.execute(create_users_table_sql)
    
    print("Users table created successfully!")
    
    # Close the cursor and connection
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"Error: {e}")
