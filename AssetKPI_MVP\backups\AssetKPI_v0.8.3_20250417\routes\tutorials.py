"""
Tutorial routes for the AssetKPI application.

This module defines the API routes for interactive tutorials.
"""

import logging
import json
import os
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Path
from sqlalchemy.orm import Session
from pydantic import BaseModel

from models.user import User as UserModel

# Create a logger for this module
logger = logging.getLogger(__name__)

# Create a router for tutorial endpoints
tutorials_router = APIRouter(prefix="/api/tutorials", tags=["Tutorials"])

# Store dependencies
get_db_dependency = None
get_current_user_dependency = None


def init_router(
    get_db: Callable,
    get_current_user: Callable
):
    """
    Initialize the router with dependencies.
    
    Args:
        get_db: Dependency to get database session
        get_current_user: Dependency to get current user
    """
    global get_db_dependency, get_current_user_dependency
    
    get_db_dependency = get_db
    get_current_user_dependency = get_current_user


class TutorialCompletionRequest(BaseModel):
    """
    Request model for recording tutorial completion.
    """
    tutorialId: str
    completedAt: datetime


class TutorialCompletionResponse(BaseModel):
    """
    Response model for tutorial completion.
    """
    success: bool
    message: str


@tutorials_router.get(
    "/{tutorial_id}",
    summary="Get tutorial by ID"
)
async def get_tutorial(
    tutorial_id: str = Path(..., description="ID of the tutorial"),
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Get a tutorial by ID.
    
    Args:
        tutorial_id: ID of the tutorial
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Tutorial data
    """
    try:
        # Get tutorial file path
        tutorial_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "static",
            "js",
            "tutorials",
            f"{tutorial_id}-tutorial.json"
        )
        
        # Check if tutorial file exists
        if not os.path.exists(tutorial_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Tutorial with ID {tutorial_id} not found"
            )
        
        # Read tutorial file
        with open(tutorial_path, "r") as f:
            tutorial_data = json.load(f)
        
        return tutorial_data
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting tutorial: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting tutorial: {str(e)}"
        )


@tutorials_router.get(
    "",
    summary="Get all tutorials"
)
async def get_tutorials(
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Get all available tutorials.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of tutorials
    """
    try:
        # Get tutorials directory path
        tutorials_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "static",
            "js",
            "tutorials"
        )
        
        # Check if tutorials directory exists
        if not os.path.exists(tutorials_dir):
            return []
        
        # Get tutorial files
        tutorial_files = [
            f for f in os.listdir(tutorials_dir)
            if f.endswith("-tutorial.json")
        ]
        
        # Read tutorial files
        tutorials = []
        for file_name in tutorial_files:
            with open(os.path.join(tutorials_dir, file_name), "r") as f:
                tutorial_data = json.load(f)
                
                # Add only basic info, not all steps
                tutorials.append({
                    "id": tutorial_data.get("id"),
                    "title": tutorial_data.get("title"),
                    "description": tutorial_data.get("description"),
                    "version": tutorial_data.get("version"),
                    "steps_count": len(tutorial_data.get("steps", []))
                })
        
        return tutorials
    
    except Exception as e:
        logger.error(f"Error getting tutorials: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting tutorials: {str(e)}"
        )


@tutorials_router.post(
    "/completion",
    response_model=TutorialCompletionResponse,
    summary="Record tutorial completion"
)
async def record_tutorial_completion(
    request: TutorialCompletionRequest,
    db: Session = Depends(get_db_dependency),
    current_user: UserModel = Depends(get_current_user_dependency)
):
    """
    Record tutorial completion for the current user.
    
    Args:
        request: Tutorial completion request
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Success response
    """
    try:
        # Get user from database
        user = db.query(UserModel).filter(UserModel.user_id == current_user.user_id).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update user completed tutorials
        # In a real implementation, you would store this in a database table
        # For now, we'll just return a success response
        
        return TutorialCompletionResponse(
            success=True,
            message=f"Tutorial {request.tutorialId} marked as completed"
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error recording tutorial completion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error recording tutorial completion: {str(e)}"
        )
