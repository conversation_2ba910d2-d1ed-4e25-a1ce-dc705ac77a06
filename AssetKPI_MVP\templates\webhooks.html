{% extends "base.html" %}

{% block title %}Webhooks - AssetKPI{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Webhooks</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
        <li class="breadcrumb-item active">Webhooks</li>
    </ol>
    
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-exchange-alt me-1"></i>
                    Webhook Subscriptions
                    <button class="btn btn-primary btn-sm float-end" id="createWebhookBtn">
                        <i class="fas fa-plus"></i> Create Webhook
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="webhooksTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>URL</th>
                                    <th>Event Types</th>
                                    <th>Status</th>
                                    <th>Success/Failure</th>
                                    <th>Last Delivery</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Webhook subscriptions will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    About Webhooks
                </div>
                <div class="card-body">
                    <h5>What are Webhooks?</h5>
                    <p>
                        Webhooks allow you to receive real-time notifications when specific events occur in AssetKPI.
                        When an event happens, AssetKPI sends an HTTP POST request to the URL you specify.
                    </p>
                    
                    <h5>Available Event Types</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">Inventory Events</div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><code>inventory.updated</code> - When inventory is updated</li>
                                        <li><code>inventory.created</code> - When new inventory is created</li>
                                        <li><code>inventory.deleted</code> - When inventory is deleted</li>
                                        <li><code>inventory.threshold_reached</code> - When inventory reaches a threshold</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-success text-white">Work Order Events</div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><code>workorder.created</code> - When a work order is created</li>
                                        <li><code>workorder.updated</code> - When a work order is updated</li>
                                        <li><code>workorder.completed</code> - When a work order is completed</li>
                                        <li><code>workorder.deleted</code> - When a work order is deleted</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">Asset Events</div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><code>asset.created</code> - When an asset is created</li>
                                        <li><code>asset.updated</code> - When an asset is updated</li>
                                        <li><code>asset.deleted</code> - When an asset is deleted</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-warning text-dark">KPI Events</div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><code>kpi.calculated</code> - When KPIs are calculated</li>
                                        <li><code>kpi.threshold_reached</code> - When a KPI reaches a threshold</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-danger text-white">Recommendation Events</div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><code>recommendation.created</code> - When a recommendation is created</li>
                                        <li><code>recommendation.updated</code> - When a recommendation is updated</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-secondary text-white">System Events</div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><code>system.backup_completed</code> - When a system backup is completed</li>
                                        <li><code>system.error</code> - When a system error occurs</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h5>Webhook Payload Format</h5>
                    <p>
                        Webhooks are sent as HTTP POST requests with a JSON payload. Here's an example payload:
                    </p>
                    <pre><code>{
  "id": "123",
  "event": "inventory.updated",
  "timestamp": "2023-07-15T14:30:00Z",
  "data": {
    "resource_id": 456,
    "resource_type": "sparepart",
    "data": {
      "partname": "Bearing Assembly",
      "stockquantity": 25,
      "reorderlevel": 10
    }
  }
}</code></pre>
                    
                    <h5>Authentication</h5>
                    <p>
                        You can secure your webhook endpoints using one of the following authentication methods:
                    </p>
                    <ul>
                        <li><strong>Basic Auth</strong>: AssetKPI will send HTTP Basic Authentication headers</li>
                        <li><strong>Bearer Token</strong>: AssetKPI will send an Authorization header with a Bearer token</li>
                        <li><strong>API Key</strong>: AssetKPI will send a custom header with your API key</li>
                    </ul>
                    
                    <h5>Best Practices</h5>
                    <ul>
                        <li>Respond quickly to webhook requests (within 10 seconds)</li>
                        <li>Implement proper error handling in your webhook receiver</li>
                        <li>Verify webhook signatures if available</li>
                        <li>Process webhooks asynchronously when possible</li>
                        <li>Implement idempotency to handle duplicate webhook deliveries</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Webhook Modal -->
<div class="modal fade" id="createWebhookModal" tabindex="-1" aria-labelledby="createWebhookModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createWebhookModalLabel">Create Webhook Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createWebhookForm">
                    <div class="mb-3">
                        <label for="webhookName" class="form-label">Name</label>
                        <input type="text" class="form-control" id="webhookName" required>
                    </div>
                    <div class="mb-3">
                        <label for="webhookUrl" class="form-label">URL</label>
                        <input type="url" class="form-control" id="webhookUrl" required>
                    </div>
                    <div class="mb-3">
                        <label for="webhookDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="webhookDescription" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Event Types</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="inventory.updated" id="eventInventoryUpdated">
                                    <label class="form-check-label" for="eventInventoryUpdated">
                                        inventory.updated
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="inventory.created" id="eventInventoryCreated">
                                    <label class="form-check-label" for="eventInventoryCreated">
                                        inventory.created
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="inventory.deleted" id="eventInventoryDeleted">
                                    <label class="form-check-label" for="eventInventoryDeleted">
                                        inventory.deleted
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="inventory.threshold_reached" id="eventInventoryThreshold">
                                    <label class="form-check-label" for="eventInventoryThreshold">
                                        inventory.threshold_reached
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="workorder.created" id="eventWorkorderCreated">
                                    <label class="form-check-label" for="eventWorkorderCreated">
                                        workorder.created
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="workorder.updated" id="eventWorkorderUpdated">
                                    <label class="form-check-label" for="eventWorkorderUpdated">
                                        workorder.updated
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="workorder.completed" id="eventWorkorderCompleted">
                                    <label class="form-check-label" for="eventWorkorderCompleted">
                                        workorder.completed
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="kpi.calculated" id="eventKpiCalculated">
                                    <label class="form-check-label" for="eventKpiCalculated">
                                        kpi.calculated
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="asset.created" id="eventAssetCreated">
                                    <label class="form-check-label" for="eventAssetCreated">
                                        asset.created
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="asset.updated" id="eventAssetUpdated">
                                    <label class="form-check-label" for="eventAssetUpdated">
                                        asset.updated
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="recommendation.created" id="eventRecommendationCreated">
                                    <label class="form-check-label" for="eventRecommendationCreated">
                                        recommendation.created
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="system.error" id="eventSystemError">
                                    <label class="form-check-label" for="eventSystemError">
                                        system.error
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="webhookAuthType" class="form-label">Authentication Type</label>
                        <select class="form-select" id="webhookAuthType">
                            <option value="none">None</option>
                            <option value="basic">Basic Auth</option>
                            <option value="bearer">Bearer Token</option>
                            <option value="api_key">API Key</option>
                        </select>
                    </div>
                    <div class="mb-3 auth-credentials" style="display: none;">
                        <label for="webhookAuthCredentials" class="form-label">Authentication Credentials</label>
                        <input type="text" class="form-control" id="webhookAuthCredentials">
                        <small class="form-text text-muted">
                            For Basic Auth: "username:password"<br>
                            For Bearer Token: "your-token"<br>
                            For API Key: "header-name:key-value"
                        </small>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="webhookRetryCount" class="form-label">Retry Count</label>
                                <input type="number" class="form-control" id="webhookRetryCount" min="0" max="10" value="3">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="webhookRetryInterval" class="form-label">Retry Interval (seconds)</label>
                                <input type="number" class="form-control" id="webhookRetryInterval" min="10" max="3600" value="60">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="webhookTimeout" class="form-label">Timeout (seconds)</label>
                                <input type="number" class="form-control" id="webhookTimeout" min="5" max="300" value="30">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveWebhookBtn">Create Webhook</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Webhook Modal -->
<div class="modal fade" id="editWebhookModal" tabindex="-1" aria-labelledby="editWebhookModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editWebhookModalLabel">Edit Webhook Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Same form fields as create modal, but with id prefix "edit" -->
                <form id="editWebhookForm">
                    <input type="hidden" id="editWebhookId">
                    <!-- Form fields will be similar to create form -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateWebhookBtn">Update Webhook</button>
            </div>
        </div>
    </div>
</div>

<!-- Test Webhook Modal -->
<div class="modal fade" id="testWebhookModal" tabindex="-1" aria-labelledby="testWebhookModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testWebhookModalLabel">Test Webhook</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Send a test webhook to verify your endpoint is working correctly.</p>
                <div id="testWebhookResult" class="alert" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="sendTestWebhookBtn">Send Test Webhook</button>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Events Modal -->
<div class="modal fade" id="webhookEventsModal" tabindex="-1" aria-labelledby="webhookEventsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="webhookEventsModalLabel">Webhook Events</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="webhookEventsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Event Type</th>
                                <th>Status</th>
                                <th>Attempts</th>
                                <th>Created At</th>
                                <th>Delivered At</th>
                                <th>Response</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Webhook events will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize DataTable
        const webhooksTable = $('#webhooksTable').DataTable({
            responsive: true,
            order: [[0, 'desc']],
            columns: [
                { data: 'id' },
                { data: 'name' },
                { data: 'url' },
                { 
                    data: 'event_types',
                    render: function(data) {
                        if (!data || data.length === 0) return '';
                        return data.map(type => `<span class="badge bg-primary">${type}</span>`).join(' ');
                    }
                },
                { 
                    data: 'is_active',
                    render: function(data) {
                        return data ? 
                            '<span class="badge bg-success">Active</span>' : 
                            '<span class="badge bg-danger">Inactive</span>';
                    }
                },
                { 
                    data: null,
                    render: function(data) {
                        return `${data.delivery_success_count} / ${data.delivery_failure_count}`;
                    }
                },
                { 
                    data: 'last_delivery_time',
                    render: function(data) {
                        return data ? new Date(data).toLocaleString() : 'Never';
                    }
                },
                {
                    data: null,
                    render: function(data) {
                        const editBtn = `<button class="btn btn-sm btn-primary edit-webhook" data-id="${data.id}"><i class="fas fa-edit"></i></button>`;
                        const deleteBtn = `<button class="btn btn-sm btn-danger delete-webhook" data-id="${data.id}"><i class="fas fa-trash"></i></button>`;
                        const testBtn = `<button class="btn btn-sm btn-info test-webhook" data-id="${data.id}"><i class="fas fa-paper-plane"></i></button>`;
                        const eventsBtn = `<button class="btn btn-sm btn-secondary view-events" data-id="${data.id}"><i class="fas fa-history"></i></button>`;
                        
                        return `<div class="btn-group" role="group">${editBtn} ${testBtn} ${eventsBtn} ${deleteBtn}</div>`;
                    }
                }
            ]
        });
        
        // Load webhooks
        function loadWebhooks() {
            $.ajax({
                url: '/api/webhooks',
                type: 'GET',
                success: function(response) {
                    webhooksTable.clear();
                    if (response.items && response.items.length > 0) {
                        webhooksTable.rows.add(response.items);
                    }
                    webhooksTable.draw();
                },
                error: function(xhr) {
                    console.error('Error loading webhooks:', xhr.responseText);
                    alert('Error loading webhooks. Please try again.');
                }
            });
        }
        
        // Initial load
        loadWebhooks();
        
        // Show/hide auth credentials based on auth type
        $('#webhookAuthType').change(function() {
            const authType = $(this).val();
            if (authType === 'none') {
                $('.auth-credentials').hide();
            } else {
                $('.auth-credentials').show();
            }
        });
        
        // Create webhook button click
        $('#createWebhookBtn').click(function() {
            $('#createWebhookForm')[0].reset();
            $('#createWebhookModal').modal('show');
        });
        
        // Save webhook button click
        $('#saveWebhookBtn').click(function() {
            // Get form values
            const name = $('#webhookName').val();
            const url = $('#webhookUrl').val();
            const description = $('#webhookDescription').val();
            
            // Get selected event types
            const eventTypes = [];
            $('input[type="checkbox"]:checked').each(function() {
                eventTypes.push($(this).val());
            });
            
            if (eventTypes.length === 0) {
                alert('Please select at least one event type.');
                return;
            }
            
            const authType = $('#webhookAuthType').val();
            const authCredentials = authType !== 'none' ? $('#webhookAuthCredentials').val() : null;
            const retryCount = parseInt($('#webhookRetryCount').val());
            const retryInterval = parseInt($('#webhookRetryInterval').val());
            const timeout = parseInt($('#webhookTimeout').val());
            
            // Create webhook data
            const webhookData = {
                name: name,
                url: url,
                description: description,
                event_types: eventTypes,
                auth_type: authType,
                auth_credentials: authCredentials,
                retry_count: retryCount,
                retry_interval: retryInterval,
                timeout: timeout
            };
            
            // Send API request
            $.ajax({
                url: '/api/webhooks',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(webhookData),
                success: function(response) {
                    $('#createWebhookModal').modal('hide');
                    loadWebhooks();
                    alert('Webhook created successfully!');
                },
                error: function(xhr) {
                    console.error('Error creating webhook:', xhr.responseText);
                    alert('Error creating webhook. Please try again.');
                }
            });
        });
        
        // Edit webhook button click
        $(document).on('click', '.edit-webhook', function() {
            const webhookId = $(this).data('id');
            
            // Load webhook data
            $.ajax({
                url: `/api/webhooks/${webhookId}`,
                type: 'GET',
                success: function(webhook) {
                    // Populate form fields
                    $('#editWebhookId').val(webhook.id);
                    // Populate other fields...
                    
                    $('#editWebhookModal').modal('show');
                },
                error: function(xhr) {
                    console.error('Error loading webhook:', xhr.responseText);
                    alert('Error loading webhook. Please try again.');
                }
            });
        });
        
        // Delete webhook button click
        $(document).on('click', '.delete-webhook', function() {
            const webhookId = $(this).data('id');
            
            if (confirm('Are you sure you want to delete this webhook?')) {
                $.ajax({
                    url: `/api/webhooks/${webhookId}`,
                    type: 'DELETE',
                    success: function() {
                        loadWebhooks();
                        alert('Webhook deleted successfully!');
                    },
                    error: function(xhr) {
                        console.error('Error deleting webhook:', xhr.responseText);
                        alert('Error deleting webhook. Please try again.');
                    }
                });
            }
        });
        
        // Test webhook button click
        $(document).on('click', '.test-webhook', function() {
            const webhookId = $(this).data('id');
            $('#testWebhookResult').hide();
            $('#testWebhookModal').modal('show');
            
            // Store webhook ID for test
            $('#sendTestWebhookBtn').data('id', webhookId);
        });
        
        // Send test webhook button click
        $('#sendTestWebhookBtn').click(function() {
            const webhookId = $(this).data('id');
            
            $.ajax({
                url: `/api/webhooks/test/${webhookId}`,
                type: 'POST',
                success: function(response) {
                    $('#testWebhookResult')
                        .removeClass('alert-danger')
                        .addClass('alert-success')
                        .text('Test webhook sent successfully! Check your endpoint for the delivery.')
                        .show();
                },
                error: function(xhr) {
                    console.error('Error sending test webhook:', xhr.responseText);
                    $('#testWebhookResult')
                        .removeClass('alert-success')
                        .addClass('alert-danger')
                        .text('Error sending test webhook. Please try again.')
                        .show();
                }
            });
        });
        
        // View events button click
        $(document).on('click', '.view-events', function() {
            const webhookId = $(this).data('id');
            
            // Load webhook events
            $.ajax({
                url: `/api/webhooks/events?subscription_id=${webhookId}`,
                type: 'GET',
                success: function(response) {
                    const eventsTable = $('#webhookEventsTable').DataTable();
                    eventsTable.clear();
                    
                    if (response.items && response.items.length > 0) {
                        eventsTable.rows.add(response.items.map(event => ({
                            id: event.id,
                            event_type: event.event_type,
                            status: event.status,
                            attempts: event.attempts,
                            created_at: new Date(event.created_at).toLocaleString(),
                            delivered_at: event.delivered_at ? new Date(event.delivered_at).toLocaleString() : 'Not delivered',
                            response: event.response_status_code ? 
                                `${event.response_status_code} ${event.error_message ? '- ' + event.error_message : ''}` : 
                                'No response',
                            actions: event.status === 'failure' ? 
                                `<button class="btn btn-sm btn-warning retry-event" data-id="${event.id}">Retry</button>` : 
                                ''
                        })));
                    }
                    
                    eventsTable.draw();
                    $('#webhookEventsModal').modal('show');
                },
                error: function(xhr) {
                    console.error('Error loading webhook events:', xhr.responseText);
                    alert('Error loading webhook events. Please try again.');
                }
            });
        });
        
        // Retry event button click
        $(document).on('click', '.retry-event', function() {
            const eventId = $(this).data('id');
            
            $.ajax({
                url: `/api/webhooks/events/retry/${eventId}`,
                type: 'POST',
                success: function() {
                    alert('Webhook event retry scheduled!');
                    // Refresh events table
                    $('.view-events').trigger('click');
                },
                error: function(xhr) {
                    console.error('Error retrying webhook event:', xhr.responseText);
                    alert('Error retrying webhook event. Please try again.');
                }
            });
        });
    });
</script>
{% endblock %}
