"""
Permissions package for AssetKPI API.

This package contains modules for permission management.
"""
from .constants import (
    PermissionScope,
    DEFAULT_VIEWER_PERMISSIONS,
    DEFAULT_ENGINEER_PERMISSIONS,
    DEFAULT_MANAGER_PERMISSIONS,
    DEFAULT_ADMIN_PERMISSIONS
)

from .middleware import (
    PermissionMiddleware,
    init_permissions,
    has_permission,
    check_endpoint_permission,
    require_permission,
    get_user_permissions
)

__all__ = [
    'PermissionScope',
    'DEFAULT_VIEWER_PERMISSIONS',
    'DEFAULT_ENGINEER_PERMISSIONS',
    'DEFAULT_MANAGER_PERMISSIONS',
    'DEFAULT_ADMIN_PERMISSIONS',
    'PermissionMiddleware',
    'init_permissions',
    'has_permission',
    'check_endpoint_permission',
    'require_permission',
    'get_user_permissions'
]
