/**
 * Tutorial Menu Component
 * 
 * This component provides a menu for accessing tutorials in the AssetKPI application.
 */

class TutorialMenu {
    /**
     * Initialize the tutorial menu.
     * 
     * @param {Object} options - Configuration options
     * @param {string} options.containerId - ID of the container element
     * @param {Function} options.onTutorialSelect - Callback function when a tutorial is selected
     */
    constructor(options = {}) {
        this.containerId = options.containerId || 'tutorial-menu-container';
        this.onTutorialSelect = options.onTutorialSelect || function() {};
        
        // Initialize state
        this.tutorials = [];
        this.isOpen = false;
        
        // UI elements
        this.container = null;
        this.menuButton = null;
        this.menuContent = null;
    }
    
    /**
     * Initialize the tutorial menu.
     */
    async init() {
        // Create container if it doesn't exist
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = this.containerId;
            document.body.appendChild(this.container);
        }
        
        // Create UI elements
        this.createUIElements();
        
        // Add event listeners
        this.addEventListeners();
        
        // Load tutorials
        await this.loadTutorials();
        
        // Render tutorials
        this.renderTutorials();
    }
    
    /**
     * Create UI elements for the tutorial menu.
     */
    createUIElements() {
        // Create menu
        this.menu = document.createElement('div');
        this.menu.className = 'tutorial-menu';
        
        // Create menu button
        this.menuButton = document.createElement('button');
        this.menuButton.className = 'tutorial-menu-button';
        this.menuButton.innerHTML = '<i class="bi bi-question-circle"></i>';
        this.menuButton.setAttribute('aria-label', 'Tutorials');
        this.menu.appendChild(this.menuButton);
        
        // Create menu content
        this.menuContent = document.createElement('div');
        this.menuContent.className = 'tutorial-menu-content';
        this.menuContent.innerHTML = `
            <h3 class="tutorial-menu-title">Tutorials</h3>
            <ul class="tutorial-menu-list"></ul>
        `;
        this.menu.appendChild(this.menuContent);
        
        // Add menu to container
        this.container.appendChild(this.menu);
    }
    
    /**
     * Add event listeners to UI elements.
     */
    addEventListeners() {
        // Menu button click
        this.menuButton.addEventListener('click', () => {
            this.toggleMenu();
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', (event) => {
            if (this.isOpen && !this.menu.contains(event.target)) {
                this.closeMenu();
            }
        });
    }
    
    /**
     * Toggle the menu open/closed.
     */
    toggleMenu() {
        if (this.isOpen) {
            this.closeMenu();
        } else {
            this.openMenu();
        }
    }
    
    /**
     * Open the menu.
     */
    openMenu() {
        this.menu.classList.add('open');
        this.isOpen = true;
    }
    
    /**
     * Close the menu.
     */
    closeMenu() {
        this.menu.classList.remove('open');
        this.isOpen = false;
    }
    
    /**
     * Load tutorials from the API.
     */
    async loadTutorials() {
        try {
            const response = await fetch('/api/tutorials');
            if (!response.ok) {
                throw new Error(`Failed to load tutorials: ${response.statusText}`);
            }
            
            this.tutorials = await response.json();
        } catch (error) {
            console.error('Error loading tutorials:', error);
            this.tutorials = [];
        }
    }
    
    /**
     * Render tutorials in the menu.
     */
    renderTutorials() {
        const listEl = this.menuContent.querySelector('.tutorial-menu-list');
        
        // Clear list
        listEl.innerHTML = '';
        
        // Add tutorials
        if (this.tutorials.length === 0) {
            listEl.innerHTML = '<li class="tutorial-menu-item">No tutorials available</li>';
            return;
        }
        
        this.tutorials.forEach(tutorial => {
            const itemEl = document.createElement('li');
            itemEl.className = 'tutorial-menu-item';
            
            const linkEl = document.createElement('a');
            linkEl.className = 'tutorial-menu-link';
            linkEl.href = '#';
            linkEl.dataset.tutorialId = tutorial.id;
            linkEl.innerHTML = `
                ${tutorial.title}
                <span class="tutorial-menu-status" data-tutorial-id="${tutorial.id}"></span>
            `;
            
            // Add click event
            linkEl.addEventListener('click', (event) => {
                event.preventDefault();
                this.onTutorialSelect(tutorial.id);
                this.closeMenu();
            });
            
            itemEl.appendChild(linkEl);
            listEl.appendChild(itemEl);
        });
        
        // Update completion status
        this.updateCompletionStatus();
    }
    
    /**
     * Update completion status for tutorials.
     */
    updateCompletionStatus() {
        // Get completed tutorials from localStorage
        const completedTutorialsJson = localStorage.getItem('completedTutorials');
        const completedTutorials = completedTutorialsJson ? JSON.parse(completedTutorialsJson) : [];
        
        // Update status indicators
        const statusEls = this.menuContent.querySelectorAll('.tutorial-menu-status');
        statusEls.forEach(statusEl => {
            const tutorialId = statusEl.dataset.tutorialId;
            if (completedTutorials.includes(tutorialId)) {
                statusEl.classList.add('completed');
                statusEl.setAttribute('title', 'Completed');
            } else {
                statusEl.classList.remove('completed');
                statusEl.setAttribute('title', 'Not completed');
            }
        });
    }
}

// Export the TutorialMenu class
export default TutorialMenu;
