import os
import sys
import psycopg2
from psycopg2 import sql
from datetime import datetime

# Database connection string
DB_CONNECTION_STRING = "postgresql://postgres:Arcanum@localhost:5432/AssetKPI"

def execute_query(conn, query, params=None):
    """Execute a query and return results"""
    with conn.cursor() as cur:
        if params:
            cur.execute(query, params)
        else:
            cur.execute(query)
        conn.commit()
        try:
            return cur.fetchall()
        except:
            return None

def column_exists(conn, table, column):
    """Check if a column exists in a table"""
    query = """
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_name = %s AND column_name = %s
    """
    result = execute_query(conn, query, (table, column))
    return bool(result)

def add_column_if_not_exists(conn, table, column, data_type):
    """Add a column to a table if it doesn't exist"""
    if not column_exists(conn, table, column):
        query = sql.SQL("ALTER TABLE {} ADD COLUMN {} {}").format(
            sql.Identifier(table),
            sql.Identifier(column),
            sql.SQL(data_type)
        )
        execute_query(conn, query)
        print(f"Added column {column} to {table}")
    else:
        print(f"Column {column} already exists in {table}")

def main():
    """Main migration function"""
    print(f"Starting database migration for inventory optimization at {datetime.now()}")
    
    try:
        # Connect to the database
        conn = psycopg2.connect(DB_CONNECTION_STRING)
        print("Connected to database")
        
        # Add new columns to spareparts table
        add_column_if_not_exists(conn, "spareparts", "annual_demand", "NUMERIC(12, 2)")
        add_column_if_not_exists(conn, "spareparts", "ordering_cost", "NUMERIC(10, 2)")
        add_column_if_not_exists(conn, "spareparts", "holding_cost_percent", "NUMERIC(5, 2)")
        add_column_if_not_exists(conn, "spareparts", "lead_time_variability", "NUMERIC(5, 2)")
        add_column_if_not_exists(conn, "spareparts", "demand_variability", "NUMERIC(5, 2)")
        add_column_if_not_exists(conn, "spareparts", "service_level", "NUMERIC(5, 2)")
        add_column_if_not_exists(conn, "spareparts", "reorder_point", "NUMERIC(10, 2)")
        add_column_if_not_exists(conn, "spareparts", "min_order_quantity", "INTEGER")
        add_column_if_not_exists(conn, "spareparts", "max_order_quantity", "INTEGER")
        add_column_if_not_exists(conn, "spareparts", "last_usage_date", "DATE")
        add_column_if_not_exists(conn, "spareparts", "days_of_supply", "INTEGER")
        add_column_if_not_exists(conn, "spareparts", "storage_location_id", "INTEGER")
        
        # Create EOQ calculations table if it doesn't exist
        execute_query(conn, """
        CREATE TABLE IF NOT EXISTS eoq_calculations (
            id SERIAL PRIMARY KEY,
            part_id INTEGER NOT NULL REFERENCES spareparts(partid),
            annual_demand NUMERIC(12, 2),
            ordering_cost NUMERIC(10, 2),
            holding_cost NUMERIC(10, 2),
            eoq_value NUMERIC(10, 2),
            annual_ordering_cost NUMERIC(12, 2),
            annual_holding_cost NUMERIC(12, 2),
            total_annual_cost NUMERIC(12, 2),
            optimal_order_frequency INTEGER,
            calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        print("Created eoq_calculations table if it didn't exist")
        
        # Create safety stock calculations table if it doesn't exist
        execute_query(conn, """
        CREATE TABLE IF NOT EXISTS safety_stock_calculations (
            id SERIAL PRIMARY KEY,
            part_id INTEGER NOT NULL REFERENCES spareparts(partid),
            avg_daily_demand NUMERIC(10, 2),
            lead_time_days INTEGER,
            demand_variability NUMERIC(5, 2),
            lead_time_variability NUMERIC(5, 2),
            service_level NUMERIC(5, 2),
            service_level_z NUMERIC(5, 2),
            safety_stock_value NUMERIC(10, 2),
            reorder_point NUMERIC(10, 2),
            calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        print("Created safety_stock_calculations table if it didn't exist")
        
        # Create inventory analysis table if it doesn't exist
        execute_query(conn, """
        CREATE TABLE IF NOT EXISTS inventory_analysis (
            id SERIAL PRIMARY KEY,
            part_id INTEGER NOT NULL REFERENCES spareparts(partid),
            current_stock INTEGER,
            optimal_stock INTEGER,
            stock_difference INTEGER,
            current_cost NUMERIC(12, 2),
            optimal_cost NUMERIC(12, 2),
            potential_savings NUMERIC(12, 2),
            days_of_supply INTEGER,
            stockout_risk NUMERIC(5, 2),
            last_usage_date DATE,
            analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        print("Created inventory_analysis table if it didn't exist")
        
        # Create storage locations table if it doesn't exist
        execute_query(conn, """
        CREATE TABLE IF NOT EXISTS storage_locations (
            location_id SERIAL PRIMARY KEY,
            location_name VARCHAR(100) NOT NULL,
            location_type VARCHAR(50),
            description TEXT,
            parent_location_id INTEGER REFERENCES storage_locations(location_id)
        )
        """)
        print("Created storage_locations table if it didn't exist")
        
        # Add foreign key constraint if it doesn't exist
        if not column_exists(conn, "information_schema.table_constraints", "spareparts_storage_location_id_fkey"):
            try:
                execute_query(conn, """
                ALTER TABLE spareparts 
                ADD CONSTRAINT spareparts_storage_location_id_fkey 
                FOREIGN KEY (storage_location_id) 
                REFERENCES storage_locations(location_id)
                """)
                print("Added foreign key constraint for storage_location_id")
            except Exception as e:
                print(f"Could not add foreign key constraint: {e}")
        
        print(f"Database migration completed successfully at {datetime.now()}")
        
    except Exception as e:
        print(f"Error during migration: {e}")
        sys.exit(1)
    finally:
        if 'conn' in locals():
            conn.close()
            print("Database connection closed")

if __name__ == "__main__":
    main()
