import psycopg2
import json

# Connect to the database
conn = psycopg2.connect("postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
cur = conn.cursor()

# Get the column names of the user_permissions table
cur.execute("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'user_permissions' ORDER BY ordinal_position")
columns = cur.fetchall()
print("Columns in user_permissions table:")
for column in columns:
    print(f"{column[0]} ({column[1]})")

# Get sample data from the user_permissions table
cur.execute("SELECT * FROM user_permissions WHERE user_id = 'test-admin-uid'")
rows = cur.fetchall()
column_names = [desc[0] for desc in cur.description]

print("\nSample data from user_permissions table for test-admin-uid:")
for row in rows:
    permission_data = dict(zip(column_names, row))
    print(json.dumps(permission_data, default=str, indent=2))

# Close the connection
cur.close()
conn.close()
