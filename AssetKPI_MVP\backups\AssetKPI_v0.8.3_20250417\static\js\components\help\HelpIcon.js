/**
 * Help Icon Component
 * 
 * This component renders a help icon that can be clicked to show help content.
 */

class HelpIcon {
    /**
     * Initialize the help icon.
     * 
     * @param {Object} options - Configuration options
     * @param {string} options.selector - CSS selector for the target element
     * @param {string} options.title - Title of the help content
     * @param {string} options.content - Help content
     * @param {string} options.topicId - ID of the help topic
     * @param {string} options.elementId - ID of the help element
     * @param {string} options.type - Type of help display (tooltip, panel, modal)
     * @param {Function} options.onClick - Callback function when the icon is clicked
     * @param {Function} options.onMouseEnter - Callback function when the mouse enters the icon
     * @param {Function} options.onMouseLeave - Callback function when the mouse leaves the icon
     */
    constructor(options = {}) {
        this.selector = options.selector;
        this.title = options.title || 'Help';
        this.content = options.content || '';
        this.topicId = options.topicId || '';
        this.elementId = options.elementId || '';
        this.type = options.type || 'tooltip';
        this.onClick = options.onClick || function() {};
        this.onMouseEnter = options.onMouseEnter || function() {};
        this.onMouseLeave = options.onMouseLeave || function() {};
        
        // Initialize state
        this.element = null;
    }
    
    /**
     * Render the help icon.
     */
    render() {
        // Find target element
        const targetEl = document.querySelector(this.selector);
        if (!targetEl) {
            console.warn(`Target element not found: ${this.selector}`);
            return;
        }
        
        // Check if help icon already exists
        if (targetEl.querySelector('.help-icon')) {
            return;
        }
        
        // Create help icon
        this.element = document.createElement('span');
        this.element.className = 'help-icon';
        this.element.innerHTML = '<i class="bi bi-question-circle"></i>';
        this.element.setAttribute('data-help-topic', this.topicId);
        this.element.setAttribute('data-help-element', this.elementId);
        this.element.setAttribute('data-help-type', this.type);
        this.element.setAttribute('title', this.title);
        
        // Add event listeners
        this.element.addEventListener('click', (event) => {
            event.preventDefault();
            event.stopPropagation();
            this.onClick(this.element, {
                title: this.title,
                content: this.content,
                topicId: this.topicId,
                elementId: this.elementId,
                type: this.type
            });
        });
        
        this.element.addEventListener('mouseenter', () => {
            this.onMouseEnter(this.element, {
                title: this.title,
                content: this.content,
                topicId: this.topicId,
                elementId: this.elementId,
                type: this.type
            });
        });
        
        this.element.addEventListener('mouseleave', () => {
            this.onMouseLeave(this.element);
        });
        
        // Add icon to target element
        targetEl.appendChild(this.element);
    }
    
    /**
     * Remove the help icon.
     */
    remove() {
        if (this.element) {
            this.element.remove();
            this.element = null;
        }
    }
}

// Export the HelpIcon class
export default HelpIcon;
