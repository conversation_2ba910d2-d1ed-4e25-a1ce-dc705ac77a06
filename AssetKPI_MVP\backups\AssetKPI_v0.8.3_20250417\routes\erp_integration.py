"""
ERP integration routes for the AssetKPI application.

This module defines the API routes for ERP integration.
"""

import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status, BackgroundTasks
from sqlalchemy.orm import Session

from models.erp_integration import ERPConnection, ERPDataMapping, ERPSyncLog
from schemas.erp_integration import (
    ERPConnectionCreate,
    ERPConnectionUpdate,
    ERPConnectionInDB,
    ERPDataMappingCreate,
    ERPDataMappingUpdate,
    ERPDataMappingInDB,
    ERPSyncLogInDB,
    ERPTestConnectionRequest,
    ERPTestConnectionResponse,
    ERPSyncRequest,
    ERPSyncResponse
)
from services.erp_integration_service import ERPIntegrationService
from main import User, PaginationParams, paginate_query

# Create a logger for this module
logger = logging.getLogger(__name__)

# Create a router for ERP integration endpoints
erp_integration_router = APIRouter(prefix="/api/erp", tags=["ERP Integration"])

# Store dependencies
get_db_dependency = None
require_admin_dependency = None
require_manager_plus_dependency = None
require_viewer_plus_dependency = None


def init_router(
    get_db: Callable,
    require_admin: Callable,
    require_manager_plus: Callable,
    require_viewer_plus: Callable
):
    """
    Initialize the router with dependencies.
    
    Args:
        get_db: Dependency to get database session
        require_admin: Dependency to require admin role
        require_manager_plus: Dependency to require manager or admin role
        require_viewer_plus: Dependency to require viewer, engineer, manager, or admin role
    """
    global get_db_dependency, require_admin_dependency, require_manager_plus_dependency, require_viewer_plus_dependency
    
    get_db_dependency = get_db
    require_admin_dependency = require_admin
    require_manager_plus_dependency = require_manager_plus
    require_viewer_plus_dependency = require_viewer_plus


# --- ERP Connection Endpoints ---

@erp_integration_router.get(
    "/connections",
    response_model=List[ERPConnectionInDB],
    summary="Get all ERP connections"
)
async def get_erp_connections(
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get all ERP connections.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of ERP connections
    """
    try:
        connections = ERPIntegrationService.get_connections(db)
        return connections
    
    except Exception as e:
        logger.error(f"Error getting ERP connections: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting ERP connections: {str(e)}"
        )


@erp_integration_router.get(
    "/connections/{connection_id}",
    response_model=ERPConnectionInDB,
    summary="Get an ERP connection by ID"
)
async def get_erp_connection(
    connection_id: int = Path(..., ge=1, description="ID of the ERP connection"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get an ERP connection by ID.
    
    Args:
        connection_id: ID of the ERP connection
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ERP connection
    """
    try:
        connection = ERPIntegrationService.get_connection(db, connection_id)
        
        if not connection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ERP connection with ID {connection_id} not found"
            )
        
        return connection
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting ERP connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting ERP connection: {str(e)}"
        )


@erp_integration_router.post(
    "/connections",
    response_model=ERPConnectionInDB,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new ERP connection"
)
async def create_erp_connection(
    connection: ERPConnectionCreate,
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Create a new ERP connection.
    
    Args:
        connection: ERP connection data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created ERP connection
    """
    try:
        connection_data = connection.dict()
        new_connection = ERPIntegrationService.create_connection(db, connection_data, current_user.user_id)
        return new_connection
    
    except Exception as e:
        logger.error(f"Error creating ERP connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating ERP connection: {str(e)}"
        )


@erp_integration_router.put(
    "/connections/{connection_id}",
    response_model=ERPConnectionInDB,
    summary="Update an ERP connection"
)
async def update_erp_connection(
    connection_data: ERPConnectionUpdate,
    connection_id: int = Path(..., ge=1, description="ID of the ERP connection"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Update an ERP connection.
    
    Args:
        connection_data: Updated ERP connection data
        connection_id: ID of the ERP connection
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated ERP connection
    """
    try:
        connection = ERPIntegrationService.update_connection(db, connection_id, connection_data.dict(exclude_unset=True))
        
        if not connection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ERP connection with ID {connection_id} not found"
            )
        
        return connection
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error updating ERP connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating ERP connection: {str(e)}"
        )


@erp_integration_router.delete(
    "/connections/{connection_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete an ERP connection"
)
async def delete_erp_connection(
    connection_id: int = Path(..., ge=1, description="ID of the ERP connection"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Delete an ERP connection.
    
    Args:
        connection_id: ID of the ERP connection
        db: Database session
        current_user: Current authenticated user
    """
    try:
        success = ERPIntegrationService.delete_connection(db, connection_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ERP connection with ID {connection_id} not found"
            )
        
        return None
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error deleting ERP connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting ERP connection: {str(e)}"
        )


@erp_integration_router.post(
    "/connections/test",
    response_model=ERPTestConnectionResponse,
    summary="Test an ERP connection"
)
async def test_erp_connection(
    request: ERPTestConnectionRequest,
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Test an ERP connection.
    
    Args:
        request: Test connection request
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Test results
    """
    try:
        connection_id = request.connection_id
        connection_details = request.connection_details.dict() if request.connection_details else None
        
        result = ERPIntegrationService.test_connection(db, connection_id, connection_details)
        
        return ERPTestConnectionResponse(
            success=result["success"],
            message=result["message"],
            details=result.get("details")
        )
    
    except Exception as e:
        logger.error(f"Error testing ERP connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error testing ERP connection: {str(e)}"
        )


# --- ERP Data Mapping Endpoints ---

@erp_integration_router.get(
    "/mappings",
    response_model=List[ERPDataMappingInDB],
    summary="Get all ERP data mappings"
)
async def get_erp_data_mappings(
    connection_id: Optional[int] = Query(None, description="Filter by connection ID"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get all ERP data mappings.
    
    Args:
        connection_id: Optional connection ID to filter by
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of ERP data mappings
    """
    try:
        mappings = ERPIntegrationService.get_data_mappings(db, connection_id)
        return mappings
    
    except Exception as e:
        logger.error(f"Error getting ERP data mappings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting ERP data mappings: {str(e)}"
        )


@erp_integration_router.get(
    "/mappings/{mapping_id}",
    response_model=ERPDataMappingInDB,
    summary="Get an ERP data mapping by ID"
)
async def get_erp_data_mapping(
    mapping_id: int = Path(..., ge=1, description="ID of the ERP data mapping"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get an ERP data mapping by ID.
    
    Args:
        mapping_id: ID of the ERP data mapping
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ERP data mapping
    """
    try:
        mapping = ERPIntegrationService.get_data_mapping(db, mapping_id)
        
        if not mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ERP data mapping with ID {mapping_id} not found"
            )
        
        return mapping
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting ERP data mapping: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting ERP data mapping: {str(e)}"
        )


@erp_integration_router.post(
    "/mappings",
    response_model=ERPDataMappingInDB,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new ERP data mapping"
)
async def create_erp_data_mapping(
    mapping: ERPDataMappingCreate,
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Create a new ERP data mapping.
    
    Args:
        mapping: ERP data mapping data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created ERP data mapping
    """
    try:
        mapping_data = mapping.dict()
        new_mapping = ERPIntegrationService.create_data_mapping(db, mapping_data)
        return new_mapping
    
    except Exception as e:
        logger.error(f"Error creating ERP data mapping: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating ERP data mapping: {str(e)}"
        )


@erp_integration_router.put(
    "/mappings/{mapping_id}",
    response_model=ERPDataMappingInDB,
    summary="Update an ERP data mapping"
)
async def update_erp_data_mapping(
    mapping_data: ERPDataMappingUpdate,
    mapping_id: int = Path(..., ge=1, description="ID of the ERP data mapping"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Update an ERP data mapping.
    
    Args:
        mapping_data: Updated ERP data mapping data
        mapping_id: ID of the ERP data mapping
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated ERP data mapping
    """
    try:
        mapping = ERPIntegrationService.update_data_mapping(db, mapping_id, mapping_data.dict(exclude_unset=True))
        
        if not mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ERP data mapping with ID {mapping_id} not found"
            )
        
        return mapping
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error updating ERP data mapping: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating ERP data mapping: {str(e)}"
        )


@erp_integration_router.delete(
    "/mappings/{mapping_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete an ERP data mapping"
)
async def delete_erp_data_mapping(
    mapping_id: int = Path(..., ge=1, description="ID of the ERP data mapping"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Delete an ERP data mapping.
    
    Args:
        mapping_id: ID of the ERP data mapping
        db: Database session
        current_user: Current authenticated user
    """
    try:
        success = ERPIntegrationService.delete_data_mapping(db, mapping_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ERP data mapping with ID {mapping_id} not found"
            )
        
        return None
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error deleting ERP data mapping: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting ERP data mapping: {str(e)}"
        )


@erp_integration_router.get(
    "/schema/{connection_id}/{entity_type}",
    summary="Get the schema for an entity type in the ERP system"
)
async def get_erp_entity_schema(
    connection_id: int = Path(..., ge=1, description="ID of the ERP connection"),
    entity_type: str = Path(..., description="Type of entity"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Get the schema for an entity type in the ERP system.
    
    Args:
        connection_id: ID of the ERP connection
        entity_type: Type of entity
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Entity schema
    """
    try:
        result = ERPIntegrationService.get_entity_schema(db, connection_id, entity_type)
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
        
        return result
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting entity schema: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting entity schema: {str(e)}"
        )


@erp_integration_router.post(
    "/sync",
    response_model=ERPSyncResponse,
    summary="Synchronize data between AssetKPI and ERP system"
)
async def sync_erp_data(
    request: ERPSyncRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Synchronize data between AssetKPI and ERP system.
    
    Args:
        request: Synchronization request
        background_tasks: FastAPI background tasks
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Synchronization results
    """
    try:
        # Start synchronization in the background
        def sync_data_task():
            try:
                ERPIntegrationService.sync_data(db, request.mapping_id, request.force)
            except Exception as e:
                logger.error(f"Error in background sync task: {str(e)}")
        
        background_tasks.add_task(sync_data_task)
        
        return ERPSyncResponse(
            success=True,
            message="Synchronization started in the background",
            details={"mapping_id": request.mapping_id}
        )
    
    except Exception as e:
        logger.error(f"Error starting synchronization: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting synchronization: {str(e)}"
        )


@erp_integration_router.get(
    "/sync/logs",
    response_model=List[ERPSyncLogInDB],
    summary="Get synchronization logs"
)
async def get_erp_sync_logs(
    connection_id: Optional[int] = Query(None, description="Filter by connection ID"),
    mapping_id: Optional[int] = Query(None, description="Filter by mapping ID"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of logs to return"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get synchronization logs.
    
    Args:
        connection_id: Optional connection ID to filter by
        mapping_id: Optional mapping ID to filter by
        limit: Maximum number of logs to return
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of synchronization logs
    """
    try:
        logs = ERPIntegrationService.get_sync_logs(db, connection_id, mapping_id, limit)
        return logs
    
    except Exception as e:
        logger.error(f"Error getting synchronization logs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting synchronization logs: {str(e)}"
        )


@erp_integration_router.get(
    "/sync/logs/{log_id}",
    response_model=ERPSyncLogInDB,
    summary="Get a synchronization log by ID"
)
async def get_erp_sync_log(
    log_id: int = Path(..., ge=1, description="ID of the synchronization log"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get a synchronization log by ID.
    
    Args:
        log_id: ID of the synchronization log
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Synchronization log
    """
    try:
        log = ERPIntegrationService.get_sync_log(db, log_id)
        
        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Synchronization log with ID {log_id} not found"
            )
        
        return log
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting synchronization log: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting synchronization log: {str(e)}"
        )
