"""
Webhook routes for the AssetKPI application.

This module defines the API routes for webhook subscriptions and webhook events.
"""

import json
import logging
import secrets
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc

from models.webhooks import WebhookSubscription, WebhookEvent, WebhookEventType
from schemas.webhooks import (
    WebhookSubscriptionCreate,
    WebhookSubscriptionUpdate,
    WebhookSubscriptionInDB,
    WebhookEventCreate,
    WebhookEventInDB,
    WebhookDeliveryStatus,
    WebhookEventTypeEnum
)
from main import User, PaginationParams, paginate_query

# Create a logger for this module
logger = logging.getLogger(__name__)

# Create a router for webhook endpoints
webhooks_router = APIRouter(prefix="/api/webhooks", tags=["Webhooks"])

# Store dependencies
get_db_dependency = None
require_admin_dependency = None
require_manager_plus_dependency = None
require_viewer_plus_dependency = None


def init_router(
    get_db: Callable,
    require_admin: Callable,
    require_manager_plus: Callable,
    require_viewer_plus: Callable
):
    """
    Initialize the router with dependencies.
    
    Args:
        get_db: Dependency to get database session
        require_admin: Dependency to require admin role
        require_manager_plus: Dependency to require manager or admin role
        require_viewer_plus: Dependency to require viewer, engineer, manager, or admin role
    """
    global get_db_dependency, require_admin_dependency, require_manager_plus_dependency, require_viewer_plus_dependency
    
    get_db_dependency = get_db
    require_admin_dependency = require_admin
    require_manager_plus_dependency = require_manager_plus
    require_viewer_plus_dependency = require_viewer_plus


# --- Webhook Subscription Endpoints ---

@webhooks_router.get(
    "",
    response_model=Dict[str, Any],
    summary="Get all webhook subscriptions"
)
async def get_webhook_subscriptions(
    limit: int = Query(10, ge=1, le=100, description="Number of items to return per page"),
    offset: int = Query(0, ge=0, description="Number of items to skip"),
    sort_by: Optional[str] = Query("id", description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name (case-insensitive)"),
    event_type: Optional[WebhookEventTypeEnum] = Query(None, description="Filter by event type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get all webhook subscriptions with pagination and filtering.
    
    Args:
        limit: Number of items to return per page
        offset: Number of items to skip
        sort_by: Field to sort by
        sort_order: Sort order (asc or desc)
        name: Filter by name (case-insensitive)
        event_type: Filter by event type
        is_active: Filter by active status
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Paginated list of webhook subscriptions
    """
    try:
        # Create query
        query = db.query(WebhookSubscription)
        
        # Apply filters
        if name:
            query = query.filter(WebhookSubscription.name.ilike(f"%{name}%"))
        
        if event_type:
            # Filter subscriptions that include the specified event type
            query = query.filter(
                WebhookSubscription.event_types.contains([event_type.value])
            )
        
        if is_active is not None:
            query = query.filter(WebhookSubscription.is_active == is_active)
        
        # Create pagination parameters
        pagination = PaginationParams(
            limit=limit,
            offset=offset,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # Apply pagination
        result = paginate_query(query, pagination, WebhookSubscription, None)
        
        return result
    
    except Exception as e:
        logger.error(f"Error getting webhook subscriptions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting webhook subscriptions: {str(e)}"
        )


@webhooks_router.get(
    "/{subscription_id}",
    response_model=WebhookSubscriptionInDB,
    summary="Get a webhook subscription by ID"
)
async def get_webhook_subscription(
    subscription_id: int = Path(..., ge=1, description="ID of the webhook subscription"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get a webhook subscription by ID.
    
    Args:
        subscription_id: ID of the webhook subscription
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Webhook subscription
    """
    try:
        subscription = db.query(WebhookSubscription).filter(
            WebhookSubscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Webhook subscription with ID {subscription_id} not found"
            )
        
        return subscription
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting webhook subscription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting webhook subscription: {str(e)}"
        )


@webhooks_router.post(
    "",
    response_model=WebhookSubscriptionInDB,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new webhook subscription"
)
async def create_webhook_subscription(
    subscription: WebhookSubscriptionCreate,
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Create a new webhook subscription.
    
    Args:
        subscription: Webhook subscription data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created webhook subscription
    """
    try:
        # Convert event types to JSON array
        event_types_json = [event_type.value for event_type in subscription.event_types]
        
        # Create new subscription
        new_subscription = WebhookSubscription(
            name=subscription.name,
            url=str(subscription.url),
            description=subscription.description,
            event_types=event_types_json,
            auth_type=subscription.auth_type.value,
            auth_credentials=subscription.auth_credentials,
            retry_count=subscription.retry_count,
            retry_interval=subscription.retry_interval,
            timeout=subscription.timeout,
            is_active=True,
            created_by=current_user.user_id,
            delivery_success_count=0,
            delivery_failure_count=0
        )
        
        # Add to database
        db.add(new_subscription)
        db.commit()
        db.refresh(new_subscription)
        
        return new_subscription
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating webhook subscription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating webhook subscription: {str(e)}"
        )


@webhooks_router.put(
    "/{subscription_id}",
    response_model=WebhookSubscriptionInDB,
    summary="Update a webhook subscription"
)
async def update_webhook_subscription(
    subscription_data: WebhookSubscriptionUpdate,
    subscription_id: int = Path(..., ge=1, description="ID of the webhook subscription"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Update a webhook subscription.
    
    Args:
        subscription_data: Updated webhook subscription data
        subscription_id: ID of the webhook subscription
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated webhook subscription
    """
    try:
        # Get existing subscription
        subscription = db.query(WebhookSubscription).filter(
            WebhookSubscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Webhook subscription with ID {subscription_id} not found"
            )
        
        # Update fields if provided
        if subscription_data.name is not None:
            subscription.name = subscription_data.name
        
        if subscription_data.url is not None:
            subscription.url = str(subscription_data.url)
        
        if subscription_data.description is not None:
            subscription.description = subscription_data.description
        
        if subscription_data.event_types is not None:
            subscription.event_types = [event_type.value for event_type in subscription_data.event_types]
        
        if subscription_data.auth_type is not None:
            subscription.auth_type = subscription_data.auth_type.value
        
        if subscription_data.auth_credentials is not None:
            subscription.auth_credentials = subscription_data.auth_credentials
        
        if subscription_data.is_active is not None:
            subscription.is_active = subscription_data.is_active
        
        if subscription_data.retry_count is not None:
            subscription.retry_count = subscription_data.retry_count
        
        if subscription_data.retry_interval is not None:
            subscription.retry_interval = subscription_data.retry_interval
        
        if subscription_data.timeout is not None:
            subscription.timeout = subscription_data.timeout
        
        # Update timestamp
        subscription.updated_at = datetime.now()
        
        # Save changes
        db.commit()
        db.refresh(subscription)
        
        return subscription
    
    except HTTPException:
        raise
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating webhook subscription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating webhook subscription: {str(e)}"
        )


@webhooks_router.delete(
    "/{subscription_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a webhook subscription"
)
async def delete_webhook_subscription(
    subscription_id: int = Path(..., ge=1, description="ID of the webhook subscription"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Delete a webhook subscription.
    
    Args:
        subscription_id: ID of the webhook subscription
        db: Database session
        current_user: Current authenticated user
    """
    try:
        # Get existing subscription
        subscription = db.query(WebhookSubscription).filter(
            WebhookSubscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Webhook subscription with ID {subscription_id} not found"
            )
        
        # Delete subscription
        db.delete(subscription)
        db.commit()
        
        return None
    
    except HTTPException:
        raise
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting webhook subscription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting webhook subscription: {str(e)}"
        )


# --- Webhook Event Endpoints ---

@webhooks_router.get(
    "/events",
    response_model=Dict[str, Any],
    summary="Get webhook events"
)
async def get_webhook_events(
    limit: int = Query(10, ge=1, le=100, description="Number of items to return per page"),
    offset: int = Query(0, ge=0, description="Number of items to skip"),
    sort_by: Optional[str] = Query("id", description="Field to sort by"),
    sort_order: str = Query("desc", description="Sort order (asc or desc)"),
    subscription_id: Optional[int] = Query(None, description="Filter by subscription ID"),
    event_type: Optional[WebhookEventTypeEnum] = Query(None, description="Filter by event type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get webhook events with pagination and filtering.
    
    Args:
        limit: Number of items to return per page
        offset: Number of items to skip
        sort_by: Field to sort by
        sort_order: Sort order (asc or desc)
        subscription_id: Filter by subscription ID
        event_type: Filter by event type
        status: Filter by status
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Paginated list of webhook events
    """
    try:
        # Create query
        query = db.query(WebhookEvent)
        
        # Apply filters
        if subscription_id:
            query = query.filter(WebhookEvent.subscription_id == subscription_id)
        
        if event_type:
            query = query.filter(WebhookEvent.event_type == event_type.value)
        
        if status:
            query = query.filter(WebhookEvent.status == status)
        
        # Create pagination parameters
        pagination = PaginationParams(
            limit=limit,
            offset=offset,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # Apply pagination
        result = paginate_query(query, pagination, WebhookEvent, None)
        
        return result
    
    except Exception as e:
        logger.error(f"Error getting webhook events: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting webhook events: {str(e)}"
        )


@webhooks_router.get(
    "/events/{event_id}",
    response_model=WebhookEventInDB,
    summary="Get a webhook event by ID"
)
async def get_webhook_event(
    event_id: int = Path(..., ge=1, description="ID of the webhook event"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_viewer_plus_dependency)
):
    """
    Get a webhook event by ID.
    
    Args:
        event_id: ID of the webhook event
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Webhook event
    """
    try:
        event = db.query(WebhookEvent).filter(
            WebhookEvent.id == event_id
        ).first()
        
        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Webhook event with ID {event_id} not found"
            )
        
        return event
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Error getting webhook event: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting webhook event: {str(e)}"
        )


@webhooks_router.post(
    "/events/retry/{event_id}",
    response_model=WebhookDeliveryStatus,
    summary="Retry a failed webhook event"
)
async def retry_webhook_event(
    background_tasks: BackgroundTasks,
    event_id: int = Path(..., ge=1, description="ID of the webhook event"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Retry a failed webhook event.
    
    Args:
        background_tasks: FastAPI background tasks
        event_id: ID of the webhook event
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Webhook delivery status
    """
    try:
        # Get event
        event = db.query(WebhookEvent).filter(
            WebhookEvent.id == event_id
        ).first()
        
        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Webhook event with ID {event_id} not found"
            )
        
        # Check if event can be retried
        if event.status == "success":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot retry a successful webhook event"
            )
        
        # Update event status
        event.status = "pending"
        event.next_attempt_time = datetime.now()
        event.updated_at = datetime.now()
        
        # Save changes
        db.commit()
        db.refresh(event)
        
        # Add to background tasks for delivery
        # This will be implemented in the webhook delivery service
        # background_tasks.add_task(deliver_webhook_event, event.id)
        
        # Return delivery status
        return WebhookDeliveryStatus(
            event_id=event.id,
            subscription_id=event.subscription_id,
            event_type=event.event_type,
            status=event.status,
            attempts=event.attempts,
            last_attempt_time=event.updated_at,
            next_attempt_time=event.next_attempt_time,
            response_status_code=event.response_status_code,
            error_message=event.error_message
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error retrying webhook event: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrying webhook event: {str(e)}"
        )


@webhooks_router.post(
    "/test/{subscription_id}",
    response_model=WebhookDeliveryStatus,
    summary="Send a test webhook event"
)
async def send_test_webhook(
    background_tasks: BackgroundTasks,
    subscription_id: int = Path(..., ge=1, description="ID of the webhook subscription"),
    db: Session = Depends(get_db_dependency),
    current_user: User = Depends(require_manager_plus_dependency)
):
    """
    Send a test webhook event.
    
    Args:
        background_tasks: FastAPI background tasks
        subscription_id: ID of the webhook subscription
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Webhook delivery status
    """
    try:
        # Get subscription
        subscription = db.query(WebhookSubscription).filter(
            WebhookSubscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Webhook subscription with ID {subscription_id} not found"
            )
        
        # Check if subscription is active
        if not subscription.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot send test webhook to inactive subscription"
            )
        
        # Create test event
        test_event = WebhookEvent(
            subscription_id=subscription.id,
            event_type="system.test",
            payload={
                "message": "This is a test webhook event",
                "timestamp": datetime.now().isoformat(),
                "test_id": secrets.token_hex(8)
            },
            status="pending",
            attempts=0,
            next_attempt_time=datetime.now(),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # Add to database
        db.add(test_event)
        db.commit()
        db.refresh(test_event)
        
        # Add to background tasks for delivery
        # This will be implemented in the webhook delivery service
        # background_tasks.add_task(deliver_webhook_event, test_event.id)
        
        # Return delivery status
        return WebhookDeliveryStatus(
            event_id=test_event.id,
            subscription_id=test_event.subscription_id,
            event_type=test_event.event_type,
            status=test_event.status,
            attempts=test_event.attempts,
            last_attempt_time=None,
            next_attempt_time=test_event.next_attempt_time,
            response_status_code=None,
            error_message=None
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error sending test webhook: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error sending test webhook: {str(e)}"
        )
