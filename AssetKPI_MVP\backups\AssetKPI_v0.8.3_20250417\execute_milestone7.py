import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def execute_migration():
    """Execute the SQL migration script for Milestone 7."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Read the SQL migration script
        with open('db_migration_milestone7.sql', 'r') as f:
            sql_script = f.read()
        
        # Split the script into individual statements
        statements = sql_script.split(';')
        
        # Execute each statement
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement + ';')
                    print(f"Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"Error executing statement: {statement[:50]}...")
                    print(f"Error: {e}")
        
        # Commit the changes
        conn.commit()
        print("Milestone 7 migration completed successfully!")
        
        # Return the connection and cursor for data population
        return conn, cursor
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return None, None

def populate_sample_data(conn, cursor):
    """Populate the database with sample data for Milestone 7."""
    try:
        print("\nPopulating sample data for Milestone 7...")
        
        # Get labor resources
        cursor.execute("SELECT resource_id, person_name, craft FROM labor_resources")
        resources = cursor.fetchall()
        
        if not resources:
            print("No labor resources found. Please run Milestone 3 first.")
            return
        
        # Sample skills by craft
        skills_by_craft = {
            'Mechanical': [
                ('Bearing Replacement', 'Advanced', 'Bearing Specialist Certification'),
                ('Pump Overhaul', 'Expert', 'Pump Maintenance Certification'),
                ('Gearbox Repair', 'Intermediate', None),
                ('Alignment', 'Advanced', 'Laser Alignment Certification'),
                ('Vibration Analysis', 'Intermediate', 'Vibration Analyst Level I'),
                ('Welding', 'Basic', None),
                ('Machining', 'Intermediate', None),
                ('Lubrication', 'Advanced', 'Lubrication Specialist Certification')
            ],
            'Electrical': [
                ('Motor Control', 'Advanced', 'Motor Control Specialist'),
                ('PLC Programming', 'Expert', 'Allen Bradley Certification'),
                ('VFD Configuration', 'Intermediate', None),
                ('Power Distribution', 'Advanced', 'Electrical Safety Certification'),
                ('Troubleshooting', 'Expert', None),
                ('Wiring', 'Advanced', 'Electrical License'),
                ('Instrumentation', 'Intermediate', None),
                ('Control Systems', 'Advanced', 'Control Systems Certification')
            ],
            'HVAC': [
                ('Refrigeration', 'Advanced', 'EPA Certification'),
                ('Chiller Maintenance', 'Expert', 'Chiller Specialist Certification'),
                ('Air Handling', 'Intermediate', None),
                ('Controls', 'Advanced', 'HVAC Controls Certification'),
                ('Heat Pumps', 'Intermediate', None),
                ('Boilers', 'Advanced', 'Boiler Operator License'),
                ('Ductwork', 'Basic', None),
                ('Energy Management', 'Intermediate', 'Energy Management Certification')
            ],
            'Plumbing': [
                ('Pipe Fitting', 'Advanced', 'Plumbing License'),
                ('Backflow Prevention', 'Expert', 'Backflow Certification'),
                ('Water Treatment', 'Intermediate', None),
                ('Steam Systems', 'Advanced', 'Steam Systems Certification'),
                ('Drainage', 'Intermediate', None),
                ('Pumping Systems', 'Advanced', None),
                ('Valve Repair', 'Expert', None),
                ('Leak Detection', 'Intermediate', None)
            ],
            'Instrumentation': [
                ('Calibration', 'Expert', 'Calibration Technician Certification'),
                ('Flow Measurement', 'Advanced', None),
                ('Pressure Measurement', 'Advanced', None),
                ('Temperature Measurement', 'Intermediate', None),
                ('Level Measurement', 'Intermediate', None),
                ('Analytical Instruments', 'Expert', 'Analytical Specialist Certification'),
                ('Control Valves', 'Advanced', None),
                ('SCADA Systems', 'Intermediate', 'SCADA Certification')
            ],
            'General': [
                ('Safety', 'Advanced', 'OSHA 30 Certification'),
                ('Forklift Operation', 'Intermediate', 'Forklift Operator Certification'),
                ('Inventory Management', 'Basic', None),
                ('Computer Skills', 'Intermediate', None),
                ('Project Management', 'Basic', None),
                ('Documentation', 'Intermediate', None),
                ('Training', 'Basic', None),
                ('Customer Service', 'Intermediate', None)
            ],
            'Welding': [
                ('TIG Welding', 'Expert', 'AWS Certification'),
                ('MIG Welding', 'Advanced', 'AWS Certification'),
                ('Stick Welding', 'Intermediate', None),
                ('Pipe Welding', 'Expert', 'Pipe Welding Certification'),
                ('Structural Welding', 'Advanced', 'Structural Welding Certification'),
                ('Aluminum Welding', 'Intermediate', None),
                ('Stainless Steel Welding', 'Advanced', None),
                ('Welding Inspection', 'Basic', None)
            ],
            'Machinist': [
                ('CNC Programming', 'Expert', 'CNC Programmer Certification'),
                ('Lathe Operation', 'Advanced', None),
                ('Milling', 'Advanced', None),
                ('Grinding', 'Intermediate', None),
                ('Precision Measurement', 'Expert', 'Metrology Certification'),
                ('Blueprint Reading', 'Advanced', None),
                ('Tooling', 'Intermediate', None),
                ('CAD/CAM', 'Basic', None)
            ]
        }
        
        # Default skills for any craft not specifically listed
        default_skills = [
            ('Equipment Inspection', 'Intermediate', None),
            ('Preventive Maintenance', 'Intermediate', None),
            ('Troubleshooting', 'Basic', None),
            ('Documentation', 'Basic', None),
            ('Safety Procedures', 'Intermediate', 'Safety Training Certification'),
            ('Basic Hand Tools', 'Advanced', None),
            ('Computer Skills', 'Basic', None),
            ('Communication', 'Intermediate', None)
        ]
        
        # Add skills to resources
        print("Adding skills to labor resources...")
        
        skills_added = 0
        
        for resource_id, person_name, craft in resources:
            # Determine which skills to add based on craft
            if craft in skills_by_craft:
                available_skills = skills_by_craft[craft]
            else:
                available_skills = default_skills
            
            # Add 3-6 skills per resource
            num_skills = random.randint(3, min(6, len(available_skills)))
            
            # Select random skills
            selected_skills = random.sample(available_skills, num_skills)
            
            for skill_name, proficiency_level, certification in selected_skills:
                # Determine if certification has an expiration date
                expiration_date = None
                if certification:
                    # 70% chance of having an expiration date
                    if random.random() < 0.7:
                        # Random date in the future (1-3 years)
                        expiration_date = datetime.now().date() + timedelta(days=random.randint(30, 365*3))
                
                cursor.execute(
                    """
                    INSERT INTO resource_skills
                    (resource_id, skill_name, proficiency_level, certification, expiration_date)
                    VALUES (%s, %s, %s, %s, %s)
                    """,
                    (resource_id, skill_name, proficiency_level, certification, expiration_date)
                )
                
                skills_added += 1
            
            print(f"  - Added {num_skills} skills to {person_name}")
        
        print(f"  - Total skills added: {skills_added}")
        
        # Sample tools and equipment
        tools = [
            # Hand Tools
            ('Wrench Set - Standard', 'Hand Tool', 'HT-001', 'Available', 'Tool Crib A', None),
            ('Wrench Set - Metric', 'Hand Tool', 'HT-002', 'Available', 'Tool Crib A', None),
            ('Socket Set - Standard', 'Hand Tool', 'HT-003', 'Available', 'Tool Crib A', None),
            ('Socket Set - Metric', 'Hand Tool', 'HT-004', 'Available', 'Tool Crib A', None),
            ('Screwdriver Set', 'Hand Tool', 'HT-005', 'Available', 'Tool Crib A', None),
            ('Hammer Set', 'Hand Tool', 'HT-006', 'Available', 'Tool Crib A', None),
            ('Pliers Set', 'Hand Tool', 'HT-007', 'Available', 'Tool Crib A', None),
            ('Allen Wrench Set', 'Hand Tool', 'HT-008', 'Available', 'Tool Crib A', None),
            
            # Power Tools
            ('Drill - Cordless', 'Power Tool', 'PT-001', 'Available', 'Tool Crib B', None),
            ('Impact Driver', 'Power Tool', 'PT-002', 'Available', 'Tool Crib B', None),
            ('Angle Grinder', 'Power Tool', 'PT-003', 'Available', 'Tool Crib B', None),
            ('Circular Saw', 'Power Tool', 'PT-004', 'Available', 'Tool Crib B', None),
            ('Reciprocating Saw', 'Power Tool', 'PT-005', 'Available', 'Tool Crib B', None),
            ('Heat Gun', 'Power Tool', 'PT-006', 'Available', 'Tool Crib B', None),
            ('Rotary Tool', 'Power Tool', 'PT-007', 'Available', 'Tool Crib B', None),
            ('Jigsaw', 'Power Tool', 'PT-008', 'Available', 'Tool Crib B', None),
            
            # Diagnostic Equipment
            ('Multimeter', 'Diagnostic', 'DE-001', 'Available', 'Electrical Shop', datetime.now().date() + timedelta(days=180)),
            ('Clamp Meter', 'Diagnostic', 'DE-002', 'Available', 'Electrical Shop', datetime.now().date() + timedelta(days=180)),
            ('Oscilloscope', 'Diagnostic', 'DE-003', 'Available', 'Electrical Shop', datetime.now().date() + timedelta(days=180)),
            ('Infrared Thermometer', 'Diagnostic', 'DE-004', 'Available', 'Maintenance Shop', datetime.now().date() + timedelta(days=180)),
            ('Vibration Analyzer', 'Diagnostic', 'DE-005', 'Available', 'Maintenance Shop', datetime.now().date() + timedelta(days=180)),
            ('Ultrasonic Leak Detector', 'Diagnostic', 'DE-006', 'Available', 'Maintenance Shop', datetime.now().date() + timedelta(days=180)),
            ('Pressure Gauge Set', 'Diagnostic', 'DE-007', 'Available', 'HVAC Shop', datetime.now().date() + timedelta(days=180)),
            ('Thermal Imaging Camera', 'Diagnostic', 'DE-008', 'Available', 'Electrical Shop', datetime.now().date() + timedelta(days=180)),
            
            # Specialty Tools
            ('Bearing Puller Set', 'Specialty', 'ST-001', 'Available', 'Maintenance Shop', None),
            ('Hydraulic Press', 'Specialty', 'ST-002', 'Available', 'Maintenance Shop', None),
            ('Pipe Threading Machine', 'Specialty', 'ST-003', 'Available', 'Plumbing Shop', None),
            ('Tube Bender', 'Specialty', 'ST-004', 'Available', 'HVAC Shop', None),
            ('Refrigerant Recovery Machine', 'Specialty', 'ST-005', 'Available', 'HVAC Shop', datetime.now().date() + timedelta(days=365)),
            ('Welding Machine - TIG', 'Specialty', 'ST-006', 'Available', 'Welding Shop', None),
            ('Welding Machine - MIG', 'Specialty', 'ST-007', 'Available', 'Welding Shop', None),
            ('Laser Alignment Tool', 'Specialty', 'ST-008', 'Available', 'Maintenance Shop', datetime.now().date() + timedelta(days=365)),
            
            # Calibration Equipment
            ('Pressure Calibrator', 'Calibration', 'CE-001', 'Available', 'Instrumentation Shop', datetime.now().date() + timedelta(days=90)),
            ('Temperature Calibrator', 'Calibration', 'CE-002', 'Available', 'Instrumentation Shop', datetime.now().date() + timedelta(days=90)),
            ('Flow Calibrator', 'Calibration', 'CE-003', 'Available', 'Instrumentation Shop', datetime.now().date() + timedelta(days=90)),
            ('Electrical Calibrator', 'Calibration', 'CE-004', 'Available', 'Instrumentation Shop', datetime.now().date() + timedelta(days=90)),
            ('Torque Wrench Calibrator', 'Calibration', 'CE-005', 'Available', 'Calibration Lab', datetime.now().date() + timedelta(days=90)),
            ('Weight Set', 'Calibration', 'CE-006', 'Available', 'Calibration Lab', datetime.now().date() + timedelta(days=90)),
            ('Gauge Block Set', 'Calibration', 'CE-007', 'Available', 'Calibration Lab', datetime.now().date() + timedelta(days=90)),
            ('Multimeter Calibrator', 'Calibration', 'CE-008', 'Available', 'Calibration Lab', datetime.now().date() + timedelta(days=90))
        ]
        
        print("Adding tools and equipment...")
        
        tool_ids = {}
        for tool in tools:
            cursor.execute(
                """
                INSERT INTO tools_equipment
                (tool_name, tool_type, serial_number, status, location, calibration_due_date)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING tool_id
                """,
                tool
            )
            tool_id = cursor.fetchone()[0]
            tool_ids[tool[0]] = tool_id
        
        print(f"  - Added {len(tools)} tools and equipment")
        
        # Get completed work orders
        cursor.execute("""
            SELECT workorderid, startdate, enddate, assignedto
            FROM workorders
            WHERE status IN ('Completed', 'Closed')
            AND startdate IS NOT NULL
            AND enddate IS NOT NULL
        """)
        work_orders = cursor.fetchall()
        
        if work_orders:
            print("Adding work order tool usage...")
            
            # Create tool usage records
            tool_usages = 0
            
            for wo_id, start_date, end_date, assigned_to in work_orders:
                # Determine if this work order used tools (70% chance)
                if random.random() < 0.7:
                    # Determine how many tools were used (1-3)
                    num_tools = random.randint(1, 3)
                    
                    # Select random tools
                    selected_tools = random.sample(list(tool_ids.items()), num_tools)
                    
                    for tool_name, tool_id in selected_tools:
                        # Checkout date should be on or after start date
                        checkout_date = start_date + timedelta(hours=random.randint(0, 8))
                        
                        # Return date should be before end date
                        return_date = min(end_date, checkout_date + timedelta(hours=random.randint(1, 24)))
                        
                        cursor.execute(
                            """
                            INSERT INTO work_order_tools
                            (workorder_id, tool_id, checkout_date, return_date, checked_out_by)
                            VALUES (%s, %s, %s, %s, %s)
                            """,
                            (wo_id, tool_id, checkout_date, return_date, assigned_to)
                        )
                        
                        tool_usages += 1
            
            print(f"  - Added {tool_usages} work order tool usage records")
        else:
            print("  - No completed work orders found for tool usage")
        
        # Commit the changes
        conn.commit()
        print("Sample data population completed successfully!")
        
    except Exception as e:
        print(f"Error populating sample data: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()

def main():
    """Main function to execute migration and populate data."""
    conn, cursor = execute_migration()
    
    if conn and cursor:
        populate_sample_data(conn, cursor)
        cursor.close()
        conn.close()
    
    print("Milestone 7 completed!")

if __name__ == "__main__":
    main()
