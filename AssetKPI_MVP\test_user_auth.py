"""
Test authentication module for AssetKPI.
This module provides functions to handle test users without requiring Firebase authentication.
"""

from fastapi import HTTPEx<PERSON>, status
from sqlalchemy.orm import Session
import sys
import os

# Add the parent directory to sys.path to allow imports from main
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import User model from db_models to avoid circular imports
from db_models import User, UserRole

# Map test tokens to user IDs
TEST_TOKEN_MAP = {
    'test-admin-token': ('test-admin-uid', 'admin'),
    'test-manager-token': ('test-manager-uid', 'manager'),
    'test-engineer-token': ('test-engineer-uid', 'engineer'),
    'test-viewer-token': ('test-viewer-uid', 'viewer'),
    'johan-token': ('uasUzj4IXFaqJC3pcEiOCL3vD3t2', 'admin')  # <PERSON>'s user ID
}

# Print debug information
print(f"DEBUG: test_user_auth.py loaded successfully")

def handle_test_user_token(token: str, db: Session) -> User:
    """
    Handle test user tokens without requiring Firebase authentication.

    Args:
        token: The test token
        db: Database session

    Returns:
        User object if token is valid, None otherwise

    Raises:
        HTTPException: If token is invalid or user not found
    """
    if token in TEST_TOKEN_MAP:
        user_id, role = TEST_TOKEN_MAP[token]
        user = db.query(User).filter(User.user_id == user_id).first()
        if user:
            print(f"DEBUG: Found test {role} user: {user.email} with role {user.role}")
            return user
        else:
            print(f"DEBUG: Test {role} user with ID {user_id} not found in database")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Test user with ID {user_id} not found in database"
            )
    else:
        print(f"DEBUG: Invalid test token: {token}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid test user token or user not found"
        )


def is_test_user_token(token: str) -> bool:
    """
    Check if the token is a test user token.

    Args:
        token: The token to check

    Returns:
        True if token is a test user token, False otherwise
    """
    return token in TEST_TOKEN_MAP
