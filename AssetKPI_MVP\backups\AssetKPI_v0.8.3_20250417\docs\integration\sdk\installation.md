# Installation

This page explains how to install the AssetKPI Python SDK.

## Table of Contents

- [Requirements](#requirements)
- [Installation Methods](#installation-methods)
- [Verifying Installation](#verifying-installation)
- [Troubleshooting](#troubleshooting)
- [Related Topics](#related-topics)

## Requirements

Before installing the AssetKPI Python SDK, ensure that you have the following:

- Python 3.7 or later
- pip (Python package installer)
- Internet connection (for downloading the package)

## Installation Methods

### Installing from PyPI

The recommended way to install the AssetKPI Python SDK is from PyPI using pip:

```bash
pip install assetkpi-sdk
```

This will install the latest stable version of the SDK and its dependencies.

### Installing from Source

You can also install the SDK from source:

1. Clone the repository:
   ```bash
   git clone https://github.com/assetkpi/assetkpi-sdk-python.git
   ```

2. Navigate to the repository directory:
   ```bash
   cd assetkpi-sdk-python
   ```

3. Install the package:
   ```bash
   pip install -e .
   ```

This will install the SDK in development mode, allowing you to modify the source code and see the changes immediately.

### Installing in a Virtual Environment

It's recommended to install the SDK in a virtual environment to avoid conflicts with other packages:

1. Create a virtual environment:
   ```bash
   python -m venv assetkpi-env
   ```

2. Activate the virtual environment:
   - On Windows:
     ```bash
     assetkpi-env\\Scripts\\activate
     ```
   - On macOS and Linux:
     ```bash
     source assetkpi-env/bin/activate
     ```

3. Install the SDK:
   ```bash
   pip install assetkpi-sdk
   ```

## Verifying Installation

To verify that the SDK is installed correctly, you can import it in Python:

```python
import assetkpi
print(assetkpi.__version__)
```

This should print the version of the SDK without any errors.

You can also run a simple test to verify that the SDK can connect to the AssetKPI API:

```python
from assetkpi import AssetKPISDK

# Initialize the SDK with your API key
sdk = AssetKPISDK(
    base_url="http://localhost:8000/api",
    api_key="your-api-key"
)

# Get the current user
try:
    user = sdk.get_current_user()
    print(f"Connected as: {user['email']}")
except Exception as e:
    print(f"Error: {str(e)}")
```

Replace `"your-api-key"` with your actual API key.

## Troubleshooting

### Common Installation Issues

#### Package Not Found

If you get a "Package not found" error when installing from PyPI, try updating pip:

```bash
pip install --upgrade pip
```

#### Dependency Conflicts

If you encounter dependency conflicts, try installing the SDK in a virtual environment as described above.

#### Permission Errors

If you get permission errors when installing the SDK, try using the `--user` flag:

```bash
pip install --user assetkpi-sdk
```

Or run the installation with administrator privileges.

### Getting Help

If you continue to experience issues with the installation, please contact AssetKPI support or open an issue on the GitHub repository.

## Related Topics

- [Authentication](./authentication.md)
- [Basic Usage](./basic-usage.md)
- [Advanced Usage](./advanced-usage.md)
- [Examples](./examples.md)
- [API Reference](../reference/sdk.md)
