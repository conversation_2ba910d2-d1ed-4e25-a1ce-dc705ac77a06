"""
KPI API client module.

This module provides methods for interacting with the KPI-related endpoints
of the AssetKPI API.
"""

from typing import Dict, List, Optional, Union, Any

from .client import AssetKPIClient


class KPIClient:
    """Client for KPI-related API endpoints."""
    
    def __init__(self, client: AssetKPIClient):
        """
        Initialize the KPI client.
        
        Args:
            client: The AssetKPI API client
        """
        self.client = client
    
    def get_latest_kpis(self) -> Dict[str, Any]:
        """
        Get the latest KPI values.
        
        Returns:
            Dictionary of KPI values
        """
        return self.client.get("/kpis/latest")
    
    def get_kpi_history(
        self,
        kpi_name: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        asset_id: Optional[int] = None,
        limit: int = 1000,
    ) -> List[Dict[str, Any]]:
        """
        Get historical data for a specific KPI.
        
        Args:
            kpi_name: Name of the KPI
            start_date: Start date for filtering (YYYY-MM-DD)
            end_date: End date for filtering (YYYY-MM-DD)
            asset_id: Filter by asset ID
            limit: Maximum number of data points to return
            
        Returns:
            List of KPI history points
        """
        params = {"limit": limit}
        
        if start_date:
            params["start_date"] = start_date
        
        if end_date:
            params["end_date"] = end_date
        
        if asset_id:
            params["asset_id"] = asset_id
        
        return self.client.get(f"/kpi/history/{kpi_name}", params=params)
    
    def get_kpi_analytics(
        self,
        kpi_type: str = "oee",
        asset_category: str = "all",
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
        time_aggregation: str = "monthly",
        anomaly_detection: str = "auto",
        benchmark: str = "none",
    ) -> Dict[str, Any]:
        """
        Get KPI analytics data for visualization.
        
        Args:
            kpi_type: Type of KPI to analyze
            asset_category: Asset category to filter by
            date_from: Start date for filtering (YYYY-MM-DD)
            date_to: End date for filtering (YYYY-MM-DD)
            time_aggregation: Time aggregation level
            anomaly_detection: Anomaly detection method
            benchmark: Benchmark to compare against
            
        Returns:
            KPI analytics data
        """
        params = {
            "kpi_type": kpi_type,
            "asset_category": asset_category,
            "time_aggregation": time_aggregation,
            "anomaly_detection": anomaly_detection,
            "benchmark": benchmark,
        }
        
        if date_from:
            params["date_from"] = date_from
        
        if date_to:
            params["date_to"] = date_to
        
        return self.client.get("/kpi/analytics", params=params)
    
    def get_dashboard_kpi_history(
        self,
        kpi_name: str,
        limit: int = 100,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        asset_id: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get historical data for a specific KPI for dashboard use.
        
        Args:
            kpi_name: Name of the KPI
            limit: Maximum number of data points to return
            start_date: Start date for filtering (YYYY-MM-DD)
            end_date: End date for filtering (YYYY-MM-DD)
            asset_id: Filter by asset ID
            
        Returns:
            List of KPI history points
        """
        params = {"limit": limit}
        
        if start_date:
            params["start_date"] = start_date
        
        if end_date:
            params["end_date"] = end_date
        
        if asset_id:
            params["asset_id"] = asset_id
        
        return self.client.get(f"/dashboard/kpi/history/{kpi_name}", params=params)
    
    def get_asset_performance(
        self,
        metric: str = "availability",
        time_period: str = "month",
        aggregation: str = "daily",
        limit: str = "10",
    ) -> Dict[str, Any]:
        """
        Get asset performance data for visualization.
        
        Args:
            metric: Performance metric to analyze
            time_period: Time period to analyze
            aggregation: Data aggregation level
            limit: Number of assets to return
            
        Returns:
            Asset performance data
        """
        params = {
            "metric": metric,
            "time_period": time_period,
            "aggregation": aggregation,
            "limit": limit,
        }
        
        return self.client.get("/asset-performance", params=params)
