{% extends "layout.html" %}

{% block title %}Chart Examples{% endblock %}

{% block styles %}
<style>
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 30px;
    }
    
    .chart-actions {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .chart-title {
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2">Chart Examples</h1>
            <p class="text-muted">Demonstration of chart utility functions and responsive charts</p>
        </div>
    </div>

    <div class="row">
        <!-- Line Chart -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Line Chart</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="chart-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('lineChart', 'line-chart')">
                                    <i class="bi bi-file-earmark-image"></i> PNG
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('lineChart', 'Line Chart Example', 'line-chart')">
                                    <i class="bi bi-file-earmark-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                        <canvas id="lineChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bar Chart -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Bar Chart</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="chart-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('barChart', 'bar-chart')">
                                    <i class="bi bi-file-earmark-image"></i> PNG
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('barChart', 'Bar Chart Example', 'bar-chart')">
                                    <i class="bi bi-file-earmark-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                        <canvas id="barChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Pie Chart -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Pie Chart</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="chart-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('pieChart', 'pie-chart')">
                                    <i class="bi bi-file-earmark-image"></i> PNG
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('pieChart', 'Pie Chart Example', 'pie-chart')">
                                    <i class="bi bi-file-earmark-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                        <canvas id="pieChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Doughnut Chart -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Doughnut Chart</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="chart-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('doughnutChart', 'doughnut-chart')">
                                    <i class="bi bi-file-earmark-image"></i> PNG
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('doughnutChart', 'Doughnut Chart Example', 'doughnut-chart')">
                                    <i class="bi bi-file-earmark-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                        <canvas id="doughnutChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Radar Chart -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Radar Chart</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="chart-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('radarChart', 'radar-chart')">
                                    <i class="bi bi-file-earmark-image"></i> PNG
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('radarChart', 'Radar Chart Example', 'radar-chart')">
                                    <i class="bi bi-file-earmark-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                        <canvas id="radarChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Responsive Chart -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Responsive Chart</h5>
                    <p class="card-subtitle text-muted">Resize your browser window to see the chart adapt</p>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="chart-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPNG('responsiveChart', 'responsive-chart')">
                                    <i class="bi bi-file-earmark-image"></i> PNG
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="AssetKPICharts.exportChartAsPDF('responsiveChart', 'Responsive Chart Example', 'responsive-chart')">
                                    <i class="bi bi-file-earmark-pdf"></i> PDF
                                </button>
                            </div>
                        </div>
                        <canvas id="responsiveChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Register the datalabels plugin
        Chart.register(ChartDataLabels);
        
        // Sample data for charts
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const lineData = [65, 59, 80, 81, 56, 55, 40, 45, 60, 70, 75, 80];
        const barData = [28, 48, 40, 19, 86, 27, 90, 65, 59, 80, 81, 56];
        const pieData = [300, 50, 100, 75, 25];
        const pieLabels = ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'];
        const radarLabels = ['Reliability', 'Performance', 'Quality', 'Availability', 'Maintainability', 'Safety'];
        const radarData1 = [85, 70, 90, 80, 75, 95];
        const radarData2 = [65, 80, 70, 90, 85, 75];
        
        // Create Line Chart
        const lineDatasets = [{
            label: 'Dataset 1',
            data: lineData,
            borderColor: AssetKPICharts.colors.primary,
            backgroundColor: AssetKPICharts.colors.primaryLight,
            fill: true
        }];
        
        const lineOptions = {
            plugins: {
                title: {
                    text: 'Monthly Performance'
                }
            }
        };
        
        AssetKPICharts.createLineChart('lineChart', months, lineDatasets, lineOptions);
        
        // Create Bar Chart
        const barDatasets = [{
            label: 'Dataset 1',
            data: barData,
            backgroundColor: AssetKPICharts.colors.chart1
        }];
        
        const barOptions = {
            plugins: {
                title: {
                    text: 'Monthly Production'
                },
                datalabels: {
                    color: '#fff',
                    font: {
                        weight: 'bold'
                    },
                    formatter: Math.round
                }
            }
        };
        
        AssetKPICharts.createBarChart('barChart', months, barDatasets, barOptions);
        
        // Create Pie Chart
        const pieColors = [
            AssetKPICharts.colors.chart1,
            AssetKPICharts.colors.chart2,
            AssetKPICharts.colors.chart3,
            AssetKPICharts.colors.chart4,
            AssetKPICharts.colors.chart5
        ];
        
        const pieOptions = {
            plugins: {
                title: {
                    text: 'Resource Allocation'
                },
                datalabels: {
                    color: '#fff',
                    font: {
                        weight: 'bold'
                    },
                    formatter: (value, ctx) => {
                        const total = ctx.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = Math.round((value / total) * 100);
                        return percentage + '%';
                    }
                }
            }
        };
        
        AssetKPICharts.createPieChart('pieChart', pieLabels, pieData, pieColors, pieOptions);
        
        // Create Doughnut Chart
        const doughnutOptions = {
            plugins: {
                title: {
                    text: 'Budget Distribution'
                },
                datalabels: {
                    color: '#fff',
                    font: {
                        weight: 'bold'
                    },
                    formatter: (value, ctx) => {
                        const total = ctx.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = Math.round((value / total) * 100);
                        return percentage + '%';
                    }
                }
            }
        };
        
        AssetKPICharts.createDoughnutChart('doughnutChart', pieLabels, pieData, pieColors, doughnutOptions);
        
        // Create Radar Chart
        const radarDatasets = [
            {
                label: 'Current Period',
                data: radarData1,
                borderColor: AssetKPICharts.colors.primary,
                backgroundColor: 'rgba(13, 110, 253, 0.2)',
                pointBackgroundColor: AssetKPICharts.colors.primary
            },
            {
                label: 'Previous Period',
                data: radarData2,
                borderColor: AssetKPICharts.colors.secondary,
                backgroundColor: 'rgba(108, 117, 125, 0.2)',
                pointBackgroundColor: AssetKPICharts.colors.secondary
            }
        ];
        
        const radarOptions = {
            plugins: {
                title: {
                    text: 'Asset Performance Metrics'
                }
            },
            scales: {
                r: {
                    min: 0,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            }
        };
        
        AssetKPICharts.createRadarChart('radarChart', radarLabels, radarDatasets, radarOptions);
        
        // Create Responsive Chart
        const responsiveDatasets = [
            {
                label: 'Actual',
                data: [65, 59, 80, 81, 56, 55],
                borderColor: AssetKPICharts.colors.primary,
                backgroundColor: 'rgba(13, 110, 253, 0.2)',
                fill: true
            },
            {
                label: 'Target',
                data: [70, 65, 85, 85, 60, 60],
                borderColor: AssetKPICharts.colors.success,
                backgroundColor: 'transparent',
                borderDash: [5, 5]
            }
        ];
        
        const responsiveOptions = {
            plugins: {
                title: {
                    text: 'KPI Tracking'
                }
            },
            scales: {
                y: {
                    min: 0,
                    max: 100
                }
            }
        };
        
        // Create a responsive chart that adapts to screen size
        AssetKPICharts.createResponsiveChart('responsiveChart', function() {
            return AssetKPICharts.createLineChart('responsiveChart', ['Q1', 'Q2', 'Q3', 'Q4', 'Q5', 'Q6'], responsiveDatasets, responsiveOptions);
        });
    });
</script>
{% endblock %}
