# ERP Integration

AssetKPI can integrate with various ERP systems to synchronize data such as assets, inventory, and work orders. This enables you to maintain consistent data across your enterprise systems.

## Table of Contents

- [Introduction](#introduction)
- [ERP Integration Concepts](./concepts.md)
- [Supported ERP Systems](./supported-systems.md)
- [Connection Setup](./connection-setup.md)
- [Data Mapping](./data-mapping.md)
- [Synchronization](./synchronization.md)
- [Examples](./examples.md)
- [API Reference](../reference/erp.md)

## Introduction

ERP integration allows AssetKPI to exchange data with your enterprise resource planning (ERP) system. This enables you to maintain consistent data across your organization and leverage the strengths of both systems.

### Key Benefits

- **Data Consistency**: Maintain consistent data across systems
- **Reduced Manual Entry**: Eliminate the need for duplicate data entry
- **Process Automation**: Automate data exchange between systems
- **Enhanced Reporting**: Combine data from multiple systems for comprehensive reporting
- **Improved Decision Making**: Access up-to-date information from all systems

### How ERP Integration Works

1. You configure a connection to your ERP system in AssetKPI
2. You define data mappings between AssetKPI and your ERP system
3. Data is synchronized between the systems based on your configuration
4. You can view synchronization logs to monitor the integration

## Getting Started

To get started with ERP integration, follow these steps:

1. [Understand ERP integration concepts](./concepts.md)
2. [Check if your ERP system is supported](./supported-systems.md)
3. [Set up a connection to your ERP system](./connection-setup.md)
4. [Define data mappings](./data-mapping.md)
5. [Configure synchronization](./synchronization.md)
6. [Explore examples](./examples.md)

## Next Steps

- [ERP Integration Concepts](./concepts.md)
- [Supported ERP Systems](./supported-systems.md)
- [Connection Setup](./connection-setup.md)
- [Data Mapping](./data-mapping.md)
- [Synchronization](./synchronization.md)
- [Examples](./examples.md)
- [API Reference](../reference/erp.md)
