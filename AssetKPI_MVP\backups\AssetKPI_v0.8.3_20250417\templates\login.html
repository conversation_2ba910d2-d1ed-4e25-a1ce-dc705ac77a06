<!DOCTYPE html>
<html lang="en">
<head>
    <title>AssetKPI - Login</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 40px;
        }
        .login-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header img {
            max-width: 200px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .btn-login {
            width: 100%;
            padding: 10px;
            background-color: #0d6efd;
            border: none;
        }
        .login-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #6c757d;
        }
        .alert {
            display: none;
            margin-top: 20px;
        }
        #loading-spinner {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        .auth-token-section {
            margin-top: 30px;
            display: none;
        }
        .auth-token-section pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .token-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <h2>AssetKPI</h2>
                <p class="text-muted">Sign in to access your dashboard</p>
            </div>

            <div id="login-form">
                <div class="form-group">
                    <label for="email">Email address</label>
                    <input type="email" class="form-control" id="email" placeholder="Enter email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" class="form-control" id="password" placeholder="Password" value="TestTest">
                </div>
                <button type="button" class="btn btn-primary btn-login" id="login-button">Sign In</button>

                <div class="alert alert-danger" id="error-message" role="alert"></div>
                <div class="alert alert-success" id="success-message" role="alert"></div>

                <div id="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Authenticating...</p>
                </div>
            </div>

            <div class="auth-token-section" id="auth-token-section">
                <h5>Authentication Successful</h5>
                <p>Your ID token:</p>
                <pre id="id-token"></pre>
                <div class="token-actions">
                    <button type="button" class="btn btn-outline-secondary" id="copy-token-button">Copy Token</button>
                    <button type="button" class="btn btn-success" id="test-api-button">Test API Endpoint</button>
                    <button type="button" class="btn btn-primary" id="go-to-dashboard">Go to Dashboard</button>
                </div>
                <div class="alert alert-info mt-3" id="api-response" role="alert"></div>
                <button type="button" class="btn btn-outline-danger mt-3" id="sign-out-button">Sign Out</button>
            </div>

            <div class="login-footer">
                <p>AssetKPI &copy; 2025. All rights reserved.</p>
            </div>
        </div>
    </div>

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <!-- Firebase Auth -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <!-- AssetKPI Auth Utility -->
    <script src="/static/js/auth.js"></script>

    <script>
        // DOM elements
        const loginButton = document.getElementById('login-button');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const errorMessage = document.getElementById('error-message');
        const successMessage = document.getElementById('success-message');
        const loadingSpinner = document.getElementById('loading-spinner');
        const authTokenSection = document.getElementById('auth-token-section');
        const idTokenElement = document.getElementById('id-token');
        const copyTokenButton = document.getElementById('copy-token-button');
        const testApiButton = document.getElementById('test-api-button');
        const apiResponse = document.getElementById('api-response');
        const goToDashboardButton = document.getElementById('go-to-dashboard');
        const signOutButton = document.getElementById('sign-out-button');
        const loginForm = document.getElementById('login-form');

        // Function to show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
            loadingSpinner.style.display = 'none';
        }

        // Function to show success message
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        // Function to handle login
        async function handleLogin() {
            const email = emailInput.value.trim();
            const password = passwordInput.value;

            // Basic validation
            if (!email || !password) {
                showError('Please enter both email and password.');
                return;
            }

            // Show loading spinner
            loadingSpinner.style.display = 'block';
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            try {
                console.log('Attempting to sign in with:', email);

                // Check if Firebase is initialized properly
                if (!firebase || !firebase.auth) {
                    console.error('Firebase not initialized properly');
                    showError('Firebase initialization error. Please check the console for details.');
                    return;
                }

                // Try direct Firebase auth first for debugging
                try {
                    console.log('Trying direct Firebase authentication...');
                    const directUserCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
                    console.log('Direct Firebase auth successful:', directUserCredential.user.email);
                } catch (directError) {
                    console.error('Direct Firebase auth failed:', directError);
                }

                // Sign in with Firebase using our auth utility
                console.log('Trying auth utility...');
                const result = await AssetKPIAuth.signIn(email, password);
                console.log('Auth utility result:', result);

                if (result.success) {
                    // Show success and token
                    showSuccess('Authentication successful!');
                    idTokenElement.textContent = result.token;

                    // Show token section and hide login form
                    authTokenSection.style.display = 'block';
                    loginForm.style.display = 'none';

                    console.log('User authenticated:', result.user.email);
                } else {
                    console.error('Auth utility returned error:', result.error);
                    throw result.error;
                }
            } catch (error) {
                console.error('Authentication error:', error);
                console.error('Error code:', error.code);
                console.error('Error message:', error.message);

                // Handle specific Firebase auth errors
                let errorMsg = 'Authentication failed. Please check your credentials.';

                if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
                    errorMsg = 'Invalid email or password.';
                } else if (error.code === 'auth/invalid-email') {
                    errorMsg = 'Invalid email format.';
                } else if (error.code === 'auth/too-many-requests') {
                    errorMsg = 'Too many failed login attempts. Please try again later.';
                } else if (error.code) {
                    errorMsg = `Authentication error: ${error.code}`;
                }

                showError(errorMsg);
            } finally {
                // Hide loading spinner
                loadingSpinner.style.display = 'none';
            }
        }

        // Function to handle sign out
        async function handleSignOut() {
            try {
                const result = await AssetKPIAuth.signOut();

                if (result.success) {
                    // Show login form and hide token section
                    authTokenSection.style.display = 'none';
                    loginForm.style.display = 'block';

                    // Clear token display
                    idTokenElement.textContent = '';

                    // Hide any messages
                    errorMessage.style.display = 'none';
                    successMessage.style.display = 'none';
                    apiResponse.style.display = 'none';

                    console.log('User signed out');
                } else {
                    throw result.error;
                }
            } catch (error) {
                console.error('Sign out error:', error);
                showError('Error signing out: ' + error.message);
            }
        }

        // Function to test API endpoint with token
        async function testApiEndpoint() {
            apiResponse.textContent = 'Testing API endpoint...';
            apiResponse.style.display = 'block';

            try {
                // Use our auth utility to make an authenticated request
                const response = await AssetKPIAuth.authenticatedFetch('/api/kpi/history/MTTR_Calculated');

                if (response.ok) {
                    const data = await response.json();
                    apiResponse.textContent = 'API call successful! Received data for MTTR_Calculated.';
                    apiResponse.className = 'alert alert-success mt-3';
                } else {
                    apiResponse.textContent = `API call failed: ${response.status} ${response.statusText}`;
                    apiResponse.className = 'alert alert-danger mt-3';
                }
            } catch (error) {
                console.error('API test error:', error);
                apiResponse.textContent = `Error testing API: ${error.message}`;
                apiResponse.className = 'alert alert-danger mt-3';
            }
        }

        // Function to copy token to clipboard
        async function copyTokenToClipboard() {
            const token = await AssetKPIAuth.getIdToken();

            if (token) {
                navigator.clipboard.writeText(token)
                    .then(() => {
                        // Show temporary success message
                        const originalText = copyTokenButton.textContent;
                        copyTokenButton.textContent = 'Copied!';
                        setTimeout(() => {
                            copyTokenButton.textContent = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        showError('Failed to copy token to clipboard.');
                    });
            }
        }

        // Event listeners
        loginButton.addEventListener('click', handleLogin);
        signOutButton.addEventListener('click', handleSignOut);
        testApiButton.addEventListener('click', testApiEndpoint);
        copyTokenButton.addEventListener('click', copyTokenToClipboard);
        goToDashboardButton.addEventListener('click', () => window.location.href = '/');

        // Allow Enter key to submit the form
        passwordInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                handleLogin();
            }
        });

        // Initialize auth state
        AssetKPIAuth.initAuth(function(user) {
            if (user) {
                // User is signed in
                console.log('User is already signed in:', user.email);

                // Get and display token
                AssetKPIAuth.getIdToken().then(token => {
                    idTokenElement.textContent = token;
                    authTokenSection.style.display = 'block';
                    loginForm.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html>