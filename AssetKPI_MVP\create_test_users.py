"""
Create test users in the database for testing purposes.
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# Get database URL from environment or use default
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Add the parent directory to sys.path to allow imports from main
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the User model and UserRole enum
from main import User, UserRole

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def create_test_user(user_id, email, role, full_name=None):
    """Create a test user in the database."""
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.user_id == user_id).first()
        if existing_user:
            print(f"User with ID {user_id} already exists.")
            return existing_user
        
        # Create new user
        user = User(
            user_id=user_id,
            email=email,
            role=role,
            full_name=full_name,
            created_at=datetime.now(),
            last_login=None
        )
        
        # Add to database
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print(f"Created user: {user.email} with role {user.role}")
        return user
    
    except Exception as e:
        db.rollback()
        print(f"Error creating user: {e}")
        return None

def main():
    """Create test users in the database."""
    try:
        # Create test admin user
        create_test_user(
            user_id="test-admin-uid",
            email="<EMAIL>",
            role=UserRole.ADMIN,
            full_name="Test Admin"
        )
        
        # Create test manager user
        create_test_user(
            user_id="test-manager-uid",
            email="<EMAIL>",
            role=UserRole.MANAGER,
            full_name="Test Manager"
        )
        
        # Create test engineer user
        create_test_user(
            user_id="test-engineer-uid",
            email="<EMAIL>",
            role=UserRole.ENGINEER,
            full_name="Test Engineer"
        )
        
        # Create test viewer user
        create_test_user(
            user_id="test-viewer-uid",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test Viewer"
        )
        
        print("Test users created successfully.")
    
    except Exception as e:
        print(f"Error creating test users: {e}")

if __name__ == "__main__":
    main()
