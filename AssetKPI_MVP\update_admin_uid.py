import psycopg2

# Connect to the database
conn = psycopg2.connect("postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
cur = conn.cursor()

# Update the user_id for the test admin user
cur.execute("UPDATE users SET user_id = 'firebase-test-admin-uid' WHERE user_id = 'test-admin-uid'")
print(f"Updated {cur.rowcount} rows")

# Commit the changes
conn.commit()

# Close the connection
cur.close()
conn.close()
