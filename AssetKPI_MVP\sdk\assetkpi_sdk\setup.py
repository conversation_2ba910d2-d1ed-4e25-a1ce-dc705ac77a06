from setuptools import setup, find_packages

setup(
    name="assetkpi-sdk",
    version="0.1.0",
    description="Python SDK for AssetKPI API",
    author="AssetKPI Team",
    author_email="<EMAIL>",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        "requests>=2.25.0",
        "urllib3>=1.26.0",
        "python-dotenv>=0.15.0",
    ],
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
)
