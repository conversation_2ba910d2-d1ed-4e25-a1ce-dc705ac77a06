import re

# Read the main.py file
with open('main.py', 'r') as file:
    content = file.read()

# Define the endpoints to add
auth_endpoints = '''
@app.get("/api/user-check/{user_id}", tags=["Authentication"])
async def user_check(user_id: str, db: Session = Depends(get_db)):
    """
    Check if a user with the given user_id exists in the database.
    """
    try:
        # Get user information from the database
        try:
            # Query the users table for the user with this user_id
            print(f"Querying database for user with user_id: {user_id}")
            user = db.query(User).filter(User.user_id == user_id).first()

            if user:
                print(f"Found user in database: {user}")
                # Return user information
                user_info = {
                    "user_id": user.user_id,
                    "email": user.email,
                    "role": user.role,
                    "full_name": user.full_name
                }
                print(f"Returning user info: {user_info}")
                return user_info
            else:
                print(f"User with user_id {user_id} not found in database")
                # Let's check what users are in the database
                all_users = db.query(User).limit(5).all()
                print(f"Sample users in database: {[u.user_id for u in all_users]}")

                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with user_id {user_id} not found in database"}
                )
        except Exception as e:
            print(f"Error querying database: {e}")
            import traceback
            traceback.print_exc()
            raise
    except Exception as e:
        print(f"Error checking user: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": f"Error checking user: {str(e)}"}
        )

@app.post("/api/debug-token", tags=["Authentication"])
async def debug_token(request: Request):
    """
    Debug endpoint for token verification.
    """
    print("=== Debug Token Request ===")
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        print(f"Authorization header: {auth_header[:20]}..." if auth_header else "No Authorization header")

        if not auth_header or not auth_header.startswith('Bearer '):
            print("No valid authorization header provided")
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        print(f"Token extracted: {token[:20]}...")

        # Verify the token with Firebase Admin SDK
        try:
            print("Attempting to verify token with Firebase Admin SDK...")
            decoded_token = auth.verify_id_token(token)
            firebase_uid = decoded_token.get("uid")
            if not firebase_uid:
                return JSONResponse(
                    status_code=401,
                    content={"error": "UID not found in decoded token"}
                )

            print(f"Successfully verified token for UID: {firebase_uid}")
            print(f"Decoded token claims: {decoded_token}")

            # Check if user exists in database
            db = SessionLocal()
            try:
                # Query the users table for the user with this UID
                print(f"Querying database for user with UID: {firebase_uid}")
                user = db.query(User).filter(User.user_id == firebase_uid).first()

                if user:
                    print(f"Found user in database: {user}")
                    user_exists = True
                else:
                    print(f"User with UID {firebase_uid} not found in database")
                    # Let's check what users are in the database
                    all_users = db.query(User).limit(5).all()
                    print(f"Sample users in database: {[u.user_id for u in all_users]}")
                    user_exists = False
            finally:
                db.close()

            # Return the decoded token
            return {
                "success": True,
                "uid": firebase_uid,
                "decoded_token": {k: v for k, v in decoded_token.items() if k not in ['iat', 'exp', 'aud', 'iss']},
                "user_exists_in_db": user_exists
            }
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            print(f"Token that failed verification: {token[:30]}...")
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )
    except Exception as e:
        print(f"Error debugging token: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )
'''

# Add JSONResponse import if it's not already there
if 'from fastapi.responses import JSONResponse' not in content:
    content = content.replace(
        'from fastapi.responses import HTMLResponse',
        'from fastapi.responses import HTMLResponse, JSONResponse'
    )

# Add the endpoints before the webhooks section
if '# --- Webhooks Page ---' in content:
    content = content.replace(
        '# --- Webhooks Page ---',
        '# --- Authentication Debug Endpoints ---\n' + auth_endpoints + '\n\n# --- Webhooks Page ---'
    )
else:
    # Fallback: Add at the end of the file
    content = content + '\n\n# --- Authentication Debug Endpoints ---\n' + auth_endpoints

# Write the updated content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Added authentication endpoints to main.py")
