# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Add the auth import if it's not already there
if 'from firebase_admin import auth' not in content:
    import_line = 'import firebase_admin'
    new_import_line = 'import firebase_admin\nfrom firebase_admin import auth'
    content = content.replace(import_line, new_import_line)

# Fix the token verification endpoint
old_function = '''@app.post("/api/verify-token", tags=["Authentication"])
async def verify_token(request: Request):
    """
    Verifies a Firebase ID token and returns user information.
    """
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )
        
        token = auth_header.split(' ')[1]
        
        # Verify the token with Firebase Admin SDK
        decoded_token = auth.verify_id_token(token)
        uid = decoded_token['uid']
        
        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            result = db.execute(text("SELECT * FROM users WHERE uid = :uid"), {"uid": uid}).fetchone()
            
            if result:
                # Return user information
                return {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
            else:
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error verifying token: {e}")
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

new_function = '''@app.post("/api/verify-token", tags=["Authentication"])
async def verify_token(request: Request):
    """
    Verifies a Firebase ID token and returns user information.
    """
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )
        
        token = auth_header.split(' ')[1]
        
        # Verify the token with Firebase Admin SDK
        try:
            decoded_token = auth.verify_id_token(token)
            uid = decoded_token['uid']
            print(f"Successfully verified token for UID: {uid}")
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )
        
        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            print(f"Querying database for user with UID: {uid}")
            result = db.execute(text("SELECT * FROM users WHERE uid = :uid"), {"uid": uid}).fetchone()
            
            if result:
                print(f"Found user in database: {result}")
                # Return user information
                return {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
            else:
                print(f"User with UID {uid} not found in database")
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error verifying token: {e}")
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

content = content.replace(old_function, new_function)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed the token verification endpoint in main.py")
