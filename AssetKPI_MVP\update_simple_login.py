# Create a temporary file with the fixed code
with open('templates/simple_login.html', 'r') as file:
    content = file.read()

# Update the fetch call to use the debug endpoint
old_fetch = '''                const response = await fetch('/api/verify-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({ email: userCredential.user.email })
                });'''

new_fetch = '''                // Try the debug endpoint first
                const debugResponse = await fetch('/api/debug-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({ email: userCredential.user.email })
                });
                
                if (debugResponse.ok) {
                    const debugData = await debugResponse.json();
                    console.log('Debug token response:', debugData);
                    resultDiv.textContent += '\\nDebug token verification successful. UID: ' + debugData.uid;
                } else {
                    const debugError = await debugResponse.text();
                    console.error('Debug token verification failed:', debugError);
                    resultDiv.textContent += '\\nDebug token verification failed: ' + debugResponse.statusText;
                }
                
                // Now try the regular endpoint
                const response = await fetch('/api/verify-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({ email: userCredential.user.email })
                });'''

content = content.replace(old_fetch, new_fetch)

# Write the fixed content back to the file
with open('templates/simple_login.html', 'w') as file:
    file.write(content)

print("Updated simple_login.html to use the debug endpoint")
