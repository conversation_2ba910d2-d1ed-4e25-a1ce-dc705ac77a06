import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')

    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def execute_migration():
    """Execute the SQL migration script for Milestone 3."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()

        # Read the SQL migration script
        with open('db_migration_milestone3.sql', 'r') as f:
            sql_script = f.read()

        # Split the script into individual statements
        statements = sql_script.split(';')

        # Execute each statement
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement + ';')
                    print(f"Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"Error executing statement: {statement[:50]}...")
                    print(f"Error: {e}")

        # Commit the changes
        conn.commit()
        print("Milestone 3 migration completed successfully!")

        # Return the connection and cursor for data population
        return conn, cursor

    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return None, None

def populate_sample_data(conn, cursor):
    """Populate the database with sample data for Milestone 3."""
    try:
        print("\nPopulating sample data for Milestone 3...")

        # Sample work order plans
        plans = [
            ('Preventive Maintenance - Mechanical', 'Standard mechanical PM procedure', 120, 2.5, 250.00),
            ('Preventive Maintenance - Electrical', 'Standard electrical PM procedure', 90, 2.0, 200.00),
            ('Corrective Maintenance - Mechanical', 'Standard mechanical repair procedure', 180, 3.5, 350.00),
            ('Corrective Maintenance - Electrical', 'Standard electrical repair procedure', 150, 3.0, 300.00),
            ('Equipment Inspection', 'Standard equipment inspection procedure', 60, 1.0, 100.00),
            ('Lubrication Service', 'Standard lubrication service procedure', 45, 0.75, 75.00),
            ('Calibration Service', 'Standard calibration service procedure', 90, 1.5, 150.00),
            ('Emergency Repair', 'Emergency repair procedure', 240, 4.0, 500.00),
            ('Installation', 'Standard installation procedure', 300, 5.0, 600.00),
            ('Overhaul', 'Standard overhaul procedure', 480, 8.0, 1000.00)
        ]

        print("Inserting work order plans...")
        plan_ids = []
        for plan in plans:
            cursor.execute(
                """
                INSERT INTO work_order_plans
                (plan_name, description, estimated_duration, estimated_labor_hours, estimated_cost)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING plan_id
                """,
                plan
            )
            plan_id = cursor.fetchone()[0]
            plan_ids.append(plan_id)
            print(f"  - Added work order plan: {plan[0]}")

        # Sample labor resources
        labor_resources = [
            ('John Smith', 'EMP001', 'Mechanical', 'Senior', 45.00, 'Available', '<EMAIL>, 555-123-4567'),
            ('Jane Doe', 'EMP002', 'Electrical', 'Senior', 50.00, 'Available', '<EMAIL>, 555-234-5678'),
            ('Bob Johnson', 'EMP003', 'Mechanical', 'Junior', 35.00, 'Available', '<EMAIL>, 555-345-6789'),
            ('Alice Brown', 'EMP004', 'Electrical', 'Junior', 40.00, 'Available', '<EMAIL>, 555-456-7890'),
            ('Charlie Davis', 'EMP005', 'HVAC', 'Senior', 48.00, 'Available', '<EMAIL>, 555-567-8901'),
            ('Diana Wilson', 'EMP006', 'Plumbing', 'Senior', 47.00, 'Available', '<EMAIL>, 555-678-9012'),
            ('Edward Miller', 'EMP007', 'Instrumentation', 'Senior', 52.00, 'Available', '<EMAIL>, 555-789-0123'),
            ('Fiona Taylor', 'EMP008', 'General', 'Junior', 30.00, 'Available', '<EMAIL>, 555-890-1234'),
            ('George White', 'EMP009', 'Welding', 'Senior', 49.00, 'Available', '<EMAIL>, 555-901-2345'),
            ('Hannah Black', 'EMP010', 'Machinist', 'Senior', 48.00, 'Available', '<EMAIL>, 555-012-3456')
        ]

        print("Inserting labor resources...")
        resource_ids = []
        for resource in labor_resources:
            cursor.execute(
                """
                INSERT INTO labor_resources
                (person_name, employee_id, craft, skill_level, labor_rate, availability_status, contact_info)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING resource_id
                """,
                resource
            )
            resource_id = cursor.fetchone()[0]
            resource_ids.append(resource_id)
            print(f"  - Added labor resource: {resource[0]}")

        # Update existing work orders with new fields
        print("Updating existing work orders...")

        # Get all existing work orders
        cursor.execute("SELECT workorderid, workordertype, startdate, enddate FROM workorders")
        work_orders = cursor.fetchall()

        if work_orders:
            # Priority levels
            priorities = ['Low', 'Medium', 'High', 'Critical', 'Emergency']

            for wo_id, wo_type, start_date, end_date in work_orders:
                # Assign a random plan based on work order type
                matching_plans = [p_id for i, p_id in enumerate(plan_ids) if plans[i][0].lower().startswith(wo_type.lower())] if wo_type else []
                plan_id = random.choice(matching_plans) if matching_plans else random.choice(plan_ids)

                # Assign a random priority
                priority = random.choice(priorities)

                # Calculate estimated completion date based on start date and plan duration
                est_completion_date = None
                if start_date:
                    # Get the plan's estimated duration
                    cursor.execute("SELECT estimated_duration FROM work_order_plans WHERE plan_id = %s", (plan_id,))
                    plan_result = cursor.fetchone()
                    if plan_result:
                        est_duration = plan_result[0]
                        est_completion_date = start_date + timedelta(minutes=est_duration)

                # Calculate actual labor hours (random value around the plan's estimated hours)
                actual_labor_hours = None
                if end_date and start_date:
                    # Get the plan's estimated labor hours
                    cursor.execute("SELECT estimated_labor_hours FROM work_order_plans WHERE plan_id = %s", (plan_id,))
                    plan_result = cursor.fetchone()
                    if plan_result:
                        est_labor_hours = plan_result[0]
                        # Random actual hours between 80% and 120% of estimated
                        actual_labor_hours = round(float(est_labor_hours) * random.uniform(0.8, 1.2), 2)

                # Update the work order
                cursor.execute(
                    """
                    UPDATE workorders
                    SET plan_id = %s, priority = %s, estimated_completion_date = %s, actual_labor_hours = %s
                    WHERE workorderid = %s
                    """,
                    (plan_id, priority, est_completion_date, actual_labor_hours, wo_id)
                )
                print(f"  - Updated work order ID {wo_id} with plan and priority")

                # Add tasks for this work order
                num_tasks = random.randint(2, 5)
                for i in range(num_tasks):
                    sequence = i + 1
                    if wo_type and wo_type.lower() == 'preventive':
                        task_desc = f"PM Task {sequence}: {random.choice(['Inspect', 'Check', 'Test', 'Measure', 'Lubricate'])} {random.choice(['bearings', 'motor', 'belts', 'filters', 'connections'])}"
                    else:
                        task_desc = f"Task {sequence}: {random.choice(['Repair', 'Replace', 'Adjust', 'Clean', 'Troubleshoot'])} {random.choice(['bearings', 'motor', 'belts', 'filters', 'connections'])}"

                    est_hours = round(random.uniform(0.5, 2.0), 2)
                    actual_hours = round(est_hours * random.uniform(0.8, 1.2), 2) if end_date else None
                    status = 'Completed' if end_date else random.choice(['Planned', 'In Progress', 'On Hold'])

                    # Assign to a random resource
                    assigned_resource = random.choice(resource_ids)
                    assigned_to = labor_resources[resource_ids.index(assigned_resource)][0]

                    completed_by = assigned_to if end_date else None
                    completed_date = end_date if end_date else None

                    cursor.execute(
                        """
                        INSERT INTO work_order_tasks
                        (workorder_id, task_description, sequence_number, estimated_hours, actual_hours,
                         status, assigned_to, completed_by, completed_date)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (wo_id, task_desc, sequence, est_hours, actual_hours, status, assigned_to, completed_by, completed_date)
                    )

                # Add labor entries for completed work orders
                if end_date:
                    # Number of labor entries (1-3)
                    num_labor = random.randint(1, 3)

                    for i in range(num_labor):
                        # Pick a random resource
                        resource_id = random.choice(resource_ids)
                        resource_index = resource_ids.index(resource_id)
                        craft = labor_resources[resource_index][2]
                        labor_rate = labor_resources[resource_index][4]

                        # Calculate hours worked (divide actual_labor_hours among resources)
                        if actual_labor_hours:
                            hours_worked = round(actual_labor_hours / num_labor * random.uniform(0.8, 1.2), 2)
                        else:
                            hours_worked = round(random.uniform(1.0, 4.0), 2)

                        # Calculate labor cost
                        labor_cost = round(hours_worked * labor_rate, 2)

                        # Work date (random date between start and end)
                        if start_date and end_date:
                            delta = (end_date - start_date).total_seconds()
                            random_seconds = random.randint(0, int(delta))
                            work_date = start_date + timedelta(seconds=random_seconds)
                        else:
                            work_date = datetime.now().date()

                        cursor.execute(
                            """
                            INSERT INTO work_order_labor
                            (workorder_id, labor_code, craft, person_id, hours_worked, labor_cost, work_date)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """,
                            (wo_id, f"LC{i+1}", craft, resource_id, hours_worked, labor_cost, work_date)
                        )
        else:
            print("  - No existing work orders found to update")

        # Commit the changes
        conn.commit()
        print("Sample data population completed successfully!")

    except Exception as e:
        print(f"Error populating sample data: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()

def main():
    """Main function to execute migration and populate data."""
    conn, cursor = execute_migration()

    if conn and cursor:
        populate_sample_data(conn, cursor)
        cursor.close()
        conn.close()

    print("Milestone 3 completed!")

if __name__ == "__main__":
    main()
