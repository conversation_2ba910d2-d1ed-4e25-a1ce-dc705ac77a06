/**
 * Tutorial Styles
 */

/* Tutorial Container */
#tutorial-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    pointer-events: none;
}

/* Tutorial Overlay */
.tutorial-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    pointer-events: auto;
}

/* Tutorial Highlight */
.tutorial-highlight {
    position: absolute;
    box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    z-index: 10001;
    pointer-events: none;
    box-sizing: content-box;
    border: 2px solid #007bff;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.5), 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.5), 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.5), 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

/* Tutorial Tooltip */
.tutorial-tooltip {
    position: absolute;
    width: 320px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10002;
    pointer-events: auto;
    overflow: hidden;
}

.tutorial-tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tutorial-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #212529;
}

.tutorial-close-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.tutorial-close-btn:hover {
    color: #343a40;
}

.tutorial-tooltip-body {
    padding: 16px;
    color: #212529;
    font-size: 14px;
    line-height: 1.5;
}

.tutorial-tooltip-body p {
    margin: 0 0 12px;
}

.tutorial-tooltip-body p:last-child {
    margin-bottom: 0;
}

.tutorial-tooltip-body img {
    max-width: 100%;
    height: auto;
    margin: 8px 0;
    border-radius: 4px;
}

.tutorial-tooltip-body code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
}

.tutorial-tooltip-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.tutorial-prev-btn,
.tutorial-next-btn {
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.tutorial-prev-btn:hover,
.tutorial-next-btn:hover {
    background-color: #0069d9;
}

.tutorial-prev-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.tutorial-progress {
    font-size: 12px;
    color: #6c757d;
}

/* Tooltip Positions */
.tutorial-tooltip.tooltip-top::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    margin-left: -10px;
    border-width: 10px 10px 0;
    border-style: solid;
    border-color: #f8f9fa transparent transparent;
}

.tutorial-tooltip.tooltip-right::after {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    margin-top: -10px;
    border-width: 10px 10px 10px 0;
    border-style: solid;
    border-color: transparent #f8f9fa transparent transparent;
}

.tutorial-tooltip.tooltip-bottom::after {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    margin-left: -10px;
    border-width: 0 10px 10px;
    border-style: solid;
    border-color: transparent transparent #f8f9fa;
}

.tutorial-tooltip.tooltip-left::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    margin-top: -10px;
    border-width: 10px 0 10px 10px;
    border-style: solid;
    border-color: transparent transparent transparent #f8f9fa;
}

/* Progress Indicator */
.tutorial-progress-indicator {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    background-color: #fff;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 4px;
    z-index: 10003;
    pointer-events: auto;
}

.tutorial-step-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #dee2e6;
    margin: 0 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.tutorial-step-indicator.active {
    background-color: #007bff;
}

.tutorial-step-indicator.completed {
    background-color: #28a745;
}

/* Tutorial Menu */
.tutorial-menu {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9998;
}

.tutorial-menu-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #007bff;
    color: #fff;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.tutorial-menu-content {
    position: absolute;
    bottom: 60px;
    right: 0;
    width: 240px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    display: none;
}

.tutorial-menu.open .tutorial-menu-content {
    display: block;
}

.tutorial-menu-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 12px;
}

.tutorial-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tutorial-menu-item {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.tutorial-menu-item:last-child {
    border-bottom: none;
}

.tutorial-menu-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #212529;
    text-decoration: none;
    font-size: 14px;
}

.tutorial-menu-link:hover {
    color: #007bff;
}

.tutorial-menu-status {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #dee2e6;
}

.tutorial-menu-status.completed {
    background-color: #28a745;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .tutorial-tooltip {
        width: 280px;
    }
}
