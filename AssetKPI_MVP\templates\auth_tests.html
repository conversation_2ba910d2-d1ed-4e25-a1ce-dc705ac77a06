<!DOCTYPE html>
<html lang="en">
<head>
    <title>AssetKPI - Authentication Tests</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 40px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .btn-test {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
        }
        .result-section {
            margin-top: 30px;
            display: none;
        }
        .result-section pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .token-section {
            margin-top: 20px;
        }
        .token-section input {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>AssetKPI Authentication Tests</h2>
            <p class="text-muted">Test various authentication methods</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Test User Authentication</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="test-token">Test Token</label>
                            <input type="text" class="form-control" id="test-token" placeholder="Enter test token" value="test-admin-token">
                        </div>
                        <button type="button" class="btn btn-primary btn-test" id="test-auth-button">Test Authentication</button>
                        <div class="alert alert-info">
                            <strong>Available Test Tokens:</strong>
                            <ul>
                                <li><code>test-admin-token</code> - Admin role</li>
                                <li><code>test-manager-token</code> - Manager role</li>
                                <li><code>test-engineer-token</code> - Engineer role</li>
                                <li><code>test-viewer-token</code> - Viewer role</li>
                                <li><code>johan-token</code> - Johan Borgulf (Admin)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Firebase Authentication</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="firebase-token">Firebase ID Token</label>
                            <input type="text" class="form-control" id="firebase-token" placeholder="Enter Firebase ID token">
                        </div>
                        <button type="button" class="btn btn-success btn-test" id="firebase-auth-button">Test Firebase Auth</button>
                        <p class="text-muted mt-2">
                            Get a token from the login page by signing in with Firebase.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5>API Endpoint Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-info btn-test" id="test-profile-button">Test User Profile</button>
                        <button type="button" class="btn btn-info btn-test" id="test-inventory-button">Test Inventory API</button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-info btn-test" id="test-assets-button">Test Assets API</button>
                        <button type="button" class="btn btn-info btn-test" id="test-kpi-button">Test KPI API</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="result-section" id="result-section">
            <h5>Test Results</h5>
            <div class="alert" id="result-alert" role="alert"></div>
            <pre id="result-data"></pre>
        </div>

        <div class="token-section">
            <h5>Current Token</h5>
            <div class="form-group">
                <input type="text" class="form-control" id="current-token" readonly>
                <button type="button" class="btn btn-outline-secondary" id="copy-token-button">Copy Token</button>
                <button type="button" class="btn btn-outline-danger" id="clear-token-button">Clear Token</button>
            </div>
        </div>

        <div class="mt-4 text-center">
            <a href="/" class="btn btn-outline-primary">Back to Dashboard</a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const testTokenInput = document.getElementById('test-token');
            const firebaseTokenInput = document.getElementById('firebase-token');
            const currentTokenInput = document.getElementById('current-token');
            const resultSection = document.getElementById('result-section');
            const resultAlert = document.getElementById('result-alert');
            const resultData = document.getElementById('result-data');
            
            // Buttons
            const testAuthButton = document.getElementById('test-auth-button');
            const firebaseAuthButton = document.getElementById('firebase-auth-button');
            const testProfileButton = document.getElementById('test-profile-button');
            const testInventoryButton = document.getElementById('test-inventory-button');
            const testAssetsButton = document.getElementById('test-assets-button');
            const testKpiButton = document.getElementById('test-kpi-button');
            const copyTokenButton = document.getElementById('copy-token-button');
            const clearTokenButton = document.getElementById('clear-token-button');

            // Load token from localStorage
            const savedToken = localStorage.getItem('authToken');
            if (savedToken) {
                currentTokenInput.value = savedToken;
            }

            // Test User Authentication
            testAuthButton.addEventListener('click', async function() {
                const token = testTokenInput.value.trim();
                if (!token) {
                    showResult('error', 'Please enter a test token');
                    return;
                }

                try {
                    const response = await fetch(`/api/test-user-auth?token=${token}`);
                    const data = await response.json();
                    
                    if (response.ok) {
                        showResult('success', 'Test authentication successful', data);
                        saveToken(token);
                    } else {
                        showResult('error', 'Test authentication failed', data);
                    }
                } catch (error) {
                    showResult('error', 'Error testing authentication', { error: error.message });
                }
            });

            // Firebase Authentication
            firebaseAuthButton.addEventListener('click', async function() {
                const token = firebaseTokenInput.value.trim();
                if (!token) {
                    showResult('error', 'Please enter a Firebase ID token');
                    return;
                }

                try {
                    const response = await fetch('/api/debug-token', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    const data = await response.json();
                    
                    if (response.ok) {
                        showResult('success', 'Firebase authentication successful', data);
                        saveToken(token);
                    } else {
                        showResult('error', 'Firebase authentication failed', data);
                    }
                } catch (error) {
                    showResult('error', 'Error testing Firebase authentication', { error: error.message });
                }
            });

            // Test User Profile
            testProfileButton.addEventListener('click', async function() {
                const token = currentTokenInput.value.trim();
                if (!token) {
                    showResult('error', 'No token available. Please authenticate first.');
                    return;
                }

                try {
                    const response = await fetch('/api/user/profile', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    const data = await response.json();
                    
                    if (response.ok) {
                        showResult('success', 'User profile retrieved successfully', data);
                    } else {
                        showResult('error', 'Failed to retrieve user profile', data);
                    }
                } catch (error) {
                    showResult('error', 'Error testing user profile', { error: error.message });
                }
            });

            // Test Inventory API
            testInventoryButton.addEventListener('click', async function() {
                const token = currentTokenInput.value.trim();
                if (!token) {
                    showResult('error', 'No token available. Please authenticate first.');
                    return;
                }

                try {
                    const response = await fetch('/api/inventory/optimization-report', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    const data = await response.json();
                    
                    if (response.ok) {
                        showResult('success', 'Inventory API call successful', data);
                    } else {
                        showResult('error', 'Inventory API call failed', data);
                    }
                } catch (error) {
                    showResult('error', 'Error testing inventory API', { error: error.message });
                }
            });

            // Test Assets API
            testAssetsButton.addEventListener('click', async function() {
                const token = currentTokenInput.value.trim();
                if (!token) {
                    showResult('error', 'No token available. Please authenticate first.');
                    return;
                }

                try {
                    const response = await fetch('/api/assets/performance', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    const data = await response.json();
                    
                    if (response.ok) {
                        showResult('success', 'Assets API call successful', data);
                    } else {
                        showResult('error', 'Assets API call failed', data);
                    }
                } catch (error) {
                    showResult('error', 'Error testing assets API', { error: error.message });
                }
            });

            // Test KPI API
            testKpiButton.addEventListener('click', async function() {
                const token = currentTokenInput.value.trim();
                if (!token) {
                    showResult('error', 'No token available. Please authenticate first.');
                    return;
                }

                try {
                    const response = await fetch('/api/kpi/history/MTTR_Calculated', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    const data = await response.json();
                    
                    if (response.ok) {
                        showResult('success', 'KPI API call successful', data);
                    } else {
                        showResult('error', 'KPI API call failed', data);
                    }
                } catch (error) {
                    showResult('error', 'Error testing KPI API', { error: error.message });
                }
            });

            // Copy Token
            copyTokenButton.addEventListener('click', function() {
                const token = currentTokenInput.value.trim();
                if (!token) {
                    showResult('error', 'No token to copy');
                    return;
                }

                navigator.clipboard.writeText(token)
                    .then(() => {
                        showResult('success', 'Token copied to clipboard');
                    })
                    .catch(err => {
                        showResult('error', 'Failed to copy token', { error: err.message });
                    });
            });

            // Clear Token
            clearTokenButton.addEventListener('click', function() {
                currentTokenInput.value = '';
                localStorage.removeItem('authToken');
                showResult('info', 'Token cleared');
            });

            // Helper function to show results
            function showResult(type, message, data = null) {
                resultSection.style.display = 'block';
                resultAlert.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
                resultAlert.textContent = message;
                
                if (data) {
                    resultData.style.display = 'block';
                    resultData.textContent = JSON.stringify(data, null, 2);
                } else {
                    resultData.style.display = 'none';
                }

                // Scroll to result section
                resultSection.scrollIntoView({ behavior: 'smooth' });
            }

            // Helper function to save token
            function saveToken(token) {
                currentTokenInput.value = token;
                localStorage.setItem('authToken', token);
            }
        });
    </script>
</body>
</html>
