/**
 * Mobile Responsiveness Styles
 * 
 * This file contains styles specifically for enhancing mobile responsiveness
 * across the AssetKPI application.
 */

/* ===== Global Mobile Styles ===== */

/* Improve touch targets */
@media (max-width: 767.98px) {
  .btn,
  .nav-link,
  .dropdown-item,
  .form-check-label,
  .page-link {
    padding: 0.5rem 0.75rem;
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Increase form control height for better touch */
  .form-control,
  .form-select {
    min-height: 44px;
    padding: 0.5rem 0.75rem;
  }
  
  /* Adjust font sizes for readability */
  body {
    font-size: 16px;
  }
  
  h1 {
    font-size: 1.75rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }
  
  /* Add more spacing between elements */
  .container-fluid,
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
  
  .col,
  [class*="col-"] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  /* Adjust card padding */
  .card-body {
    padding: 1rem;
  }
  
  /* Ensure images don't overflow */
  img {
    max-width: 100%;
    height: auto;
  }
}

/* ===== Navigation ===== */

/* Navbar adjustments */
@media (max-width: 991.98px) {
  .navbar-brand {
    max-width: 50%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .navbar-collapse {
    max-height: calc(100vh - 56px);
    overflow-y: auto;
  }
  
  /* Ensure dropdown menus don't overflow */
  .dropdown-menu {
    max-height: 50vh;
    overflow-y: auto;
  }
}

/* ===== Sidebar ===== */

/* Off-canvas sidebar for mobile */
@media (max-width: 767.98px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    z-index: 1050;
    background-color: #fff;
    transition: left 0.3s ease;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
  }
  
  .sidebar.show {
    left: 0;
  }
  
  .sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    display: none;
  }
  
  .sidebar-backdrop.show {
    display: block;
  }
  
  /* Adjust main content when sidebar is hidden */
  .content-with-sidebar {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

/* ===== Tables ===== */

/* Responsive tables */
@media (max-width: 767.98px) {
  /* Option 1: Horizontal scrolling for tables */
  .table-responsive {
    border: 0;
  }
  
  /* Option 2: Card view for tables */
  .table-to-cards {
    display: block;
    width: 100%;
    border: 0;
  }
  
  .table-to-cards thead {
    display: none;
  }
  
  .table-to-cards tbody {
    display: block;
    width: 100%;
  }
  
  .table-to-cards tr {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
  }
  
  .table-to-cards td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 0;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem;
  }
  
  .table-to-cards td:last-child {
    border-bottom: 0;
  }
  
  .table-to-cards td::before {
    content: attr(data-label);
    font-weight: bold;
    margin-right: 1rem;
  }
  
  /* Option 3: Column toggle */
  .table-column-toggle .toggle-column {
    display: none;
  }
  
  .table-column-toggle .toggle-column.show {
    display: table-cell;
  }
  
  .column-toggle-dropdown {
    margin-bottom: 1rem;
  }
}

/* ===== Forms ===== */

/* Responsive forms */
@media (max-width: 767.98px) {
  /* Stack form fields */
  .form-row {
    flex-direction: column;
  }
  
  /* Full-width inputs */
  .form-control,
  .form-select,
  .input-group {
    width: 100%;
  }
  
  /* Adjust date inputs */
  input[type="date"],
  input[type="time"],
  input[type="datetime-local"] {
    min-height: 44px;
  }
  
  /* Adjust form buttons */
  .form-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .form-buttons .btn {
    width: 100%;
  }
  
  /* Adjust form validation messages */
  .invalid-feedback,
  .valid-feedback {
    font-size: 0.875rem;
  }
}

/* ===== Dashboard ===== */

/* Responsive dashboard */
@media (max-width: 767.98px) {
  /* Stack dashboard widgets */
  .dashboard-widget {
    margin-bottom: 1rem;
  }
  
  /* Simplify charts */
  .chart-container {
    height: 250px !important;
  }
  
  /* Adjust KPI cards */
  .kpi-card {
    margin-bottom: 1rem;
  }
  
  /* Stack dashboard actions */
  .dashboard-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .dashboard-actions .btn,
  .dashboard-actions .dropdown {
    width: 100%;
  }
}

/* ===== Asset Management ===== */

/* Responsive asset views */
@media (max-width: 767.98px) {
  /* Adjust asset cards */
  .asset-card {
    margin-bottom: 1rem;
  }
  
  /* Stack asset details */
  .asset-detail-section {
    margin-bottom: 1.5rem;
  }
  
  /* Adjust asset filters */
  .filter-section {
    padding: 0.75rem;
  }
  
  /* Adjust asset actions */
  .asset-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .asset-actions .btn {
    width: 100%;
  }
}

/* ===== Inventory Management ===== */

/* Responsive inventory views */
@media (max-width: 767.98px) {
  /* Adjust inventory cards */
  .inventory-card {
    margin-bottom: 1rem;
  }
  
  /* Stack inventory details */
  .inventory-detail-section {
    margin-bottom: 1.5rem;
  }
  
  /* Adjust inventory filters */
  .inventory-filters {
    flex-direction: column;
  }
  
  /* Adjust inventory actions */
  .inventory-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .inventory-actions .btn {
    width: 100%;
  }
}

/* ===== Work Orders ===== */

/* Responsive work order views */
@media (max-width: 767.98px) {
  /* Adjust work order cards */
  .work-order-card {
    margin-bottom: 1rem;
  }
  
  /* Stack work order details */
  .work-order-detail-section {
    margin-bottom: 1.5rem;
  }
  
  /* Adjust work order filters */
  .work-order-filters {
    flex-direction: column;
  }
  
  /* Adjust work order actions */
  .work-order-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .work-order-actions .btn {
    width: 100%;
  }
}

/* ===== Modals ===== */

/* Responsive modals */
@media (max-width: 767.98px) {
  .modal-dialog {
    margin: 0.5rem;
  }
  
  .modal-content {
    border-radius: 0.25rem;
  }
  
  .modal-header {
    padding: 0.75rem 1rem;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  .modal-footer {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .modal-footer .btn {
    width: 100%;
  }
}

/* ===== Utilities ===== */

/* Mobile-specific utility classes */
@media (max-width: 767.98px) {
  /* Hide elements on mobile */
  .d-mobile-none {
    display: none !important;
  }
  
  /* Show elements only on mobile */
  .d-mobile-block {
    display: block !important;
  }
  
  /* Full width on mobile */
  .w-mobile-100 {
    width: 100% !important;
  }
  
  /* Center text on mobile */
  .text-mobile-center {
    text-align: center !important;
  }
  
  /* Stack flex items on mobile */
  .flex-mobile-column {
    flex-direction: column !important;
  }
  
  /* Adjust spacing on mobile */
  .mb-mobile-3 {
    margin-bottom: 1rem !important;
  }
  
  .p-mobile-2 {
    padding: 0.5rem !important;
  }
}

/* ===== Animations ===== */

/* Optimize animations for mobile */
@media (max-width: 767.98px) {
  /* Reduce or disable animations for better performance */
  .animated {
    animation-duration: 0.3s;
  }
  
  /* For users who prefer reduced motion */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}
