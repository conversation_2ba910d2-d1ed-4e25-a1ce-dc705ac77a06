{% extends "layout.html" %}

{% block title %}KPI Analytics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2">KPI Analytics</h1>
            <p class="text-muted">Advanced analysis of key performance indicators with drill-down capabilities</p>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Filters</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="kpiType">KPI Type:</label>
                                <select class="form-control" id="kpiType">
                                    <option value="oee">Overall Equipment Effectiveness (OEE)</option>
                                    <option value="mttr">Mean Time To Repair (MTTR)</option>
                                    <option value="mtbf">Mean Time Between Failures (MTBF)</option>
                                    <option value="availability">Availability</option>
                                    <option value="performance">Performance</option>
                                    <option value="quality">Quality</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="assetCategory">Asset Category:</label>
                                <select class="form-control" id="assetCategory">
                                    <option value="all">All Categories</option>
                                    <option value="production">Production</option>
                                    <option value="utility">Utility</option>
                                    <option value="facility">Facility</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="dateFrom">From Date:</label>
                                <input type="date" class="form-control" id="dateFrom">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="dateTo">To Date:</label>
                                <input type="date" class="form-control" id="dateTo">
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="timeAggregation">Time Aggregation:</label>
                                <select class="form-control" id="timeAggregation">
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="quarterly">Quarterly</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="anomalyDetection">Anomaly Detection:</label>
                                <select class="form-control" id="anomalyDetection">
                                    <option value="off">Off</option>
                                    <option value="standard">Standard (±2σ)</option>
                                    <option value="strict">Strict (±1.5σ)</option>
                                    <option value="loose">Loose (±3σ)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="benchmark">Benchmark:</label>
                                <select class="form-control" id="benchmark">
                                    <option value="none">None</option>
                                    <option value="industry">Industry Average</option>
                                    <option value="target">Target Values</option>
                                    <option value="historical">Historical Best</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" class="btn btn-primary w-100" id="applyFilters">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title text-muted">Current Value</h5>
                    <h2 class="display-4" id="currentValue">85.2%</h2>
                    <p class="text-success">
                        <i class="bi bi-arrow-up"></i> 2.3% vs previous period
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title text-muted">Average</h5>
                    <h2 class="display-4" id="averageValue">78.5%</h2>
                    <p class="text-muted">Last 12 months</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title text-muted">Target</h5>
                    <h2 class="display-4" id="targetValue">90.0%</h2>
                    <p class="text-danger">4.8% gap to target</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title text-muted">Anomalies</h5>
                    <h2 class="display-4" id="anomalyCount">3</h2>
                    <p class="text-warning">Detected in period</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">KPI Trend Analysis</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportCsv">
                            <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportPdf">
                            <i class="bi bi-file-earmark-pdf"></i> Export PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:400px;">
                        <canvas id="mainChart"></canvas>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">Click on any data point to see detailed breakdown</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Drill-Down Section (initially hidden) -->
    <div class="row mb-4" id="drillDownSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Detailed Analysis: <span id="drillDownTitle">January 2023</span></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container" style="position: relative; height:300px;">
                                <canvas id="drillDownChart1"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container" style="position: relative; height:300px;">
                                <canvas id="drillDownChart2"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Anomaly Detection Section -->
    <div class="row mb-4" id="anomalySection">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Anomaly Detection</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="anomalyTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Asset</th>
                                    <th>KPI Value</th>
                                    <th>Expected Range</th>
                                    <th>Deviation</th>
                                    <th>Severity</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2023-01-15</td>
                                    <td>CNC Machine</td>
                                    <td>58.2%</td>
                                    <td>75-85%</td>
                                    <td>-16.8%</td>
                                    <td><span class="badge bg-danger">High</span></td>
                                    <td><button class="btn btn-sm btn-outline-primary">Analyze</button></td>
                                </tr>
                                <tr>
                                    <td>2023-02-03</td>
                                    <td>Injection Molding Machine 1</td>
                                    <td>92.7%</td>
                                    <td>70-80%</td>
                                    <td>+12.7%</td>
                                    <td><span class="badge bg-warning">Medium</span></td>
                                    <td><button class="btn btn-sm btn-outline-primary">Analyze</button></td>
                                </tr>
                                <tr>
                                    <td>2023-03-22</td>
                                    <td>HVAC System</td>
                                    <td>45.3%</td>
                                    <td>65-75%</td>
                                    <td>-19.7%</td>
                                    <td><span class="badge bg-danger">High</span></td>
                                    <td><button class="btn btn-sm btn-outline-primary">Analyze</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set default dates (last 12 months)
        const today = new Date();
        const oneYearAgo = new Date(today);
        oneYearAgo.setFullYear(today.getFullYear() - 1);

        document.getElementById('dateFrom').value = oneYearAgo.toISOString().split('T')[0];
        document.getElementById('dateTo').value = today.toISOString().split('T')[0];

        // Initialize variables for charts
        let mainChart = null;
        let componentsChart = null;
        let assetsChart = null;

        // Create fallback charts with sample data in case the API fails
        function createFallbackCharts() {
            console.log('Creating fallback charts');

            // Sample data for main chart
            const timeLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const kpiData = [82.5, 84.3, 79.8, 85.2, 87.1, 86.5, 88.2, 85.7, 83.9, 86.3, 88.5, 89.2];
            const benchmarkData = [];

            // Update summary cards with sample data
            const summary = {
                currentValue: 89.2,
                averageValue: 85.6,
                targetValue: 90.0,
                trend: 0.7,
                gap: 0.8,
                anomalyCount: 0
            };

            // Update UI with sample data
            updateSummaryCards(summary);
            createMainChart(timeLabels, kpiData, benchmarkData, 'oee');

            // Clear anomaly table
            document.querySelector('#anomalyTable tbody').innerHTML = '<tr><td colspan="7" class="text-center">No anomalies detected in the selected period.</td></tr>';
        }

        // Try to fetch data from API, fall back to sample data if it fails
        fetchKpiData();

        // Set a timeout to create fallback charts if the API call takes too long
        const fallbackTimeout = setTimeout(() => {
            if (!mainChart) {
                console.log('API call taking too long, creating fallback charts');
                createFallbackCharts();
            }
        }, 5000); // 5 second timeout

        // Function to fetch KPI data from API
        function fetchKpiData() {
            const kpiType = document.getElementById('kpiType').value;
            const assetCategory = document.getElementById('assetCategory').value;
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            const timeAggregation = document.getElementById('timeAggregation').value;
            const anomalyDetection = document.getElementById('anomalyDetection').value;
            const benchmark = document.getElementById('benchmark').value;

            // Show loading indicators
            document.querySelectorAll('.chart-container').forEach(container => {
                container.innerHTML = '<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
            });

            // Fetch data from API
            const apiUrl = `/api/kpi/analytics?kpi_type=${kpiType}&asset_category=${assetCategory}&date_from=${dateFrom}&date_to=${dateTo}&time_aggregation=${timeAggregation}&anomaly_detection=${anomalyDetection}&benchmark=${benchmark}`;
            console.log('Fetching data from:', apiUrl);

            fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Received data:', data);
                // Restore chart containers
                document.querySelectorAll('.chart-container').forEach(container => {
                    const canvasId = container.querySelector('canvas') ? container.querySelector('canvas').id : null;
                    if (canvasId) {
                        // Clear the container and create a new canvas
                        container.innerHTML = '';
                        const canvas = document.createElement('canvas');
                        canvas.id = canvasId;
                        container.appendChild(canvas);
                        console.log(`Restored canvas with id: ${canvasId}`);
                    }
                });

                // Update summary cards
                updateSummaryCards(data.summary);

                // Update anomaly table
                updateAnomalyTable(data.anomalies, data.timeLabels);

                // Create main chart
                createMainChart(data.timeLabels, data.kpiData, data.benchmarkData, kpiType);

                // Hide drill-down section initially
                document.getElementById('drillDownSection').style.display = 'none';

                // Clear the fallback timeout since the API call succeeded
                clearTimeout(fallbackTimeout);
            })
            .catch(error => {
                console.error('Error fetching data:', error);
                // Show detailed error message
                const errorMessage = error.message || 'Unknown error';
                document.querySelectorAll('.chart-container').forEach(container => {
                    container.innerHTML = `<div class="alert alert-danger">Error loading chart data: ${errorMessage}</div>`;
                });
                document.querySelector('#anomalyTable tbody').innerHTML = `<tr><td colspan="7" class="text-center text-danger">Error loading data: ${errorMessage}</td></tr>`;
            });
        }

        // Function to update summary cards
        function updateSummaryCards(summary) {
            document.getElementById('currentValue').textContent = formatValue(summary.currentValue);
            document.getElementById('averageValue').textContent = formatValue(summary.averageValue);
            document.getElementById('targetValue').textContent = formatValue(summary.targetValue);
            document.getElementById('anomalyCount').textContent = summary.anomalyCount;

            // Update trend indicator
            const trendElement = document.getElementById('currentValue').nextElementSibling;
            if (summary.trend > 0) {
                trendElement.className = 'text-success';
                trendElement.innerHTML = `<i class="bi bi-arrow-up"></i> ${Math.abs(summary.trend).toFixed(1)}% vs previous period`;
            } else if (summary.trend < 0) {
                trendElement.className = 'text-danger';
                trendElement.innerHTML = `<i class="bi bi-arrow-down"></i> ${Math.abs(summary.trend).toFixed(1)}% vs previous period`;
            } else {
                trendElement.className = 'text-muted';
                trendElement.innerHTML = `<i class="bi bi-dash"></i> No change vs previous period`;
            }

            // Update gap to target
            const gapElement = document.getElementById('targetValue').nextElementSibling;
            if (summary.gap > 0) {
                gapElement.className = 'text-danger';
                gapElement.textContent = `${summary.gap.toFixed(1)}% gap to target`;
            } else if (summary.gap < 0) {
                gapElement.className = 'text-success';
                gapElement.textContent = `${Math.abs(summary.gap).toFixed(1)}% above target`;
            } else {
                gapElement.className = 'text-muted';
                gapElement.textContent = `At target`;
            }
        }

        // Function to format KPI values based on type
        function formatValue(value) {
            const kpiType = document.getElementById('kpiType').value;
            if (kpiType === 'mttr' || kpiType === 'mtbf') {
                return value.toFixed(1);
            } else {
                return value.toFixed(1) + '%';
            }
        }

        // Function to update anomaly table
        function updateAnomalyTable(anomalies, timeLabels) {
            const tableBody = document.querySelector('#anomalyTable tbody');
            tableBody.innerHTML = '';

            if (anomalies.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">No anomalies detected in the selected period.</td></tr>';
                return;
            }

            anomalies.forEach(anomaly => {
                const row = document.createElement('tr');

                // Add severity class
                if (anomaly.severity === 'high') {
                    row.classList.add('table-danger');
                } else if (anomaly.severity === 'medium') {
                    row.classList.add('table-warning');
                }

                row.innerHTML = `
                    <td>${anomaly.date}</td>
                    <td>Multiple Assets</td>
                    <td>${formatValue(anomaly.value)}</td>
                    <td>${formatValue(anomaly.expected_range[0])} - ${formatValue(anomaly.expected_range[1])}</td>
                    <td>${anomaly.deviation > 0 ? '+' : ''}${formatValue(anomaly.deviation)}</td>
                    <td><span class="badge ${anomaly.severity === 'high' ? 'bg-danger' : 'bg-warning'}">${anomaly.severity.charAt(0).toUpperCase() + anomaly.severity.slice(1)}</span></td>
                    <td><button class="btn btn-sm btn-outline-primary" data-index="${anomaly.index}">Analyze</button></td>
                `;

                tableBody.appendChild(row);
            });

            // Add event listeners to analyze buttons
            document.querySelectorAll('#anomalyTable button').forEach(button => {
                button.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    showDrillDown(timeLabels[index], anomalies.find(a => a.index === index).value);
                });
            });
        }

        // Function to create main chart
        function createMainChart(timeLabels, kpiData, benchmarkData, kpiType) {
            console.log('Creating main chart with data:', { timeLabels, kpiData, benchmarkData, kpiType });
            const canvas = document.getElementById('mainChart');
            if (!canvas) {
                console.error('Canvas element not found');
                return;
            }
            const ctx = canvas.getContext('2d');

            // Destroy existing chart if it exists
            if (mainChart) {
                mainChart.destroy();
            }

            // Determine y-axis label based on KPI type
            let yAxisLabel = 'Value';
            let chartTitle = 'KPI Trend';
            let datasetLabel = 'Value';

            if (kpiType === 'oee') {
                yAxisLabel = 'OEE (%)';
                chartTitle = 'Overall Equipment Effectiveness (OEE) Trend';
                datasetLabel = 'OEE (%)';
            } else if (kpiType === 'mttr') {
                yAxisLabel = 'MTTR (hours)';
                chartTitle = 'Mean Time To Repair (MTTR) Trend';
                datasetLabel = 'MTTR (hours)';
            } else if (kpiType === 'mtbf') {
                yAxisLabel = 'MTBF (hours)';
                chartTitle = 'Mean Time Between Failures (MTBF) Trend';
                datasetLabel = 'MTBF (hours)';
            } else if (kpiType === 'availability') {
                yAxisLabel = 'Availability (%)';
                chartTitle = 'Availability Trend';
                datasetLabel = 'Availability (%)';
            } else if (kpiType === 'performance') {
                yAxisLabel = 'Performance (%)';
                chartTitle = 'Performance Trend';
                datasetLabel = 'Performance (%)';
            } else if (kpiType === 'quality') {
                yAxisLabel = 'Quality (%)';
                chartTitle = 'Quality Trend';
                datasetLabel = 'Quality (%)';
            }

            // Create datasets array
            const datasets = [
                {
                    label: datasetLabel,
                    data: kpiData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }
            ];

            // Add benchmark dataset if available
            if (benchmarkData && benchmarkData.length > 0) {
                datasets.push({
                    label: 'Benchmark',
                    data: benchmarkData,
                    borderColor: 'rgb(255, 99, 132)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    fill: false,
                    pointRadius: 0
                });
            }

            // Create chart
            mainChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: yAxisLabel
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Time Period'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: chartTitle
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    onClick: function(event, elements) {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            showDrillDown(timeLabels[index], kpiData[index]);
                        }
                    }
                }
            });
        }

        // Function to show drill-down section
        function showDrillDown(period, value) {
            // Update drill-down title
            document.getElementById('drillDownTitle').textContent = period;

            // Show drill-down section
            document.getElementById('drillDownSection').style.display = 'block';

            // Scroll to drill-down section
            document.getElementById('drillDownSection').scrollIntoView({ behavior: 'smooth' });

            // Fetch drill-down data from API
            const kpiType = document.getElementById('kpiType').value;
            const assetCategory = document.getElementById('assetCategory').value;

            // Create drill-down charts with sample data for now
            // In a real application, this would fetch detailed data for the selected period
            if (kpiType === 'oee') {
                createComponentsChart();
            } else {
                // For other KPI types, show a message
                document.getElementById('drillDownChart1').getContext('2d').canvas.style.display = 'none';
                document.getElementById('drillDownChart1').parentNode.innerHTML = '<div class="alert alert-info">Component breakdown is only available for OEE.</div>';
            }

            // Always show assets breakdown
            createAssetsChart(kpiType);
        }

        // Create components breakdown chart (for OEE)
        function createComponentsChart() {
            const ctx = document.getElementById('drillDownChart1').getContext('2d');

            // Destroy existing chart if it exists
            if (componentsChart) {
                componentsChart.destroy();
            }

            // Sample data for OEE components
            const componentLabels = ['Availability', 'Performance', 'Quality'];
            const componentData = [92.5, 85.3, 97.2];

            componentsChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: componentLabels,
                    datasets: [{
                        label: 'Component Value (%)',
                        data: componentData,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(75, 192, 192, 0.6)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(75, 192, 192, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Value (%)'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'OEE Components Breakdown'
                        }
                    }
                }
            });
        }

        // Create assets breakdown chart
        function createAssetsChart(kpiType) {
            const ctx = document.getElementById('drillDownChart2').getContext('2d');

            // Destroy existing chart if it exists
            if (assetsChart) {
                assetsChart.destroy();
            }

            // Sample data for assets
            const assetLabels = ['CNC Machine', 'HVAC System', 'Injection Molding 1', 'Injection Molding 2', 'CNC Milling 1'];
            const assetData = [82.5, 88.3, 78.9, 86.2, 90.1];

            // Determine label based on KPI type
            let datasetLabel = 'Value';
            let yAxisLabel = 'Value';

            if (kpiType === 'oee') {
                datasetLabel = 'OEE (%)';
                yAxisLabel = 'OEE (%)';
            } else if (kpiType === 'mttr') {
                datasetLabel = 'MTTR (hours)';
                yAxisLabel = 'MTTR (hours)';
            } else if (kpiType === 'mtbf') {
                datasetLabel = 'MTBF (hours)';
                yAxisLabel = 'MTBF (hours)';
            } else if (kpiType === 'availability') {
                datasetLabel = 'Availability (%)';
                yAxisLabel = 'Availability (%)';
            } else if (kpiType === 'performance') {
                datasetLabel = 'Performance (%)';
                yAxisLabel = 'Performance (%)';
            } else if (kpiType === 'quality') {
                datasetLabel = 'Quality (%)';
                yAxisLabel = 'Quality (%)';
            }

            assetsChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: assetLabels,
                    datasets: [{
                        label: datasetLabel,
                        data: assetData,
                        backgroundColor: 'rgba(153, 102, 255, 0.6)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: yAxisLabel
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `${datasetLabel} by Asset`
                        }
                    }
                }
            });
        }

        // Event listener for Apply Filters button
        document.getElementById('applyFilters').addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default form submission
            fetchKpiData();
        });

        // Event listeners for export buttons
        document.getElementById('exportCsv').addEventListener('click', function() {
            // Export main chart data to CSV
            if (!mainChart) return;

            const data = [];

            // Add header row
            const headers = ['Time Period', mainChart.data.datasets[0].label];
            if (mainChart.data.datasets.length > 1) {
                headers.push(mainChart.data.datasets[1].label);
            }
            data.push(headers);

            // Add data rows
            for (let i = 0; i < mainChart.data.labels.length; i++) {
                const row = [mainChart.data.labels[i], mainChart.data.datasets[0].data[i]];
                if (mainChart.data.datasets.length > 1) {
                    row.push(mainChart.data.datasets[1].data[i]);
                }
                data.push(row);
            }

            // Convert to CSV string
            let csvContent = "data:text/csv;charset=utf-8,";
            data.forEach(rowArray => {
                const row = rowArray.join(",");
                csvContent += row + "\r\n";
            });

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `kpi_analytics_${document.getElementById('kpiType').value}_${new Date().toISOString().slice(0,10)}.csv`);
            document.body.appendChild(link);

            // Trigger download and remove link
            link.click();
            document.body.removeChild(link);
        });

        document.getElementById('exportPdf').addEventListener('click', function() {
            // Export main chart as image
            if (!mainChart) return;

            const canvas = document.getElementById('mainChart');
            const imgData = canvas.toDataURL('image/png');

            // Create a PDF with the chart image
            const docDefinition = {
                content: [
                    { text: mainChart.options.plugins.title.text, style: 'header' },
                    { text: `Generated on ${new Date().toLocaleString()}`, style: 'subheader' },
                    { image: imgData, width: 500 }
                ],
                styles: {
                    header: { fontSize: 18, bold: true, margin: [0, 0, 0, 10] },
                    subheader: { fontSize: 12, italics: true, margin: [0, 0, 0, 5] }
                }
            };

            // Download PDF (this would use a PDF library like pdfmake in a real application)
            alert('PDF export functionality would be implemented with a library like pdfmake.');
        });

        // Add event listeners for filter changes
        document.getElementById('kpiType').addEventListener('change', function() {
            // Reset drill-down section when KPI type changes
            document.getElementById('drillDownSection').style.display = 'none';
        });
    });
</script>
{% endblock %}
