import os
import sys
import json
import requests
from dotenv import load_dotenv
import firebase_admin
from firebase_admin import credentials, auth

# Load environment variables
load_dotenv()

# Path to your service account key file
SERVICE_ACCOUNT_KEY_PATH = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if already initialized
        firebase_admin.get_app()
        print("Firebase Admin SDK already initialized")
    except ValueError:
        # Initialize with service account
        try:
            cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)
            firebase_admin.initialize_app(cred)
            print("Firebase Admin SDK initialized successfully")
        except Exception as e:
            print(f"Error initializing Firebase Admin SDK: {e}")
            sys.exit(1)

def create_custom_token(uid):
    """Create a custom token for the given user ID"""
    try:
        custom_token = auth.create_custom_token(uid)
        print(f"Custom token created for user {uid}")
        return custom_token
    except Exception as e:
        print(f"Error creating custom token: {e}")
        return None

def exchange_custom_token_for_id_token(custom_token, api_key):
    """Exchange a custom token for an ID token using the Firebase Auth REST API"""
    url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key={api_key}"
    payload = {
        "token": custom_token.decode('utf-8'),
        "returnSecureToken": True
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        data = response.json()
        id_token = data.get("idToken")
        
        if id_token:
            print("Successfully obtained ID token")
            return id_token
        else:
            print("ID token not found in response")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Error exchanging custom token for ID token: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def get_user_by_email(email):
    """Get a user by email"""
    try:
        user = auth.get_user_by_email(email)
        print(f"Found user: {user.uid} ({user.email})")
        return user
    except auth.UserNotFoundError:
        print(f"User with email {email} not found")
        return None
    except Exception as e:
        print(f"Error getting user by email: {e}")
        return None

def create_user(email, password):
    """Create a new user with the given email and password"""
    try:
        user = auth.create_user(
            email=email,
            password=password,
            email_verified=True
        )
        print(f"Created new user: {user.uid} ({user.email})")
        return user
    except auth.EmailAlreadyExistsError:
        print(f"User with email {email} already exists")
        return get_user_by_email(email)
    except Exception as e:
        print(f"Error creating user: {e}")
        return None

def main():
    # Initialize Firebase Admin SDK
    initialize_firebase()
    
    # User credentials
    email = "<EMAIL>"
    password = "TestTest"
    
    # Firebase Web API Key (from Firebase console -> Project settings -> Web API Key)
    # This is different from your service account key
    firebase_api_key = input("Enter your Firebase Web API Key: ")
    
    # Get or create user
    user = get_user_by_email(email)
    if not user:
        user = create_user(email, password)
        if not user:
            print("Failed to get or create user")
            sys.exit(1)
    
    # Create custom token
    custom_token = create_custom_token(user.uid)
    if not custom_token:
        print("Failed to create custom token")
        sys.exit(1)
    
    # Exchange custom token for ID token
    id_token = exchange_custom_token_for_id_token(custom_token, firebase_api_key)
    if not id_token:
        print("Failed to get ID token")
        sys.exit(1)
    
    # Print the ID token
    print("\n=== Firebase ID Token ===")
    print(id_token)
    print("\n=== Use this token in your Authorization header ===")
    print(f"Authorization: Bearer {id_token}")
    
    # Save token to file for convenience
    with open("firebase_id_token.txt", "w") as f:
        f.write(id_token)
    print("\nToken saved to firebase_id_token.txt")
    
    # Create a sample curl command
    curl_command = f'curl -X GET "http://localhost:8000/api/kpi/history/MTTR_Calculated" -H "Authorization: Bearer {id_token}"'
    with open("test_api_curl.sh", "w") as f:
        f.write(curl_command)
    print("\nSample curl command saved to test_api_curl.sh")

if __name__ == "__main__":
    main()
