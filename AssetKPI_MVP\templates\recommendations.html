{% extends "layout.html" %}

{% block title %}AssetKPI - Recommendations{% endblock %}

{% block styles %}
<style>
    .recommendation-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .recommendation-card:hover {
        transform: translateY(-5px);
    }
    .recommendation-item {
        border-left: 4px solid #0d6efd;
        padding-left: 10px;
        margin-bottom: 10px;
    }
    .recommendation-item.priority-1 {
        border-left-color: #dc3545;
    }
    .recommendation-item.priority-2 {
        border-left-color: #fd7e14;
    }
    .recommendation-item.priority-3 {
        border-left-color: #0d6efd;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .status-acknowledged {
        background-color: rgba(25, 135, 84, 0.1);
    }
    .status-dismissed {
        background-color: rgba(108, 117, 125, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>Inventory Recommendations</h1>
        <p class="text-muted">View and manage inventory optimization recommendations</p>
    </div>
</div>

<!-- Active Recommendations -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Active Recommendations</h5>
            </div>
            <div class="card-body">
                {% if active_recommendations %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="activeRecommendationsTable">
                            <thead>
                                <tr>
                                    <th>Priority</th>
                                    <th>Type</th>
                                    <th>Part Details</th>
                                    <th>Reason</th>
                                    <th>Generated At</th>
                                    <th class="auth-required-content" data-role="MANAGER,ADMIN">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rec, part_id, part_name, part_number in active_recommendations %}
                                    <tr id="rec-row-{{ rec.id }}">
                                        <td class="priority-{{ rec.priority }}">{{ rec.priority }}</td>
                                        <td>{{ rec.recommendation_type }}</td>
                                        <td>({{ part_id }}) {{ part_name }} [{{ part_number }}]</td>
                                        <td>{{ rec.reason }}</td>
                                        <td>{{ rec.generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        <td class="auth-required-content" data-role="MANAGER,ADMIN">
                                            <button class="btn btn-sm btn-outline-success action-button" data-rec-id="{{ rec.id }}" data-action="ACKNOWLEDGED">Acknowledge</button>
                                            <button class="btn btn-sm btn-outline-danger action-button" data-rec-id="{{ rec.id }}" data-action="DISMISSED">Dismiss</button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No active recommendations at this time.</p>
                {% endif %}
                <div id="rec-action-message" class="mt-2 text-success"></div>
            </div>
        </div>
    </div>
</div>

<!-- Historical Recommendations -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Historical Recommendations</h5>
            </div>
            <div class="card-body">
                {% if historical_recommendations %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="historicalRecommendationsTable">
                            <thead>
                                <tr>
                                    <th>Priority</th>
                                    <th>Type</th>
                                    <th>Part Details</th>
                                    <th>Reason</th>
                                    <th>Generated At</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rec, part_id, part_name, part_number in historical_recommendations %}
                                    <tr class="status-{{ rec.status|lower }}">
                                        <td class="priority-{{ rec.priority }}">{{ rec.priority }}</td>
                                        <td>{{ rec.recommendation_type }}</td>
                                        <td>({{ part_id }}) {{ part_name }} [{{ part_number }}]</td>
                                        <td>{{ rec.reason }}</td>
                                        <td>{{ rec.generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        <td>{{ rec.status }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No historical recommendations available.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Function to handle recommendation actions
    async function handleRecommendationAction(recId, action) {
        const actionMessageDiv = document.getElementById('rec-action-message');
        actionMessageDiv.textContent = '';

        try {
            const response = await AssetKPIAuth.authenticatedFetch(`/api/recommendations/${recId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ new_status: action })
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.detail || `Failed to update status: ${response.status}`);
            }

            // Success!
            actionMessageDiv.textContent = result.message || `Recommendation ${recId} updated to ${action}.`;
            actionMessageDiv.className = 'mt-2 text-success';

            // Remove the recommendation item from UI
            const rowToRemove = document.getElementById(`rec-row-${recId}`);
            if (rowToRemove) {
                rowToRemove.remove();
            }
            
            // Reload the page after a short delay to refresh the historical recommendations
            setTimeout(() => {
                window.location.reload();
            }, 2000);

        } catch (error) {
            console.error("Error updating recommendation status:", error);
            actionMessageDiv.textContent = `Error: ${error.message}`;
            actionMessageDiv.className = 'mt-2 text-danger';
        }
    }

    // Function to setup recommendation buttons
    function setupRecommendationButtons() {
        document.querySelectorAll('.action-button').forEach(btn => {
            btn.addEventListener('click', function() {
                const recId = this.dataset.recId;
                const action = this.dataset.action;

                // Disable button to prevent double-clicks
                this.disabled = true;
                this.textContent = '...';

                handleRecommendationAction(recId, action).finally(() => {
                    // Re-enable button on completion
                    this.disabled = false;
                    this.textContent = action === 'ACKNOWLEDGED' ? 'Acknowledge' : 'Dismiss';
                });
            });
        });
    }
    
    // Function to update UI based on authentication
    function updateUI(user) {
        const authRequiredContent = document.querySelectorAll('.auth-required-content');
        
        if (user) {
            // User is authenticated, show role-specific content
            AssetKPIAuth.authenticatedFetch('/api/users/me')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('Failed to fetch user info');
                })
                .then(data => {
                    const userRole = data.role || '';
                    console.log('User role:', userRole);
                    
                    // Show content based on role
                    authRequiredContent.forEach(el => {
                        const requiredRoles = (el.dataset.role || '').split(',');
                        if (requiredRoles.includes(userRole)) {
                            el.style.display = 'table-cell'; // Use table-cell for td elements
                        } else {
                            el.style.display = 'none';
                        }
                    });
                    
                    // Setup recommendation buttons if user has appropriate role
                    if (['MANAGER', 'ADMIN'].includes(userRole)) {
                        setupRecommendationButtons();
                    }
                })
                .catch(error => {
                    console.error('Error fetching user info:', error);
                });
        } else {
            // User is not authenticated, hide all authenticated content
            authRequiredContent.forEach(el => {
                el.style.display = 'none';
            });
        }
    }
    
    // Initialize authentication UI
    document.addEventListener('DOMContentLoaded', function() {
        AssetKPIAuth.initAuth(updateUI);
    });
</script>
{% endblock %}
