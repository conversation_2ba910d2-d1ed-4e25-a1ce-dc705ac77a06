# AssetKPI API Documentation

## Overview

The AssetKPI API provides programmatic access to asset management, inventory optimization, and KPI calculation functionality. This documentation describes the available endpoints, authentication requirements, and provides examples for common operations.

## Table of Contents

### Getting Started
- [Getting Started Guide](getting_started.md)
- [Use Cases and Examples](examples/use_cases.md)
- [Postman Collection Guide](examples/postman_guide.md)

### General Information
- [Authentication](../api_documentation.md#authentication)
- [Base URL](../api_documentation.md#base-url)
- [Response Format](../api_documentation.md#response-format)
- [Rate Limits](../api_documentation.md#rate-limits)
- [Detailed Rate Limiting Guide](rate_limiting.md)
- [Error Codes](../api_documentation.md#error-codes)

### API Endpoints
- [Inventory Endpoints](endpoints/inventory.md)
- [KPI Endpoints](endpoints/kpis.md)
- [Work Order Endpoints](endpoints/workorders.md)
- [User Management Endpoints](endpoints/users.md)

### Code Examples
- [Python Client Examples](examples/python_client.md)
- [JavaScript Client Examples](examples/javascript_client.md)
- [cURL Examples](examples/curl_examples.md)
- [Use Case Examples](examples/use_cases.md)

## Getting Started

To get started with the AssetKPI API, you'll need:

1. A valid Firebase ID token or API key for authentication
2. The base URL for the API (e.g., `http://localhost:8000/api` for local development)

### Authentication Methods

The API supports two authentication methods:

#### Firebase Authentication Token

For browser-based applications and user-specific operations, use a Firebase ID token in the Authorization header:

```
Authorization: Bearer <firebase_id_token>
```

#### API Key Authentication

For server-to-server communication and automated processes, use an API key in the X-API-Key header:

```
X-API-Key: <api_key>
```

### Example Request

Here's a simple example of how to make a request to the API using cURL:

```bash
# Using Firebase ID token
curl -X GET "http://localhost:8000/api/inventory/parts" \
  -H "Authorization: Bearer <firebase_id_token>"

# Using API key
curl -X GET "http://localhost:8000/api/inventory/parts" \
  -H "X-API-Key: <api_key>"
```

## API Versioning

The current version of the API is v1. All endpoints are prefixed with `/api` and do not require an explicit version in the URL.

Future versions of the API may use a different prefix (e.g., `/api/v2`) or require a version header.

## Support

If you encounter any issues or have questions about the API, please contact the AssetKPI support team.

## Changelog

| Date | Version | Description |
|------|---------|-------------|
| 2023-04-16 | 1.0.0 | Initial API documentation |
