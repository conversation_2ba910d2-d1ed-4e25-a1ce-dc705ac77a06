import psycopg2

# Connect to the database
conn = psycopg2.connect('postgresql://postgres:Arcanum@localhost:5432/AssetKPI')
cur = conn.cursor()

# Get the column names
cur.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' ORDER BY ordinal_position")
columns = [col[0] for col in cur.fetchall()]
print("Columns in users table:", columns)

# Get the data
cur.execute("SELECT * FROM users")
rows = cur.fetchall()
print("\nData in users table:")
for row in rows:
    print(row)

# Check if user_id column exists
if 'user_id' in columns:
    print("\nuser_id column exists")
else:
    print("\nuser_id column does not exist")

# Check if uid column exists
if 'uid' in columns:
    print("uid column exists")
else:
    print("uid column does not exist")

# Close the connection
cur.close()
conn.close()
