# cURL Examples

This document provides examples of how to use the AssetKPI API with cURL, a command-line tool for making HTTP requests.

## Authentication

### Using Firebase ID Token

```bash
# Store your Firebase ID token in a variable for easier use
FIREBASE_TOKEN="your-firebase-id-token"

# Get current user
curl -X GET "http://localhost:8000/api/users/me" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Using API Key

```bash
# Store your API key in a variable for easier use
API_KEY="your-api-key"

# Get all spare parts
curl -X GET "http://localhost:8000/api/inventory/parts" \
  -H "X-API-Key: $API_KEY"
```

## Inventory Management Examples

### Get All Spare Parts

```bash
# Get all spare parts
curl -X GET "http://localhost:8000/api/inventory/parts" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"

# Get spare parts with pagination and sorting
curl -X GET "http://localhost:8000/api/inventory/parts?limit=10&offset=0&sort=unitprice&order=desc" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get Specific Spare Part

```bash
# Get a specific spare part by ID
curl -X GET "http://localhost:8000/api/inventory/parts/1" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Update Spare Part

```bash
# Update a spare part
curl -X PUT "http://localhost:8000/api/inventory/parts/1" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "stockquantity": 20,
    "reorderlevel": 8
  }'
```

### Create Spare Part

```bash
# Create a new spare part
curl -X POST "http://localhost:8000/api/inventory/parts" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "partname": "Motor Coupling",
    "partnumber": "MC-3045",
    "manufacturer": "ABB",
    "stockquantity": 10,
    "reorderlevel": 3,
    "unitprice": 85.25,
    "leadtimedays": 7
  }'
```

### Delete Spare Part

```bash
# Delete a spare part
curl -X DELETE "http://localhost:8000/api/inventory/parts/97" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get Inventory Analysis

```bash
# Get inventory analysis for all parts
curl -X GET "http://localhost:8000/api/inventory/analysis" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get Part Inventory Analysis

```bash
# Get detailed inventory analysis for a specific part
curl -X GET "http://localhost:8000/api/inventory/analysis/1" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get Inventory Optimization Report

```bash
# Get inventory optimization report
curl -X GET "http://localhost:8000/api/inventory/optimization-report" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Run Inventory Optimization

```bash
# Run inventory optimization job
curl -X GET "http://localhost:8000/api/inventory/run-optimization" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

## KPI Management Examples

### Get Latest KPIs

```bash
# Get latest KPI values
curl -X GET "http://localhost:8000/api/kpis/latest" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get KPI History

```bash
# Get KPI history for MTTR with date range
curl -X GET "http://localhost:8000/api/kpis/history/MTTR_Calculated?start_date=2023-01-01&end_date=2023-04-15" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get KPI Report

```bash
# Get comprehensive KPI report
curl -X GET "http://localhost:8000/api/kpis/report?start_date=2023-01-01&end_date=2023-04-15" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Run KPI Calculations

```bash
# Run KPI calculation job
curl -X GET "http://localhost:8000/api/kpis/run-calculations" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Save Custom KPI

```bash
# Save a custom KPI value
curl -X POST "http://localhost:8000/api/kpis/custom" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "kpi_name": "Custom_Efficiency",
    "kpi_value": 92.5,
    "kpi_unit": "percent",
    "asset_id": 1,
    "notes": "Manually calculated efficiency based on production data"
  }'
```

## Work Order Management Examples

### Get Work Orders

```bash
# Get all work orders
curl -X GET "http://localhost:8000/api/workorders" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"

# Get open corrective work orders
curl -X GET "http://localhost:8000/api/workorders?status=OPEN&type=Corrective" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get Work Order Details

```bash
# Get details for a specific work order
curl -X GET "http://localhost:8000/api/workorders/1" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Create Work Order

```bash
# Create a new work order
curl -X POST "http://localhost:8000/api/ingest/workorder" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "assetId": 5,
    "workOrderType": "Corrective",
    "description": "Replace motor coupling",
    "status": "OPEN",
    "assignedTo": "Jane Doe",
    "failureCode": "MECH-002",
    "failureType": "Mechanical",
    "downtimeMinutes": 180,
    "repairTimeMinutes": 120,
    "maintenanceCost": 450.00,
    "startDate": "2023-04-16T09:00:00Z"
  }'
```

### Update Work Order

```bash
# Update an existing work order
curl -X PUT "http://localhost:8000/api/workorders/25" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "CLOSED",
    "endDate": "2023-04-16T14:30:00Z",
    "repairTimeMinutes": 150,
    "maintenanceCost": 520.00
  }'
```

### Delete Work Order

```bash
# Delete a work order
curl -X DELETE "http://localhost:8000/api/workorders/25" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Add Parts to Work Order

```bash
# Add parts to a work order
curl -X POST "http://localhost:8000/api/workorders/1/parts" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "parts": [
      {
        "partid": 1,
        "quantityused": 1
      },
      {
        "partid": 15,
        "quantityused": 2
      }
    ]
  }'
```

### Update Work Order Parts

```bash
# Update a part in a work order
curl -X PUT "http://localhost:8000/api/workorders/1/parts/15" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quantityused": 3
  }'
```

### Remove Part from Work Order

```bash
# Remove a part from a work order
curl -X DELETE "http://localhost:8000/api/workorders/1/parts/15" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

## User Management Examples

### Get Current User

```bash
# Get current user information
curl -X GET "http://localhost:8000/api/users/me" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get All Users

```bash
# Get all users (admin only)
curl -X GET "http://localhost:8000/api/users" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Get User by ID

```bash
# Get a specific user by ID
curl -X GET "http://localhost:8000/api/users/firebase-test-manager-uid" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Create User

```bash
# Create a new user
curl -X POST "http://localhost:8000/api/users" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securePassword123",
    "role": "ENGINEER",
    "full_name": "New User"
  }'
```

### Update User

```bash
# Update an existing user
curl -X PUT "http://localhost:8000/api/users/firebase-test-manager-uid" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "MANAGER",
    "full_name": "Updated User Name"
  }'
```

### Delete User

```bash
# Delete a user
curl -X DELETE "http://localhost:8000/api/users/firebase-test-manager-uid" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Update User Role

```bash
# Update a user's role
curl -X PUT "http://localhost:8000/api/users/firebase-test-manager-uid/role" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "MANAGER"
  }'
```

## API Key Management Examples

### Get API Keys

```bash
# Get all API keys
curl -X GET "http://localhost:8000/api/users/api-keys" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

### Create API Key

```bash
# Create a new API key
curl -X POST "http://localhost:8000/api/users/api-keys" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New API Key",
    "permissions": ["READ", "WRITE"]
  }'
```

### Delete API Key

```bash
# Delete an API key
curl -X DELETE "http://localhost:8000/api/users/api-keys/key3" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

## Bulk Operations Examples

### Bulk Import Work Orders

```bash
# Import multiple work orders at once
curl -X POST "http://localhost:8000/api/ingest/workorders/bulk" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "workorders": [
      {
        "assetId": 5,
        "workOrderType": "Corrective",
        "description": "Replace motor coupling",
        "status": "CLOSED",
        "assignedTo": "Jane Doe",
        "failureCode": "MECH-002",
        "failureType": "Mechanical",
        "downtimeMinutes": 180,
        "repairTimeMinutes": 120,
        "maintenanceCost": 450.00,
        "startDate": "2023-04-16T09:00:00Z",
        "endDate": "2023-04-16T14:30:00Z"
      },
      {
        "assetId": 8,
        "workOrderType": "Preventive",
        "description": "Lubricate bearings",
        "status": "CLOSED",
        "assignedTo": "John Smith",
        "failureCode": null,
        "failureType": null,
        "downtimeMinutes": 60,
        "repairTimeMinutes": 45,
        "maintenanceCost": 150.00,
        "startDate": "2023-04-17T10:00:00Z",
        "endDate": "2023-04-17T11:00:00Z"
      }
    ]
  }'
```

### Upload Work Orders CSV

```bash
# Upload work orders from a CSV file
curl -X POST "http://localhost:8000/upload/workorders" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -F "file=@/path/to/workorders.csv"
```

## Shell Script Example

Here's a complete shell script example that demonstrates how to use cURL to interact with the AssetKPI API:

```bash
#!/bin/bash

# AssetKPI API Client Script
# This script demonstrates how to use cURL to interact with the AssetKPI API

# Configuration
API_BASE_URL="http://localhost:8000/api"
API_KEY="c5e52be8-9b1c-4fcd-8457-741c91ef5c85"

# Function to make API requests
function api_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    # Build cURL command
    local curl_cmd="curl -s -X $method \"$API_BASE_URL$endpoint\" -H \"X-API-Key: $API_KEY\""
    
    # Add data if provided
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -H \"Content-Type: application/json\" -d '$data'"
    fi
    
    # Execute the command
    eval $curl_cmd
}

# Get inventory summary
echo "Fetching inventory summary..."
inventory_summary=$(api_request "GET" "/inventory/summary")
echo $inventory_summary | jq .
echo

# Get parts that need reordering
echo "Fetching parts that need reordering..."
parts=$(api_request "GET" "/inventory/parts")
echo "Parts to reorder:"
echo $parts | jq '.[] | select(.stockquantity <= .reorderlevel) | {partid, partname, stockquantity, reorderlevel}'
echo

# Get recent work orders
echo "Fetching recent work orders..."
work_orders=$(api_request "GET" "/workorders?limit=5&order=desc")
echo "Recent work orders:"
echo $work_orders | jq '.[] | {workorderid, description, status, assetid, startdate}'
echo

# Get latest KPIs
echo "Fetching latest KPIs..."
kpis=$(api_request "GET" "/kpis/latest")
echo "Latest KPIs:"
echo $kpis | jq .
echo

# Create a new work order
echo "Creating a new work order..."
new_work_order='{
    "assetId": 5,
    "workOrderType": "Preventive",
    "description": "Quarterly maintenance check",
    "status": "OPEN",
    "assignedTo": "John Smith",
    "downtimeMinutes": 60,
    "startDate": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"
}'
result=$(api_request "POST" "/ingest/workorder" "$new_work_order")
echo "Work order creation result:"
echo $result | jq .
echo

echo "Script completed successfully!"
```

To use this script:

1. Save it to a file (e.g., `assetkpi_api_client.sh`)
2. Make it executable: `chmod +x assetkpi_api_client.sh`
3. Run it: `./assetkpi_api_client.sh`

Note: This script requires `jq` for JSON processing. Install it with `apt-get install jq` on Debian/Ubuntu or `brew install jq` on macOS.
