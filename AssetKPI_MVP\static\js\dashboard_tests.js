// Dashboard Tests JavaScript
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

// Auto-refresh functionality
let autoRefreshInterval;

document.addEventListener('DOMContentLoaded', function() {
    setupAutoRefresh();
});

function setupAutoRefresh() {
    const autoRefreshCheckbox = document.getElementById('autoRefresh');
    
    if (autoRefreshCheckbox.checked) {
        startAutoRefresh();
    }
    
    autoRefreshCheckbox.addEventListener('change', function() {
        if (this.checked) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    });
}

function startAutoRefresh() {
    autoRefreshInterval = setInterval(() => {
        runAllTests();
    }, 30000); // 30 seconds
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
}

function runAllTests() {
    clearResults();
    
    // Run all tests
    testAssetsAPI();
    testAssetsCount();
    testInventorySummary();
    testWorkOrdersCount();
    testKPIAnalytics();
    testAssetPerformance();
}

function clearResults() {
    testResults = { passed: 0, failed: 0, warnings: 0 };
    
    // Clear all test result divs
    const resultDivs = document.querySelectorAll('.test-result');
    resultDivs.forEach(div => {
        div.style.display = 'none';
        div.innerHTML = '';
    });
    
    // Reset card styles
    const testCards = document.querySelectorAll('.test-card');
    testCards.forEach(card => {
        card.classList.remove('test-success', 'test-error', 'test-warning');
    });
    
    updateSummary();
}

function updateSummary() {
    document.getElementById('passed-count').textContent = testResults.passed;
    document.getElementById('failed-count').textContent = testResults.failed;
    document.getElementById('warning-count').textContent = testResults.warnings;
}

function showTestResult(testId, result, status = 'success') {
    const resultDiv = document.getElementById(testId + '-result');
    const testCard = document.getElementById(testId + '-test');
    
    resultDiv.innerHTML = result;
    resultDiv.style.display = 'block';
    
    // Update card styling
    testCard.classList.remove('test-success', 'test-error', 'test-warning');
    if (status === 'success') {
        testCard.classList.add('test-success');
        testResults.passed++;
    } else if (status === 'error') {
        testCard.classList.add('test-error');
        testResults.failed++;
    } else if (status === 'warning') {
        testCard.classList.add('test-warning');
        testResults.warnings++;
    }
    
    updateSummary();
}

async function testAssetsAPI() {
    try {
        const response = await fetch('/api/assets?limit=5', {
            headers: {
                'Authorization': 'Bearer test-admin-token'
            }
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            const result = `
                <strong>✅ SUCCESS</strong><br>
                Status: ${response.status}<br>
                Total Assets: ${data.total}<br>
                Retrieved: ${data.data.length}<br>
                Sample Asset: ${data.data[0] ? data.data[0].assetname : 'None'}<br>
                Response Time: ${new Date().toLocaleTimeString()}
            `;
            showTestResult('assets', result, 'success');
        } else {
            throw new Error(`API returned error: ${data.detail || 'Unknown error'}`);
        }
    } catch (error) {
        const result = `
            <strong>❌ ERROR</strong><br>
            Error: ${error.message}<br>
            Time: ${new Date().toLocaleTimeString()}
        `;
        showTestResult('assets', result, 'error');
    }
}

async function testAssetsCount() {
    try {
        const response = await fetch('/api/assets/count', {
            headers: {
                'Authorization': 'Bearer test-admin-token'
            }
        });
        
        const data = await response.json();
        
        if (response.ok && typeof data.count === 'number') {
            const result = `
                <strong>✅ SUCCESS</strong><br>
                Status: ${response.status}<br>
                Asset Count: ${data.count}<br>
                Response Time: ${new Date().toLocaleTimeString()}
            `;
            showTestResult('assets-count', result, 'success');
        } else {
            throw new Error(`Invalid response: ${JSON.stringify(data)}`);
        }
    } catch (error) {
        const result = `
            <strong>❌ ERROR</strong><br>
            Error: ${error.message}<br>
            Time: ${new Date().toLocaleTimeString()}
        `;
        showTestResult('assets-count', result, 'error');
    }
}

async function testInventorySummary() {
    try {
        const response = await fetch('/api/inventory/summary', {
            headers: {
                'Authorization': 'Bearer test-admin-token'
            }
        });
        
        const data = await response.json();
        
        if (response.ok && data.total_parts !== undefined) {
            const result = `
                <strong>✅ SUCCESS</strong><br>
                Status: ${response.status}<br>
                Total Parts: ${data.total_parts}<br>
                Total Value: $${data.total_value?.toFixed(2) || '0.00'}<br>
                Below Reorder: ${data.below_reorder}<br>
                Response Time: ${new Date().toLocaleTimeString()}
            `;
            showTestResult('inventory', result, 'success');
        } else {
            throw new Error(`Invalid response: ${JSON.stringify(data)}`);
        }
    } catch (error) {
        const result = `
            <strong>❌ ERROR</strong><br>
            Error: ${error.message}<br>
            Time: ${new Date().toLocaleTimeString()}
        `;
        showTestResult('inventory', result, 'error');
    }
}

async function testWorkOrdersCount() {
    try {
        const response = await fetch('/api/workorders/count', {
            headers: {
                'Authorization': 'Bearer test-admin-token'
            }
        });
        
        const data = await response.json();
        
        if (response.ok && typeof data.count === 'number') {
            const result = `
                <strong>✅ SUCCESS</strong><br>
                Status: ${response.status}<br>
                Work Orders Count: ${data.count}<br>
                Response Time: ${new Date().toLocaleTimeString()}
            `;
            showTestResult('workorders', result, 'success');
        } else {
            throw new Error(`Invalid response: ${JSON.stringify(data)}`);
        }
    } catch (error) {
        const result = `
            <strong>❌ ERROR</strong><br>
            Error: ${error.message}<br>
            Time: ${new Date().toLocaleTimeString()}
        `;
        showTestResult('workorders', result, 'error');
    }
}

async function testKPIAnalytics() {
    try {
        const response = await fetch('/api/kpi/analytics', {
            headers: {
                'Authorization': 'Bearer test-admin-token'
            }
        });

        const data = await response.json();

        if (response.ok && data.timeLabels && data.kpiData && data.summary) {
            const result = `
                <strong>✅ SUCCESS</strong><br>
                Status: ${response.status}<br>
                Time Labels: ${data.timeLabels.length}<br>
                KPI Data Points: ${data.kpiData.length}<br>
                Current Value: ${data.summary.currentValue}<br>
                Average Value: ${data.summary.averageValue?.toFixed(2) || 'N/A'}<br>
                Trend: ${data.summary.trend > 0 ? '↗️' : data.summary.trend < 0 ? '↘️' : '➡️'} ${data.summary.trend?.toFixed(2) || 'N/A'}<br>
                Anomalies: ${data.anomalies ? data.anomalies.length : 0}<br>
                Response Time: ${new Date().toLocaleTimeString()}
            `;
            showTestResult('kpi', result, 'success');
        } else {
            throw new Error(`Invalid response structure. Expected {timeLabels, kpiData, summary} but got: ${Object.keys(data).join(', ')}`);
        }
    } catch (error) {
        const result = `
            <strong>❌ ERROR</strong><br>
            Error: ${error.message}<br>
            Time: ${new Date().toLocaleTimeString()}
        `;
        showTestResult('kpi', result, 'error');
    }
}

async function testAssetPerformance() {
    try {
        const response = await fetch('/api/assets/performance?chart_type=oee&limit=5', {
            headers: {
                'Authorization': 'Bearer test-admin-token'
            }
        });
        
        const data = await response.json();
        
        if (response.ok && data.timeLabels && data.mainChartData) {
            const result = `
                <strong>✅ SUCCESS</strong><br>
                Status: ${response.status}<br>
                Time Labels: ${data.timeLabels.length}<br>
                Data Points: ${data.mainChartData.length}<br>
                Assets: ${data.assets ? data.assets.length : 0}<br>
                Response Time: ${new Date().toLocaleTimeString()}
            `;
            showTestResult('performance', result, 'success');
        } else {
            throw new Error(`Invalid response structure`);
        }
    } catch (error) {
        const result = `
            <strong>❌ ERROR</strong><br>
            Error: ${error.message}<br>
            Time: ${new Date().toLocaleTimeString()}
        `;
        showTestResult('performance', result, 'error');
    }
}
