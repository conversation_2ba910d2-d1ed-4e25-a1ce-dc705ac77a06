import os
import sys
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base URL for the API
BASE_URL = "http://localhost:8000"

def test_protected_endpoint():
    """
    Test a protected endpoint with a Firebase ID token.

    In a real scenario, you would get this token from Firebase Authentication
    after signing in a user. For testing, you can manually create a token
    in the Firebase console or use a Firebase client SDK to sign in and get a token.
    """
    # Try to read the token from file
    try:
        with open("firebase_id_token.txt", "r") as f:
            firebase_token = f.read().strip()
    except FileNotFoundError:
        print("Token file not found. Please run get_token_with_password.py first.")
        return
    except Exception as e:
        print(f"Error reading token file: {e}")
        return

    # Test the KPI history endpoint
    endpoint = f"{BASE_URL}/api/kpi/history/MTTR_Calculated"

    # Make the request with the token in the Authorization header
    headers = {
        "Authorization": f"Bearer {firebase_token}"
    }

    try:
        response = requests.get(endpoint, headers=headers)

        # Print the response
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.json() if response.status_code == 200 else response.text}")

        if response.status_code == 200:
            print("Authentication successful!")
        elif response.status_code == 401:
            print("Authentication failed: Invalid or expired token")
        elif response.status_code == 403:
            print("Authentication successful but authorization failed: Insufficient permissions")
        else:
            print(f"Unexpected status code: {response.status_code}")

    except Exception as e:
        print(f"Error: {e}")

def test_without_token():
    """Test a protected endpoint without a token."""
    endpoint = f"{BASE_URL}/api/kpi/history/MTTR_Calculated"

    try:
        response = requests.get(endpoint)

        # Print the response
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.json() if response.status_code == 200 else response.text}")

        if response.status_code == 401:
            print("Expected 401 Unauthorized: No token provided")
        else:
            print(f"Unexpected status code: {response.status_code}")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("Testing protected endpoint without token:")
    test_without_token()

    print("\nTesting protected endpoint with token:")
    test_protected_endpoint()
