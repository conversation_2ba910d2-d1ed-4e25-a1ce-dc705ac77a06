<!DOCTYPE html>
<html lang="en">
<head>
    <title>AssetKPI - Auth Debug</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 40px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .token-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .result-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">Authentication Debug Tool</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>Authentication Status</h5>
            </div>
            <div class="card-body">
                <div id="auth-status">Checking authentication status...</div>
                <div id="user-info" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>ID Token</h5>
            </div>
            <div class="card-body">
                <div class="token-display" id="token-display">No token available</div>
                <button class="btn btn-sm btn-outline-secondary mt-2" id="copy-token-btn">Copy Token</button>
                <button class="btn btn-sm btn-primary mt-2" id="refresh-token-btn">Refresh Token</button>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Test API Endpoints</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <button class="btn btn-primary" id="test-auth-btn">Test Auth Endpoint</button>
                    <button class="btn btn-info" id="test-kpi-btn">Test KPI Endpoint</button>
                    <button class="btn btn-success" id="test-dashboard-btn">Go to Dashboard</button>
                </div>
                <div class="result-display" id="api-result">Results will appear here...</div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Authentication Actions</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-danger" id="sign-out-btn">Sign Out</button>
                <a href="/login" class="btn btn-secondary">Go to Login Page</a>
            </div>
        </div>
    </div>
    
    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <!-- Firebase Auth -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <!-- AssetKPI Auth Utility -->
    <script src="/static/js/auth.js"></script>
    
    <script>
        // DOM elements
        const authStatus = document.getElementById('auth-status');
        const userInfo = document.getElementById('user-info');
        const tokenDisplay = document.getElementById('token-display');
        const copyTokenBtn = document.getElementById('copy-token-btn');
        const refreshTokenBtn = document.getElementById('refresh-token-btn');
        const testAuthBtn = document.getElementById('test-auth-btn');
        const testKpiBtn = document.getElementById('test-kpi-btn');
        const testDashboardBtn = document.getElementById('test-dashboard-btn');
        const apiResult = document.getElementById('api-result');
        const signOutBtn = document.getElementById('sign-out-btn');
        
        // Function to update auth status
        function updateAuthStatus(user) {
            if (user) {
                authStatus.innerHTML = `<div class="alert alert-success">Authenticated as ${user.email}</div>`;
                userInfo.innerHTML = `
                    <strong>User ID:</strong> ${user.uid}<br>
                    <strong>Email:</strong> ${user.email}<br>
                    <strong>Email Verified:</strong> ${user.emailVerified}<br>
                    <strong>Provider:</strong> ${user.providerData[0].providerId}
                `;
                
                // Get and display token
                AssetKPIAuth.getIdToken(true).then(token => {
                    if (token) {
                        tokenDisplay.textContent = token;
                    } else {
                        tokenDisplay.textContent = "Failed to get token";
                    }
                });
            } else {
                authStatus.innerHTML = '<div class="alert alert-warning">Not authenticated</div>';
                userInfo.innerHTML = '';
                tokenDisplay.textContent = 'No token available';
            }
        }
        
        // Function to test auth endpoint
        async function testAuthEndpoint() {
            apiResult.innerHTML = 'Testing auth endpoint...';
            
            try {
                const response = await AssetKPIAuth.authenticatedFetch('/api/auth-test');
                const data = await response.json();
                
                if (response.ok) {
                    apiResult.innerHTML = `<div class="alert alert-success">Auth test successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    apiResult.innerHTML = `<div class="alert alert-danger">Auth test failed: ${response.status} ${response.statusText}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                apiResult.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
                console.error('Auth test error:', error);
            }
        }
        
        // Function to test KPI endpoint
        async function testKpiEndpoint() {
            apiResult.innerHTML = 'Testing KPI endpoint...';
            
            try {
                const response = await AssetKPIAuth.authenticatedFetch('/api/kpi/history/MTTR_Calculated?limit=1');
                
                if (response.ok) {
                    const data = await response.json();
                    apiResult.innerHTML = `<div class="alert alert-success">KPI test successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    let errorText = '';
                    try {
                        const errorData = await response.json();
                        errorText = JSON.stringify(errorData, null, 2);
                    } catch (e) {
                        errorText = await response.text();
                    }
                    
                    apiResult.innerHTML = `<div class="alert alert-danger">KPI test failed: ${response.status} ${response.statusText}</div>
                        <pre>${errorText}</pre>`;
                }
            } catch (error) {
                apiResult.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
                console.error('KPI test error:', error);
            }
        }
        
        // Function to copy token
        function copyToken() {
            const token = tokenDisplay.textContent;
            if (token && token !== 'No token available' && token !== 'Failed to get token') {
                navigator.clipboard.writeText(token)
                    .then(() => {
                        copyTokenBtn.textContent = 'Copied!';
                        setTimeout(() => {
                            copyTokenBtn.textContent = 'Copy Token';
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        alert('Failed to copy token');
                    });
            }
        }
        
        // Function to refresh token
        async function refreshToken() {
            tokenDisplay.textContent = 'Refreshing token...';
            
            try {
                const token = await AssetKPIAuth.getIdToken(true);
                if (token) {
                    tokenDisplay.textContent = token;
                    apiResult.innerHTML = '<div class="alert alert-success">Token refreshed successfully</div>';
                } else {
                    tokenDisplay.textContent = 'Failed to refresh token';
                    apiResult.innerHTML = '<div class="alert alert-danger">Failed to refresh token</div>';
                }
            } catch (error) {
                console.error('Token refresh error:', error);
                tokenDisplay.textContent = 'Error refreshing token';
                apiResult.innerHTML = `<div class="alert alert-danger">Error refreshing token: ${error.message}</div>`;
            }
        }
        
        // Function to sign out
        async function signOut() {
            try {
                await AssetKPIAuth.signOut();
                apiResult.innerHTML = '<div class="alert alert-success">Signed out successfully</div>';
                updateAuthStatus(null);
            } catch (error) {
                console.error('Sign out error:', error);
                apiResult.innerHTML = `<div class="alert alert-danger">Error signing out: ${error.message}</div>`;
            }
        }
        
        // Event listeners
        copyTokenBtn.addEventListener('click', copyToken);
        refreshTokenBtn.addEventListener('click', refreshToken);
        testAuthBtn.addEventListener('click', testAuthEndpoint);
        testKpiBtn.addEventListener('click', testKpiEndpoint);
        testDashboardBtn.addEventListener('click', () => window.location.href = '/');
        signOutBtn.addEventListener('click', signOut);
        
        // Initialize auth state
        AssetKPIAuth.initAuth(updateAuthStatus);
    </script>
</body>
</html>
