{"info": {"_postman_id": "f8e52be8-9b1c-4fcd-8457-741c91ef5c85", "name": "AssetKPI API", "description": "A collection of requests for interacting with the AssetKPI API.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "if (jsonData.token) {", "    pm.environment.set(\"firebase_token\", jsonData.token);", "    console.log(\"Firebase token saved to environment\");", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{user_email}}\",\n    \"password\": \"{{user_password}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}, "description": "Authenticates a user and returns a Firebase ID token."}, "response": []}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/users/me", "host": ["{{base_url}}"], "path": ["api", "users", "me"]}, "description": "Retrieves information about the currently authenticated user."}, "response": []}], "description": "Endpoints related to authentication and user management."}, {"name": "Inventory", "item": [{"name": "Get All Spare Parts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/inventory/parts?limit=100&offset=0&sort=partid&order=asc", "host": ["{{base_url}}"], "path": ["api", "inventory", "parts"], "query": [{"key": "limit", "value": "100"}, {"key": "offset", "value": "0"}, {"key": "sort", "value": "partid"}, {"key": "order", "value": "asc"}]}, "description": "Retrieves a list of all spare parts in the inventory."}, "response": []}, {"name": "Get Specific Spare Part", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/inventory/parts/1", "host": ["{{base_url}}"], "path": ["api", "inventory", "parts", "1"]}, "description": "Retrieves details for a specific spare part."}, "response": []}, {"name": "Update Spare Part", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"stockquantity\": 20,\n    \"reorderlevel\": 8\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/inventory/parts/1", "host": ["{{base_url}}"], "path": ["api", "inventory", "parts", "1"]}, "description": "Updates details for a specific spare part."}, "response": []}, {"name": "Create Spare Part", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"partname\": \"Motor Coupling\",\n    \"partnumber\": \"MC-3045\",\n    \"manufacturer\": \"ABB\",\n    \"stockquantity\": 10,\n    \"reorderlevel\": 3,\n    \"unitprice\": 85.25,\n    \"leadtimedays\": 7\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/inventory/parts", "host": ["{{base_url}}"], "path": ["api", "inventory", "parts"]}, "description": "Creates a new spare part in the inventory."}, "response": []}, {"name": "Get Inventory Analysis", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/inventory/analysis", "host": ["{{base_url}}"], "path": ["api", "inventory", "analysis"]}, "description": "Retrieves inventory analysis data for all parts."}, "response": []}, {"name": "Get Part Inventory Analysis", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/inventory/analysis/1", "host": ["{{base_url}}"], "path": ["api", "inventory", "analysis", "1"]}, "description": "Retrieves detailed inventory analysis for a specific part."}, "response": []}, {"name": "Get Inventory Optimization Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/inventory/optimization-report", "host": ["{{base_url}}"], "path": ["api", "inventory", "optimization-report"]}, "description": "Retrieves a comprehensive inventory optimization report."}, "response": []}, {"name": "Run Inventory Optimization", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/inventory/run-optimization", "host": ["{{base_url}}"], "path": ["api", "inventory", "run-optimization"]}, "description": "Manually triggers the inventory optimization job."}, "response": []}], "description": "Endpoints related to inventory management and optimization."}, {"name": "KPIs", "item": [{"name": "Get Latest KPIs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/kpis/latest", "host": ["{{base_url}}"], "path": ["api", "kpis", "latest"]}, "description": "Retrieves the latest KPI values."}, "response": []}, {"name": "Get KPI History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/kpis/history/MTTR_Calculated?start_date=2023-01-01&end_date=2023-04-15&limit=100", "host": ["{{base_url}}"], "path": ["api", "kpis", "history", "MTTR_Calculated"], "query": [{"key": "start_date", "value": "2023-01-01"}, {"key": "end_date", "value": "2023-04-15"}, {"key": "limit", "value": "100"}]}, "description": "Retrieves historical values for a specific KPI."}, "response": []}, {"name": "Run KPI Calculations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/kpis/run-calculations", "host": ["{{base_url}}"], "path": ["api", "kpis", "run-calculations"]}, "description": "Manually triggers the KPI calculation job."}, "response": []}, {"name": "Save Custom KPI", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"kpi_name\": \"Custom_Efficiency\",\n    \"kpi_value\": 92.5,\n    \"kpi_unit\": \"percent\",\n    \"asset_id\": 1,\n    \"notes\": \"Manually calculated efficiency based on production data\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/kpis/custom", "host": ["{{base_url}}"], "path": ["api", "kpis", "custom"]}, "description": "Saves a custom KPI value."}, "response": []}], "description": "Endpoints related to KPI management and calculation."}, {"name": "Work Orders", "item": [{"name": "Get Work Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/workorders?status=OPEN&type=Corrective&limit=100&offset=0", "host": ["{{base_url}}"], "path": ["api", "workorders"], "query": [{"key": "status", "value": "OPEN"}, {"key": "type", "value": "Corrective"}, {"key": "limit", "value": "100"}, {"key": "offset", "value": "0"}]}, "description": "Retrieves a list of work orders."}, "response": []}, {"name": "Get Work Order Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/workorders/1", "host": ["{{base_url}}"], "path": ["api", "workorders", "1"]}, "description": "Retrieves details for a specific work order."}, "response": []}, {"name": "Create Work Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"assetId\": 5,\n    \"workOrderType\": \"Corrective\",\n    \"description\": \"Replace motor coupling\",\n    \"status\": \"OPEN\",\n    \"assignedTo\": \"<PERSON>\",\n    \"failureCode\": \"MECH-002\",\n    \"failureType\": \"Mechanical\",\n    \"downtimeMinutes\": 180,\n    \"repairTimeMinutes\": 120,\n    \"maintenanceCost\": 450.00,\n    \"startDate\": \"2023-04-16T09:00:00Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/ingest/workorder", "host": ["{{base_url}}"], "path": ["api", "ingest", "workorder"]}, "description": "Creates a new work order."}, "response": []}, {"name": "Update Work Order", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"CLOSED\",\n    \"endDate\": \"2023-04-16T14:30:00Z\",\n    \"repairTimeMinutes\": 150,\n    \"maintenanceCost\": 520.00\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/workorders/25", "host": ["{{base_url}}"], "path": ["api", "workorders", "25"]}, "description": "Updates an existing work order."}, "response": []}, {"name": "Add Parts to Work Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"parts\": [\n        {\n            \"partid\": 1,\n            \"quantityused\": 1\n        },\n        {\n            \"partid\": 15,\n            \"quantityused\": 2\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/workorders/1/parts", "host": ["{{base_url}}"], "path": ["api", "workorders", "1", "parts"]}, "description": "Adds parts to an existing work order."}, "response": []}, {"name": "Bulk Import Work Orders", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text", "disabled": true}, {"key": "X-API-Key", "value": "{{api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"workorders\": [\n        {\n            \"assetId\": 5,\n            \"workOrderType\": \"Corrective\",\n            \"description\": \"Replace motor coupling\",\n            \"status\": \"CLOSED\",\n            \"assignedTo\": \"<PERSON>\",\n            \"failureCode\": \"MECH-002\",\n            \"failureType\": \"Mechanical\",\n            \"downtimeMinutes\": 180,\n            \"repairTimeMinutes\": 120,\n            \"maintenanceCost\": 450.00,\n            \"startDate\": \"2023-04-16T09:00:00Z\",\n            \"endDate\": \"2023-04-16T14:30:00Z\"\n        },\n        {\n            \"assetId\": 8,\n            \"workOrderType\": \"Preventive\",\n            \"description\": \"Lubricate bearings\",\n            \"status\": \"CLOSED\",\n            \"assignedTo\": \"<PERSON>\",\n            \"failureCode\": null,\n            \"failureType\": null,\n            \"downtimeMinutes\": 60,\n            \"repairTimeMinutes\": 45,\n            \"maintenanceCost\": 150.00,\n            \"startDate\": \"2023-04-17T10:00:00Z\",\n            \"endDate\": \"2023-04-17T11:00:00Z\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/ingest/workorders/bulk", "host": ["{{base_url}}"], "path": ["api", "ingest", "workorders", "bulk"]}, "description": "Imports multiple work orders at once."}, "response": []}], "description": "Endpoints related to work order management."}, {"name": "Users", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}, "description": "Retrieves a list of all users."}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/users/firebase-test-manager-uid", "host": ["{{base_url}}"], "path": ["api", "users", "firebase-test-manager-uid"]}, "description": "Retrieves information about a specific user."}, "response": []}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"securePassword123\",\n    \"role\": \"ENGINEER\",\n    \"full_name\": \"New User\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}, "description": "Creates a new user in the system."}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"role\": \"MANAGER\",\n    \"full_name\": \"Updated User Name\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/users/firebase-test-manager-uid", "host": ["{{base_url}}"], "path": ["api", "users", "firebase-test-manager-uid"]}, "description": "Updates an existing user."}, "response": []}, {"name": "Get API Keys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/users/api-keys", "host": ["{{base_url}}"], "path": ["api", "users", "api-keys"]}, "description": "Retrieves a list of API keys for the current user."}, "response": []}, {"name": "Create API Key", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{firebase_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"New API Key\",\n    \"permissions\": [\"READ\", \"WRITE\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/users/api-keys", "host": ["{{base_url}}"], "path": ["api", "users", "api-keys"]}, "description": "Creates a new API key."}, "response": []}], "description": "Endpoints related to user management."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "api_key", "value": "c5e52be8-9b1c-4fcd-8457-741c91ef5c85", "type": "string"}]}