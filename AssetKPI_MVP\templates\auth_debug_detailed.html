<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Debug - AssetKPI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }
        .debug-output {
            background-color: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>AssetKPI
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/auth-tests">Back to Auth Tests</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-bug me-2"></i>Detailed Authentication Debug</h1>
                <p class="text-muted">Comprehensive debugging for Firebase authentication issues</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="debug-section">
                    <h5><i class="fas fa-info-circle me-2"></i>Authentication Status</h5>
                    <div class="mb-3">
                        <strong>Current User:</strong> <span id="currentUser">Checking...</span><br>
                        <strong>Authentication Mode:</strong> <span id="authMode">Checking...</span><br>
                        <strong>Token Available:</strong> <span id="tokenAvailable">Checking...</span><br>
                        <strong>Firebase State:</strong> <span id="firebaseState">Checking...</span>
                    </div>
                    <button class="btn btn-primary me-2" onclick="checkAuthStatus()">
                        <i class="fas fa-sync me-1"></i>Refresh Status
                    </button>
                    <button class="btn btn-secondary" onclick="clearDebugOutput()">
                        <i class="fas fa-trash me-1"></i>Clear Output
                    </button>
                </div>

                <div class="debug-section">
                    <h5><i class="fas fa-key me-2"></i>Token Testing</h5>
                    <div class="mb-3">
                        <button class="btn btn-info me-2" onclick="testCurrentToken()">
                            <i class="fas fa-vial me-1"></i>Test Current Token
                        </button>
                        <button class="btn btn-warning me-2" onclick="testApiUsersMe()">
                            <i class="fas fa-user me-1"></i>Test /api/users/me
                        </button>
                        <button class="btn btn-success" onclick="testDebugMeEndpoint()">
                            <i class="fas fa-search me-1"></i>Test Debug Endpoint
                        </button>
                    </div>
                </div>

                <div class="debug-section">
                    <h5><i class="fas fa-sign-in-alt me-2"></i>Firebase Authentication</h5>
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="email" class="form-control mb-2" id="debugEmail" placeholder="Email" value="<EMAIL>">
                            </div>
                            <div class="col-md-6">
                                <input type="password" class="form-control mb-2" id="debugPassword" placeholder="Password">
                            </div>
                        </div>
                        <button class="btn btn-primary me-2" onclick="testFirebaseSignIn()">
                            <i class="fas fa-sign-in-alt me-1"></i>Test Firebase Sign In
                        </button>
                        <button class="btn btn-danger" onclick="testFirebaseSignOut()">
                            <i class="fas fa-sign-out-alt me-1"></i>Sign Out
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="debug-section">
                    <h5><i class="fas fa-terminal me-2"></i>Debug Output</h5>
                    <div class="debug-output" id="debugOutput">
Waiting for debug commands...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    
    <!-- Firebase Configuration -->
    <script>
        const firebaseConfig = {
            apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
            authDomain: "ikios-59679.firebaseapp.com",
            projectId: "ikios-59679",
            storageBucket: "ikios-59679.appspot.com",
            messagingSenderId: "1045286122604",
            appId: "1:1045286122604:web:c9e9c9b9b9b9b9b9b9b9b9"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
    </script>

    <!-- Authentication Scripts -->
    <script src="{{ url_for('static', path='js/auth.js') }}"></script>
    <script src="{{ url_for('static', path='js/combined_auth.js') }}"></script>

    <script>
        let debugOutput = document.getElementById('debugOutput');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : type === 'warning' ? '[WARN]' : '[INFO]';
            const line = `${timestamp} ${prefix} ${message}\n`;
            debugOutput.textContent += line;
            debugOutput.scrollTop = debugOutput.scrollHeight;
            console.log(message);
        }

        function clearDebugOutput() {
            debugOutput.textContent = 'Debug output cleared...\n';
        }

        async function checkAuthStatus() {
            log('=== Checking Authentication Status ===');
            
            try {
                // Check Firebase auth state
                const firebaseUser = firebase.auth().currentUser;
                log(`Firebase currentUser: ${firebaseUser ? firebaseUser.email : 'null'}`);
                document.getElementById('firebaseState').textContent = firebaseUser ? firebaseUser.email : 'Not signed in';
                
                // Check AssetKPIAuth
                if (window.AssetKPIAuth) {
                    const isAuth = window.AssetKPIAuth.isAuthenticated();
                    const currentUser = window.AssetKPIAuth.getCurrentUser();
                    log(`AssetKPIAuth.isAuthenticated(): ${isAuth}`);
                    log(`AssetKPIAuth.getCurrentUser(): ${currentUser ? currentUser.email : 'null'}`);
                    document.getElementById('currentUser').textContent = currentUser ? currentUser.email : 'Not authenticated';
                } else {
                    log('AssetKPIAuth not available');
                    document.getElementById('currentUser').textContent = 'AssetKPIAuth not available';
                }
                
                // Check CombinedAuth
                if (window.CombinedAuth) {
                    const mode = window.CombinedAuth.mode;
                    const isAuth = window.CombinedAuth.isAuthenticated();
                    log(`CombinedAuth.mode: ${mode}`);
                    log(`CombinedAuth.isAuthenticated(): ${isAuth}`);
                    document.getElementById('authMode').textContent = mode || 'none';
                } else {
                    log('CombinedAuth not available');
                    document.getElementById('authMode').textContent = 'CombinedAuth not available';
                }
                
                // Check token availability
                try {
                    const token = await getToken();
                    log(`Token available: ${token ? 'Yes' : 'No'}`);
                    if (token) {
                        log(`Token preview: ${token.substring(0, 20)}...`);
                    }
                    document.getElementById('tokenAvailable').textContent = token ? 'Yes' : 'No';
                } catch (error) {
                    log(`Error getting token: ${error.message}`, 'error');
                    document.getElementById('tokenAvailable').textContent = 'Error';
                }
                
            } catch (error) {
                log(`Error checking auth status: ${error.message}`, 'error');
            }
        }

        async function getToken() {
            if (window.CombinedAuth) {
                return await window.CombinedAuth.getToken();
            } else if (window.AssetKPIAuth) {
                return await window.AssetKPIAuth.getIdToken();
            }
            return null;
        }

        async function testCurrentToken() {
            log('=== Testing Current Token ===');
            
            try {
                const token = await getToken();
                if (!token) {
                    log('No token available', 'error');
                    return;
                }
                
                log(`Testing token: ${token.substring(0, 20)}...`);
                
                const response = await fetch('/api/auth/debug', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                log(`Response status: ${response.status}`);
                log(`Response data: ${JSON.stringify(data, null, 2)}`);
                
            } catch (error) {
                log(`Error testing token: ${error.message}`, 'error');
            }
        }

        async function testApiUsersMe() {
            log('=== Testing /api/users/me ===');
            
            try {
                const token = await getToken();
                if (!token) {
                    log('No token available', 'error');
                    return;
                }
                
                log(`Making request to /api/users/me with token: ${token.substring(0, 20)}...`);
                
                const response = await fetch('/api/users/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                log(`Response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Success! User data: ${JSON.stringify(data, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    log(`Error response: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`Error testing /api/users/me: ${error.message}`, 'error');
            }
        }

        async function testDebugMeEndpoint() {
            log('=== Testing Debug Me Endpoint ===');
            
            try {
                const token = await getToken();
                if (!token) {
                    log('No token available', 'error');
                    return;
                }
                
                log(`Making request to /api/auth/debug-me with token: ${token.substring(0, 20)}...`);
                
                const response = await fetch('/api/auth/debug-me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                log(`Response status: ${response.status}`);
                log(`Debug response: ${JSON.stringify(data, null, 2)}`);
                
            } catch (error) {
                log(`Error testing debug endpoint: ${error.message}`, 'error');
            }
        }

        async function testFirebaseSignIn() {
            log('=== Testing Firebase Sign In ===');
            
            const email = document.getElementById('debugEmail').value;
            const password = document.getElementById('debugPassword').value;
            
            if (!email || !password) {
                log('Please enter email and password', 'error');
                return;
            }
            
            try {
                log(`Attempting to sign in with: ${email}`);
                
                const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
                log(`Firebase sign in successful: ${userCredential.user.email}`);
                
                const token = await userCredential.user.getIdToken();
                log(`Got Firebase token: ${token.substring(0, 20)}...`);
                
                // Store token
                localStorage.setItem('firebaseIdToken', token);
                document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;
                log('Token stored in localStorage and cookie');
                
                // Refresh status
                setTimeout(checkAuthStatus, 1000);
                
            } catch (error) {
                log(`Firebase sign in error: ${error.message}`, 'error');
            }
        }

        async function testFirebaseSignOut() {
            log('=== Testing Firebase Sign Out ===');
            
            try {
                await firebase.auth().signOut();
                log('Firebase sign out successful');
                
                // Clear tokens
                localStorage.removeItem('firebaseIdToken');
                document.cookie = 'firebaseIdToken=; path=/; max-age=0; SameSite=Strict';
                log('Tokens cleared');
                
                // Refresh status
                setTimeout(checkAuthStatus, 1000);
                
            } catch (error) {
                log(`Firebase sign out error: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Authentication debug page loaded');
            setTimeout(checkAuthStatus, 1000);
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
