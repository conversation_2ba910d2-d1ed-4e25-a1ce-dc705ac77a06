# JavaScript Client Examples

This document provides examples of how to use the AssetKPI API with JavaScript.

## Setup

These examples can be used in a browser environment or with Node.js.

### Browser Setup

```html
<script>
  // AssetKPI API client will be defined here
</script>
```

### Node.js Setup

```bash
npm install node-fetch
```

```javascript
const fetch = require('node-fetch');
```

## API Client

Here's a simple API client that can be used in both browser and Node.js environments:

```javascript
class AssetKPIClient {
  constructor(baseUrl, firebaseIdToken = null, apiKey = null) {
    this.baseUrl = baseUrl;
    this.firebaseIdToken = firebaseIdToken;
    this.apiKey = apiKey;
  }

  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.firebaseIdToken) {
      headers['Authorization'] = `Bearer ${this.firebaseIdToken}`;
    } else if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey;
    }

    return headers;
  }

  async get(endpoint, params = {}) {
    const url = new URL(`${this.baseUrl}${endpoint}`);
    
    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async post(endpoint, data) {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async put(endpoint, data) {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async delete(endpoint) {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }
}
```

## Authentication

### Using Firebase ID Token

```javascript
// Initialize client with Firebase ID token
const firebaseToken = 'your-firebase-id-token';
const client = new AssetKPIClient('http://localhost:8000/api', firebaseToken);

// Get current user
async function getCurrentUser() {
  try {
    const currentUser = await client.get('/users/me');
    console.log(`Logged in as: ${currentUser.email}`);
    return currentUser;
  } catch (error) {
    console.error('Error getting current user:', error);
    throw error;
  }
}
```

### Using API Key

```javascript
// Initialize client with API key
const apiKey = 'your-api-key';
const client = new AssetKPIClient('http://localhost:8000/api', null, apiKey);

// Get all spare parts
async function getAllSpareParts() {
  try {
    const spareParts = await client.get('/inventory/parts');
    console.log(`Found ${spareParts.length} spare parts`);
    return spareParts;
  } catch (error) {
    console.error('Error getting spare parts:', error);
    throw error;
  }
}
```

## Inventory Management Examples

### Get All Spare Parts

```javascript
async function getAllSpareParts(limit = 100, offset = 0, sort = 'partid', order = 'asc') {
  try {
    const params = { limit, offset, sort, order };
    const spareParts = await client.get('/inventory/parts', params);
    
    spareParts.forEach(part => {
      console.log(`Part ID: ${part.partid}, Name: ${part.partname}, Stock: ${part.stockquantity}`);
    });
    
    return spareParts;
  } catch (error) {
    console.error('Error getting spare parts:', error);
    throw error;
  }
}
```

### Get Specific Spare Part

```javascript
async function getSparePart(partId) {
  try {
    const part = await client.get(`/inventory/parts/${partId}`);
    
    console.log(`Part Details: ${part.partname} (${part.partnumber})`);
    console.log(`Stock: ${part.stockquantity}, Reorder Level: ${part.reorderlevel}`);
    console.log(`EOQ: ${part.eoq}, Safety Stock: ${part.calculated_safety_stock}`);
    
    return part;
  } catch (error) {
    console.error(`Error getting part ${partId}:`, error);
    throw error;
  }
}
```

### Update Spare Part

```javascript
async function updateSparePart(partId, updates) {
  try {
    const result = await client.put(`/inventory/parts/${partId}`, updates);
    console.log(`Update result: ${result.message}`);
    return result;
  } catch (error) {
    console.error(`Error updating part ${partId}:`, error);
    throw error;
  }
}

// Example usage
const updates = {
  stockquantity: 20,
  reorderlevel: 8
};
updateSparePart(1, updates);
```

### Get Inventory Analysis

```javascript
async function getInventoryAnalysis() {
  try {
    const analysis = await client.get('/inventory/analysis');
    
    analysis.forEach(item => {
      console.log(`Part: ${item.part_name}`);
      console.log(`  Current Stock: ${item.current_stock}, Optimal Stock: ${item.optimal_stock}`);
      console.log(`  Potential Savings: $${item.potential_savings.toFixed(2)}`);
      console.log(`  Stockout Risk: ${item.stockout_risk}%`);
    });
    
    return analysis;
  } catch (error) {
    console.error('Error getting inventory analysis:', error);
    throw error;
  }
}
```

### Run Inventory Optimization

```javascript
async function runInventoryOptimization() {
  try {
    const result = await client.get('/inventory/run-optimization');
    console.log(`Optimization result: ${result.message}`);
    return result;
  } catch (error) {
    console.error('Error running inventory optimization:', error);
    throw error;
  }
}
```

## KPI Management Examples

### Get Latest KPIs

```javascript
async function getLatestKPIs() {
  try {
    const kpis = await client.get('/kpis/latest');
    
    console.log(`MTTR: ${kpis.mttr} hours`);
    console.log(`MTBF: ${kpis.mtbf} hours`);
    console.log(`Failure Rate: ${kpis.failure_rate} failures/year`);
    console.log(`Data Quality Score: ${kpis.data_quality_score}%`);
    
    return kpis;
  } catch (error) {
    console.error('Error getting latest KPIs:', error);
    throw error;
  }
}
```

### Get KPI History

```javascript
async function getKPIHistory(kpiName, startDate = null, endDate = null, limit = 100) {
  try {
    const params = { limit };
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    
    const history = await client.get(`/kpis/history/${kpiName}`, params);
    
    console.log(`${kpiName} History (${history.length} records):`);
    history.forEach(record => {
      console.log(`  ${record.calculation_date}: ${record.kpi_value} ${record.kpi_unit}`);
    });
    
    return history;
  } catch (error) {
    console.error(`Error getting ${kpiName} history:`, error);
    throw error;
  }
}

// Example usage
getKPIHistory('MTTR_Calculated', '2023-01-01', '2023-04-15');
```

## Work Order Management Examples

### Get Work Orders

```javascript
async function getWorkOrders(status = null, type = null, assetId = null, limit = 100, offset = 0) {
  try {
    const params = { limit, offset };
    if (status) params.status = status;
    if (type) params.type = type;
    if (assetId) params.asset_id = assetId;
    
    const workOrders = await client.get('/workorders', params);
    
    console.log(`Work Orders (${workOrders.length}):`);
    workOrders.forEach(wo => {
      console.log(`  WO ID: ${wo.workorderid}, Asset: ${wo.assetid}`);
      console.log(`  Description: ${wo.description}`);
      console.log(`  Assigned To: ${wo.assignedto}`);
    });
    
    return workOrders;
  } catch (error) {
    console.error('Error getting work orders:', error);
    throw error;
  }
}

// Example usage
getWorkOrders('OPEN', 'Corrective');
```

### Create Work Order

```javascript
async function createWorkOrder(workOrderData) {
  try {
    const result = await client.post('/ingest/workorder', workOrderData);
    console.log(`Work Order created with ID: ${result.workorder_id}`);
    return result;
  } catch (error) {
    console.error('Error creating work order:', error);
    throw error;
  }
}

// Example usage
const newWorkOrder = {
  assetId: 5,
  workOrderType: 'Corrective',
  description: 'Replace motor coupling',
  status: 'OPEN',
  assignedTo: 'Jane Doe',
  failureCode: 'MECH-002',
  failureType: 'Mechanical',
  downtimeMinutes: 180,
  repairTimeMinutes: 120,
  maintenanceCost: 450.00,
  startDate: new Date().toISOString()
};
createWorkOrder(newWorkOrder);
```

### Update Work Order

```javascript
async function updateWorkOrder(workorderId, updates) {
  try {
    const result = await client.put(`/workorders/${workorderId}`, updates);
    console.log(`Update result: ${result.message}`);
    return result;
  } catch (error) {
    console.error(`Error updating work order ${workorderId}:`, error);
    throw error;
  }
}

// Example usage
const updates = {
  status: 'CLOSED',
  endDate: new Date().toISOString(),
  repairTimeMinutes: 150,
  maintenanceCost: 520.00
};
updateWorkOrder(25, updates);
```

## User Management Examples

### Get Current User

```javascript
async function getCurrentUser() {
  try {
    const user = await client.get('/users/me');
    console.log(`Current User: ${user.full_name} (${user.email})`);
    console.log(`Role: ${user.role}`);
    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    throw error;
  }
}
```

### Get All Users (Admin Only)

```javascript
async function getAllUsers() {
  try {
    const users = await client.get('/users');
    console.log(`Users (${users.length}):`);
    users.forEach(user => {
      console.log(`  ${user.full_name} (${user.email}) - ${user.role}`);
    });
    return users;
  } catch (error) {
    console.error('Error getting all users:', error);
    throw error;
  }
}
```

## Complete Example

Here's a complete example that demonstrates how to use the AssetKPIClient to perform various operations:

```javascript
// AssetKPI API Client
class AssetKPIClient {
  constructor(baseUrl, firebaseIdToken = null, apiKey = null) {
    this.baseUrl = baseUrl;
    this.firebaseIdToken = firebaseIdToken;
    this.apiKey = apiKey;
  }

  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.firebaseIdToken) {
      headers['Authorization'] = `Bearer ${this.firebaseIdToken}`;
    } else if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey;
    }

    return headers;
  }

  async get(endpoint, params = {}) {
    const url = new URL(`${this.baseUrl}${endpoint}`);
    
    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async post(endpoint, data) {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async put(endpoint, data) {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async delete(endpoint) {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }
}

// Main function to demonstrate API usage
async function main() {
  // Initialize client with API key
  const apiKey = 'c5e52be8-9b1c-4fcd-8457-741c91ef5c85';
  const client = new AssetKPIClient('http://localhost:8000/api', null, apiKey);
  
  // Get inventory summary
  try {
    console.log('Fetching inventory summary...');
    const inventorySummary = await client.get('/inventory/summary');
    console.log(`Total Parts: ${inventorySummary.total_parts}`);
    console.log(`Total Value: $${inventorySummary.total_value.toFixed(2)}`);
    console.log(`Parts Below Reorder Level: ${inventorySummary.below_reorder}`);
    console.log();
  } catch (error) {
    console.error('Error fetching inventory summary:', error);
  }
  
  // Get parts that need reordering
  try {
    console.log('Fetching parts that need reordering...');
    const parts = await client.get('/inventory/parts');
    const partsToReorder = parts.filter(p => p.stockquantity <= p.reorderlevel);
    
    console.log(`Parts to Reorder (${partsToReorder.length}):`);
    partsToReorder.forEach(part => {
      console.log(`  ${part.partname} (ID: ${part.partid})`);
      console.log(`    Current Stock: ${part.stockquantity}, Reorder Level: ${part.reorderlevel}`);
      console.log(`    EOQ: ${part.eoq}`);
    });
    console.log();
  } catch (error) {
    console.error('Error fetching parts:', error);
  }
  
  // Get recent work orders
  try {
    console.log('Fetching recent work orders...');
    const workOrders = await client.get('/workorders', { limit: 5, order: 'desc' });
    
    console.log(`Recent Work Orders (${workOrders.length}):`);
    workOrders.forEach(wo => {
      const status = wo.status;
      const startDate = wo.startdate || 'N/A';
      console.log(`  WO ${wo.workorderid}: ${wo.description} (${status})`);
      console.log(`    Asset: ${wo.assetid}, Started: ${startDate}`);
    });
    console.log();
  } catch (error) {
    console.error('Error fetching work orders:', error);
  }
  
  // Get latest KPIs
  try {
    console.log('Fetching latest KPIs...');
    const kpis = await client.get('/kpis/latest');
    
    console.log('Latest KPIs:');
    console.log(`  MTTR: ${kpis.mttr} hours`);
    console.log(`  MTBF: ${kpis.mtbf} hours`);
    console.log(`  Failure Rate: ${kpis.failure_rate} failures/year`);
    console.log();
  } catch (error) {
    console.error('Error fetching KPIs:', error);
  }
  
  // Create a new work order
  try {
    console.log('Creating a new work order...');
    const newWorkOrder = {
      assetId: 5,
      workOrderType: 'Preventive',
      description: 'Quarterly maintenance check',
      status: 'OPEN',
      assignedTo: 'John Smith',
      downtimeMinutes: 60,
      startDate: new Date().toISOString()
    };
    
    const result = await client.post('/ingest/workorder', newWorkOrder);
    console.log(`Work Order created with ID: ${result.workorder_id}`);
    console.log();
  } catch (error) {
    console.error('Error creating work order:', error);
  }
}

// Run the example
main().catch(error => {
  console.error('Error in main function:', error);
});
```
