# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Find the login_page function
login_page_pattern = '@app.get("/login", response_class=HTMLResponse, tags=["Authentication"])\nasync def login_page(request: Request):\n    """\n    Serves the login page for testing Firebase Authentication.\n    """\n    return templates.TemplateResponse("login.html", {"request": request})'

# Replace it with the updated function
updated_login_page = '''@app.get("/login", response_class=HTMLResponse, tags=["Authentication"])
async def login_page(request: Request):
    """
    Serves the login page for testing Firebase Authentication.
    """
    # Get Firebase Web API key from environment variable or use a default for development
    firebase_api_key = os.getenv("FIREBASE_WEB_API_KEY", "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4")
    firebase_project_id = os.getenv("FIREBASE_PROJECT_ID", "ikios-59679")
    
    # Create Firebase config to pass to the template
    firebase_config = {
        "apiKey": firebase_api_key,
        "authDomain": f"{firebase_project_id}.firebaseapp.com",
        "projectId": firebase_project_id,
        "storageBucket": f"{firebase_project_id}.appspot.com",
        "messagingSenderId": "1234567890",  # Replace with actual value if needed
        "appId": "1:1234567890:web:1234567890"  # Replace with actual value if needed
    }
    
    return templates.TemplateResponse("login.html", {
        "request": request,
        "firebase_config": firebase_config
    })'''

content = content.replace(login_page_pattern, updated_login_page)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed the login_page function in main.py")
