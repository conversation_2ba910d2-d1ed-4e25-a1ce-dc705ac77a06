"""
Database migration script for webhook tables.

This script creates the webhook_subscriptions and webhook_events tables in the database.
"""

import os
import sys
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, ForeignKey, JSON, Text, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)

# Create base class for declarative models
Base = declarative_base()

# We'll create the tables without the foreign key constraint for now

# Define models for migration
class WebhookSubscription(Base):
    """
    Model for webhook subscriptions.

    This model stores information about webhook subscriptions, including the URL,
    event types, and authentication details.
    """
    __tablename__ = "webhook_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    url = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)

    # Event types to subscribe to (stored as JSON array)
    event_types = Column(JSON, nullable=False)

    # Authentication
    auth_type = Column(String(50), nullable=True)  # "basic", "bearer", "api_key", etc.
    auth_credentials = Column(String(500), nullable=True)  # Encrypted credentials

    # Status
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # User who created the subscription
    created_by = Column(String(255))

    # Delivery settings
    retry_count = Column(Integer, default=3)
    retry_interval = Column(Integer, default=60)  # seconds
    timeout = Column(Integer, default=30)  # seconds

    # Delivery history
    delivery_success_count = Column(Integer, default=0)
    delivery_failure_count = Column(Integer, default=0)
    last_delivery_status = Column(String(50), nullable=True)
    last_delivery_time = Column(DateTime(timezone=True), nullable=True)


class WebhookEvent(Base):
    """
    Model for webhook events.

    This model stores information about webhook events, including the event type,
    payload, and delivery status.
    """
    __tablename__ = "webhook_events"

    id = Column(Integer, primary_key=True, index=True)
    subscription_id = Column(Integer, ForeignKey("webhook_subscriptions.id"))
    event_type = Column(String(50), nullable=False)
    payload = Column(JSON, nullable=False)

    # Delivery status
    status = Column(String(50), default="pending")  # pending, success, failure
    attempts = Column(Integer, default=0)
    next_attempt_time = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    delivered_at = Column(DateTime(timezone=True), nullable=True)

    # Response details
    response_status_code = Column(Integer, nullable=True)
    response_body = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)


def create_tables():
    """Create the webhook tables in the database."""
    try:
        # Create tables
        Base.metadata.create_all(engine)
        print("Webhook tables created successfully.")
    except Exception as e:
        print(f"Error creating webhook tables: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print(f"Creating webhook tables in database: {DATABASE_URL}")
    create_tables()
    print("Database migration completed successfully.")
