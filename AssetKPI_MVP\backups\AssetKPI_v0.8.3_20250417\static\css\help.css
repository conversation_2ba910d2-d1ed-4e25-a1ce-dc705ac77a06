/**
 * Help System Styles
 */

/* Help Container */
#help-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9998;
    pointer-events: none;
}

/* Help Icon */
.help-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-left: 4px;
    color: #6c757d;
    cursor: pointer;
    pointer-events: auto;
    transition: color 0.2s;
}

.help-icon:hover {
    color: #007bff;
}

.help-icon i {
    font-size: 14px;
}

/* Help Tooltip */
.help-tooltip {
    position: absolute;
    width: 300px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    pointer-events: auto;
    overflow: hidden;
}

.help-tooltip-content {
    padding: 12px;
    color: #212529;
    font-size: 14px;
    line-height: 1.5;
}

.help-tooltip-footer {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.help-more-link {
    font-size: 12px;
    color: #007bff;
    text-decoration: none;
}

.help-more-link:hover {
    text-decoration: underline;
}

/* Tooltip Positions */
.help-tooltip.tooltip-top::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    margin-left: -10px;
    border-width: 10px 10px 0;
    border-style: solid;
    border-color: #f8f9fa transparent transparent;
}

.help-tooltip.tooltip-bottom::after {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    margin-left: -10px;
    border-width: 0 10px 10px;
    border-style: solid;
    border-color: transparent transparent #fff;
}

/* Help Panel */
.help-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 350px;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.help-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.help-panel-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.help-panel-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.help-panel-close-btn:hover {
    color: #343a40;
}

.help-panel-search {
    display: flex;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
}

.help-search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
}

.help-search-btn {
    padding: 8px 12px;
    background-color: #007bff;
    color: #fff;
    border: 1px solid #007bff;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.help-search-btn:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.help-panel-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* Help Modal */
.help-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.help-modal-dialog {
    width: 600px;
    max-width: 90%;
    max-height: 90%;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
}

.help-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.help-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.help-modal-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.help-modal-close-btn:hover {
    color: #343a40;
}

.help-modal-body {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.help-modal-footer {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.help-modal-close-btn-bottom {
    padding: 6px 12px;
    background-color: #6c757d;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.help-modal-close-btn-bottom:hover {
    background-color: #5a6268;
}

/* Help Content Styles */
.help-topics-list {
    margin-bottom: 16px;
}

.help-topic-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.help-topic-item:last-child {
    border-bottom: none;
}

.help-topic-title {
    margin: 0 0 8px;
    font-size: 16px;
    font-weight: 600;
}

.help-topic-description {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.help-topic-link {
    color: #007bff;
    text-decoration: none;
}

.help-topic-link:hover {
    text-decoration: underline;
}

.help-topic-content {
    margin-bottom: 16px;
}

.help-elements-list {
    margin-bottom: 16px;
}

.help-element-item {
    margin-bottom: 16px;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.help-element-item.highlighted {
    border-color: #007bff;
    background-color: #f0f7ff;
}

.help-element-title {
    margin: 0 0 8px;
    font-size: 14px;
    font-weight: 600;
}

.help-element-content {
    font-size: 14px;
}

.help-topic-footer {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

.help-back-link {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
}

.help-back-link:hover {
    text-decoration: underline;
}

/* Search Results */
.help-search-title {
    margin: 0 0 16px;
    font-size: 16px;
    font-weight: 600;
}

.help-search-result-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.help-search-result-item:last-child {
    border-bottom: none;
}

.help-search-excerpt {
    margin: 8px 0;
    font-size: 14px;
    color: #6c757d;
}

.help-search-elements {
    margin: 8px 0;
    padding-left: 20px;
}

.help-search-elements li {
    margin-bottom: 4px;
}

.help-element-link {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
}

.help-element-link:hover {
    text-decoration: underline;
}

mark {
    background-color: #fff3cd;
    padding: 0 2px;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .help-panel {
        width: 100%;
    }
}
