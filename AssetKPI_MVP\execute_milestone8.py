import os
import psycopg2
from dotenv import load_dotenv
import random
from datetime import datetime, timedelta

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")

# Parse the DATABASE_URL
try:
    # Format: postgresql://username:password@host:port/dbname
    parts = DATABASE_URL.split('://', 1)[1].split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    db_params = {
        'dbname': host_port_db[1],
        'user': user_pass[0],
        'password': user_pass[1],
        'host': host_port[0],
        'port': host_port[1] if len(host_port) > 1 else '5432'
    }
    print(f"Using database connection parameters from DATABASE_URL")
except Exception as e:
    print(f"Error parsing DATABASE_URL: {e}")
    print(f"Using default database connection parameters")
    db_params = {
        'dbname': 'AssetKPI',
        'user': 'postgres',
        'password': 'Arcanum',
        'host': 'localhost',
        'port': '5432'
    }

def execute_migration():
    """Execute the SQL migration script for Milestone 8."""
    conn = None
    try:
        # Connect to the database
        print(f"Connecting to database {db_params['dbname']} on {db_params['host']}...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Read the SQL migration script
        with open('db_migration_milestone8.sql', 'r') as f:
            sql_script = f.read()
        
        # Split the script into individual statements
        statements = sql_script.split(';')
        
        # Execute each statement
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement + ';')
                    print(f"Executed: {statement[:50]}...")
                except Exception as e:
                    print(f"Error executing statement: {statement[:50]}...")
                    print(f"Error: {e}")
        
        # Commit the changes
        conn.commit()
        print("Milestone 8 migration completed successfully!")
        
        # Return the connection and cursor for data population
        return conn, cursor
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return None, None

def populate_sample_data(conn, cursor):
    """Populate the database with sample data for Milestone 8."""
    try:
        print("\nPopulating sample data for Milestone 8...")
        
        # Get assets
        cursor.execute("SELECT assetid, assetname, assettype, manufacturer, model FROM assets")
        assets = cursor.fetchall()
        
        if not assets:
            print("No assets found. Please add assets first.")
            return
        
        # Get work orders
        cursor.execute("""
            SELECT workorderid, workordertype, description, assetid
            FROM workorders
            WHERE status IN ('New', 'Assigned', 'In Progress')
        """)
        work_orders = cursor.fetchall()
        
        # Sample document creators
        document_creators = ['John Smith', 'Jane Doe', 'Bob Johnson', 'Alice Brown', 'System']
        
        # Sample document types
        document_types = [
            'Manual', 'Specification', 'Drawing', 'Procedure', 'Certificate', 
            'Report', 'Checklist', 'Warranty', 'Datasheet', 'Schematic'
        ]
        
        # Sample technical documents
        print("Creating technical documents...")
        
        # Generic documents (not asset-specific)
        generic_documents = [
            ('General Safety Guidelines', 'Procedure', 'Company-wide safety guidelines for all maintenance activities', 'docs/safety/general_safety.pdf', '1.2'),
            ('Lockout/Tagout Procedure', 'Procedure', 'Standard procedure for equipment lockout/tagout', 'docs/safety/loto_procedure.pdf', '2.1'),
            ('Electrical Safety Manual', 'Manual', 'Comprehensive guide to electrical safety', 'docs/safety/electrical_safety.pdf', '3.0'),
            ('Confined Space Entry Procedure', 'Procedure', 'Guidelines for safe confined space entry', 'docs/safety/confined_space.pdf', '1.5'),
            ('Hot Work Permit Process', 'Procedure', 'Process for obtaining and using hot work permits', 'docs/safety/hot_work.pdf', '2.2'),
            ('Chemical Handling Guidelines', 'Procedure', 'Safe handling procedures for industrial chemicals', 'docs/safety/chemical_handling.pdf', '1.3'),
            ('Emergency Response Plan', 'Procedure', 'Facility emergency response procedures', 'docs/safety/emergency_response.pdf', '2.0'),
            ('Preventive Maintenance Standards', 'Procedure', 'Company standards for preventive maintenance', 'docs/maintenance/pm_standards.pdf', '1.7'),
            ('Quality Control Procedures', 'Procedure', 'Standard procedures for quality control', 'docs/quality/qc_procedures.pdf', '2.3'),
            ('Equipment Inspection Checklist', 'Checklist', 'General equipment inspection checklist', 'docs/maintenance/inspection_checklist.pdf', '1.1')
        ]
        
        # Insert generic documents
        generic_document_ids = {}
        for doc in generic_documents:
            document_name, document_type, description, file_path, version = doc
            created_by = random.choice(document_creators)
            created_at = datetime.now() - timedelta(days=random.randint(30, 365))
            updated_at = created_at + timedelta(days=random.randint(0, 30))
            
            cursor.execute(
                """
                INSERT INTO technical_documents
                (document_name, document_type, description, file_path, version, created_by, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING document_id
                """,
                (document_name, document_type, description, file_path, version, created_by, created_at, updated_at)
            )
            
            document_id = cursor.fetchone()[0]
            generic_document_ids[document_name] = document_id
            
            print(f"  - Added generic document: {document_name}")
        
        # Create asset-specific documents
        asset_documents_created = 0
        
        for asset_id, asset_name, asset_type, manufacturer, model in assets:
            # Determine how many documents to create (1-3)
            num_docs = random.randint(1, 3)
            
            for i in range(num_docs):
                # Determine document type
                doc_type = random.choice(document_types)
                
                # Create document name and description based on type
                if doc_type == 'Manual':
                    doc_name = f"{asset_name} - Operation Manual"
                    description = f"Operation and maintenance manual for {asset_name}"
                    file_path = f"docs/manuals/{manufacturer}_{model}_manual.pdf"
                elif doc_type == 'Specification':
                    doc_name = f"{asset_name} - Technical Specifications"
                    description = f"Technical specifications for {asset_name}"
                    file_path = f"docs/specs/{manufacturer}_{model}_specs.pdf"
                elif doc_type == 'Drawing':
                    doc_name = f"{asset_name} - Engineering Drawing"
                    description = f"Engineering drawings for {asset_name}"
                    file_path = f"docs/drawings/{manufacturer}_{model}_drawing.pdf"
                elif doc_type == 'Certificate':
                    doc_name = f"{asset_name} - Certification"
                    description = f"Certification documents for {asset_name}"
                    file_path = f"docs/certificates/{manufacturer}_{model}_cert.pdf"
                elif doc_type == 'Datasheet':
                    doc_name = f"{asset_name} - Datasheet"
                    description = f"Technical datasheet for {asset_name}"
                    file_path = f"docs/datasheets/{manufacturer}_{model}_datasheet.pdf"
                elif doc_type == 'Schematic':
                    doc_name = f"{asset_name} - Schematic Diagram"
                    description = f"Schematic diagrams for {asset_name}"
                    file_path = f"docs/schematics/{manufacturer}_{model}_schematic.pdf"
                else:
                    doc_name = f"{asset_name} - {doc_type}"
                    description = f"{doc_type} for {asset_name}"
                    file_path = f"docs/other/{manufacturer}_{model}_{doc_type.lower()}.pdf"
                
                # Generate version
                version = f"{random.randint(1, 3)}.{random.randint(0, 9)}"
                
                # Select creator
                created_by = random.choice(document_creators)
                
                # Generate dates
                created_at = datetime.now() - timedelta(days=random.randint(30, 365))
                updated_at = created_at + timedelta(days=random.randint(0, 30))
                
                # Insert document
                cursor.execute(
                    """
                    INSERT INTO technical_documents
                    (document_name, document_type, description, file_path, version, created_by, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING document_id
                    """,
                    (doc_name, doc_type, description, file_path, version, created_by, created_at, updated_at)
                )
                
                document_id = cursor.fetchone()[0]
                
                # Link document to asset
                cursor.execute(
                    """
                    INSERT INTO asset_documents
                    (asset_id, document_id)
                    VALUES (%s, %s)
                    """,
                    (asset_id, document_id)
                )
                
                asset_documents_created += 1
        
        print(f"  - Added {asset_documents_created} asset-specific documents")
        
        # Sample safety procedures
        safety_procedures = [
            ('Electrical Safety Procedure', 'Procedures for working safely with electrical equipment',
             '1. Verify power is off using appropriate testing equipment\n2. Apply lockout/tagout devices\n3. Verify zero energy state\n4. Complete work\n5. Remove lockout/tagout devices\n6. Test equipment operation',
             'Safety glasses, insulated gloves, arc flash protection when required',
             'Electric shock, arc flash, burns'),
            
            ('Confined Space Entry Procedure', 'Procedures for safely entering confined spaces',
             '1. Obtain confined space entry permit\n2. Test atmosphere for oxygen, flammable gases, and toxic contaminants\n3. Establish ventilation if needed\n4. Assign entry attendant\n5. Establish rescue plan\n6. Enter space with proper equipment\n7. Monitor atmosphere continuously\n8. Exit and secure space when complete',
             'Hard hat, safety glasses, respiratory protection if required, fall protection if required',
             'Oxygen deficiency, toxic gases, engulfment, entrapment'),
            
            ('Hot Work Procedure', 'Procedures for safely performing hot work',
             '1. Obtain hot work permit\n2. Clear area of flammable materials\n3. Have fire extinguisher ready\n4. Assign fire watch\n5. Perform hot work\n6. Maintain fire watch for 30 minutes after completion\n7. Inspect area before leaving',
             'Welding helmet, welding gloves, flame-resistant clothing, safety glasses',
             'Burns, fire, eye damage from UV radiation, inhalation of fumes'),
            
            ('Hazardous Material Handling', 'Procedures for safely handling hazardous materials',
             '1. Review Safety Data Sheet (SDS)\n2. Ensure proper ventilation\n3. Use appropriate PPE\n4. Use proper handling techniques\n5. Store materials properly\n6. Know emergency procedures for spills\n7. Dispose of materials properly',
             'Chemical-resistant gloves, safety glasses, face shield if required, chemical-resistant clothing if required',
             'Chemical burns, inhalation of toxic vapors, skin absorption of chemicals'),
            
            ('Lockout/Tagout Procedure', 'Procedures for controlling hazardous energy',
             '1. Notify affected employees\n2. Shut down equipment\n3. Isolate energy sources\n4. Apply lockout/tagout devices\n5. Verify zero energy state\n6. Perform service or maintenance\n7. Remove lockout/tagout devices\n8. Notify employees of restart',
             'Safety glasses, insulated tools if required',
             'Unexpected startup, release of stored energy, electric shock'),
            
            ('Fall Protection Procedure', 'Procedures for working at heights',
             '1. Assess fall hazards\n2. Select appropriate fall protection system\n3. Inspect fall protection equipment\n4. Secure anchor points\n5. Don fall protection equipment properly\n6. Maintain 100% tie-off when required\n7. Inspect work area for additional hazards',
             'Full-body harness, lanyard, hard hat, safety glasses',
             'Falls from height, struck by falling objects'),
            
            ('Machine Guarding Procedure', 'Procedures for working with machinery',
             '1. Ensure all guards are in place\n2. Never bypass safety devices\n3. Use appropriate tools for adjustments\n4. Keep loose clothing and jewelry away from moving parts\n5. Follow lockout/tagout procedures for maintenance\n6. Report missing or damaged guards',
             'Safety glasses, hearing protection, gloves appropriate for the task',
             'Caught in moving parts, cuts, amputation, eye injuries from flying particles'),
            
            ('Respiratory Protection Procedure', 'Procedures for using respiratory protection',
             '1. Identify respiratory hazards\n2. Select appropriate respirator\n3. Ensure proper fit testing\n4. Inspect respirator before use\n5. Don respirator properly\n6. Perform user seal check\n7. Use in appropriate conditions\n8. Clean and store properly',
             'Appropriate respirator for the hazard, additional PPE as required',
             'Inhalation of toxic substances, oxygen deficiency'),
            
            ('Powered Industrial Truck Procedure', 'Procedures for operating forklifts and other powered industrial trucks',
             '1. Inspect vehicle before use\n2. Wear seatbelt\n3. Keep load stable and secure\n4. Maintain clear view of travel path\n5. Travel with load low\n6. Observe speed limits\n7. Yield to pedestrians\n8. Park with forks lowered and brake set',
             'Safety shoes, high-visibility vest',
             'Tip-over, collision, struck-by injuries, falls'),
            
            ('Ladder Safety Procedure', 'Procedures for safely using ladders',
             '1. Inspect ladder before use\n2. Use proper ladder for the job\n3. Set up on stable, level surface\n4. Maintain three points of contact\n5. Do not stand on top two rungs\n6. Do not overreach\n7. Secure tall ladders\n8. Store properly',
             'Safety shoes, fall protection if required',
             'Falls, electric shock from metal ladders near electrical sources')
        ]
        
        print("Creating safety procedures...")
        
        procedure_ids = {}
        for procedure in safety_procedures:
            procedure_name, description, procedure_steps, required_ppe, hazards = procedure
            
            cursor.execute(
                """
                INSERT INTO safety_procedures
                (procedure_name, description, procedure_steps, required_ppe, hazards, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING procedure_id
                """,
                (procedure_name, description, procedure_steps, required_ppe, hazards, 
                 datetime.now() - timedelta(days=random.randint(30, 180)),
                 datetime.now() - timedelta(days=random.randint(0, 30)))
            )
            
            procedure_id = cursor.fetchone()[0]
            procedure_ids[procedure_name] = procedure_id
            
            print(f"  - Added safety procedure: {procedure_name}")
        
        # Link procedures to work orders
        if work_orders:
            print("Linking safety procedures to work orders...")
            
            wo_procedures_added = 0
            
            for wo_id, wo_type, description, asset_id in work_orders:
                # Determine if this work order needs safety procedures (70% chance)
                if random.random() < 0.7:
                    # Determine how many procedures to link (1-3)
                    num_procedures = random.randint(1, 3)
                    
                    # Select random procedures
                    selected_procedures = random.sample(list(procedure_ids.items()), num_procedures)
                    
                    for procedure_name, procedure_id in selected_procedures:
                        cursor.execute(
                            """
                            INSERT INTO work_order_procedures
                            (workorder_id, procedure_id)
                            VALUES (%s, %s)
                            """,
                            (wo_id, procedure_id)
                        )
                        
                        wo_procedures_added += 1
            
            print(f"  - Linked {wo_procedures_added} safety procedures to work orders")
        else:
            print("  - No work orders found to link safety procedures")
        
        # Commit the changes
        conn.commit()
        print("Sample data population completed successfully!")
        
    except Exception as e:
        print(f"Error populating sample data: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()

def main():
    """Main function to execute migration and populate data."""
    conn, cursor = execute_migration()
    
    if conn and cursor:
        populate_sample_data(conn, cursor)
        cursor.close()
        conn.close()
    
    print("Milestone 8 completed!")

if __name__ == "__main__":
    main()
