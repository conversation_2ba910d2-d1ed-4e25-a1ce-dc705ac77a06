# Python Client Examples

This document provides examples of how to use the AssetKPI API with Python.

## Setup

First, install the required packages:

```bash
pip install requests
```

## Authentication

### Using Firebase ID Token

```python
import requests

class AssetKPIClient:
    def __init__(self, base_url, firebase_id_token=None, api_key=None):
        self.base_url = base_url
        self.firebase_id_token = firebase_id_token
        self.api_key = api_key
        
    def get_headers(self):
        headers = {'Content-Type': 'application/json'}
        if self.firebase_id_token:
            headers['Authorization'] = f'Bearer {self.firebase_id_token}'
        elif self.api_key:
            headers['X-API-Key'] = self.api_key
        return headers
    
    def get(self, endpoint, params=None):
        url = f"{self.base_url}{endpoint}"
        response = requests.get(url, headers=self.get_headers(), params=params)
        response.raise_for_status()
        return response.json()
    
    def post(self, endpoint, data):
        url = f"{self.base_url}{endpoint}"
        response = requests.post(url, headers=self.get_headers(), json=data)
        response.raise_for_status()
        return response.json()
    
    def put(self, endpoint, data):
        url = f"{self.base_url}{endpoint}"
        response = requests.put(url, headers=self.get_headers(), json=data)
        response.raise_for_status()
        return response.json()
    
    def delete(self, endpoint):
        url = f"{self.base_url}{endpoint}"
        response = requests.delete(url, headers=self.get_headers())
        response.raise_for_status()
        return response.json()

# Example usage with Firebase ID token
firebase_token = "your-firebase-id-token"
client = AssetKPIClient("http://localhost:8000/api", firebase_id_token=firebase_token)

# Get current user
current_user = client.get("/users/me")
print(f"Logged in as: {current_user['email']}")
```

### Using API Key

```python
import requests

# Example usage with API key
api_key = "your-api-key"
client = AssetKPIClient("http://localhost:8000/api", api_key=api_key)

# Get all spare parts
spare_parts = client.get("/inventory/parts")
print(f"Found {len(spare_parts)} spare parts")
```

## Inventory Management Examples

### Get All Spare Parts

```python
def get_all_spare_parts(client, limit=100, offset=0, sort="partid", order="asc"):
    params = {
        "limit": limit,
        "offset": offset,
        "sort": sort,
        "order": order
    }
    return client.get("/inventory/parts", params=params)

# Example usage
spare_parts = get_all_spare_parts(client)
for part in spare_parts:
    print(f"Part ID: {part['partid']}, Name: {part['partname']}, Stock: {part['stockquantity']}")
```

### Get Specific Spare Part

```python
def get_spare_part(client, part_id):
    return client.get(f"/inventory/parts/{part_id}")

# Example usage
part = get_spare_part(client, 1)
print(f"Part Details: {part['partname']} ({part['partnumber']})")
print(f"Stock: {part['stockquantity']}, Reorder Level: {part['reorderlevel']}")
print(f"EOQ: {part['eoq']}, Safety Stock: {part['calculated_safety_stock']}")
```

### Update Spare Part

```python
def update_spare_part(client, part_id, updates):
    return client.put(f"/inventory/parts/{part_id}", updates)

# Example usage
updates = {
    "stockquantity": 20,
    "reorderlevel": 8
}
result = update_spare_part(client, 1, updates)
print(f"Update result: {result['message']}")
```

### Get Inventory Analysis

```python
def get_inventory_analysis(client):
    return client.get("/inventory/analysis")

# Example usage
analysis = get_inventory_analysis(client)
for item in analysis:
    print(f"Part: {item['part_name']}")
    print(f"  Current Stock: {item['current_stock']}, Optimal Stock: {item['optimal_stock']}")
    print(f"  Potential Savings: ${item['potential_savings']:.2f}")
    print(f"  Stockout Risk: {item['stockout_risk']}%")
```

### Run Inventory Optimization

```python
def run_inventory_optimization(client):
    return client.get("/inventory/run-optimization")

# Example usage
result = run_inventory_optimization(client)
print(f"Optimization result: {result['message']}")
```

## KPI Management Examples

### Get Latest KPIs

```python
def get_latest_kpis(client):
    return client.get("/kpis/latest")

# Example usage
kpis = get_latest_kpis(client)
print(f"MTTR: {kpis['mttr']} hours")
print(f"MTBF: {kpis['mtbf']} hours")
print(f"Failure Rate: {kpis['failure_rate']} failures/year")
print(f"Data Quality Score: {kpis['data_quality_score']}%")
```

### Get KPI History

```python
def get_kpi_history(client, kpi_name, start_date=None, end_date=None, limit=100):
    params = {"limit": limit}
    if start_date:
        params["start_date"] = start_date
    if end_date:
        params["end_date"] = end_date
    
    return client.get(f"/kpis/history/{kpi_name}", params=params)

# Example usage
mttr_history = get_kpi_history(client, "MTTR_Calculated", "2023-01-01", "2023-04-15")
print(f"MTTR History ({len(mttr_history)} records):")
for record in mttr_history:
    print(f"  {record['calculation_date']}: {record['kpi_value']} {record['kpi_unit']}")
```

## Work Order Management Examples

### Get Work Orders

```python
def get_work_orders(client, status=None, type=None, asset_id=None, limit=100, offset=0):
    params = {
        "limit": limit,
        "offset": offset
    }
    if status:
        params["status"] = status
    if type:
        params["type"] = type
    if asset_id:
        params["asset_id"] = asset_id
    
    return client.get("/workorders", params=params)

# Example usage
open_corrective_work_orders = get_work_orders(client, status="OPEN", type="Corrective")
print(f"Open Corrective Work Orders ({len(open_corrective_work_orders)}):")
for wo in open_corrective_work_orders:
    print(f"  WO ID: {wo['workorderid']}, Asset: {wo['assetid']}")
    print(f"  Description: {wo['description']}")
    print(f"  Assigned To: {wo['assignedto']}")
```

### Create Work Order

```python
def create_work_order(client, work_order_data):
    return client.post("/ingest/workorder", work_order_data)

# Example usage
new_work_order = {
    "assetId": 5,
    "workOrderType": "Corrective",
    "description": "Replace motor coupling",
    "status": "OPEN",
    "assignedTo": "Jane Doe",
    "failureCode": "MECH-002",
    "failureType": "Mechanical",
    "downtimeMinutes": 180,
    "repairTimeMinutes": 120,
    "maintenanceCost": 450.00,
    "startDate": "2023-04-16T09:00:00Z"
}
result = create_work_order(client, new_work_order)
print(f"Work Order created with ID: {result['workorder_id']}")
```

### Update Work Order

```python
def update_work_order(client, workorder_id, updates):
    return client.put(f"/workorders/{workorder_id}", updates)

# Example usage
updates = {
    "status": "CLOSED",
    "endDate": "2023-04-16T14:30:00Z",
    "repairTimeMinutes": 150,
    "maintenanceCost": 520.00
}
result = update_work_order(client, 25, updates)
print(f"Update result: {result['message']}")
```

## User Management Examples

### Get Current User

```python
def get_current_user(client):
    return client.get("/users/me")

# Example usage
user = get_current_user(client)
print(f"Current User: {user['full_name']} ({user['email']})")
print(f"Role: {user['role']}")
```

### Get All Users (Admin Only)

```python
def get_all_users(client):
    return client.get("/users")

# Example usage
users = get_all_users(client)
print(f"Users ({len(users)}):")
for user in users:
    print(f"  {user['full_name']} ({user['email']}) - {user['role']}")
```

## Complete Example

Here's a complete example that demonstrates how to use the AssetKPIClient to perform various operations:

```python
import requests
from datetime import datetime, timedelta

class AssetKPIClient:
    def __init__(self, base_url, firebase_id_token=None, api_key=None):
        self.base_url = base_url
        self.firebase_id_token = firebase_id_token
        self.api_key = api_key
        
    def get_headers(self):
        headers = {'Content-Type': 'application/json'}
        if self.firebase_id_token:
            headers['Authorization'] = f'Bearer {self.firebase_id_token}'
        elif self.api_key:
            headers['X-API-Key'] = self.api_key
        return headers
    
    def get(self, endpoint, params=None):
        url = f"{self.base_url}{endpoint}"
        response = requests.get(url, headers=self.get_headers(), params=params)
        response.raise_for_status()
        return response.json()
    
    def post(self, endpoint, data):
        url = f"{self.base_url}{endpoint}"
        response = requests.post(url, headers=self.get_headers(), json=data)
        response.raise_for_status()
        return response.json()
    
    def put(self, endpoint, data):
        url = f"{self.base_url}{endpoint}"
        response = requests.put(url, headers=self.get_headers(), json=data)
        response.raise_for_status()
        return response.json()
    
    def delete(self, endpoint):
        url = f"{self.base_url}{endpoint}"
        response = requests.delete(url, headers=self.get_headers())
        response.raise_for_status()
        return response.json()

def main():
    # Initialize client with API key
    api_key = "c5e52be8-9b1c-4fcd-8457-741c91ef5c85"
    client = AssetKPIClient("http://localhost:8000/api", api_key=api_key)
    
    # Get inventory summary
    try:
        print("Fetching inventory summary...")
        inventory_summary = client.get("/inventory/summary")
        print(f"Total Parts: {inventory_summary['total_parts']}")
        print(f"Total Value: ${inventory_summary['total_value']:.2f}")
        print(f"Parts Below Reorder Level: {inventory_summary['below_reorder']}")
        print()
    except Exception as e:
        print(f"Error fetching inventory summary: {e}")
    
    # Get parts that need reordering
    try:
        print("Fetching parts that need reordering...")
        parts = client.get("/inventory/parts")
        parts_to_reorder = [p for p in parts if p['stockquantity'] <= p['reorderlevel']]
        
        print(f"Parts to Reorder ({len(parts_to_reorder)}):")
        for part in parts_to_reorder:
            print(f"  {part['partname']} (ID: {part['partid']})")
            print(f"    Current Stock: {part['stockquantity']}, Reorder Level: {part['reorderlevel']}")
            print(f"    EOQ: {part['eoq']}")
        print()
    except Exception as e:
        print(f"Error fetching parts: {e}")
    
    # Get recent work orders
    try:
        print("Fetching recent work orders...")
        work_orders = client.get("/workorders", {"limit": 5, "order": "desc"})
        
        print(f"Recent Work Orders ({len(work_orders)}):")
        for wo in work_orders:
            status = wo['status']
            start_date = wo.get('startdate', 'N/A')
            print(f"  WO {wo['workorderid']}: {wo['description']} ({status})")
            print(f"    Asset: {wo['assetid']}, Started: {start_date}")
        print()
    except Exception as e:
        print(f"Error fetching work orders: {e}")
    
    # Get latest KPIs
    try:
        print("Fetching latest KPIs...")
        kpis = client.get("/kpis/latest")
        
        print("Latest KPIs:")
        print(f"  MTTR: {kpis['mttr']} hours")
        print(f"  MTBF: {kpis['mtbf']} hours")
        print(f"  Failure Rate: {kpis['failure_rate']} failures/year")
        print()
    except Exception as e:
        print(f"Error fetching KPIs: {e}")
    
    # Create a new work order
    try:
        print("Creating a new work order...")
        new_work_order = {
            "assetId": 5,
            "workOrderType": "Preventive",
            "description": "Quarterly maintenance check",
            "status": "OPEN",
            "assignedTo": "John Smith",
            "downtimeMinutes": 60,
            "startDate": datetime.now().isoformat()
        }
        
        result = client.post("/ingest/workorder", new_work_order)
        print(f"Work Order created with ID: {result['workorder_id']}")
        print()
    except Exception as e:
        print(f"Error creating work order: {e}")

if __name__ == "__main__":
    main()
```
