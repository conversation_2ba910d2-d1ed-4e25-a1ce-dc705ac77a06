import os
import sys
import firebase_admin
from firebase_admin import credentials, auth
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if already initialized
        try:
            app = firebase_admin.get_app()
            print(f"Firebase Admin SDK already initialized with app: {app.name}")
            return True
        except ValueError:
            pass
        
        # Initialize with service account
        try:
            SERVICE_ACCOUNT_KEY_PATH = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
            cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)
            firebase_admin.initialize_app(cred)
            print("Firebase Admin SDK initialized successfully")
            return True
        except Exception as e:
            print(f"Error initializing Firebase Admin SDK: {e}")
            return False
    except Exception as e:
        print(f"Error in initialize_firebase: {e}")
        return False

def check_user_exists(email):
    """Check if a user with the given email exists in Firebase"""
    try:
        user = auth.get_user_by_email(email)
        print(f"User found in Firebase:")
        print(f"  UID: {user.uid}")
        print(f"  Email: {user.email}")
        print(f"  Email verified: {user.email_verified}")
        print(f"  Display name: {user.display_name}")
        print(f"  Disabled: {user.disabled}")
        return user
    except auth.UserNotFoundError:
        print(f"User with email {email} not found in Firebase")
        return None
    except Exception as e:
        print(f"Error checking user in Firebase: {e}")
        return None

def create_firebase_user(email, password, display_name=None):
    """Create a new user in Firebase"""
    try:
        user = auth.create_user(
            email=email,
            password=password,
            display_name=display_name,
            email_verified=False
        )
        print(f"Created user in Firebase:")
        print(f"  UID: {user.uid}")
        print(f"  Email: {user.email}")
        return user
    except Exception as e:
        print(f"Error creating user in Firebase: {e}")
        return None

def main():
    # Initialize Firebase Admin SDK
    if not initialize_firebase():
        print("Failed to initialize Firebase Admin SDK. Exiting.")
        sys.exit(1)
    
    # Check if user exists
    email = "<EMAIL>"
    user = check_user_exists(email)
    
    # If user doesn't exist, ask if we should create it
    if not user:
        print("\nUser not found in Firebase. Would you like to create it? (y/n)")
        choice = input().lower()
        if choice == 'y':
            password = "TestTest"  # Default password
            display_name = "Johan Borgulf"  # Default display name
            
            # Ask for password
            custom_password = input(f"Enter password (default: {password}): ")
            if custom_password:
                password = custom_password
            
            # Ask for display name
            custom_display_name = input(f"Enter display name (default: {display_name}): ")
            if custom_display_name:
                display_name = custom_display_name
            
            # Create user
            user = create_firebase_user(email, password, display_name)
            if user:
                print("User created successfully!")
            else:
                print("Failed to create user.")

if __name__ == "__main__":
    main()
