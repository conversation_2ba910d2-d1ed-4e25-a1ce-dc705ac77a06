-- AssetKPI Database Migration: Milestone 6 - Purchase Orders and Inventory Transactions

-- 6.1 Purchase Orders
CREATE TABLE IF NOT EXISTS purchase_orders (
    po_id SERIAL PRIMARY KEY,
    po_number VARCHAR(50) UNIQUE NOT NULL,
    vendor_id INTEGER REFERENCES vendors(vendor_id),
    order_date DATE,
    expected_delivery_date DATE,
    status VARCHAR(30),
    total_amount NUMERIC(15, 2),
    shipping_address TEXT,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS po_items (
    item_id SERIAL PRIMARY KEY,
    po_id INTEGER REFERENCES purchase_orders(po_id),
    part_id INTEGER REFERENCES spareparts(partid),
    quantity INTEGER,
    unit_price NUMERIC(10, 2),
    line_total NUMERIC(15, 2),
    received_quantity INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 6.2 Inventory Transactions
CREATE TABLE IF NOT EXISTS inventory_transactions (
    transaction_id SERIAL PRIMARY KEY,
    part_id INTEGER REFERENCES spareparts(partid),
    transaction_type VARCHAR(50), -- Issue, Receipt, Return, Adjustment, Transfer
    quantity INTEGER,
    transaction_date TIMESTAMP,
    reference_type VARCHAR(50), -- WorkOrder, PO, Adjustment
    reference_id INTEGER,
    location_from INTEGER REFERENCES storage_locations(location_id),
    location_to INTEGER REFERENCES storage_locations(location_id),
    unit_cost NUMERIC(10, 2),
    total_cost NUMERIC(15, 2),
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_purchase_orders_vendor_id ON purchase_orders(vendor_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX IF NOT EXISTS idx_po_items_po_id ON po_items(po_id);
CREATE INDEX IF NOT EXISTS idx_po_items_part_id ON po_items(part_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_part_id ON inventory_transactions(part_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_transaction_type ON inventory_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_reference_type ON inventory_transactions(reference_type);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_location_from ON inventory_transactions(location_from);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_location_to ON inventory_transactions(location_to);

-- End of Milestone 6 migration script
