"""
Users API client module.

This module provides methods for interacting with the user-related endpoints
of the AssetKPI API.
"""

from typing import Dict, List, Optional, Union, Any

from .client import AssetKPIClient


class UsersClient:
    """Client for user-related API endpoints."""
    
    def __init__(self, client: AssetKPIClient):
        """
        Initialize the users client.
        
        Args:
            client: The AssetKPI API client
        """
        self.client = client
    
    def get_current_user(self) -> Dict[str, Any]:
        """
        Get the current authenticated user.
        
        Returns:
            User information
        """
        return self.client.get("/users/me")
    
    def get_users(
        self,
        limit: int = 100,
        offset: int = 0,
        sort: str = "user_id",
        order: str = "asc",
        filters: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Get a list of users.
        
        Args:
            limit: Maximum number of items to return
            offset: Number of items to skip
            sort: Field to sort by
            order: Sort order (asc or desc)
            filters: List of filter conditions
            
        Returns:
            Paginated response with users
        """
        params = {
            "limit": limit,
            "offset": offset,
            "sort": sort,
            "order": order,
        }
        
        if filters:
            params["filters"] = filters
        
        return self.client.get("/users", params=params)
    
    def get_user(self, user_id: str) -> Dict[str, Any]:
        """
        Get a specific user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            User details
        """
        return self.client.get(f"/users/{user_id}")
    
    def get_user_permissions(self, user_id: str) -> Dict[str, Any]:
        """
        Get permissions for a specific user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            User permissions
        """
        return self.client.get(f"/users/{user_id}/permissions")
    
    def update_user_permissions(
        self, user_id: str, permissions_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update permissions for a specific user.
        
        Args:
            user_id: ID of the user
            permissions_data: Updated permissions data
            
        Returns:
            Updated permissions
        """
        return self.client.put(f"/users/{user_id}/permissions", data=permissions_data)
    
    def generate_api_key(self, user_id: str) -> Dict[str, Any]:
        """
        Generate a new API key for a specific user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Generated API key
        """
        return self.client.post(f"/users/{user_id}/api-key")
    
    def revoke_api_key(self, user_id: str) -> Dict[str, Any]:
        """
        Revoke the API key for a specific user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Revocation confirmation
        """
        return self.client.delete(f"/users/{user_id}/api-key")
