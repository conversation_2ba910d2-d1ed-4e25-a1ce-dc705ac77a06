import psycopg2

# Connect to the database
conn = psycopg2.connect("postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
cur = conn.cursor()

# Get the column names of the users table
cur.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' ORDER BY ordinal_position")
columns = cur.fetchall()
print("Columns in users table:")
for column in columns:
    print(column[0])

# Close the connection
cur.close()
conn.close()
