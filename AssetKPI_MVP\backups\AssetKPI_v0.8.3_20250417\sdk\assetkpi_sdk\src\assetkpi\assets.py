"""
Assets API client module.

This module provides methods for interacting with the asset-related endpoints
of the AssetKPI API.
"""

from typing import Dict, List, Optional, Union, Any

from .client import AssetKPIClient


class AssetsClient:
    """Client for asset-related API endpoints."""
    
    def __init__(self, client: AssetKPIClient):
        """
        Initialize the assets client.
        
        Args:
            client: The AssetKPI API client
        """
        self.client = client
    
    def get_assets(
        self,
        limit: int = 100,
        offset: int = 0,
        sort: str = "assetid",
        order: str = "asc",
        filters: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Get a list of assets.
        
        Args:
            limit: Maximum number of items to return
            offset: Number of items to skip
            sort: Field to sort by
            order: Sort order (asc or desc)
            filters: List of filter conditions
            
        Returns:
            Paginated response with assets
        """
        params = {
            "limit": limit,
            "offset": offset,
            "sort": sort,
            "order": order,
        }
        
        if filters:
            params["filters"] = filters
        
        return self.client.get("/assets", params=params)
    
    def get_asset(self, asset_id: int) -> Dict[str, Any]:
        """
        Get a specific asset.
        
        Args:
            asset_id: ID of the asset
            
        Returns:
            Asset details
        """
        return self.client.get(f"/assets/{asset_id}")
    
    def create_asset(self, asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new asset.
        
        Args:
            asset_data: Asset data
            
        Returns:
            Created asset
        """
        return self.client.post("/assets", data=asset_data)
    
    def update_asset(self, asset_id: int, asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an asset.
        
        Args:
            asset_id: ID of the asset
            asset_data: Updated asset data
            
        Returns:
            Updated asset
        """
        return self.client.put(f"/assets/{asset_id}", data=asset_data)
    
    def delete_asset(self, asset_id: int) -> Dict[str, Any]:
        """
        Delete an asset.
        
        Args:
            asset_id: ID of the asset
            
        Returns:
            Deletion confirmation
        """
        return self.client.delete(f"/assets/{asset_id}")
    
    def get_assets_count(self) -> Dict[str, Any]:
        """
        Get the total number of assets.
        
        Returns:
            Asset count
        """
        return self.client.get("/assets/count")
