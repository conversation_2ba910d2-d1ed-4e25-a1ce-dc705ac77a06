# Using the AssetKPI API Postman Collection

This guide explains how to use the provided Postman collection to interact with the AssetKPI API.

## Prerequisites

1. [<PERSON><PERSON>](https://www.postman.com/downloads/) installed on your computer
2. Access to the AssetKPI API (running locally or on a server)
3. Valid authentication credentials (Firebase account or API key)

## Setup

### Importing the Collection and Environment

1. Open Postman
2. Click on "Import" in the top left corner
3. Select the `AssetKPI_API.postman_collection.json` and `AssetKPI_API_Environment.postman_environment.json` files
4. Both the collection and environment should now be imported into Postman

### Configuring the Environment

1. Click on the "Environments" tab in the left sidebar
2. Select the "AssetKPI API Environment"
3. Update the following variables:
   - `base_url`: The base URL of your AssetKPI API (default: `http://localhost:8000`)
   - `api_key`: Your API key for authentication (default: `c5e52be8-9b1c-4fcd-8457-741c91ef5c85`)
   - `user_email`: Your Firebase user email (default: `johan<PERSON><PERSON>@gmail.com`)
   - `user_password`: Your Firebase user password (replace `your-password-here` with your actual password)
4. Click "Save"
5. Make sure to select the "AssetKPI API Environment" from the environment dropdown in the top right corner

## Authentication

The collection supports two authentication methods:

### Firebase Authentication

1. In the collection, navigate to "Authentication" > "Login"
2. Click "Send" to authenticate with Firebase using the credentials in your environment
3. The response will contain a token, which will be automatically saved to the `firebase_token` environment variable
4. All subsequent requests that use Firebase authentication will use this token

### API Key Authentication

1. Make sure your API key is set in the environment
2. Requests that use API key authentication will automatically include the API key in the `X-API-Key` header

## Using the Collection

The collection is organized into folders based on the API's functionality:

### Authentication

- **Login**: Authenticates with Firebase and saves the token
- **Get Current User**: Retrieves information about the currently authenticated user

### Inventory

- **Get All Spare Parts**: Retrieves a list of all spare parts
- **Get Specific Spare Part**: Retrieves details for a specific part
- **Update Spare Part**: Updates a part's details
- **Create Spare Part**: Creates a new part
- **Get Inventory Analysis**: Retrieves inventory analysis data
- **Get Part Inventory Analysis**: Retrieves detailed analysis for a specific part
- **Get Inventory Optimization Report**: Retrieves the optimization report
- **Run Inventory Optimization**: Triggers the optimization job

### KPIs

- **Get Latest KPIs**: Retrieves the latest KPI values
- **Get KPI History**: Retrieves historical values for a specific KPI
- **Run KPI Calculations**: Triggers the KPI calculation job
- **Save Custom KPI**: Saves a custom KPI value

### Work Orders

- **Get Work Orders**: Retrieves a list of work orders
- **Get Work Order Details**: Retrieves details for a specific work order
- **Create Work Order**: Creates a new work order
- **Update Work Order**: Updates an existing work order
- **Add Parts to Work Order**: Adds parts to a work order
- **Bulk Import Work Orders**: Imports multiple work orders at once

### Users

- **Get All Users**: Retrieves a list of all users
- **Get User by ID**: Retrieves details for a specific user
- **Create User**: Creates a new user
- **Update User**: Updates an existing user
- **Get API Keys**: Retrieves a list of API keys
- **Create API Key**: Creates a new API key

## Example Workflows

### Inventory Management Workflow

1. **Login** to get a Firebase token
2. **Get All Spare Parts** to see what's in inventory
3. **Get Specific Spare Part** to see details for a part of interest
4. **Update Spare Part** to adjust stock levels
5. **Get Inventory Analysis** to see the impact of your changes
6. **Run Inventory Optimization** to generate new recommendations

### Work Order Management Workflow

1. **Login** to get a Firebase token
2. **Create Work Order** to record a maintenance activity
3. **Add Parts to Work Order** to record parts used
4. **Update Work Order** to mark it as completed
5. **Run KPI Calculations** to update KPIs based on the new work order
6. **Get Latest KPIs** to see the updated values

## Troubleshooting

### Authentication Issues

- If you get a 401 Unauthorized error, your token may have expired. Run the "Login" request again to get a new token.
- If you get a 403 Forbidden error, you may not have the required permissions for the operation.

### Request Issues

- Check that you're using the correct HTTP method (GET, POST, PUT, DELETE)
- Verify that your request body is properly formatted JSON
- Make sure all required fields are included in your request

### Environment Issues

- Ensure the environment is selected in the top right corner
- Verify that all environment variables are correctly set
- Check that the `base_url` points to your AssetKPI instance

## Customizing Requests

Feel free to modify the requests to suit your needs:

- Change URL parameters to filter results
- Modify request bodies to include different data
- Add new requests for additional endpoints
- Create tests to validate responses

## Exporting Results

You can export the results of any request:

1. Send the request
2. In the response section, click the "Save" button
3. Choose a format (JSON, CSV, etc.)
4. Select a location to save the file

## Creating Collections for Specific Use Cases

You can create your own collections for specific use cases by:

1. Right-clicking on a request and selecting "Duplicate"
2. Modifying the duplicated request for your specific use case
3. Saving the request to a new collection

This allows you to create specialized collections for different aspects of your workflow.
