"""
Database migration script for creating the user_activity_logs table.
This script is part of Milestone 2: Usage Analytics (v0.6.0)
"""

import os
import sys
import json
from datetime import datetime
from dotenv import load_dotenv
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Index, text, inspect
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declarative_base

# Load environment variables
load_dotenv()

# Get database URL from environment variable
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    print("ERROR:    DATABASE_URL environment variable not set")
    sys.exit(1)

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)

# Create base class for declarative models
Base = declarative_base()

# Define UserActivityLog model for migration
class UserActivityLog(Base):
    """Model for tracking user activity and feature usage in the application."""
    __tablename__ = "user_activity_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    # Note: We're not using ForeignKey here to avoid issues if the users table doesn't exist
    user_id = Column(String(255), nullable=True,
                    comment="Firebase UID of the user, nullable for anonymous users")
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False,
                      comment="When the activity occurred")
    event_type = Column(String(50), nullable=False,
                       comment="Type of activity: PAGE_VIEW, FEATURE_USE, API_CALL, REPORT_GENERATED, etc.")
    details = Column(JSONB, nullable=True,
                    comment="JSON details about the activity (page path, feature name, parameters, etc.)")
    session_id = Column(String(255), nullable=True,
                       comment="Browser session ID to track user journey")

    # Create indexes for efficient querying
    __table_args__ = (
        Index('idx_user_activity_user_id', 'user_id'),
        Index('idx_user_activity_timestamp', 'timestamp'),
        Index('idx_user_activity_event_type', 'event_type'),
        Index('idx_user_activity_user_timestamp', 'user_id', 'timestamp'),
        {'comment': 'Logs of user activity for analytics purposes'}
    )

def create_tables():
    """Create the user_activity_logs table if it doesn't exist."""
    try:
        # Check if the table already exists
        inspector = inspect(engine)
        tables = inspector.get_table_names()

        if 'user_activity_logs' in tables:
            print(f"INFO:     user_activity_logs table already exists")
        else:
            # Create the table
            UserActivityLog.__table__.create(engine)
            print(f"INFO:     Created user_activity_logs table successfully")

        # Get a valid user_id from the users table if it exists
        valid_user_id = None
        if 'users' in tables:
            with engine.connect() as conn:
                try:
                    user_result = conn.execute(text("SELECT user_id FROM users LIMIT 1"))
                    user_row = user_result.fetchone()
                    if user_row:
                        valid_user_id = user_row[0]
                        print(f"INFO:     Found valid user_id: {valid_user_id}")
                except Exception as e:
                    print(f"WARNING:  Could not fetch user_id from users table: {e}")

        # Use the valid user_id or fall back to the test ID
        user_id_to_use = valid_user_id or "firebase-test-admin-uid"

        # Insert sample data for testing if the table is empty
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM user_activity_logs"))
            count = result.scalar()

            if count == 0:
                # Insert sample data
                sample_data = [
                    {
                        "user_id": user_id_to_use,
                        "event_type": "PAGE_VIEW",
                        "details": {"page": "/dashboard", "referrer": "/login"},
                        "session_id": "test-session-1"
                    },
                    {
                        "user_id": user_id_to_use,
                        "event_type": "FEATURE_USE",
                        "details": {"feature": "inventory_analysis", "action": "run_calculation"},
                        "session_id": "test-session-1"
                    },
                    {
                        "user_id": user_id_to_use,
                        "event_type": "REPORT_GENERATED",
                        "details": {"report_type": "inventory_optimization", "format": "pdf"},
                        "session_id": "test-session-1"
                    },
                    {
                        "user_id": None,  # Anonymous user
                        "event_type": "PAGE_VIEW",
                        "details": {"page": "/login", "referrer": "/"},
                        "session_id": "test-session-2"
                    }
                ]

                for data in sample_data:
                    # Convert Python dict to JSON string for PostgreSQL JSONB column
                    details_json = json.dumps(data["details"])

                    conn.execute(
                        text("""
                            INSERT INTO user_activity_logs
                            (user_id, event_type, details, session_id)
                            VALUES (:user_id, :event_type, :details, :session_id)
                        """),
                        {
                            "user_id": data["user_id"],
                            "event_type": data["event_type"],
                            "details": details_json,
                            "session_id": data["session_id"]
                        }
                    )

                print(f"INFO:     Inserted sample data into user_activity_logs table")
            else:
                print(f"INFO:     user_activity_logs table already contains data, skipping sample data insertion")

    except Exception as e:
        print(f"ERROR:    Failed to create user_activity_logs table: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    print(f"INFO:     Running user_activity_logs table migration at {datetime.now()}")
    create_tables()
    print(f"INFO:     Completed user_activity_logs table migration at {datetime.now()}")
