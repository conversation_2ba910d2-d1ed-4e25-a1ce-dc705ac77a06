#!/bin/bash

# Base URL
BASE_URL="http://localhost:8000"

# Read token from file
TOKEN=$(cat firebase_id_token.txt)

# Test 1: Public endpoint (should work without token)
echo "Test 1: Public endpoint (should work without token)"
curl -X GET "$BASE_URL/"

# Test 2: Protected endpoint without token (should fail with 401)
echo -e "\n\nTest 2: Protected endpoint without token (should fail with 401)"
curl -X GET "$BASE_URL/api/kpi/history/MTTR_Calculated"

# Test 3: Protected endpoint with valid token (should work)
echo -e "\n\nTest 3: Protected endpoint with valid token (should work)"
curl -X GET "$BASE_URL/api/kpi/history/MTTR_Calculated" -H "Authorization: Bearer $TOKEN"

# Test 4: Admin-only endpoint with valid token (should work if user is admin)
echo -e "\n\nTest 4: Admin-only endpoint with valid token (should work if user is admin)"
curl -X GET "$BASE_URL/api/users" -H "Authorization: Bearer $TOKEN"

# Test 5: Protected endpoint with invalid token (should fail with 401)
echo -e "\n\nTest 5: Protected endpoint with invalid token (should fail with 401)"
curl -X GET "$BASE_URL/api/kpi/history/MTTR_Calculated" -H "Authorization: Bearer invalid.token.here"
