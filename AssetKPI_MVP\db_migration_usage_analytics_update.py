"""
Database migration script to update the user_activity_logs table with additional columns.
This script adds the enhanced tracking fields that were missing from the initial migration.
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import create_engine, text, inspect

# Load environment variables
load_dotenv()

# Get database URL from environment variable
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    print("ERROR:    DATABASE_URL environment variable not set")
    sys.exit(1)

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)

def update_user_activity_logs_table():
    """Add missing columns to the user_activity_logs table."""
    try:
        # Check if the table exists
        inspector = inspect(engine)
        tables = inspector.get_table_names()

        if 'user_activity_logs' not in tables:
            print(f"ERROR:    user_activity_logs table does not exist")
            return

        # Get existing columns
        columns = [col['name'] for col in inspector.get_columns('user_activity_logs')]

        # Add missing columns
        with engine.connect() as conn:
            # Add duration_seconds column if it doesn't exist
            if 'duration_seconds' not in columns:
                conn.execute(text("""
                    ALTER TABLE user_activity_logs
                    ADD COLUMN duration_seconds FLOAT NULL;
                """))
                conn.execute(text("""
                    COMMENT ON COLUMN user_activity_logs.duration_seconds IS 'Duration of the activity in seconds (e.g., time spent on page)';
                """))
                print(f"INFO:     Added duration_seconds column to user_activity_logs table")

            # Add component_id column if it doesn't exist
            if 'component_id' not in columns:
                conn.execute(text("""
                    ALTER TABLE user_activity_logs
                    ADD COLUMN component_id VARCHAR(255) NULL;
                """))
                conn.execute(text("""
                    COMMENT ON COLUMN user_activity_logs.component_id IS 'ID of the UI component interacted with';
                """))
                print(f"INFO:     Added component_id column to user_activity_logs table")

            # Add previous_page column if it doesn't exist
            if 'previous_page' not in columns:
                conn.execute(text("""
                    ALTER TABLE user_activity_logs
                    ADD COLUMN previous_page VARCHAR(255) NULL;
                """))
                conn.execute(text("""
                    COMMENT ON COLUMN user_activity_logs.previous_page IS 'Previous page in the user journey';
                """))
                print(f"INFO:     Added previous_page column to user_activity_logs table")

            # Add browser_info column if it doesn't exist
            if 'browser_info' not in columns:
                conn.execute(text("""
                    ALTER TABLE user_activity_logs
                    ADD COLUMN browser_info JSONB NULL;
                """))
                conn.execute(text("""
                    COMMENT ON COLUMN user_activity_logs.browser_info IS 'Browser and device information';
                """))
                print(f"INFO:     Added browser_info column to user_activity_logs table")

            # Add interaction_depth column if it doesn't exist
            if 'interaction_depth' not in columns:
                conn.execute(text("""
                    ALTER TABLE user_activity_logs
                    ADD COLUMN interaction_depth INTEGER NULL;
                """))
                conn.execute(text("""
                    COMMENT ON COLUMN user_activity_logs.interaction_depth IS 'Depth of interaction (e.g., clicks, scrolls)';
                """))
                print(f"INFO:     Added interaction_depth column to user_activity_logs table")

            # Add conversion_event column if it doesn't exist
            if 'conversion_event' not in columns:
                conn.execute(text("""
                    ALTER TABLE user_activity_logs
                    ADD COLUMN conversion_event BOOLEAN DEFAULT FALSE;
                """))
                conn.execute(text("""
                    COMMENT ON COLUMN user_activity_logs.conversion_event IS 'Whether this activity represents a conversion event';
                """))
                print(f"INFO:     Added conversion_event column to user_activity_logs table")

            # Add user_role column if it doesn't exist
            if 'user_role' not in columns:
                conn.execute(text("""
                    ALTER TABLE user_activity_logs
                    ADD COLUMN user_role VARCHAR(50) NULL;
                """))
                conn.execute(text("""
                    COMMENT ON COLUMN user_activity_logs.user_role IS 'Role of the user at the time of the activity';
                """))
                print(f"INFO:     Added user_role column to user_activity_logs table")

            # Add indexes for new columns if they don't exist
            if 'component_id' in columns and 'idx_user_activity_component_id' not in [idx['name'] for idx in inspector.get_indexes('user_activity_logs')]:
                conn.execute(text("""
                    CREATE INDEX idx_user_activity_component_id ON user_activity_logs(component_id)
                """))
                print(f"INFO:     Added index idx_user_activity_component_id to user_activity_logs table")

            if 'conversion_event' in columns and 'idx_user_activity_conversion_event' not in [idx['name'] for idx in inspector.get_indexes('user_activity_logs')]:
                conn.execute(text("""
                    CREATE INDEX idx_user_activity_conversion_event ON user_activity_logs(conversion_event)
                """))
                print(f"INFO:     Added index idx_user_activity_conversion_event to user_activity_logs table")

            conn.commit()
            print(f"INFO:     Successfully updated user_activity_logs table")

    except Exception as e:
        print(f"ERROR:    Failed to update user_activity_logs table: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    print(f"INFO:     Running user_activity_logs table update migration")
    update_user_activity_logs_table()
    print(f"INFO:     Completed user_activity_logs table update migration")
