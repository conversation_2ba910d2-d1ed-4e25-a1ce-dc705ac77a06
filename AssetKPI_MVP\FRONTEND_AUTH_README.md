# Frontend Authentication for AssetKPI

This document describes the frontend authentication system implemented in AssetKPI.

## Overview

The frontend authentication system supports both Firebase authentication and test user authentication. It provides a unified interface for both authentication methods.

## Authentication Methods

### Firebase Authentication

Firebase authentication is the primary authentication method for AssetKPI. It provides secure authentication using Firebase's authentication services.

### Test User Authentication

Test user authentication is a simplified authentication method for development and testing purposes. It uses hardcoded test tokens to authenticate users.

## Implementation

The frontend authentication system consists of three JavaScript files:

- `static/js/auth_simple.js` - Handles Firebase authentication.
- `static/js/test_auth.js` - Handles test user authentication.
- `static/js/combined_auth.js` - Provides a unified interface for both Firebase and test user authentication.

### Combined Authentication

The `combined_auth.js` file provides a unified interface for both Firebase and test user authentication. It automatically detects which authentication method to use based on the token format.

```javascript
// Example usage of CombinedAuth
// Sign in with Firebase
CombinedAuth.signInWithEmailAndPassword('<EMAIL>', 'password');

// Sign in with test token
CombinedAuth.signInWithTestToken('test-admin-token');

// Get the current token
CombinedAuth.getToken();

// Check if authenticated
CombinedAuth.isAuthenticated();

// Sign out
CombinedAuth.signOut();
```

## Login Page

The login page has been updated to include a "Test Auth" tab where users can enter a test token. This allows users to switch between Firebase authentication and test user authentication.

### Using the Test Auth UI

1. Go to the login page at `/login`.
2. Click on the "Test Auth" tab.
3. Enter one of the test tokens (e.g., `test-admin-token`).
4. Click "Sign In with Test Token".

## Security Considerations

The test user authentication system is intended for development and testing only. It should not be used in production environments.

In a production environment, the test user authentication system can be disabled by:

1. Removing the `test_user_auth.py` file.
2. Removing the test authentication code from `main.py`.
3. Removing the test authentication UI from the login page.
