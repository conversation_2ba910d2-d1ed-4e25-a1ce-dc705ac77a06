# AssetKPI - Intelligent KPI & Inventory Optimization System

## Project Overview
AssetKPI (formerly IKIOS) is an Intelligent KPI & Inventory Optimization System designed to help maintenance and inventory managers track key performance indicators, optimize inventory levels, and make data-driven decisions. The system calculates maintenance KPIs like MTTR and MTBF, provides inventory optimization recommendations, and visualizes trends through interactive charts.

## Version History

### Past Versions
- **v0.1.0** - **v0.8.1**: See version history archive for details on previous versions.

### Current Version
- **v0.8.8**: Frontend Test User Authentication
  - Added frontend implementation for test user authentication
  - Created test_auth.js for handling test user authentication
  - Created combined_auth.js for unified authentication interface
  - Updated login page with Test Auth tab for test user login
  - Enhanced get_current_user function to handle test tokens
  - Added comprehensive test script for frontend authentication
  - Created detailed documentation for frontend authentication

- **v0.8.7**: Test User Authentication
  - Added test user authentication for development and testing
  - Created test user tokens for different roles (ADMIN, MANAGER, ENGINEER, VIEWER)
  - Implemented test user authentication endpoint at `/api/test-user-auth`
  - Added test user authentication documentation
  - Created test script for verifying test user authentication
  - Enhanced error handling for test user authentication
  - Added support for test user token validation

- **v0.8.6**: Firebase Authentication Enhancements
  - Added user registration endpoint for automatic user creation
  - Implemented token refresh mechanism to handle token expiration
  - Enhanced error messages for authentication failures
  - Added detailed user information in error responses
  - Updated last login timestamp tracking
  - Added comprehensive token handling in frontend
  - Improved security with SameSite cookie attributes
  - Added client-side token storage and refresh

- **v0.8.5**: Firebase Authentication Fixes
  - Fixed Firebase service account key path issues
  - Corrected user ID field handling in authentication middleware
  - Enhanced token verification with better error handling and logging
  - Fixed duplicate endpoint definitions in main.py
  - Updated test scripts to use correct user IDs
  - Added more comprehensive debugging for authentication flows
  - Fixed Firebase configuration in frontend code
  - Added detailed logging throughout the authentication process

- **v0.8.4**: Firebase Authentication Integration
  - Implemented Firebase Authentication for secure user management
  - Added Firebase Admin SDK integration for token verification
  - Created authentication middleware for protecting API endpoints
  - Added user role-based access control (RBAC) with ADMIN, MANAGER, ENGINEER, and VIEWER roles
  - Implemented debug endpoints for testing authentication
  - Added test page for Firebase Authentication testing
  - Created utility scripts for managing Firebase users in the database
  - Enhanced error handling for authentication failures with detailed logging

- **v0.8.3**: Authentication, Error Handling & Functionality Fixes
  - Fixed logout functionality with proper server-side session cleanup
  - Fixed EOQ configuration page to handle empty data gracefully
  - Enhanced authentication token handling to prevent 401 Unauthorized errors
  - Added automatic token refresh mechanism for improved session management
  - Enhanced error handling for authentication failures with user-friendly messages
  - Fixed dashboard template rendering with proper error handling for missing data
  - Added comprehensive unit tests for authentication and dashboard functions
  - Improved user experience with better error messages and loading indicators

### Testing
The application now includes comprehensive unit tests for critical components:

- **Authentication Tests**: Tests for token handling, authentication state management, and error recovery
  - Access at: `/auth-tests`
  - Tests authentication functions in `auth.js`
  - Verifies token refresh, error handling, and authentication state management

- **Dashboard Tests**: Tests for dashboard data loading and visualization
  - Access at: `/dashboard-tests`
  - Tests chart rendering, data processing, and error handling
  - Verifies proper handling of authentication errors and empty data sets

To run the tests:
1. Start the application
2. Navigate to `/auth-tests` or `/dashboard-tests` in your browser
3. Click the "Run Tests" button to execute the tests
4. View the test results and detailed logs on the page

### Authentication

The project uses both Firebase Authentication for secure user management and a test user authentication system for development and testing.

- **Firebase Authentication**: Provides secure user management with role-based access control and token handling.
- **Test User Authentication**: A simplified method for development and testing using predefined tokens. **Not for production use.**

For detailed information on the authentication system, including implementation details, available endpoints, testing tools, and security considerations, please refer to the [Authentication Documentation](docs/authentication.md).

### Bug Fixes
- Fixed missing `except` clause in `main.py` that was causing server errors
- Fixed dashboard template rendering error with KPI objects
- Resolved 401 Unauthorized errors with improved token handling
- Fixed logout functionality that was previously not working
- Fixed EOQ configuration page to handle empty data gracefully
- Enhanced error handling throughout the application
- Fixed duplicate endpoint definitions in main.py

## Roadmap to v1.0.0

### Milestone 1: Core KPI Calculation ✅
- Implement MTTR (Mean Time To Repair) calculation
- Implement MTBF (Mean Time Between Failures) calculation
- Create basic dashboard for KPI visualization

### Milestone 2: Usage Analytics ✅
- Implement user activity tracking
- Create analytics dashboard for system usage
- Add reporting functionality for usage patterns

### Milestone 3: Inventory Optimization ✅
- Implement EOQ (Economic Order Quantity) calculation
- Add safety stock calculation with variability factors
- Create inventory optimization dashboard

### Milestone 4: Enhanced Visualization ✅
- Add interactive charts for KPI trends
- Implement comparative analysis views
- Create customizable dashboards

### Milestone 5: API Ingestion Capabilities ✅
- Create API endpoints for data ingestion
- Implement data validation and processing
- Add authentication and rate limiting

### Milestone 6: Optimization Rules ✅
- Implement rule-based optimization engine
- Create rule management interface
- Add rule execution history and impact analysis

### Milestone 7: Configuration Management ✅
- Add system configuration interface
- Implement user preference management
- Create backup and restore functionality

### Milestone 8: Cloud Deployment Preparation ✅
- Optimize for cloud deployment
- Implement containerization
- Add scaling capabilities

### Milestone 9: Final Testing & Documentation
- Comprehensive system testing
- Complete user documentation
- Create deployment guides
