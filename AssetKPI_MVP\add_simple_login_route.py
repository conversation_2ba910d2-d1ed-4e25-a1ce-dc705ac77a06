# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Find a good place to add the route (after the login_page function)
login_page_end = '''    return templates.TemplateResponse("login.html", {
        "request": request,
        "firebase_config": firebase_config
    })'''

simple_login_route = '''    return templates.TemplateResponse("login.html", {
        "request": request,
        "firebase_config": firebase_config
    })

@app.get("/simple-login", response_class=HTMLResponse, tags=["Authentication"])
async def simple_login_page(request: Request):
    """
    Serves a simple login page for testing Firebase Authentication.
    """
    return templates.TemplateResponse("simple_login.html", {"request": request})

@app.post("/api/verify-token", tags=["Authentication"])
async def verify_token(request: Request):
    """
    Verifies a Firebase ID token and returns user information.
    """
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )
        
        token = auth_header.split(' ')[1]
        
        # Verify the token with Firebase Admin SDK
        decoded_token = auth.verify_id_token(token)
        uid = decoded_token['uid']
        
        # Get user information from the database
        db = SessionLocal()
        try:
            # Query the users table for the user with this UID
            result = db.execute(text("SELECT * FROM users WHERE uid = :uid"), {"uid": uid}).fetchone()
            
            if result:
                # Return user information
                return {
                    "uid": result[0],
                    "email": result[1],
                    "role": result[2],
                    "full_name": result[3]
                }
            else:
                return JSONResponse(
                    status_code=404,
                    content={"error": f"User with UID {uid} not found in database"}
                )
        finally:
            db.close()
    except Exception as e:
        print(f"Error verifying token: {e}")
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )'''

content = content.replace(login_page_end, simple_login_route)

# Add the JSONResponse import if it's not already there
if 'from fastapi.responses import JSONResponse' not in content:
    import_line = 'from fastapi.responses import HTMLResponse'
    new_import_line = 'from fastapi.responses import HTMLResponse, JSONResponse'
    content = content.replace(import_line, new_import_line)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Added simple login route to main.py")
