{% extends "layout.html" %}

{% block title %}AssetKPI - Dashboard{% endblock %}

{% block styles %}
<style>
    .kpi-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .kpi-card:hover {
        transform: translateY(-5px);
    }
    .kpi-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .kpi-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .recommendations-card {
        max-height: 400px;
        overflow-y: auto;
    }
    .recommendation-item {
        border-left: 4px solid #0d6efd;
        padding-left: 10px;
        margin-bottom: 10px;
    }
    .recommendation-item.priority-1 {
        border-left-color: #dc3545;
    }
    .recommendation-item.priority-2 {
        border-left-color: #fd7e14;
    }
    .recommendation-item.priority-3 {
        border-left-color: #0d6efd;
    }
    .auth-required-content {
        display: none;
    }
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
        margin: 20px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>Dashboard</h1>
        <p class="text-muted">Welcome to AssetKPI - Your Intelligent KPI & Inventory Optimization System</p>
    </div>
</div>

<!-- Public Content -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Welcome to AssetKPI</h5>
                <p class="card-text">
                    AssetKPI is an intelligent system for monitoring and optimizing your maintenance KPIs and inventory management.
                    Sign in to access your personalized dashboard and analytics.
                </p>
                <div class="auth-not-required">
                    <a href="/login" class="btn btn-primary">Sign In</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Authenticated Content -->
<div class="auth-required-content" data-role="VIEWER,ENGINEER,MANAGER,ADMIN">
    <!-- KPI Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card kpi-card bg-light">
                <div class="card-body text-center">
                    <div class="kpi-value">{{ latest_stored.MTTR_Calculated.kpi_value|default('--', true) if latest_stored.MTTR_Calculated is defined else '--' }}</div>
                    <div class="kpi-label">MTTR (hours)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card kpi-card bg-light">
                <div class="card-body text-center">
                    <div class="kpi-value">{{ latest_stored.MTBF_Calculated.kpi_value|default('--', true) if latest_stored.MTBF_Calculated is defined else '--' }}</div>
                    <div class="kpi-label">MTBF (hours)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card kpi-card bg-light">
                <div class="card-body text-center">
                    <div class="kpi-value">{{ latest_stored.FailureRate_Calculated.kpi_value|default('--', true) if latest_stored.FailureRate_Calculated is defined else '--' }}</div>
                    <div class="kpi-label">Failure Rate (per year)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card kpi-card bg-light">
                <div class="card-body text-center">
                    <div class="kpi-value">{{ kpis_report.oee|default('--', true) }}%</div>
                    <div class="kpi-label">OEE</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>KPI Trends</h5>
                    <div class="d-flex mt-2">
                        <div class="input-group me-2" style="max-width: 200px;">
                            <span class="input-group-text">From</span>
                            <input type="date" id="startDateInput" class="form-control">
                        </div>
                        <div class="input-group me-2" style="max-width: 200px;">
                            <span class="input-group-text">To</span>
                            <input type="date" id="endDateInput" class="form-control">
                        </div>
                        <button id="filterButton" class="btn btn-primary">Apply Filter</button>
                    </div>
                    <div id="chartErrorMsg" class="text-danger mt-2"></div>
                </div>
                <div class="card-body">
                    <h6>MTTR Trend (Calculated)</h6>
                    <div class="chart-container">
                        <canvas id="mttrTrendChart"></canvas>
                        <p class="no-data-msg text-center" style="display: none;">No historical MTTR data available for selected period.</p>
                    </div>

                    <h6 class="mt-4">MTBF Trend (Calculated)</h6>
                    <div class="chart-container">
                        <canvas id="mtbfTrendChart"></canvas>
                        <p class="no-data-msg text-center" style="display: none;">No historical MTBF data available for selected period.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recommendations and Inventory -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card recommendations-card">
                <div class="card-header">
                    <h5>Active Recommendations</h5>
                </div>
                <div class="card-body">
                    {% if recommendations %}
                        {% for rec, part_id, part_name, part_number in recommendations %}
                            <div class="recommendation-item priority-{{ rec.priority }}" id="rec-row-{{ rec.id }}">
                                <h6>{{ rec.recommendation_type }} - {{ part_name }}</h6>
                                <p class="mb-1">{{ rec.reason }}</p>
                                <small class="text-muted">Part #: {{ part_number }} | Priority: {{ rec.priority }}</small>
                                <div class="mt-2 auth-required-content" data-role="MANAGER,ADMIN">
                                    <button class="btn btn-sm btn-outline-success action-button" data-rec-id="{{ rec.id }}" data-action="ACKNOWLEDGED">Acknowledge</button>
                                    <button class="btn btn-sm btn-outline-danger action-button" data-rec-id="{{ rec.id }}" data-action="DISMISSED">Dismiss</button>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No active recommendations at this time.</p>
                    {% endif %}
                    <div id="rec-action-message" class="mt-2 text-success"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Inventory Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <h3>{{ spare_parts|selectattr('abc_classification', 'equalto', 'A')|list|length }}</h3>
                            <p>Class A Items</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <h3>{{ spare_parts|selectattr('abc_classification', 'equalto', 'B')|list|length }}</h3>
                            <p>Class B Items</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <h3>{{ spare_parts|selectattr('abc_classification', 'equalto', 'C')|list|length }}</h3>
                            <p>Class C Items</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>Stock Status</h6>
                        <div class="progress">
                            {% set below_reorder = 0 %}
                            {% for part in spare_parts %}
                                {% if part.stockquantity < part.reorderlevel %}
                                    {% set below_reorder = below_reorder + 1 %}
                                {% endif %}
                            {% endfor %}
                            {% set total_parts = spare_parts|length %}
                            {% set percent_below = (below_reorder / total_parts * 100) if total_parts > 0 else 0 %}

                            <div class="progress-bar bg-danger" role="progressbar" style="width: {{ percent_below }}%"
                                aria-valuenow="{{ percent_below }}" aria-valuemin="0" aria-valuemax="100">
                                {{ below_reorder }} items below reorder level
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Quality Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Work Order Data Quality Issues</h5>
                </div>
                <div class="card-body">
                    {% if workorder_quality and workorder_quality.total_wos > 0 %}
                        <p>Based on {{ workorder_quality.total_wos }} total work orders analyzed:</p>
                        <ul>
                            {% set dq_mrt = latest_stored.DQ_MissingRepairTime if latest_stored is mapping and latest_stored.DQ_MissingRepairTime is defined else None %}
                            <li>Missing/Invalid Repair Time: {{ workorder_quality.get('missing_repairtime', 0) }}
                                ({{ "%.1f"|format(workorder_quality.get('missing_repairtime', 0) * 100 / workorder_quality.total_wos if workorder_quality.total_wos else 0) }}%)
                                <small>(Affects MTTR) {% if dq_mrt %} - Last Recorded: {{ "%.0f"|format(dq_mrt.kpi_value) }} on {{ dq_mrt.calculation_timestamp.strftime('%Y-%m-%d %H:%M') }}{% endif %}</small>
                            </li>
                            <li>Missing Start Date: {{ workorder_quality.get('missing_startdate', 0) }}
                                ({{ "%.1f"|format(workorder_quality.get('missing_startdate', 0) * 100 / workorder_quality.total_wos if workorder_quality.total_wos else 0) }}%)
                                <small>(Affects MTBF/FR)</small>
                            </li>
                            <li>Missing End Date: {{ workorder_quality.get('missing_enddate', 0) }}
                                ({{ "%.1f"|format(workorder_quality.get('missing_enddate', 0) * 100 / workorder_quality.total_wos if workorder_quality.total_wos else 0) }}%)
                                <small>(Affects MTBF/FR)</small>
                            </li>
                            {% set corrective_count = workorder_quality.get('corrective_wos', 0) %}
                            {% if corrective_count > 0 %}
                                {% set invalid_intervals = workorder_quality.get('invalid_corrective_intervals', 0) %}
                                {% set dq_inv = latest_stored.DQ_InvalidIntervals if latest_stored is mapping and latest_stored.DQ_InvalidIntervals is defined else None %}
                                <li>Invalid Corrective Intervals (Start <= Previous End): {{ invalid_intervals }}
                                    (out of {{ corrective_count }} WOs analyzed - {{ "%.1f"|format(invalid_intervals * 100 / corrective_count if corrective_count else 0) }}%)
                                    <small>(Affects MTBF/FR) {% if dq_inv %} - Last Recorded: {{ "%.0f"|format(dq_inv.kpi_value) }} on {{ dq_inv.calculation_timestamp.strftime('%Y-%m-%d %H:%M') }}{% endif %}</small>
                                </li>
                            {% else %} <li>No corrective work orders found to analyze intervals.</li> {% endif %}
                        </ul>
                    {% elif workorder_quality %} <p>No work orders found to analyze.</p>
                    {% else %} <p>Could not calculate data quality metrics.</p> {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Upload form section -->
    <div class="row mb-4 auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Upload Work Orders CSV</h5>
                </div>
                <div class="card-body">
                    <p>Columns: assetid, workordertype, description, status, assignedto, failurecode, failuretype, downtimeminutes, repairtimeminutes, maintenancecost, startdate, enddate.</p>
                    <form action="/upload/workorders" method="post" enctype="multipart/form-data">
                        <div class="input-group">
                            <input type="file" class="form-control" name="file" accept=".csv" required>
                            <button class="btn btn-primary" type="submit">Upload File</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>

<script>
    // Chart rendering scripts
    const chartInstances = {}; // Store chart instances to destroy them before re-rendering

    // Generic function to fetch data and render/update a KPI chart
    async function renderKpiChart(kpiName, canvasId, yAxisLabel) {
        const chartElement = document.getElementById(canvasId);
        const chartContainer = chartElement?.parentElement;
        const noDataElement = chartContainer?.querySelector('.no-data-msg');
        const errorMsgElement = document.getElementById('chartErrorMsg');

        if (!chartElement || !chartContainer || !noDataElement || !errorMsgElement) {
            console.error(`Chart rendering setup failed for canvas ID: ${canvasId}`);
            return;
        }

        // Clear previous errors/no-data messages for this specific chart
        errorMsgElement.textContent = '';
        noDataElement.style.display = 'none'; // Hide no-data message initially

        const ctx = chartElement.getContext('2d');

        // Destroy previous chart instance for this canvas if it exists
        if (chartInstances[canvasId]) {
            chartInstances[canvasId].destroy();
        }

        try {
            // Check if user is authenticated
            if (!AssetKPIAuth.isAuthenticated()) {
                console.error('User is not authenticated');
                errorMsgElement.textContent = 'Authentication required. Please log in to view charts.';
                return;
            }

            // Get date values
            const startDate = document.getElementById('startDateInput').value;
            const endDate = document.getElementById('endDateInput').value;

            // Construct API URL with query parameters if dates are provided
            let apiUrl = `/api/kpi/history/${kpiName}?limit=1000`; // Fetch more points if filtering
            if (startDate) {
                apiUrl += `&start_date=${encodeURIComponent(startDate)}`;
            }
            if (endDate) {
                apiUrl += `&end_date=${encodeURIComponent(endDate)}`;
            }

            // Fetch data with authentication
            const response = await AssetKPIAuth.authenticatedFetch(apiUrl);

            if (!response.ok) {
                // Handle 401 Unauthorized errors specifically
                if (response.status === 401) {
                    console.error('Authentication error: 401 Unauthorized');
                    errorMsgElement.textContent = 'Authentication error. Please log in again.';

                    // Try to refresh the token and retry
                    try {
                        if (AssetKPIAuth.getCurrentUser()) {
                            const token = await AssetKPIAuth.getCurrentUser().getIdToken(true);
                            localStorage.setItem('firebaseIdToken', token);
                            document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;
                            console.log('Token refreshed, retrying request...');
                            // Don't retry here, let the user refresh manually
                        }
                    } catch (refreshError) {
                        console.error('Error refreshing token:', refreshError);
                    }
                    return;
                }

                const errorData = await response.json().catch(() => ({ detail: `HTTP error! status: ${response.status}`}));
                throw new Error(errorData.detail || `Failed to fetch data: ${response.status}`);
            }

            const historyData = await response.json();

            // Check if data is empty AFTER fetch
            if (!historyData || historyData.length === 0) {
                noDataElement.style.display = 'block'; // Show no-data message
                return; // Don't try to render empty chart
            }

            // Prepare data
            const labels = historyData.map(point => new Date(point.timestamp).toLocaleString([], {
                year: 'numeric', month: 'numeric', day: 'numeric',
                hour: '2-digit', minute: '2-digit'
            }));
            const dataPoints = historyData.map(point => point.value);

            // Basic color mapping for different KPIs
            const colors = {
                'MTTR_Calculated': 'rgb(75, 192, 192)',
                'MTBF_Calculated': 'rgb(255, 99, 132)',
                'default': 'rgb(54, 162, 235)'
            };
            const borderColor = colors[kpiName] || colors['default'];

            // Chart config
            const chartConfig = {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${yAxisLabel}`,
                        data: dataPoints,
                        borderColor: borderColor,
                        tension: 0.1,
                        fill: false,
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: { display: true, text: yAxisLabel }
                        },
                        x: {
                            title: { display: true, text: 'Calculation Timestamp' },
                            ticks: {
                                maxRotation: 70,
                                minRotation: 45,
                                autoSkip: true,
                                maxTicksLimit: 25
                            }
                        }
                    },
                    plugins: {
                        legend: { display: true },
                        tooltip: { mode: 'index', intersect: false }
                    },
                    hover: { mode: 'nearest', intersect: true }
                }
            };

            // Create and store chart instance
            chartInstances[canvasId] = new Chart(ctx, chartConfig);

        } catch (error) {
            console.error(`Error fetching or rendering chart (${kpiName}):`, error);
            errorMsgElement.textContent = `Error loading ${kpiName} chart: ${error.message}`; // Show error
            noDataElement.style.display = 'none'; // Hide no-data msg if error occurred
        }
    }

    // Function to update both charts based on filter
    function updateCharts() {
        console.log("Updating charts based on date filter...");
        // Clear general error message before attempting updates
        const errorMsgElement = document.getElementById('chartErrorMsg');
        if(errorMsgElement) errorMsgElement.textContent = '';

        renderKpiChart('MTTR_Calculated', 'mttrTrendChart', 'MTTR (Hours)');
        renderKpiChart('MTBF_Calculated', 'mtbfTrendChart', 'MTBF (Hours)');
    }

    // Function to handle recommendation actions
    async function handleRecommendationAction(recId, action) {
        const actionMessageDiv = document.getElementById('rec-action-message');
        actionMessageDiv.textContent = '';

        try {
            // Check if user is authenticated
            if (!AssetKPIAuth.isAuthenticated()) {
                console.error('User is not authenticated');
                actionMessageDiv.textContent = 'Authentication required. Please log in to perform this action.';
                actionMessageDiv.className = 'mt-2 text-danger';
                return;
            }

            const response = await AssetKPIAuth.authenticatedFetch(`/api/recommendations/${recId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ new_status: action })
            });

            // Handle 401 Unauthorized errors specifically
            if (response.status === 401) {
                console.error('Authentication error: 401 Unauthorized');
                actionMessageDiv.textContent = 'Authentication error. Please log in again.';
                actionMessageDiv.className = 'mt-2 text-danger';

                // Try to refresh the token
                try {
                    if (AssetKPIAuth.getCurrentUser()) {
                        const token = await AssetKPIAuth.getCurrentUser().getIdToken(true);
                        localStorage.setItem('firebaseIdToken', token);
                        document.cookie = `firebaseIdToken=${token}; path=/; max-age=3600; SameSite=Strict`;
                        console.log('Token refreshed, please try again');
                    }
                } catch (refreshError) {
                    console.error('Error refreshing token:', refreshError);
                }
                return;
            }

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.detail || `Failed to update status: ${response.status}`);
            }

            // Success!
            actionMessageDiv.textContent = result.message || `Recommendation ${recId} updated to ${action}.`;
            actionMessageDiv.className = 'mt-2 text-success';

            // Remove the recommendation item from UI
            const rowToRemove = document.getElementById(`rec-row-${recId}`);
            if (rowToRemove) {
                rowToRemove.remove();
            }

        } catch (error) {
            console.error("Error updating recommendation status:", error);
            actionMessageDiv.textContent = `Error: ${error.message}`;
            actionMessageDiv.className = 'mt-2 text-danger';
        }
    }

    // Function to setup recommendation buttons
    function setupRecommendationButtons() {
        document.querySelectorAll('.action-button').forEach(btn => {
            btn.addEventListener('click', function() {
                const recId = this.dataset.recId;
                const action = this.dataset.action;

                // Disable button to prevent double-clicks
                this.disabled = true;
                this.textContent = '...';

                handleRecommendationAction(recId, action).finally(() => {
                    // Re-enable button on completion
                    this.disabled = false;
                    this.textContent = action === 'ACKNOWLEDGED' ? 'Acknowledge' : 'Dismiss';
                });
            });
        });
    }

    // Function to update the dashboard UI based on authentication and role
    function updateDashboardUI(user) {
        const authRequiredContent = document.querySelectorAll('.auth-required-content');

        // Check if we have a server-side authenticated user
        {% if current_user %}
        console.log('Server-side authenticated user detected: {{ current_user.email }}');
        const serverUserRole = '{{ current_user.role }}';

        // Show content based on server-side role
        authRequiredContent.forEach(el => {
            const requiredRoles = (el.dataset.role || '').split(',');
            if (requiredRoles.includes(serverUserRole)) {
                el.style.display = 'block';
            } else {
                el.style.display = 'none';
            }
        });

        // Setup recommendation buttons if user has appropriate role
        if (['MANAGER', 'ADMIN'].includes(serverUserRole)) {
            setupRecommendationButtons();
        }

        // Initialize charts if user has appropriate role
        if (['VIEWER', 'ENGINEER', 'MANAGER', 'ADMIN'].includes(serverUserRole)) {
            // Add event listener to the filter button
            const filterButton = document.getElementById('filterButton');
            if (filterButton) {
                filterButton.addEventListener('click', updateCharts);
            }

            // Initial chart render
            updateCharts();
        }
        {% else %}
        // No server-side user, check client-side authentication
        if (user) {
            // User is authenticated, show role-specific content
            AssetKPIAuth.authenticatedFetch('/api/users/me')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('Failed to fetch user info');
                })
                .then(data => {
                    const userRole = data.role || '';
                    console.log('Client-side authenticated user detected:', data.email);

                    // Show content based on role
                    authRequiredContent.forEach(el => {
                        const requiredRoles = (el.dataset.role || '').split(',');
                        if (requiredRoles.includes(userRole)) {
                            el.style.display = 'block';
                        } else {
                            el.style.display = 'none';
                        }
                    });

                    // Setup recommendation buttons if user has appropriate role
                    if (['MANAGER', 'ADMIN'].includes(userRole)) {
                        setupRecommendationButtons();
                    }

                    // Initialize charts if user has appropriate role
                    if (['VIEWER', 'ENGINEER', 'MANAGER', 'ADMIN'].includes(userRole)) {
                        // Add event listener to the filter button
                        const filterButton = document.getElementById('filterButton');
                        if (filterButton) {
                            filterButton.addEventListener('click', updateCharts);
                        }

                        // Initial chart render
                        updateCharts();
                    }
                })
                .catch(error => {
                    console.error('Error fetching user info:', error);
                });
        } else {
            // User is not authenticated, hide all authenticated content
            authRequiredContent.forEach(el => {
                el.style.display = 'none';
            });
        }
        {% endif %}
    }

    // Initialize the dashboard UI based on authentication state
    AssetKPIAuth.initAuth(updateDashboardUI);
</script>
{% endblock %}
