"""
Permissions module for AssetKPI API.

This module defines the permission scopes and utilities for checking permissions.
"""
from typing import Set
from fastapi import HTTPException, status, Depends, Request
from fastapi.responses import Response
from sqlalchemy.orm import Session
from starlette.middleware.base import BaseHTTPMiddleware

# Import permission constants
from permission_constants import PermissionScope, DEFAULT_VIEWER_PERMISSIONS, DEFAULT_ENGINEER_PERMISSIONS, DEFAULT_MANAGER_PERMISSIONS, DEFAULT_ADMIN_PERMISSIONS  # type: ignore

# Import models and database
# These imports will be resolved at runtime
User = None
UserPermission = None
SessionLocal = None
get_db = None
get_current_user = None

# Function to initialize the module with dependencies
def init_permissions(user_model, user_permission_model, session_local, get_db_func, get_current_user_func):
    global User, UserPermission, SessionLocal, get_db, get_current_user
    User = user_model
    UserPermission = user_permission_model
    SessionLocal = session_local
    get_db = get_db_func
    get_current_user = get_current_user_func

# Permission scopes and default permission sets are imported from permission_constants.py

# Map API endpoints to required permissions
ENDPOINT_PERMISSIONS = {
    # Assets endpoints
    "/api/assets": {
        "GET": PermissionScope.ASSETS_READ,
        "POST": PermissionScope.ASSETS_WRITE,
    },
    "/api/assets/{asset_id}": {
        "GET": PermissionScope.ASSETS_READ,
        "PUT": PermissionScope.ASSETS_WRITE,
        "DELETE": PermissionScope.ASSETS_WRITE,
    },

    # Inventory endpoints
    "/api/inventory/parts": {
        "GET": PermissionScope.INVENTORY_READ,
        "POST": PermissionScope.INVENTORY_WRITE,
    },
    "/api/inventory/parts/{part_id}": {
        "GET": PermissionScope.INVENTORY_READ,
        "PUT": PermissionScope.INVENTORY_WRITE,
        "DELETE": PermissionScope.INVENTORY_WRITE,
    },
    "/api/inventory/analysis": {
        "GET": PermissionScope.INVENTORY_READ,
    },
    "/api/inventory/run-optimization": {
        "GET": PermissionScope.INVENTORY_OPTIMIZE,
    },

    # Work order endpoints
    "/api/workorders": {
        "GET": PermissionScope.WORKORDERS_READ,
        "POST": PermissionScope.WORKORDERS_WRITE,
    },
    "/api/workorders/{workorder_id}": {
        "GET": PermissionScope.WORKORDERS_READ,
        "PUT": PermissionScope.WORKORDERS_WRITE,
        "DELETE": PermissionScope.WORKORDERS_WRITE,
    },

    # KPI endpoints
    "/api/kpis/latest": {
        "GET": PermissionScope.KPI_READ,
    },
    "/api/kpis/history/{kpi_name}": {
        "GET": PermissionScope.KPI_READ,
    },

    # User management endpoints
    "/api/users": {
        "GET": PermissionScope.USERS_READ,
        "POST": PermissionScope.USERS_WRITE,
    },
    "/api/users/{user_id}": {
        "GET": PermissionScope.USERS_READ,
        "PUT": PermissionScope.USERS_WRITE,
        "DELETE": PermissionScope.USERS_WRITE,
    },
    "/api/users/{user_id}/permissions": {
        "GET": PermissionScope.USERS_READ,
        "PUT": PermissionScope.USERS_WRITE,
    },
    "/api/users/{user_id}/api-key": {
        "POST": PermissionScope.API_WRITE,
        "DELETE": PermissionScope.API_WRITE,
    },
}

# Permission checking functions
def has_permission(user_permissions: Set[str], required_permission: str) -> bool:
    """
    Check if the user has the required permission.

    Args:
        user_permissions: Set of permission strings the user has
        required_permission: The permission to check for

    Returns:
        True if the user has the permission, False otherwise
    """
    # Check if the user has the exact permission
    if required_permission in user_permissions:
        return True

    # Check if the user has a wildcard permission that covers this permission
    # For example, if the user has 'assets:*' and the required permission is 'assets:read'
    if ':' in required_permission:
        resource = required_permission.split(':')[0]
        wildcard_permission = f"{resource}:*"
        if wildcard_permission in user_permissions:
            return True

    # Check if the user has the global wildcard permission
    if "*:*" in user_permissions:
        return True

    return False

def check_endpoint_permission(
    request: Request,
    user_permissions: Set[str],
) -> bool:
    """
    Check if the user has permission to access the endpoint.

    Args:
        request: The FastAPI request object
        user_permissions: Set of permission strings the user has

    Returns:
        True if the user has permission, False otherwise
    """
    path = request.url.path
    method = request.method

    # Check if the endpoint is in the permissions map
    if path in ENDPOINT_PERMISSIONS:
        if method in ENDPOINT_PERMISSIONS[path]:
            required_permission = ENDPOINT_PERMISSIONS[path][method]
            return has_permission(user_permissions, required_permission)

    # Check for dynamic path parameters
    # For example, /api/assets/123 should match /api/assets/{asset_id}
    for endpoint_pattern, methods in ENDPOINT_PERMISSIONS.items():
        # Skip if the pattern doesn't have a parameter
        if '{' not in endpoint_pattern:
            continue

        # Convert the pattern to a regex pattern
        # Replace {param} with ([^/]+)
        import re
        # Cache the compiled regex pattern for better performance
        regex_pattern = endpoint_pattern
        for param in re.findall(r'\{([^}]+)\}', endpoint_pattern):
            regex_pattern = regex_pattern.replace('{' + param + '}', '([^/]+)')
        regex_pattern = '^' + regex_pattern + '$'

        # Check if the path matches the pattern
        if re.match(regex_pattern, path):
            if method in methods:
                required_permission = methods[method]
                return has_permission(user_permissions, required_permission)

    # For endpoints not in the map, default to allowing access
    # This can be changed to be more restrictive if needed
    return True

# Permission checking middleware
class PermissionMiddleware(BaseHTTPMiddleware):
    """
    Middleware for checking permissions in FastAPI.
    """
    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        """
        Process the request and check permissions.

        Args:
            request: The FastAPI request
            call_next: The next middleware or route handler

        Returns:
            The response
        """
        # Skip permission checking for non-API routes
        path = request.url.path
        if not path.startswith("/api/"):
            return await call_next(request)

        # Get user from request state (set by authentication middleware)
        user = getattr(request.state, "user", None)

        # If no user, continue (authentication middleware will handle this)
        if not user:
            return await call_next(request)

        # Get user permissions from database
        db = SessionLocal()
        try:
            # Get user permissions
            user_permissions = db.query(UserPermission).filter(UserPermission.user_id == user.user_id).all()
            permission_set = {p.permission for p in user_permissions}

            # Store permissions in request state for route handlers
            request.state.permissions = permission_set

            # Check if user has permission for this endpoint
            if not check_endpoint_permission(request, permission_set):
                return Response(
                    content="Insufficient permissions",
                    status_code=status.HTTP_403_FORBIDDEN
                )

            # Continue with the request
            return await call_next(request)
        except Exception as e:
            print(f"Error checking permissions: {e}")
            return Response(
                content="Error checking permissions",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        finally:
            db.close()

# Permission dependency for FastAPI
def require_permission(permission: PermissionScope):
    """
    Create a dependency that requires a specific permission.

    Args:
        permission: The permission scope required

    Returns:
        A dependency function that checks for the permission
    """
    async def check_permission(
        current_user = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        # Get user permissions from database
        user_permissions = db.query(UserPermission).filter(UserPermission.user_id == current_user.user_id).all()
        permission_set = {p.permission for p in user_permissions}

        # Check if user has the required permission
        if not has_permission(permission_set, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {permission}"
            )

        return current_user

    return check_permission

# Function to get user permissions
async def get_user_permissions(user_id: str, db: Session) -> Set[str]:
    """
    Get the permissions for a user.

    Args:
        user_id: The user ID
        db: Database session

    Returns:
        Set of permission strings
    """
    user_permissions = db.query(UserPermission).filter(UserPermission.user_id == user_id).all()
    return {p.permission for p in user_permissions}
