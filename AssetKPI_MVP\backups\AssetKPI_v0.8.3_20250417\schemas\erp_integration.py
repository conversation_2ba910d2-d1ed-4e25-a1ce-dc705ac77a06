"""
ERP Integration schemas for the AssetKPI application.

This module defines the Pydantic models for ERP connections and data mappings.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, HttpUrl, validator
from enum import Enum


class ERPSystemTypeEnum(str, Enum):
    """Enum for supported ERP system types."""
    SAP = "sap"
    ORACLE = "oracle"
    MICROSOFT_DYNAMICS = "microsoft_dynamics"
    INFOR = "infor"
    NETSUITE = "netsuite"
    SAGE = "sage"
    CUSTOM = "custom"


class ERPConnectionStatusEnum(str, Enum):
    """Enum for ERP connection status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"


class ERPAuthTypeEnum(str, Enum):
    """Enum for ERP authentication types."""
    BASIC = "basic"
    OAUTH = "oauth"
    API_KEY = "api_key"
    JWT = "jwt"
    CERTIFICATE = "certificate"
    CUSTOM = "custom"


class SyncDirectionEnum(str, Enum):
    """Enum for synchronization directions."""
    IMPORT = "import"
    EXPORT = "export"
    BIDIRECTIONAL = "bidirectional"


class ERPEntityTypeEnum(str, Enum):
    """Enum for common ERP entity types."""
    ASSET = "asset"
    INVENTORY = "inventory"
    WORKORDER = "workorder"
    PURCHASE_ORDER = "purchase_order"
    VENDOR = "vendor"
    CUSTOMER = "customer"
    EMPLOYEE = "employee"
    LOCATION = "location"
    COST_CENTER = "cost_center"
    CUSTOM = "custom"


class AssetKPIEntityTypeEnum(str, Enum):
    """Enum for AssetKPI entity types."""
    ASSET = "asset"
    INVENTORY = "inventory"
    WORKORDER = "workorder"
    USER = "user"
    KPI = "kpi"
    RECOMMENDATION = "recommendation"
    CUSTOM = "custom"


class ERPConnectionBase(BaseModel):
    """Base model for ERP connections."""
    name: str = Field(..., min_length=3, max_length=100, description="Name of the ERP connection")
    description: Optional[str] = Field(None, description="Description of the ERP connection")
    system_type: ERPSystemTypeEnum = Field(..., description="Type of ERP system")
    version: Optional[str] = Field(None, description="Version of the ERP system")
    connection_url: str = Field(..., min_length=5, description="URL for connecting to the ERP system")
    connection_params: Optional[Dict[str, Any]] = Field(None, description="Additional connection parameters")
    auth_type: ERPAuthTypeEnum = Field(..., description="Authentication type")
    auth_credentials: Optional[str] = Field(None, description="Authentication credentials (encrypted)")


class ERPConnectionCreate(ERPConnectionBase):
    """Model for creating an ERP connection."""
    pass


class ERPConnectionUpdate(BaseModel):
    """Model for updating an ERP connection."""
    name: Optional[str] = Field(None, min_length=3, max_length=100, description="Name of the ERP connection")
    description: Optional[str] = Field(None, description="Description of the ERP connection")
    system_type: Optional[ERPSystemTypeEnum] = Field(None, description="Type of ERP system")
    version: Optional[str] = Field(None, description="Version of the ERP system")
    connection_url: Optional[str] = Field(None, min_length=5, description="URL for connecting to the ERP system")
    connection_params: Optional[Dict[str, Any]] = Field(None, description="Additional connection parameters")
    auth_type: Optional[ERPAuthTypeEnum] = Field(None, description="Authentication type")
    auth_credentials: Optional[str] = Field(None, description="Authentication credentials (encrypted)")
    status: Optional[ERPConnectionStatusEnum] = Field(None, description="Connection status")


class ERPConnectionInDB(ERPConnectionBase):
    """Model for an ERP connection in the database."""
    id: int
    status: ERPConnectionStatusEnum
    last_sync_time: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: str
    
    class Config:
        orm_mode = True


class FieldMapping(BaseModel):
    """Model for field mapping between AssetKPI and ERP systems."""
    assetkpi_field: str = Field(..., description="Field name in AssetKPI")
    erp_field: str = Field(..., description="Field name in ERP system")
    data_type: Optional[str] = Field(None, description="Data type for conversion")
    is_required: bool = Field(False, description="Whether the field is required")
    default_value: Optional[Any] = Field(None, description="Default value if field is missing")


class TransformationRule(BaseModel):
    """Model for data transformation rules."""
    field: str = Field(..., description="Field to transform")
    rule_type: str = Field(..., description="Type of transformation rule")
    rule_params: Dict[str, Any] = Field(..., description="Parameters for the transformation rule")


class ERPDataMappingBase(BaseModel):
    """Base model for ERP data mappings."""
    connection_id: int = Field(..., description="ID of the ERP connection")
    name: str = Field(..., min_length=3, max_length=100, description="Name of the data mapping")
    description: Optional[str] = Field(None, description="Description of the data mapping")
    assetkpi_entity: AssetKPIEntityTypeEnum = Field(..., description="Entity type in AssetKPI")
    erp_entity: str = Field(..., description="Entity type in ERP system")
    field_mappings: List[FieldMapping] = Field(..., min_items=1, description="Field mappings")
    transformation_rules: Optional[List[TransformationRule]] = Field(None, description="Transformation rules")
    sync_direction: SyncDirectionEnum = Field(..., description="Synchronization direction")
    sync_schedule: Optional[str] = Field(None, description="Synchronization schedule (cron expression)")
    is_active: bool = Field(True, description="Whether the data mapping is active")


class ERPDataMappingCreate(ERPDataMappingBase):
    """Model for creating an ERP data mapping."""
    pass


class ERPDataMappingUpdate(BaseModel):
    """Model for updating an ERP data mapping."""
    name: Optional[str] = Field(None, min_length=3, max_length=100, description="Name of the data mapping")
    description: Optional[str] = Field(None, description="Description of the data mapping")
    assetkpi_entity: Optional[AssetKPIEntityTypeEnum] = Field(None, description="Entity type in AssetKPI")
    erp_entity: Optional[str] = Field(None, description="Entity type in ERP system")
    field_mappings: Optional[List[FieldMapping]] = Field(None, min_items=1, description="Field mappings")
    transformation_rules: Optional[List[TransformationRule]] = Field(None, description="Transformation rules")
    sync_direction: Optional[SyncDirectionEnum] = Field(None, description="Synchronization direction")
    sync_schedule: Optional[str] = Field(None, description="Synchronization schedule (cron expression)")
    is_active: Optional[bool] = Field(None, description="Whether the data mapping is active")


class ERPDataMappingInDB(ERPDataMappingBase):
    """Model for an ERP data mapping in the database."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True


class ERPSyncLogBase(BaseModel):
    """Base model for ERP synchronization logs."""
    connection_id: int = Field(..., description="ID of the ERP connection")
    mapping_id: Optional[int] = Field(None, description="ID of the data mapping")
    operation_type: str = Field(..., description="Type of operation")
    entity_type: str = Field(..., description="Type of entity")
    status: str = Field(..., description="Status of the synchronization")
    start_time: datetime = Field(..., description="Start time of the synchronization")
    end_time: Optional[datetime] = Field(None, description="End time of the synchronization")
    records_processed: int = Field(0, description="Number of records processed")
    records_succeeded: int = Field(0, description="Number of records successfully processed")
    records_failed: int = Field(0, description="Number of records that failed processing")
    error_message: Optional[str] = Field(None, description="Error message if synchronization failed")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Detailed error information")


class ERPSyncLogCreate(ERPSyncLogBase):
    """Model for creating an ERP synchronization log."""
    pass


class ERPSyncLogInDB(ERPSyncLogBase):
    """Model for an ERP synchronization log in the database."""
    id: int
    
    class Config:
        orm_mode = True


class ERPTestConnectionRequest(BaseModel):
    """Model for testing an ERP connection."""
    connection_id: Optional[int] = Field(None, description="ID of an existing ERP connection")
    connection_details: Optional[ERPConnectionCreate] = Field(None, description="Details for a new connection to test")
    
    @validator('connection_details', always=True)
    def validate_connection_info(cls, v, values):
        """Validate that either connection_id or connection_details is provided."""
        if not values.get('connection_id') and not v:
            raise ValueError("Either connection_id or connection_details must be provided")
        return v


class ERPTestConnectionResponse(BaseModel):
    """Model for ERP connection test response."""
    success: bool = Field(..., description="Whether the connection test was successful")
    message: str = Field(..., description="Message describing the test result")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details about the test")


class ERPSyncRequest(BaseModel):
    """Model for requesting an ERP synchronization."""
    mapping_id: int = Field(..., description="ID of the data mapping to synchronize")
    force: bool = Field(False, description="Whether to force synchronization regardless of schedule")


class ERPSyncResponse(BaseModel):
    """Model for ERP synchronization response."""
    success: bool = Field(..., description="Whether the synchronization was successful")
    message: str = Field(..., description="Message describing the synchronization result")
    log_id: Optional[int] = Field(None, description="ID of the synchronization log")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details about the synchronization")
