{% extends "layout.html" %}

{% block title %}Asset Documents | AssetKPI{% endblock %}

{% block styles %}
<style>
    .asset-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
    .status-badge {
        font-size: 1rem;
        padding: 5px 10px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    .status-maintenance {
        background-color: #ffc107;
    }
    .document-card {
        transition: transform 0.3s;
    }
    .document-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    .document-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }
    .doc-pdf {
        color: #dc3545;
    }
    .doc-word {
        color: #0d6efd;
    }
    .doc-excel {
        color: #28a745;
    }
    .doc-image {
        color: #6f42c1;
    }
    .doc-other {
        color: #fd7e14;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/assets">Assets</a></li>
                <li class="breadcrumb-item"><a href="/assets/{{ asset.assetid }}">{{ asset.assetname }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Documents</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Asset Header -->
<div class="asset-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>{{ asset.assetname }} - Documents</h1>
            <p class="text-muted">{{ asset.assettype }} | {{ asset.manufacturer }} {{ asset.model }}</p>
            <div class="d-flex align-items-center mt-2">
                <span class="badge status-{{ asset.status|lower if asset.status else 'inactive' }} status-badge me-2">{{ asset.status }}</span>
                <span class="text-muted">Serial: {{ asset.serialnumber }}</span>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <i class="fas fa-upload"></i> Upload Document
            </button>
        </div>
    </div>
</div>

<!-- Asset Navigation -->
<ul class="nav nav-pills mb-4">
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}">Overview</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/kpi">KPIs</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/maintenance">Maintenance</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/specifications">Specifications</a>
    </li>
    <li class="nav-item">
        <a class="nav-link active" href="/assets/{{ asset.assetid }}/documents">Documents</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/meters">Meters</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="/assets/{{ asset.assetid }}/pm">PM Schedules</a>
    </li>
</ul>

<!-- Document Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <div class="input-group">
                            <span class="input-group-text">Search</span>
                            <input type="text" id="documentSearch" class="form-control" placeholder="Search documents...">
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <select class="form-select" id="documentTypeFilter">
                            <option value="">All Document Types</option>
                            <option value="Manual">Manual</option>
                            <option value="Datasheet">Datasheet</option>
                            <option value="Drawing">Drawing</option>
                            <option value="Certificate">Certificate</option>
                            <option value="Procedure">Procedure</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <select class="form-select" id="fileTypeFilter">
                            <option value="">All File Types</option>
                            <option value="pdf">PDF</option>
                            <option value="doc">Word</option>
                            <option value="xls">Excel</option>
                            <option value="img">Image</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button class="btn btn-primary w-100" id="applyFilters">Filter</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Documents -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Documents</h5>
                <div>
                    <button class="btn btn-sm btn-outline-secondary" id="toggleView">
                        <i class="fas fa-th"></i> Toggle View
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Table View (Default) -->
                <div id="tableView">
                    {% if documents %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Document Name</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Version</th>
                                        <th>Created By</th>
                                        <th>Created Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for doc in documents %}
                                        <tr class="document-item"
                                            data-name="{{ doc.document_name }}"
                                            data-type="{{ doc.document_type }}"
                                            data-file-type="{{ doc.file_path.split('.')[-1] if doc.file_path else 'other' }}">
                                            <td>{{ doc.document_name }}</td>
                                            <td>{{ doc.document_type }}</td>
                                            <td>{{ doc.description[:50] }}{% if doc.description|length > 50 %}...{% endif %}</td>
                                            <td>{{ doc.version }}</td>
                                            <td>{{ doc.created_by }}</td>
                                            <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                                            <td>
                                                <a href="{{ doc.file_path }}" class="btn btn-sm btn-primary" target="_blank">View</a>
                                                <button class="btn btn-sm btn-secondary">Download</button>
                                                <button class="btn btn-sm btn-danger auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">Delete</button>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-center">No documents found for this asset.</p>
                    {% endif %}
                </div>

                <!-- Card View (Hidden by Default) -->
                <div id="cardView" class="d-none">
                    {% if documents %}
                        <div class="row">
                            {% for doc in documents %}
                                {% set file_ext = doc.file_path.split('.')[-1] if doc.file_path else 'other' %}
                                <div class="col-md-3 mb-4 document-item"
                                     data-name="{{ doc.document_name }}"
                                     data-type="{{ doc.document_type }}"
                                     data-file-type="{{ file_ext }}">
                                    <div class="card document-card h-100">
                                        <div class="card-body text-center">
                                            <div class="document-icon">
                                                {% if file_ext == 'pdf' %}
                                                    <i class="far fa-file-pdf doc-pdf"></i>
                                                {% elif file_ext in ['doc', 'docx'] %}
                                                    <i class="far fa-file-word doc-word"></i>
                                                {% elif file_ext in ['xls', 'xlsx', 'csv'] %}
                                                    <i class="far fa-file-excel doc-excel"></i>
                                                {% elif file_ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp'] %}
                                                    <i class="far fa-file-image doc-image"></i>
                                                {% else %}
                                                    <i class="far fa-file-alt doc-other"></i>
                                                {% endif %}
                                            </div>
                                            <h5 class="card-title">{{ doc.document_name }}</h5>
                                            <h6 class="card-subtitle mb-2 text-muted">{{ doc.document_type }}</h6>
                                            <p class="card-text small">{{ doc.description[:100] }}{% if doc.description|length > 100 %}...{% endif %}</p>
                                            <p class="card-text small text-muted">Version: {{ doc.version }}</p>
                                            <p class="card-text small text-muted">Created: {{ doc.created_at.strftime('%Y-%m-%d') }}</p>
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group w-100">
                                                <a href="{{ doc.file_path }}" class="btn btn-sm btn-primary" target="_blank">View</a>
                                                <button class="btn btn-sm btn-secondary">Download</button>
                                                <button class="btn btn-sm btn-danger auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">Delete</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-center">No documents found for this asset.</p>
                    {% endif %}
                </div>
            </div>
            <div class="card-footer text-end auth-required-content" data-role="ENGINEER,MANAGER,ADMIN">
                <button class="btn btn-primary">
                    <i class="fas fa-upload"></i> Upload Document
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Document Modal (Hidden) -->
<div class="modal fade" id="uploadDocumentModal" tabindex="-1" aria-labelledby="uploadDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadDocumentModalLabel">Upload Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="uploadDocumentForm">
                    <div class="mb-3">
                        <label for="documentName" class="form-label">Document Name</label>
                        <input type="text" class="form-control" id="documentName" required>
                    </div>
                    <div class="mb-3">
                        <label for="documentType" class="form-label">Document Type</label>
                        <select class="form-select" id="documentType" required>
                            <option value="">Select Type</option>
                            <option value="Manual">Manual</option>
                            <option value="Datasheet">Datasheet</option>
                            <option value="Drawing">Drawing</option>
                            <option value="Certificate">Certificate</option>
                            <option value="Procedure">Procedure</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="documentDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="documentDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="documentVersion" class="form-label">Version</label>
                        <input type="text" class="form-control" id="documentVersion">
                    </div>
                    <div class="mb-3">
                        <label for="documentFile" class="form-label">File</label>
                        <input type="file" class="form-control" id="documentFile" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="uploadDocumentBtn">Upload</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between table and card view
        const toggleViewBtn = document.getElementById('toggleView');
        const tableView = document.getElementById('tableView');
        const cardView = document.getElementById('cardView');

        toggleViewBtn.addEventListener('click', function() {
            if (tableView.classList.contains('d-none')) {
                tableView.classList.remove('d-none');
                cardView.classList.add('d-none');
                toggleViewBtn.innerHTML = '<i class="fas fa-th"></i> Toggle View';
            } else {
                tableView.classList.add('d-none');
                cardView.classList.remove('d-none');
                toggleViewBtn.innerHTML = '<i class="fas fa-table"></i> Toggle View';
            }
        });

        // Filter functionality
        const documentSearch = document.getElementById('documentSearch');
        const documentTypeFilter = document.getElementById('documentTypeFilter');
        const fileTypeFilter = document.getElementById('fileTypeFilter');
        const applyFiltersBtn = document.getElementById('applyFilters');
        const documentItems = document.querySelectorAll('.document-item');

        applyFiltersBtn.addEventListener('click', function() {
            const searchTerm = documentSearch.value.toLowerCase();
            const docType = documentTypeFilter.value;
            const fileType = fileTypeFilter.value;

            documentItems.forEach(item => {
                const name = item.dataset.name.toLowerCase();
                const type = item.dataset.type;
                const fileExt = item.dataset.fileType;

                const matchesSearch = name.includes(searchTerm);
                const matchesDocType = !docType || type === docType;
                const matchesFileType = !fileType || (
                    fileType === 'pdf' && fileExt === 'pdf' ||
                    fileType === 'doc' && ['doc', 'docx'].includes(fileExt) ||
                    fileType === 'xls' && ['xls', 'xlsx', 'csv'].includes(fileExt) ||
                    fileType === 'img' && ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt) ||
                    fileType === 'other' && !['pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt)
                );

                if (matchesSearch && matchesDocType && matchesFileType) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // Quick search functionality
        documentSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            documentItems.forEach(item => {
                const name = item.dataset.name.toLowerCase();

                if (name.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // Upload document modal
        const uploadBtns = document.querySelectorAll('.btn-primary[data-role="ENGINEER,MANAGER,ADMIN"]');
        const uploadModal = new bootstrap.Modal(document.getElementById('uploadDocumentModal'));

        uploadBtns.forEach(btn => {
            if (btn.innerHTML.includes('Upload Document')) {
                btn.addEventListener('click', function() {
                    uploadModal.show();
                });
            }
        });

        // Upload document functionality
        const uploadDocumentBtn = document.getElementById('uploadDocumentBtn');
        uploadDocumentBtn.addEventListener('click', function() {
            // Implement upload functionality here
            alert('Document upload functionality would be implemented here');
            uploadModal.hide();
        });
    });
</script>
{% endblock %}
