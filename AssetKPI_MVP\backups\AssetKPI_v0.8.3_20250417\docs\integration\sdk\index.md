# Python SDK

The AssetKPI Python SDK provides a convenient way to interact with the AssetKPI API programmatically. It includes comprehensive client classes for all API endpoints, with advanced error handling and retry logic.

## Table of Contents

- [Introduction](#introduction)
- [Installation](./installation.md)
- [Authentication](./authentication.md)
- [Basic Usage](./basic-usage.md)
- [Advanced Usage](./advanced-usage.md)
- [Error Handling](./error-handling.md)
- [Examples](./examples.md)
- [API Reference](../reference/sdk.md)

## Introduction

The AssetKPI Python SDK simplifies the process of integrating with the AssetKPI API by providing a high-level, object-oriented interface. It handles authentication, request formatting, error handling, and response parsing, allowing you to focus on your application logic.

### Key Features

- **Comprehensive API Coverage**: Client classes for all API endpoints
- **Advanced Error Handling**: Detailed error classes and retry logic
- **Authentication Support**: Both Firebase token and API key authentication
- **Bulk Operations**: Support for efficient bulk operations
- **Filtering and Pagination**: Advanced filtering and pagination capabilities
- **Command-Line Interface**: CLI for common operations
- **Examples**: Basic and advanced usage examples

### How the SDK Works

The SDK provides a set of client classes that correspond to different areas of the AssetKPI API:

- `AssetKPISDK`: Main SDK class that provides access to all API clients
- `AssetsClient`: Client for asset-related endpoints
- `InventoryClient`: Client for inventory-related endpoints
- `KPIClient`: Client for KPI-related endpoints
- `WorkOrdersClient`: Client for work order-related endpoints
- `UsersClient`: Client for user-related endpoints
- `AnalyticsClient`: Client for analytics-related endpoints
- `RecommendationsClient`: Client for recommendation-related endpoints

## Getting Started

To get started with the Python SDK, follow these steps:

1. [Install the SDK](./installation.md)
2. [Set up authentication](./authentication.md)
3. [Learn basic usage](./basic-usage.md)
4. [Explore advanced features](./advanced-usage.md)
5. [Understand error handling](./error-handling.md)
6. [Review examples](./examples.md)

## Next Steps

- [Installation](./installation.md)
- [Authentication](./authentication.md)
- [Basic Usage](./basic-usage.md)
- [Advanced Usage](./advanced-usage.md)
- [Error Handling](./error-handling.md)
- [Examples](./examples.md)
- [API Reference](../reference/sdk.md)
