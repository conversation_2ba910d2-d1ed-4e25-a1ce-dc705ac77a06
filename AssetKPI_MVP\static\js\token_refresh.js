/**
 * Token Refresh Utility for AssetKPI
 *
 * This module provides functions for handling Firebase token refresh
 * to ensure tokens don't expire during user sessions.
 */

// Token refresh interval in milliseconds (15 minutes)
const TOKEN_REFRESH_INTERVAL = 15 * 60 * 1000;

// Variable to store the refresh interval ID
let refreshIntervalId = null;

/**
 * Start the token refresh mechanism
 * This will periodically refresh the Firebase ID token
 */
function startTokenRefresh() {
    // Clear any existing interval
    if (refreshIntervalId) {
        clearInterval(refreshIntervalId);
    }

    // Set up the refresh interval
    refreshIntervalId = setInterval(async () => {
        try {
            // Check if Firebase is initialized and user is logged in
            if (typeof firebase !== 'undefined' && firebase.auth && firebase.auth().currentUser) {
                console.log('Refreshing Firebase ID token...');
                const user = firebase.auth().currentUser;
                const newToken = await user.getIdToken(true);
                
                // Store the refreshed token
                localStorage.setItem('firebaseIdToken', newToken);
                
                // Update the cookie for server-side authentication
                document.cookie = `firebaseIdToken=${newToken}; path=/; max-age=3600; SameSite=Strict`;
                
                console.log('Firebase ID token refreshed successfully');
            }
        } catch (error) {
            console.error('Error refreshing Firebase ID token:', error);
        }
    }, TOKEN_REFRESH_INTERVAL);
    
    console.log('Token refresh mechanism started');
}

/**
 * Stop the token refresh mechanism
 */
function stopTokenRefresh() {
    if (refreshIntervalId) {
        clearInterval(refreshIntervalId);
        refreshIntervalId = null;
        console.log('Token refresh mechanism stopped');
    }
}

// Export the functions
window.tokenRefresh = {
    start: startTokenRefresh,
    stop: stopTokenRefresh
};
