/**
 * Help System Initialization Script
 * 
 * This script initializes the help system for the AssetKPI application.
 */

import HelpEngine from './components/help/HelpEngine.js';
import HelpIcon from './components/help/HelpIcon.js';

// Initialize help engine when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create help container
    const helpContainer = document.createElement('div');
    helpContainer.id = 'help-container';
    document.body.appendChild(helpContainer);
    
    // Initialize help engine
    const helpEngine = new HelpEngine({
        containerId: 'help-container',
        enabled: true,
        helpLevel: 'basic'
    });
    
    helpEngine.init();
    
    // Add help button to navigation
    addHelpButton(helpEngine);
    
    // Add to window for debugging
    window.helpEngine = helpEngine;
});

/**
 * Add help button to navigation.
 * 
 * @param {HelpEngine} helpEngine - Help engine instance
 */
function addHelpButton(helpEngine) {
    // Find navigation
    const navbarNav = document.querySelector('.navbar-nav');
    if (!navbarNav) {
        return;
    }
    
    // Create help button
    const helpButton = document.createElement('li');
    helpButton.className = 'nav-item';
    helpButton.innerHTML = `
        <a class="nav-link" href="#" id="helpButton">
            <i class="bi bi-question-circle"></i> Help
        </a>
    `;
    
    // Add click event
    const helpButtonLink = helpButton.querySelector('#helpButton');
    helpButtonLink.addEventListener('click', (event) => {
        event.preventDefault();
        helpEngine.togglePanel();
    });
    
    // Add to navigation
    navbarNav.appendChild(helpButton);
    
    // Add help settings dropdown
    addHelpSettingsDropdown(helpEngine, helpButton);
}

/**
 * Add help settings dropdown.
 * 
 * @param {HelpEngine} helpEngine - Help engine instance
 * @param {HTMLElement} helpButton - Help button element
 */
function addHelpSettingsDropdown(helpEngine, helpButton) {
    // Create dropdown
    const dropdown = document.createElement('div');
    dropdown.className = 'dropdown-menu';
    dropdown.setAttribute('aria-labelledby', 'helpButton');
    dropdown.innerHTML = `
        <h6 class="dropdown-header">Help Settings</h6>
        <div class="dropdown-item">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="enableHelp" ${helpEngine.enabled ? 'checked' : ''}>
                <label class="form-check-label" for="enableHelp">
                    Enable Help
                </label>
            </div>
        </div>
        <div class="dropdown-divider"></div>
        <h6 class="dropdown-header">Help Level</h6>
        <div class="dropdown-item">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="helpLevel" id="helpLevelBasic" value="basic" ${helpEngine.helpLevel === 'basic' ? 'checked' : ''}>
                <label class="form-check-label" for="helpLevelBasic">
                    Basic
                </label>
            </div>
        </div>
        <div class="dropdown-item">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="helpLevel" id="helpLevelDetailed" value="detailed" ${helpEngine.helpLevel === 'detailed' ? 'checked' : ''}>
                <label class="form-check-label" for="helpLevelDetailed">
                    Detailed
                </label>
            </div>
        </div>
        <div class="dropdown-item">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="helpLevel" id="helpLevelAdvanced" value="advanced" ${helpEngine.helpLevel === 'advanced' ? 'checked' : ''}>
                <label class="form-check-label" for="helpLevelAdvanced">
                    Advanced
                </label>
            </div>
        </div>
    `;
    
    // Add event listeners
    const enableHelpCheckbox = dropdown.querySelector('#enableHelp');
    enableHelpCheckbox.addEventListener('change', () => {
        if (enableHelpCheckbox.checked) {
            helpEngine.enableHelp();
        } else {
            helpEngine.disableHelp();
        }
    });
    
    const helpLevelRadios = dropdown.querySelectorAll('input[name="helpLevel"]');
    helpLevelRadios.forEach(radio => {
        radio.addEventListener('change', () => {
            if (radio.checked) {
                helpEngine.setHelpLevel(radio.value);
            }
        });
    });
    
    // Add dropdown to help button
    helpButton.appendChild(dropdown);
    
    // Initialize dropdown
    helpButton.classList.add('dropdown');
    const helpButtonLink = helpButton.querySelector('#helpButton');
    helpButtonLink.setAttribute('data-bs-toggle', 'dropdown');
    helpButtonLink.setAttribute('aria-expanded', 'false');
}
