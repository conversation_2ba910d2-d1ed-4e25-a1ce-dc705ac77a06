/**
 * KPI Dashboard JavaScript
 * Handles loading and displaying KPI data on the dashboard
 */

// KPI Dashboard namespace
const KPIDashboard = {
    // Chart instances
    charts: {},

    // KPI data
    kpiData: {
        mttr: { current: null, history: [], target: 4.0 },
        mtbf: { current: null, history: [], target: 400.0 },
        failureRate: { current: null, history: [], target: 5.0 },
        availability: { current: null, history: [], target: 95.0 }
    },

    // Initialize the dashboard
    init: function() {
        console.log('Initializing KPI Dashboard');

        // Set up event listeners
        this.setupEventListeners();

        // Load initial data
        this.loadKPIData();

        // Load assets for filter dropdown
        this.loadAssets();
    },

    // Set up event listeners
    setupEventListeners: function() {
        // Apply filters button
        const filterButton = document.getElementById('applyFilters');
        if (filterButton) {
            filterButton.addEventListener('click', () => {
                this.loadKPIData();
            });
        }

        // Tab change events to resize charts
        const tabLinks = document.querySelectorAll('a[data-bs-toggle="tab"]');
        tabLinks.forEach(tabLink => {
            tabLink.addEventListener('shown.bs.tab', event => {
                const targetId = event.target.getAttribute('aria-controls');
                const chartCanvas = document.querySelector(`#${targetId} canvas`);
                if (chartCanvas && this.charts[chartCanvas.id]) {
                    this.charts[chartCanvas.id].resize();
                }
            });
        });
    },

    // Load assets for filter dropdown
    loadAssets: function() {
        const assetFilter = document.getElementById('assetFilter');
        if (!assetFilter) return;

        // Clear existing options except the first one
        while (assetFilter.options.length > 1) {
            assetFilter.remove(1);
        }

        // Add some sample assets for now
        // In a real implementation, this would fetch from the API
        const sampleAssets = [
            { assetid: 1, assetname: 'Pump Station 1' },
            { assetid: 2, assetname: 'Conveyor Belt A' },
            { assetid: 3, assetname: 'Hydraulic Press' },
            { assetid: 4, assetname: 'CNC Machine' },
            { assetid: 5, assetname: 'Boiler System' }
        ];

        // Add assets to dropdown
        sampleAssets.forEach(asset => {
            const option = document.createElement('option');
            option.value = asset.assetid;
            option.textContent = `${asset.assetname} (${asset.assetid})`;
            assetFilter.appendChild(option);
        });

        console.log('Sample assets loaded for demonstration');
    },

    // Load KPI data from API
    loadKPIData: function() {
        // Show loading indicators
        this.showLoadingState();

        // Get filter values
        const startDate = document.getElementById('startDateInput').value;
        const endDate = document.getElementById('endDateInput').value;
        const assetId = document.getElementById('assetFilter').value;

        // Fetch MTTR data
        this.fetchKPIHistory('MTTR_Calculated', startDate, endDate, assetId)
            .then(data => {
                this.kpiData.mttr.history = data;
                this.kpiData.mttr.current = data.length > 0 ? data[data.length - 1].value : null;
                this.updateKPICard('mttr', this.kpiData.mttr);
                this.renderMTTRChart();
            })
            .catch(error => {
                console.error('Error loading MTTR data:', error);
                this.showErrorState('mttr');
            });

        // Fetch MTBF data
        this.fetchKPIHistory('MTBF_Calculated', startDate, endDate, assetId)
            .then(data => {
                this.kpiData.mtbf.history = data;
                this.kpiData.mtbf.current = data.length > 0 ? data[data.length - 1].value : null;
                this.updateKPICard('mtbf', this.kpiData.mtbf);
                this.renderMTBFChart();
            })
            .catch(error => {
                console.error('Error loading MTBF data:', error);
                this.showErrorState('mtbf');
            });

        // Fetch Failure Rate data
        this.fetchKPIHistory('FailureRate_Calculated', startDate, endDate, assetId)
            .then(data => {
                this.kpiData.failureRate.history = data;
                this.kpiData.failureRate.current = data.length > 0 ? data[data.length - 1].value : null;
                this.updateKPICard('failureRate', this.kpiData.failureRate);
                this.renderFailureRateChart();
            })
            .catch(error => {
                console.error('Error loading Failure Rate data:', error);
                this.showErrorState('failureRate');
            });

        // After all data is loaded, render comparison charts
        Promise.all([
            this.fetchKPIHistory('MTTR_Calculated', startDate, endDate, assetId),
            this.fetchKPIHistory('MTBF_Calculated', startDate, endDate, assetId),
            this.fetchKPIHistory('FailureRate_Calculated', startDate, endDate, assetId)
        ])
            .then(([mttrData, mtbfData, frData]) => {
                this.renderKPIComparisonChart();
                this.renderTargetVsActualChart();
            })
            .catch(error => {
                console.error('Error loading comparison data:', error);
            });
    },

    // Fetch KPI history data from API
    fetchKPIHistory: function(kpiName, startDate, endDate, assetId) {
        let url = `/api/dashboard/kpi/history/${kpiName}?limit=100`;

        if (startDate) {
            url += `&start_date=${encodeURIComponent(startDate)}`;
        }

        if (endDate) {
            url += `&end_date=${encodeURIComponent(endDate)}`;
        }

        if (assetId && assetId !== 'all') {
            url += `&asset_id=${encodeURIComponent(assetId)}`;
        }

        // Use regular fetch since this endpoint doesn't require authentication
        return fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to fetch ${kpiName} data`);
                }
                return response.json();
            });
    },

    // Show loading state for charts and cards
    showLoadingState: function() {
        // Update KPI cards with loading state
        ['mttr', 'mtbf', 'failureRate', 'availability'].forEach(kpi => {
            document.getElementById(`${kpi}Value`).textContent = 'Loading...';
            document.getElementById(`${kpi}Trend`).textContent = '';
            document.getElementById(`${kpi}Target`).textContent = '';
        });

        // Show loading state for chart containers
        document.querySelectorAll('.chart-container').forEach(container => {
            const canvas = container.querySelector('canvas');
            if (canvas && this.charts[canvas.id]) {
                this.charts[canvas.id].destroy();
                delete this.charts[canvas.id];
            }

            // Hide no-data message
            const noDataMsg = container.querySelector('.no-data-msg');
            if (noDataMsg) {
                noDataMsg.style.display = 'none';
            }
        });
    },

    // Show error state for a specific KPI
    showErrorState: function(kpi) {
        document.getElementById(`${kpi}Value`).textContent = 'Error';
        document.getElementById(`${kpi}Trend`).textContent = 'Failed to load data';
        document.getElementById(`${kpi}Target`).textContent = '';

        // Show error in chart
        const chartId = `${kpi}TrendChart`;
        const chartContainer = document.getElementById(chartId).parentElement;
        const noDataMsg = chartContainer.querySelector('.no-data-msg');
        if (noDataMsg) {
            noDataMsg.textContent = `Error loading ${kpi.toUpperCase()} data`;
            noDataMsg.style.display = 'block';
        }
    },

    // Update KPI card with data
    updateKPICard: function(kpi, data) {
        const valueElement = document.getElementById(`${kpi}Value`);
        const trendElement = document.getElementById(`${kpi}Trend`);
        const targetElement = document.getElementById(`${kpi}Target`);

        if (!valueElement || !trendElement || !targetElement) return;

        // Format current value based on KPI type
        if (data.current !== null) {
            if (kpi === 'mttr') {
                valueElement.textContent = `${data.current.toFixed(2)} hrs`;
            } else if (kpi === 'mtbf') {
                valueElement.textContent = `${data.current.toFixed(2)} hrs`;
            } else if (kpi === 'failureRate') {
                valueElement.textContent = `${data.current.toFixed(2)}/yr`;
            } else if (kpi === 'availability') {
                valueElement.textContent = `${data.current.toFixed(2)}%`;
            }

            // Calculate trend if we have history
            if (data.history.length >= 2) {
                const currentValue = data.history[data.history.length - 1].value;
                const previousValue = data.history[data.history.length - 2].value;
                const trend = currentValue - previousValue;

                // Determine if trend is good or bad based on KPI type
                let trendClass = '';
                let trendIcon = '';

                if (kpi === 'mttr' || kpi === 'failureRate') {
                    // For these KPIs, lower is better
                    if (trend < 0) {
                        trendClass = 'text-success';
                        trendIcon = '<i class="fas fa-arrow-down"></i>';
                    } else if (trend > 0) {
                        trendClass = 'text-danger';
                        trendIcon = '<i class="fas fa-arrow-up"></i>';
                    } else {
                        trendClass = 'text-muted';
                        trendIcon = '<i class="fas fa-equals"></i>';
                    }
                } else {
                    // For MTBF and availability, higher is better
                    if (trend > 0) {
                        trendClass = 'text-success';
                        trendIcon = '<i class="fas fa-arrow-up"></i>';
                    } else if (trend < 0) {
                        trendClass = 'text-danger';
                        trendIcon = '<i class="fas fa-arrow-down"></i>';
                    } else {
                        trendClass = 'text-muted';
                        trendIcon = '<i class="fas fa-equals"></i>';
                    }
                }

                trendElement.className = trendClass;
                trendElement.innerHTML = `${trendIcon} ${Math.abs(trend).toFixed(2)} vs previous`;
            } else {
                trendElement.className = 'text-muted';
                trendElement.textContent = 'No trend data';
            }

            // Show target
            if (data.target) {
                const gap = kpi === 'mttr' || kpi === 'failureRate'
                    ? data.current - data.target  // For these KPIs, lower is better
                    : data.target - data.current; // For MTBF and availability, higher is better

                let targetClass = '';
                if (gap > 0) {
                    targetClass = 'text-danger';
                } else if (gap < 0) {
                    targetClass = 'text-success';
                } else {
                    targetClass = 'text-muted';
                }

                targetElement.className = targetClass;

                if (kpi === 'mttr') {
                    targetElement.textContent = `Target: ${data.target.toFixed(2)} hrs`;
                } else if (kpi === 'mtbf') {
                    targetElement.textContent = `Target: ${data.target.toFixed(2)} hrs`;
                } else if (kpi === 'failureRate') {
                    targetElement.textContent = `Target: ${data.target.toFixed(2)}/yr`;
                } else if (kpi === 'availability') {
                    targetElement.textContent = `Target: ${data.target.toFixed(2)}%`;
                }
            }
        } else {
            valueElement.textContent = 'N/A';
            trendElement.className = 'text-muted';
            trendElement.textContent = 'No data available';
            targetElement.textContent = '';
        }
    },

    // Render MTTR trend chart
    renderMTTRChart: function() {
        const chartId = 'mttrTrendChart';
        const ctx = document.getElementById(chartId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
        }

        // Check if we have data
        if (!this.kpiData.mttr.history || this.kpiData.mttr.history.length === 0) {
            const noDataMsg = ctx.parentElement.querySelector('.no-data-msg');
            if (noDataMsg) {
                noDataMsg.style.display = 'block';
            }
            return;
        }

        // Prepare data
        const labels = this.kpiData.mttr.history.map(item =>
            new Date(item.timestamp).toLocaleDateString());
        const data = this.kpiData.mttr.history.map(item => item.value);

        // Create chart configuration
        const config = {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'MTTR (Hours)',
                    data: data,
                    borderColor: 'rgb(220, 53, 69)',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: AssetKPICharts.getChartOptions({
                title: 'Mean Time To Repair (MTTR) Trend',
                yAxisTitle: 'Hours',
                suggestedMin: 0
            })
        };

        // Create chart
        this.charts[chartId] = new Chart(ctx, config);
    },

    // Render MTBF trend chart
    renderMTBFChart: function() {
        const chartId = 'mtbfTrendChart';
        const ctx = document.getElementById(chartId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
        }

        // Check if we have data
        if (!this.kpiData.mtbf.history || this.kpiData.mtbf.history.length === 0) {
            const noDataMsg = ctx.parentElement.querySelector('.no-data-msg');
            if (noDataMsg) {
                noDataMsg.style.display = 'block';
            }
            return;
        }

        // Prepare data
        const labels = this.kpiData.mtbf.history.map(item =>
            new Date(item.timestamp).toLocaleDateString());
        const data = this.kpiData.mtbf.history.map(item => item.value);

        // Create chart configuration
        const config = {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'MTBF (Hours)',
                    data: data,
                    borderColor: 'rgb(40, 167, 69)',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: AssetKPICharts.getChartOptions({
                title: 'Mean Time Between Failures (MTBF) Trend',
                yAxisTitle: 'Hours',
                suggestedMin: 0
            })
        };

        // Create chart
        this.charts[chartId] = new Chart(ctx, config);
    },

    // Render Failure Rate trend chart
    renderFailureRateChart: function() {
        const chartId = 'failureRateTrendChart';
        const ctx = document.getElementById(chartId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
        }

        // Check if we have data
        if (!this.kpiData.failureRate.history || this.kpiData.failureRate.history.length === 0) {
            const noDataMsg = ctx.parentElement.querySelector('.no-data-msg');
            if (noDataMsg) {
                noDataMsg.style.display = 'block';
            }
            return;
        }

        // Prepare data
        const labels = this.kpiData.failureRate.history.map(item =>
            new Date(item.timestamp).toLocaleDateString());
        const data = this.kpiData.failureRate.history.map(item => item.value);

        // Create chart configuration
        const config = {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Failure Rate (per year)',
                    data: data,
                    borderColor: 'rgb(255, 193, 7)',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: AssetKPICharts.getChartOptions({
                title: 'Failure Rate Trend',
                yAxisTitle: 'Failures per Year',
                suggestedMin: 0
            })
        };

        // Create chart
        this.charts[chartId] = new Chart(ctx, config);
    },

    // Render KPI comparison chart
    renderKPIComparisonChart: function() {
        const chartId = 'kpiComparisonChart';
        const ctx = document.getElementById(chartId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
        }

        // Check if we have data for at least one KPI
        if ((!this.kpiData.mttr.current && !this.kpiData.mtbf.current &&
             !this.kpiData.failureRate.current && !this.kpiData.availability.current)) {
            const noDataMsg = ctx.parentElement.querySelector('.no-data-msg');
            if (noDataMsg) {
                noDataMsg.style.display = 'block';
            }
            return;
        }

        // Prepare data - normalize values for comparison
        const kpis = ['MTTR', 'MTBF', 'Failure Rate'];

        // Calculate percentages of target for each KPI
        const mttrPercentage = this.kpiData.mttr.current ?
            (this.kpiData.mttr.target / this.kpiData.mttr.current) * 100 : 0;

        const mtbfPercentage = this.kpiData.mtbf.current ?
            (this.kpiData.mtbf.current / this.kpiData.mtbf.target) * 100 : 0;

        const frPercentage = this.kpiData.failureRate.current ?
            (this.kpiData.failureRate.target / this.kpiData.failureRate.current) * 100 : 0;

        const data = [
            mttrPercentage,
            mtbfPercentage,
            frPercentage
        ];

        // Create chart configuration
        const config = {
            type: 'radar',
            data: {
                labels: kpis,
                datasets: [{
                    label: 'Performance vs Target (%)',
                    data: data,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgb(54, 162, 235)',
                    pointBackgroundColor: 'rgb(54, 162, 235)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgb(54, 162, 235)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        angleLines: {
                            display: true
                        },
                        suggestedMin: 0,
                        suggestedMax: 100,
                        ticks: {
                            stepSize: 20
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'KPI Performance vs Target'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.raw.toFixed(1)}%`;
                            }
                        }
                    }
                }
            }
        };

        // Create chart
        this.charts[chartId] = new Chart(ctx, config);
    },

    // Render Target vs Actual chart
    // Render Availability trend chart
    renderAvailabilityChart: function() {
        const chartId = "availabilityTrendChart";
        const ctx = document.getElementById(chartId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
        }

        // Check if we have data
        if (!this.kpiData.availability.history || this.kpiData.availability.history.length === 0) {
            const noDataMsg = ctx.parentElement.querySelector(".no-data-msg");
            if (noDataMsg) {
                noDataMsg.style.display = "block";
            }
            return;
        }

        // Prepare data
        const labels = this.kpiData.availability.history.map(item =>
            new Date(item.timestamp).toLocaleDateString());
        const data = this.kpiData.availability.history.map(item => item.value);

        // Create chart configuration
        const config = {
            type: "line",
            data: {
                labels: labels,
                datasets: [{
                    label: "Availability (%)",
                    data: data,
                    borderColor: "rgb(0, 123, 255)",
                    backgroundColor: "rgba(0, 123, 255, 0.1)",
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: AssetKPICharts.getChartOptions({
                title: "Asset Availability Trend",
                yAxisTitle: "Percentage (%)",
                suggestedMin: 0,
                suggestedMax: 100
            })
        };

        // Create chart
        this.charts[chartId] = new Chart(ctx, config);
    },

    renderTargetVsActualChart: function() {
        const chartId = 'targetVsActualChart';
        const ctx = document.getElementById(chartId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
        }

        // Check if we have data for at least one KPI
        if ((!this.kpiData.mttr.current && !this.kpiData.mtbf.current &&
             !this.kpiData.failureRate.current && !this.kpiData.availability.current)) {
            const noDataMsg = ctx.parentElement.querySelector('.no-data-msg');
            if (noDataMsg) {
                noDataMsg.style.display = 'block';
            }
            return;
        }

        // Prepare data
        const kpis = ['MTTR', 'MTBF', 'Failure Rate'];

        const actualValues = [
            this.kpiData.mttr.current || 0,
            this.kpiData.mtbf.current || 0,
            this.kpiData.failureRate.current || 0
        ];

        const targetValues = [
            this.kpiData.mttr.target || 0,
            this.kpiData.mtbf.target || 0,
            this.kpiData.failureRate.target || 0
        ];

        // Create chart configuration
        const config = {
            type: 'bar',
            data: {
                labels: kpis,
                datasets: [
                    {
                        label: 'Actual',
                        data: actualValues,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgb(54, 162, 235)',
                        borderWidth: 1
                    },
                    {
                        label: 'Target',
                        data: targetValues,
                        backgroundColor: 'rgba(255, 99, 132, 0.7)',
                        borderColor: 'rgb(255, 99, 132)',
                        borderWidth: 1
                    }
                ]
            },
            options: AssetKPICharts.getChartOptions({
                title: 'Target vs. Actual Values',
                yAxisTitle: 'Value',
                indexAxis: 'y'
            })
        };

        // Create chart
        this.charts[chartId] = new Chart(ctx, config);
    }
};

// The dashboard will be initialized from the kpi_dashboard.html template
