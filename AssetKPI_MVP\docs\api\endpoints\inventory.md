# Inventory API Endpoints

This document provides detailed information about the inventory-related API endpoints in the AssetKPI system.

## Spare Parts Management

### Get All Spare Parts

Retrieves a list of all spare parts in the inventory.

**Endpoint:** `GET /api/inventory/parts`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Query Parameters:**

| Parameter | Type | Required | Description | Default |
|-----------|------|----------|-------------|---------|
| limit | integer | No | Maximum number of results to return | 100 |
| offset | integer | No | Number of results to skip | 0 |
| sort | string | No | Field to sort by | partid |
| order | string | No | Sort order, either `asc` or `desc` | asc |

**Response:**

```json
[
  {
    "partid": 1,
    "partname": "Bearing Assembly",
    "partnumber": "BA-2021",
    "manufacturer": "SKF",
    "stockquantity": 15,
    "reorderlevel": 5,
    "unitprice": 120.50,
    "leadtimedays": 14,
    "eoq": 25.0,
    "calculated_safety_stock": 8.0,
    "abc_classification": "A",
    "lastrestocked": "2023-01-15"
  },
  ...
]
```

### Get Specific Spare Part

Retrieves details for a specific spare part.

**Endpoint:** `GET /api/inventory/parts/{part_id}`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| part_id | integer | Yes | ID of the spare part to retrieve |

**Response:**

```json
{
  "partid": 1,
  "partname": "Bearing Assembly",
  "partnumber": "BA-2021",
  "manufacturer": "SKF",
  "stockquantity": 15,
  "reorderlevel": 5,
  "unitprice": 120.50,
  "leadtimedays": 14,
  "eoq": 25.0,
  "calculated_safety_stock": 8.0,
  "abc_classification": "A",
  "lastrestocked": "2023-01-15",
  "ordering_cost": 50.0,
  "holding_cost_percent": 0.2,
  "annual_demand": 100.0,
  "demand_variability": 0.3,
  "lead_time_variability": 0.2,
  "service_level": 0.95,
  "days_of_supply": 45
}
```

### Update Spare Part

Updates details for a specific spare part.

**Endpoint:** `PUT /api/inventory/parts/{part_id}`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| part_id | integer | Yes | ID of the spare part to update |

**Request Body:**

```json
{
  "stockquantity": 20,
  "reorderlevel": 8,
  "unitprice": 125.75,
  "leadtimedays": 10
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Part updated successfully"
}
```

### Create Spare Part

Creates a new spare part in the inventory.

**Endpoint:** `POST /api/inventory/parts`

**Authentication Required:** Yes

**Permissions Required:** `ENGINEER` or higher

**Request Body:**

```json
{
  "partname": "Motor Coupling",
  "partnumber": "MC-3045",
  "manufacturer": "ABB",
  "stockquantity": 10,
  "reorderlevel": 3,
  "unitprice": 85.25,
  "leadtimedays": 7
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Part created successfully",
  "part_id": 97
}
```

### Delete Spare Part

Deletes a spare part from the inventory.

**Endpoint:** `DELETE /api/inventory/parts/{part_id}`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| part_id | integer | Yes | ID of the spare part to delete |

**Response:**

```json
{
  "status": "success",
  "message": "Part deleted successfully"
}
```

## Inventory Analysis

### Get Inventory Analysis

Retrieves inventory analysis data for all parts.

**Endpoint:** `GET /api/inventory/analysis`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Response:**

```json
[
  {
    "part_id": 1,
    "part_name": "Bearing Assembly",
    "current_stock": 15,
    "optimal_stock": 20,
    "stock_difference": -5,
    "current_cost": 1807.50,
    "optimal_cost": 2410.00,
    "potential_savings": 0.00,
    "days_of_supply": 45,
    "stockout_risk": 25.0,
    "abc_classification": "A"
  },
  ...
]
```

### Get Part Inventory Analysis

Retrieves detailed inventory analysis for a specific part.

**Endpoint:** `GET /api/inventory/analysis/{part_id}`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| part_id | integer | Yes | ID of the spare part to analyze |

**Response:**

```json
{
  "part_details": {
    "partid": 1,
    "partname": "Bearing Assembly",
    "partnumber": "BA-2021",
    "manufacturer": "SKF",
    "stockquantity": 15,
    "unitprice": 120.50
  },
  "eoq_calculation": {
    "annual_demand": 100,
    "ordering_cost": 50.00,
    "holding_cost": 24.10,
    "eoq_value": 25.0,
    "annual_ordering_cost": 200.00,
    "annual_holding_cost": 301.25,
    "total_annual_cost": 501.25,
    "optimal_order_frequency": 4
  },
  "safety_stock_calculation": {
    "avg_daily_demand": 0.27,
    "lead_time_days": 14,
    "demand_variability": 0.3,
    "lead_time_variability": 0.2,
    "service_level": 0.95,
    "safety_stock_value": 8.0,
    "reorder_point": 12.0
  },
  "inventory_analysis": {
    "current_stock": 15,
    "optimal_stock": 20,
    "stock_difference": -5,
    "current_cost": 1807.50,
    "optimal_cost": 2410.00,
    "potential_savings": 0.00,
    "days_of_supply": 45,
    "stockout_risk": 25.0
  }
}
```

### Get Inventory Optimization Report

Retrieves a comprehensive inventory optimization report.

**Endpoint:** `GET /api/inventory/optimization-report`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Response:**

```json
{
  "summary": {
    "total_parts": 92,
    "total_value": 125750.25,
    "optimal_value": 98450.75,
    "potential_savings": 27299.50
  },
  "status": {
    "stockout_risk_count": 15,
    "optimal_stock_count": 42,
    "overstock_count": 35
  },
  "savings": [
    {
      "part_id": 16,
      "part_name": "Hydraulic Pump",
      "current_stock": 8,
      "optimal_stock": 3,
      "stock_difference": 5,
      "current_cost": 4800.00,
      "optimal_cost": 1800.00,
      "potential_savings": 3000.00,
      "abc_classification": "A"
    },
    ...
  ],
  "stockout_risk": [
    {
      "part_id": 1,
      "part_name": "Bearing Assembly",
      "current_stock": 2,
      "reorder_point": 12,
      "days_of_supply": 6,
      "lead_time_days": 14,
      "stockout_risk": 85.0,
      "abc_classification": "A"
    },
    ...
  ],
  "recommendations": [
    {
      "part_id": 1,
      "part_name": "Bearing Assembly",
      "recommendation_type": "RESTOCK_ALERT",
      "reason": "Stock (2) <= Reorder Point (12)",
      "priority": 1
    },
    ...
  ],
  "abc_analysis": {
    "A": {
      "count": 18,
      "value": 75450.15
    },
    "B": {
      "count": 28,
      "value": 37725.08
    },
    "C": {
      "count": 46,
      "value": 12575.02
    }
  }
}
```

### Run Inventory Optimization

Manually triggers the inventory optimization job.

**Endpoint:** `GET /api/inventory/run-optimization`

**Authentication Required:** Yes

**Permissions Required:** `MANAGER` or `ADMIN`

**Response:**

```json
{
  "status": "success",
  "message": "Inventory optimization job completed successfully"
}
```

## EOQ Configuration

### Get EOQ Summary

Retrieves a summary of EOQ calculations.

**Endpoint:** `GET /api/inventory/eoq-summary`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Response:**

```json
{
  "total_parts": 92,
  "parts_with_eoq": 92
}
```

### Get EOQ Configuration

Retrieves EOQ configuration parameters.

**Endpoint:** `GET /api/inventory/config`

**Authentication Required:** Yes

**Permissions Required:** `VIEWER` or higher

**Response:**

```json
[
  {
    "id": 1,
    "parameter_name": "ordering_cost",
    "parameter_value": "50.00",
    "parameter_description": "Default cost of placing an order",
    "updated_at": "2023-03-15T10:30:00Z"
  },
  {
    "id": 2,
    "parameter_name": "holding_cost_percent",
    "parameter_value": "0.20",
    "parameter_description": "Annual holding cost as a percentage of item value",
    "updated_at": "2023-03-15T10:30:00Z"
  },
  ...
]
```

### Update EOQ Parameters for a Part

Updates EOQ parameters for a specific part.

**Endpoint:** `PUT /api/inventory/parts/{part_id}/eoq-params`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| part_id | integer | Yes | ID of the spare part to update |

**Request Body:**

```json
{
  "ordering_cost": 75.00,
  "holding_cost_percent": 0.25
}
```

**Response:**

```json
{
  "status": "success",
  "message": "EOQ parameters for part 1 updated successfully"
}
```

### Delete EOQ Parameters for a Part

Deletes custom EOQ parameters for a specific part, reverting to global defaults.

**Endpoint:** `DELETE /api/inventory/parts/{part_id}/eoq-params`

**Authentication Required:** Yes

**Permissions Required:** `ADMIN`

**URL Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| part_id | integer | Yes | ID of the spare part to update |

**Response:**

```json
{
  "status": "success",
  "message": "EOQ parameters for part 1 deleted successfully"
}
```
