# AssetKPI API Documentation

## Overview

The AssetKPI API provides programmatic access to asset management, inventory optimization, and KPI calculation functionality. This document describes the available endpoints, authentication requirements, and provides examples for common operations.

## Authentication

All API requests require authentication using one of the following methods:

### Firebase Authentication Token

For browser-based applications and user-specific operations, use a Firebase ID token in the Authorization header:

```
Authorization: Bearer <firebase_id_token>
```

### API Key Authentication

For server-to-server communication and automated processes, use an API key in the X-API-Key header:

```
X-API-Key: <api_key>
```

## Base URL

All API endpoints are relative to the base URL:

```
http://localhost:8000/api
```

For production environments, replace with your actual domain.

## Response Format

All responses are returned in JSON format. Successful responses typically include:

```json
{
  "status": "success",
  "data": { ... }
}
```

Error responses include:

```json
{
  "detail": "Error message describing what went wrong"
}
```

## Rate Limits

API requests are subject to rate limiting to ensure system stability and prevent abuse. Current limits are:

- 100 requests per minute for authenticated users (Firebase token)
- 200 requests per minute for API key authentication

Certain paths are excluded from rate limiting:
- `/static/*` - Static files (CSS, JavaScript, images)
- `/` - Home page
- `/login` - Login page
- `/logout` - Logout page
- `/dashboard` - Dashboard page

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1620000000
```

If you exceed the rate limit, you will receive a 429 Too Many Requests response with a Retry-After header indicating when you can try again:

```
Status: 429 Too Many Requests
Retry-After: 30
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1620000000
```

### Rate Limiting Best Practices

To avoid hitting rate limits:

1. **Cache responses** when appropriate to reduce the number of API calls
2. **Batch operations** when possible instead of making many small requests
3. **Implement exponential backoff** for retries when you receive a 429 response
4. **Use API keys** for server-to-server communication to get higher rate limits
5. **Monitor your usage** by checking the rate limit headers in responses

## Endpoints

### Inventory Management

#### Get All Spare Parts

Retrieves a list of all spare parts in the inventory.

- **URL**: `/inventory/parts`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `VIEWER` or higher
- **Query Parameters**:
  - `limit` (optional): Maximum number of results to return (default: 100)
  - `offset` (optional): Number of results to skip (default: 0)
  - `sort` (optional): Field to sort by (default: `partid`)
  - `order` (optional): Sort order, either `asc` or `desc` (default: `asc`)

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/inventory/parts" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
[
  {
    "partid": 1,
    "partname": "Bearing Assembly",
    "partnumber": "BA-2021",
    "manufacturer": "SKF",
    "stockquantity": 15,
    "reorderlevel": 5,
    "unitprice": 120.50,
    "leadtimedays": 14,
    "eoq": 25.0,
    "calculated_safety_stock": 8.0,
    "abc_classification": "A",
    "lastrestocked": "2023-01-15"
  },
  ...
]
```

#### Get Specific Spare Part

Retrieves details for a specific spare part.

- **URL**: `/inventory/parts/{part_id}`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `VIEWER` or higher
- **URL Parameters**:
  - `part_id`: ID of the spare part to retrieve

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/inventory/parts/1" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
{
  "partid": 1,
  "partname": "Bearing Assembly",
  "partnumber": "BA-2021",
  "manufacturer": "SKF",
  "stockquantity": 15,
  "reorderlevel": 5,
  "unitprice": 120.50,
  "leadtimedays": 14,
  "eoq": 25.0,
  "calculated_safety_stock": 8.0,
  "abc_classification": "A",
  "lastrestocked": "2023-01-15",
  "ordering_cost": 50.0,
  "holding_cost_percent": 0.2
}
```

#### Update Spare Part

Updates details for a specific spare part.

- **URL**: `/inventory/parts/{part_id}`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Permissions Required**: `ENGINEER` or higher
- **URL Parameters**:
  - `part_id`: ID of the spare part to update
- **Request Body**:
  - JSON object containing fields to update

**Example Request:**
```bash
curl -X PUT "http://localhost:8000/api/inventory/parts/1" \
  -H "Authorization: Bearer <firebase_id_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "stockquantity": 20,
    "reorderlevel": 8
  }'
```

**Example Response:**
```json
{
  "status": "success",
  "message": "Part updated successfully"
}
```

#### Get Inventory Analysis

Retrieves inventory analysis data for all parts.

- **URL**: `/inventory/analysis`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `VIEWER` or higher

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/inventory/analysis" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
[
  {
    "part_id": 1,
    "part_name": "Bearing Assembly",
    "current_stock": 15,
    "optimal_stock": 20,
    "stock_difference": -5,
    "current_cost": 1807.50,
    "optimal_cost": 2410.00,
    "potential_savings": 0.00,
    "days_of_supply": 45,
    "stockout_risk": 25.0,
    "abc_classification": "A"
  },
  ...
]
```

#### Get Part Inventory Analysis

Retrieves detailed inventory analysis for a specific part.

- **URL**: `/inventory/analysis/{part_id}`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `VIEWER` or higher
- **URL Parameters**:
  - `part_id`: ID of the spare part to analyze

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/inventory/analysis/1" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
{
  "part_details": {
    "partid": 1,
    "partname": "Bearing Assembly",
    "partnumber": "BA-2021",
    "manufacturer": "SKF",
    "stockquantity": 15,
    "unitprice": 120.50
  },
  "eoq_calculation": {
    "annual_demand": 100,
    "ordering_cost": 50.00,
    "holding_cost": 24.10,
    "eoq_value": 25.0,
    "annual_ordering_cost": 200.00,
    "annual_holding_cost": 301.25,
    "total_annual_cost": 501.25,
    "optimal_order_frequency": 4
  },
  "safety_stock_calculation": {
    "avg_daily_demand": 0.27,
    "lead_time_days": 14,
    "demand_variability": 0.3,
    "lead_time_variability": 0.2,
    "service_level": 0.95,
    "safety_stock_value": 8.0,
    "reorder_point": 12.0
  },
  "inventory_analysis": {
    "current_stock": 15,
    "optimal_stock": 20,
    "stock_difference": -5,
    "current_cost": 1807.50,
    "optimal_cost": 2410.00,
    "potential_savings": 0.00,
    "days_of_supply": 45,
    "stockout_risk": 25.0
  }
}
```

#### Run Inventory Optimization

Manually triggers the inventory optimization job.

- **URL**: `/inventory/run-optimization`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `MANAGER` or `ADMIN`

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/inventory/run-optimization" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
{
  "status": "success",
  "message": "Inventory optimization job completed successfully"
}
```

### KPI Management

#### Get Latest KPIs

Retrieves the latest KPI values.

- **URL**: `/kpis/latest`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `VIEWER` or higher

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/kpis/latest" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
{
  "mttr": 4.5,
  "mtbf": 720.0,
  "failure_rate": 12.2,
  "data_quality_score": 85.0,
  "report_date": "2023-04-15T00:00:00Z"
}
```

#### Get KPI History

Retrieves historical values for a specific KPI.

- **URL**: `/kpis/history/{kpi_name}`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `VIEWER` or higher
- **URL Parameters**:
  - `kpi_name`: Name of the KPI (e.g., `MTTR_Calculated`, `MTBF_Calculated`)
- **Query Parameters**:
  - `start_date` (optional): Start date for history (format: YYYY-MM-DD)
  - `end_date` (optional): End date for history (format: YYYY-MM-DD)
  - `limit` (optional): Maximum number of results to return (default: 100)

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/kpis/history/MTTR_Calculated?start_date=2023-01-01&end_date=2023-04-15" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
[
  {
    "id": 1,
    "kpi_name": "MTTR_Calculated",
    "kpi_value": 4.5,
    "kpi_unit": "hours",
    "calculation_date": "2023-04-15T00:00:00Z",
    "calculation_source": "scheduled_job"
  },
  {
    "id": 2,
    "kpi_name": "MTTR_Calculated",
    "kpi_value": 4.8,
    "kpi_unit": "hours",
    "calculation_date": "2023-04-08T00:00:00Z",
    "calculation_source": "scheduled_job"
  },
  ...
]
```

### Work Order Management

#### Get Work Orders

Retrieves a list of work orders.

- **URL**: `/workorders`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `VIEWER` or higher
- **Query Parameters**:
  - `status` (optional): Filter by status (e.g., `OPEN`, `CLOSED`)
  - `type` (optional): Filter by work order type (e.g., `Corrective`, `Preventive`)
  - `asset_id` (optional): Filter by asset ID
  - `limit` (optional): Maximum number of results to return (default: 100)
  - `offset` (optional): Number of results to skip (default: 0)

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/workorders?status=OPEN&type=Corrective" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
[
  {
    "workorderid": 1,
    "assetid": 5,
    "workordertype": "Corrective",
    "description": "Replace bearing assembly",
    "status": "OPEN",
    "assignedto": "John Smith",
    "failurecode": "MECH-001",
    "failuretype": "Mechanical",
    "downtimeminutes": 120,
    "repairtimeminutes": 90,
    "maintenancecost": 350.00,
    "startdate": "2023-04-10T08:00:00Z",
    "enddate": null
  },
  ...
]
```

#### Create Work Order

Creates a new work order.

- **URL**: `/ingest/workorder`
- **Method**: `POST`
- **Auth Required**: Yes
- **Permissions Required**: `ENGINEER` or higher, or valid API key
- **Request Body**:
  - JSON object containing work order details

**Example Request:**
```bash
curl -X POST "http://localhost:8000/api/ingest/workorder" \
  -H "X-API-Key: <api_key>" \
  -H "Content-Type: application/json" \
  -d '{
    "assetId": 5,
    "workOrderType": "Corrective",
    "description": "Replace motor coupling",
    "status": "OPEN",
    "assignedTo": "Jane Doe",
    "failureCode": "MECH-002",
    "failureType": "Mechanical",
    "downtimeMinutes": 180,
    "repairTimeMinutes": 120,
    "maintenanceCost": 450.00,
    "startDate": "2023-04-16T09:00:00Z"
  }'
```

**Example Response:**
```json
{
  "status": "success",
  "message": "Work order created successfully",
  "workorder_id": 25
}
```

#### Update Work Order

Updates an existing work order.

- **URL**: `/workorders/{workorder_id}`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Permissions Required**: `ENGINEER` or higher
- **URL Parameters**:
  - `workorder_id`: ID of the work order to update
- **Request Body**:
  - JSON object containing fields to update

**Example Request:**
```bash
curl -X PUT "http://localhost:8000/api/workorders/25" \
  -H "Authorization: Bearer <firebase_id_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "CLOSED",
    "endDate": "2023-04-16T14:30:00Z",
    "repairTimeMinutes": 150,
    "maintenanceCost": 520.00
  }'
```

**Example Response:**
```json
{
  "status": "success",
  "message": "Work order updated successfully"
}
```

### User Management

#### Get Current User

Retrieves information about the currently authenticated user.

- **URL**: `/users/me`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: Any authenticated user

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/users/me" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
{
  "uid": "firebase-test-admin-uid",
  "email": "<EMAIL>",
  "role": "ADMIN",
  "full_name": "Johan Borgulf",
  "created_at": "2023-03-15T10:30:00Z"
}
```

#### Get All Users

Retrieves a list of all users.

- **URL**: `/users`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `ADMIN`

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/users" \
  -H "Authorization: Bearer <firebase_id_token>"
```

**Example Response:**
```json
[
  {
    "uid": "firebase-test-admin-uid",
    "email": "<EMAIL>",
    "role": "ADMIN",
    "full_name": "Johan Borgulf",
    "created_at": "2023-03-15T10:30:00Z"
  },
  {
    "uid": "firebase-test-manager-uid",
    "email": "<EMAIL>",
    "role": "MANAGER",
    "full_name": "Manager User",
    "created_at": "2023-03-16T11:45:00Z"
  },
  ...
]
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - The request was malformed or contains invalid parameters |
| 401 | Unauthorized - Authentication is required or the provided credentials are invalid |
| 403 | Forbidden - The authenticated user does not have permission to access the requested resource |
| 404 | Not Found - The requested resource does not exist |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - An unexpected error occurred on the server |

## Changelog

| Date | Version | Description |
|------|---------|-------------|
| 2023-04-16 | 1.0.0 | Initial API documentation |
