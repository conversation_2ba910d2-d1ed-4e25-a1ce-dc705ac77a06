        // Create Connection button click event
        $('#createConnectionBtn').click(function() {
            $('#createConnectionModal').modal('show');
        });
        
        // Handle form submission
        $('#saveConnectionBtn').click(function() {
            // Get form values
            const connectionName = $('#connectionName').val();
            const systemType = $('#systemType').val();
            const serverUrl = $('#serverUrl').val();
            const username = $('#username').val();
            const password = $('#password').val();
            
            // Validate form
            if (!connectionName || !systemType || !serverUrl) {
                $('#connectionFormError').text('Please fill in all required fields.').show();
                return;
            }
            
            // Hide error message
            $('#connectionFormError').hide();
            
            // Show loading state
            $(this).prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...');
            
            // Simulate API call
            setTimeout(function() {
                // Add new row to table
                const newRow = `
                    <tr>
                        <td>${Math.floor(Math.random() * 1000)}</td>
                        <td>${connectionName}</td>
                        <td>${systemType}</td>
                        <td><span class="badge bg-success">Active</span></td>
                        <td>Never</td>
                        <td>
                            <button class="btn btn-sm btn-primary"><i class="fas fa-sync"></i></button>
                            <button class="btn btn-sm btn-info"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>
                `;
                
                // Remove "No connections" row if it exists
                if ($('#erpConnectionsTable tbody tr td').length === 1 && 
                    $('#erpConnectionsTable tbody tr td').text().includes('No ERP connections')) {
                    $('#erpConnectionsTable tbody').empty();
                }
                
                // Add new row
                $('#erpConnectionsTable tbody').append(newRow);
                
                // Reset form
                $('#createConnectionForm')[0].reset();
                
                // Hide modal
                $('#createConnectionModal').modal('hide');
                
                // Show success message
                const alert = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        Connection "${connectionName}" created successfully!
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                $('.container-fluid').prepend(alert);
                
                // Reset button
                $('#saveConnectionBtn').prop('disabled', false).html('Save Connection');
            }, 1500);
        });
