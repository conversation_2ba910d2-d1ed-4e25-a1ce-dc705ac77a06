# Create a temporary file with the fixed code
with open('main.py', 'r') as file:
    content = file.read()

# Replace the problematic code
old_code = """            # Calculate and store EOQ
            eoq_result = calculate_and_store_eoq(
                app=request.app,"""

new_code = """            # Calculate and store EOQ
            eoq_result = calculate_and_store_eoq("""

content = content.replace(old_code, new_code)

# Write the fixed content back to the file
with open('main.py', 'w') as file:
    file.write(content)

print("Fixed the EOQ calculation code in main.py")
