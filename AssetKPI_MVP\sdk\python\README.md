# AssetKPI Python Client

A Python client for the AssetKPI API.

## Installation

```bash
pip install assetkpi-client
```

## Usage

```python
from assetkpi_client import AssetKPIClient

# Initialize the client with API key
client = AssetKPIClient(
    base_url="http://localhost:8000/api",
    api_key="your-api-key"
)

# Or with Firebase token
client = AssetKPIClient(
    base_url="http://localhost:8000/api",
    firebase_token="your-firebase-token"
)

# Get all assets
assets = client.get_assets()

# Get assets with filtering and pagination
assets = client.get_assets(
    limit=10,
    offset=0,
    sort_by="assetname",
    sort_order="asc",
    status="ACTIVE",
    location="Building A"
)

# Get inventory parts
parts = client.get_inventory_parts(
    limit=20,
    sort_by="stockquantity",
    sort_order="desc"
)

# Get work orders
work_orders = client.get_work_orders(
    status="OPEN",
    sort_by="startdate",
    sort_order="desc"
)

# Get KPI history
kpi_history = client.get_kpi_history(
    kpi_name="MTTR_Calculated",
    start_date="2023-01-01",
    end_date="2023-12-31"
)

# Create a work order
result = client.create_work_order(
    asset_id=5,
    work_order_type="Corrective",
    description="Replace motor coupling",
    status="OPEN",
    assigned_to="Jane Doe",
    downtime_minutes=180,
    start_date="2023-04-16T09:00:00Z"
)
```

## Advanced Usage

### Pagination

For endpoints that return large collections, you can use pagination:

```python
# Get all items with automatic pagination
all_parts = client.get_paginated("/inventory/parts")

# Get a specific number of items
first_100_parts = client.get_paginated("/inventory/parts", max_items=100)
```

### Bulk Operations

For creating, updating, or deleting multiple items at once:

```python
# Bulk create
items = [
    {"partname": "Part 1", "stockquantity": 10},
    {"partname": "Part 2", "stockquantity": 20}
]
result = client.bulk_create("/inventory/parts", items)

# Bulk update
updates = [
    {"partid": 1, "stockquantity": 15},
    {"partid": 2, "stockquantity": 25}
]
result = client.bulk_update("/inventory/parts", updates)

# Bulk delete
result = client.bulk_delete("/inventory/parts", [1, 2, 3])
```

### Error Handling

```python
from requests.exceptions import HTTPError, ConnectionError, Timeout

try:
    result = client.get("/inventory/parts")
except HTTPError as e:
    if e.response.status_code == 401:
        print("Authentication failed")
    elif e.response.status_code == 403:
        print("Permission denied")
    elif e.response.status_code == 404:
        print("Resource not found")
    else:
        print(f"HTTP error: {e}")
except ConnectionError:
    print("Connection error")
except Timeout:
    print("Request timed out")
except Exception as e:
    print(f"An error occurred: {e}")
```

## Configuration

The client can be configured with various options:

```python
import logging

# Configure logging
logger = logging.getLogger("assetkpi")
logger.setLevel(logging.DEBUG)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s"))
logger.addHandler(handler)

# Initialize client with custom configuration
client = AssetKPIClient(
    base_url="http://localhost:8000/api",
    api_key="your-api-key",
    timeout=60,  # 60 seconds timeout
    max_retries=5,  # Retry up to 5 times
    retry_delay=2,  # Start with 2 seconds delay between retries
    logger=logger  # Custom logger
)
```

## License

MIT
