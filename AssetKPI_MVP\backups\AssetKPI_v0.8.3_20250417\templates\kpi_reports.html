{% extends "layout.html" %}

{% block title %}AssetKPI - KPI Reports{% endblock %}

{% block styles %}
<style>
    .kpi-card {
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .kpi-card:hover {
        transform: translateY(-5px);
    }
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
        margin: 20px 0;
    }
    .table-responsive {
        overflow-x: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>KPI Reports</h1>
        <p class="text-muted">View and analyze key performance indicators</p>
    </div>
</div>

<!-- KPI Charts -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>KPI Trends</h5>
                <div class="d-flex mt-2">
                    <div class="input-group me-2" style="max-width: 200px;">
                        <span class="input-group-text">From</span>
                        <input type="date" id="startDateInput" class="form-control">
                    </div>
                    <div class="input-group me-2" style="max-width: 200px;">
                        <span class="input-group-text">To</span>
                        <input type="date" id="endDateInput" class="form-control">
                    </div>
                    <button id="filterButton" class="btn btn-primary">Apply Filter</button>
                </div>
                <div id="chartErrorMsg" class="text-danger mt-2"></div>
            </div>
            <div class="card-body">
                <h6>MTTR Trend (Calculated)</h6>
                <div class="chart-container">
                    <canvas id="mttrTrendChart"></canvas>
                    <p class="no-data-msg text-center" style="display: none;">No historical MTTR data available for selected period.</p>
                </div>

                <h6 class="mt-4">MTBF Trend (Calculated)</h6>
                <div class="chart-container">
                    <canvas id="mtbfTrendChart"></canvas>
                    <p class="no-data-msg text-center" style="display: none;">No historical MTBF data available for selected period.</p>
                </div>
                
                <h6 class="mt-4">Failure Rate Trend (Calculated)</h6>
                <div class="chart-container">
                    <canvas id="failureRateTrendChart"></canvas>
                    <p class="no-data-msg text-center" style="display: none;">No historical Failure Rate data available for selected period.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPI Reports Table -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>KPI Reports History</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Report Date</th>
                                <th>MTTR (hours)</th>
                                <th>MTBF (hours)</th>
                                <th>OEE (%)</th>
                                <th>Avg Downtime</th>
                                <th>Preventive Ratio</th>
                                <th>Open WOs</th>
                                <th>Closed WOs</th>
                                <th>Maintenance Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if kpi_reports %}
                                {% for report in kpi_reports %}
                                    <tr>
                                        <td>{{ report.reportdate }}</td>
                                        <td>{{ report.mttr }}</td>
                                        <td>{{ report.mtbf }}</td>
                                        <td>{{ report.oee }}</td>
                                        <td>{{ report.avgdowntime }}</td>
                                        <td>{{ report.preventiveratio }}</td>
                                        <td>{{ report.openworkorders }}</td>
                                        <td>{{ report.closedworkorders }}</td>
                                        <td>{{ report.maintenancecost }}</td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">No KPI reports available</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>

<script>
    // Chart rendering scripts
    const chartInstances = {}; // Store chart instances to destroy them before re-rendering

    // Generic function to fetch data and render/update a KPI chart
    async function renderKpiChart(kpiName, canvasId, yAxisLabel) {
        const chartElement = document.getElementById(canvasId);
        const chartContainer = chartElement?.parentElement;
        const noDataElement = chartContainer?.querySelector('.no-data-msg');
        const errorMsgElement = document.getElementById('chartErrorMsg');

        if (!chartElement || !chartContainer || !noDataElement || !errorMsgElement) {
            console.error(`Chart rendering setup failed for canvas ID: ${canvasId}`);
            return;
        }

        // Clear previous errors/no-data messages for this specific chart
        errorMsgElement.textContent = '';
        noDataElement.style.display = 'none'; // Hide no-data message initially

        const ctx = chartElement.getContext('2d');

        // Destroy previous chart instance for this canvas if it exists
        if (chartInstances[canvasId]) {
            chartInstances[canvasId].destroy();
        }

        try {
            // Get date values
            const startDate = document.getElementById('startDateInput').value;
            const endDate = document.getElementById('endDateInput').value;

            // Construct API URL with query parameters if dates are provided
            let apiUrl = `/api/kpi/history/${kpiName}?limit=1000`; // Fetch more points if filtering
            if (startDate) {
                apiUrl += `&start_date=${encodeURIComponent(startDate)}`;
            }
            if (endDate) {
                apiUrl += `&end_date=${encodeURIComponent(endDate)}`;
            }

            // Fetch data with authentication
            const response = await AssetKPIAuth.authenticatedFetch(apiUrl);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: `HTTP error! status: ${response.status}`}));
                throw new Error(errorData.detail || `Failed to fetch data: ${response.status}`);
            }

            const historyData = await response.json();

            // Check if data is empty AFTER fetch
            if (!historyData || historyData.length === 0) {
                noDataElement.style.display = 'block'; // Show no-data message
                return; // Don't try to render empty chart
            }

            // Prepare data
            const labels = historyData.map(point => new Date(point.timestamp).toLocaleString([], {
                year: 'numeric', month: 'numeric', day: 'numeric',
                hour: '2-digit', minute: '2-digit'
            }));
            const dataPoints = historyData.map(point => point.value);

            // Basic color mapping for different KPIs
            const colors = {
                'MTTR_Calculated': 'rgb(75, 192, 192)',
                'MTBF_Calculated': 'rgb(255, 99, 132)',
                'FailureRate_Calculated': 'rgb(54, 162, 235)',
                'default': 'rgb(54, 162, 235)'
            };
            const borderColor = colors[kpiName] || colors['default'];

            // Chart config
            const chartConfig = {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${yAxisLabel}`,
                        data: dataPoints,
                        borderColor: borderColor,
                        tension: 0.1,
                        fill: false,
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: { display: true, text: yAxisLabel }
                        },
                        x: {
                            title: { display: true, text: 'Calculation Timestamp' },
                            ticks: {
                                maxRotation: 70,
                                minRotation: 45,
                                autoSkip: true,
                                maxTicksLimit: 25
                            }
                        }
                    },
                    plugins: {
                        legend: { display: true },
                        tooltip: { mode: 'index', intersect: false }
                    },
                    hover: { mode: 'nearest', intersect: true }
                }
            };

            // Create and store chart instance
            chartInstances[canvasId] = new Chart(ctx, chartConfig);

        } catch (error) {
            console.error(`Error fetching or rendering chart (${kpiName}):`, error);
            errorMsgElement.textContent = `Error loading ${kpiName} chart: ${error.message}`; // Show error
            noDataElement.style.display = 'none'; // Hide no-data msg if error occurred
        }
    }

    // Function to update all charts based on filter
    function updateCharts() {
        console.log("Updating charts based on date filter...");
        // Clear general error message before attempting updates
        const errorMsgElement = document.getElementById('chartErrorMsg');
        if(errorMsgElement) errorMsgElement.textContent = '';

        renderKpiChart('MTTR_Calculated', 'mttrTrendChart', 'MTTR (Hours)');
        renderKpiChart('MTBF_Calculated', 'mtbfTrendChart', 'MTBF (Hours)');
        renderKpiChart('FailureRate_Calculated', 'failureRateTrendChart', 'Failures per Year');
    }

    // Add event listener to the filter button
    document.addEventListener('DOMContentLoaded', function() {
        const filterButton = document.getElementById('filterButton');
        if (filterButton) {
            filterButton.addEventListener('click', updateCharts);
        }
        
        // Initial chart render
        updateCharts();
    });
</script>
{% endblock %}
