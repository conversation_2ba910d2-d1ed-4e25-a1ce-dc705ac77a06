import requests
import json
import sys
import time
import base64

# Base URL
base_url = "http://127.0.0.1:8000"

def test_login_api():
    """
    Test the login API directly.
    """
    print("Testing login API directly...")
    
    # Test the debug token endpoint
    try:
        # Get the Firebase ID token (you would normally get this from Firebase)
        # For testing, we'll use a dummy token
        token = "dummy_token"
        
        # Call the debug token endpoint
        response = requests.post(
            f"{base_url}/api/debug-token",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        print(f"Debug token endpoint status code: {response.status_code}")
        try:
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        except:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error testing debug token endpoint: {e}")
    
    # Test the user check endpoint
    try:
        # Use a known user ID from the database
        user_id = "uasUzj4IXFaqJC3pcEiOCL3vD3t2"  # Replace with a real user ID
        
        # Call the user check endpoint
        response = requests.get(f"{base_url}/api/user-check/{user_id}")
        
        print(f"User check endpoint status code: {response.status_code}")
        try:
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        except:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error testing user check endpoint: {e}")
    
    # Test the registration endpoint
    try:
        # Get the Firebase ID token (you would normally get this from Firebase)
        # For testing, we'll use a dummy token
        token = "dummy_token"
        
        # Call the registration endpoint
        response = requests.post(
            f"{base_url}/api/register",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        print(f"Registration endpoint status code: {response.status_code}")
        try:
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        except:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error testing registration endpoint: {e}")

if __name__ == "__main__":
    test_login_api()
