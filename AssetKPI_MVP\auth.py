from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2P<PERSON>wordBearer
from sqlalchemy.orm import Session
import firebase_admin
from firebase_admin import auth
import sys
import os

# Import test user authentication
from test_user_auth import handle_test_user_token, is_test_user_token

# Add the parent directory to sys.path to allow imports from main
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import User model and database dependency
from main import User, UserRole, get_db

# This scheme expects a header like "Authorization: Bearer <token>"
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")  # tokenUrl is placeholder, not used for validation here

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db), request: Request = None) -> User:
    """
    Dependency to verify Firebase ID token and retrieve user from local DB.
    Also handles test user tokens.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    if not token:
        raise credentials_exception

    # Check if this is a test user token
    if is_test_user_token(token):
        print(f"INFO: Using test user token: {token}")
        try:
            # Handle test user token
            user = handle_test_user_token(token, db)
            print(f"INFO: Test user {user.email} (Role: {user.role}) authenticated successfully.")
            return user
        except Exception as e:
            print(f"ERROR: Test user authentication failed: {e}")
            raise credentials_exception

    # Not a test user token, proceed with Firebase authentication
    try:
        # Verify the ID token while checking if the token is revoked.
        decoded_token = auth.verify_id_token(token, check_revoked=True)
        firebase_uid = decoded_token.get("uid")
        if firebase_uid is None:
            print("ERROR: UID not found in decoded token")
            raise credentials_exception

    except auth.ExpiredIdTokenError:
        print("ERROR: Firebase token expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except auth.InvalidIdTokenError as e:
        print(f"ERROR: Invalid Firebase token: {e}")
        raise credentials_exception
    except Exception as e:  # Catch other potential firebase_admin errors
        print(f"ERROR: Firebase token verification failed: {e}")
        raise credentials_exception

    # Token is valid, now get user from our database
    user = db.query(User).filter(User.user_id == firebase_uid).first()
    if user is None:
        # This case means the user is authenticated with Firebase,
        # but doesn't exist in our local 'users' table.
        print(f"ERROR: User with UID {firebase_uid} authenticated but not found in local DB.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User not registered in this application",
        )

    print(f"INFO: User {user.email} (Role: {user.role}) authenticated successfully.")
    return user

def require_role(required_roles: list[UserRole]):
    """
    Factory for creating dependency that checks if user has one of the required roles.
    """
    async def role_checker(current_user: User = Depends(get_current_user)):
        if current_user.role not in required_roles:
            print(f"ERROR: User {current_user.email} (Role: {current_user.role}) does not have required role(s): {required_roles}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required role: {required_roles}",
            )
        return current_user
    return role_checker

# Define common role requirements
require_admin = require_role([UserRole.ADMIN])
require_manager_or_admin = require_role([UserRole.MANAGER, UserRole.ADMIN])
require_engineer_plus = require_role([UserRole.ENGINEER, UserRole.MANAGER, UserRole.ADMIN])
require_viewer_plus = require_role([UserRole.VIEWER, UserRole.ENGINEER, UserRole.MANAGER, UserRole.ADMIN])
